/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.business.managedobject.dto;

import com.huawei.i2000.dvanalysisengineextservice.constants.MoConstant;
import com.huawei.oms.eam.mo.ManagedObject;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/1/23
 */

@Getter
@Setter
public class SimpleMo {
    private String dn = "/";
    private String name = MoConstant.ROOT_NAME;
    private String type = MoConstant.DISPLAY_EMPTY;
    private String parentDn = "/";

    public SimpleMo() {
    }

    public SimpleMo(ManagedObject managedObject) {
        if (Objects.isNull(managedObject)) {
            return;
        }
        this.dn = managedObject.getDN().getValue();
        this.name = managedObject.getName();
        this.type = managedObject.getType();
        this.parentDn = managedObject.getParent().getValue();
    }
}
