/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.business.alarm.kafka;

import com.huawei.bsp.deploy.util.DefaultEnvUtil;
import com.huawei.bsp.log.Logger;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.i2000.dvanalysisengineextservice.util.PropertiesUtil;

import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * KafkaConfigUtil kafka配置获取类
 *
 * <AUTHOR>
 * @since 2023-07-26
 */
public class KafkaConfigUtil {
    private static final String CONFIG_DIR = DefaultEnvUtil.getAppShareDir() + File.separator + "config" + File.separator;

    private static final String KAFKA_CONFIG_FILE = "kafka.config.properties";

    private static final String KAFKA_CONSUMER_EXTEND_PARAMS = "kafka.consumer.extend.params";

    private static final String KAFKA_CONFIG_FILEPATH = CONFIG_DIR + KAFKA_CONFIG_FILE;

    private static final Logger LOGGER = OssLogFactory.getLog(KafkaConfigUtil.class);

    private static volatile KafkaConfigUtil instance;

    private Properties props;

    private void init() {
        props = PropertiesUtil.loadProperties(KAFKA_CONFIG_FILEPATH);
    }

    private String getConfig(String key, String defaultValue) {
        return props.getProperty(key, defaultValue);
    }

    private int getIntConfig(String key, int defaultValue, int minValue, int maxValue) {
        if (StringUtils.isEmpty(props.getProperty(key))) {
            return defaultValue;
        }
        try {
            int proValue = Integer.parseInt(props.getProperty(key));
            if (proValue > maxValue) {
                LOGGER.warn("key:{} value:{}, larger then maxValue:{},will use maxValue", key , proValue, maxValue);
                proValue = maxValue;
            } else if (proValue < minValue) {
                LOGGER.warn("key:{} value:{}, less then minValue:{},will use minValue", key , proValue, minValue);
                proValue = minValue;
            }
            return proValue;
        } catch (NumberFormatException e) {
            LOGGER.error("value is not int,will use default value,key:{},value:{},default value:{}", key,
                props.getProperty(key), defaultValue);
            return defaultValue;
        }
    }

    private Long getLongConfig(String key, Long defaultValue, long minValue, long maxValue) {
        if (StringUtils.isEmpty(props.getProperty(key))) {
            return defaultValue;
        }
        try {
            long proValue = Long.parseLong(props.getProperty(key));
            if (proValue > maxValue) {
                proValue = maxValue;
                LOGGER.warn("key:{} value:{}, larger then maxValue:{},will use maxValue", key , proValue, maxValue);
            } else if (proValue < minValue) {
                proValue = minValue;
                LOGGER.warn("key:{} value:{}, less then minValue:{},will use minValue", key , proValue, minValue);
            }
            return proValue;
        } catch (NumberFormatException e) {
            LOGGER.error("value is not long,will use default value,key:{},value:{},default value:{}", key,
                props.getProperty(key), defaultValue);
            return defaultValue;
        }
    }

    private KafkaConfigUtil() {
        init();
    }

    public static KafkaConfigUtil getInstance() {
        if (instance == null) {
            synchronized (KafkaConfigUtil.class) {
                if (instance == null) {
                    instance = new KafkaConfigUtil();
                }
            }
        }
        return instance;
    }

    public Map<String, Object> getConsumerConfig(Map<String, Object> configs) {
        configs.put(KafkaConstant.KEY_HEARTBEAT_INTERVAL_MS,
            getIntConfig(KafkaConstant.KEY_HEARTBEAT_INTERVAL_MS, 3000,1000,10000));
        configs.put(KafkaConstant.KEY_FETCH_MIN_BYTES, getIntConfig(KafkaConstant.KEY_FETCH_MIN_BYTES, 1,1,10485760));
        configs.put(KafkaConstant.KEY_FETCH_MAX_WAIT_MS, getIntConfig(KafkaConstant.KEY_FETCH_MAX_WAIT_MS, 500,1,60000));
        configs.put(KafkaConstant.KEY_MAX_PARTITION_FETCH_BYTES,
            getLongConfig(KafkaConstant.KEY_MAX_PARTITION_FETCH_BYTES,
                KafkaConstant.MAX_PARTITION_FETCH_BYTES_DEFAULT_VALUE,1L,209715200L));
        configs.put(KafkaConstant.KEY_FETCH_MAX_BYTES,
            getLongConfig(KafkaConstant.KEY_FETCH_MAX_BYTES, KafkaConstant.FETCH_MAX_BYTES_DEFAULT_VALUE,1L,209715200L));
        configs.put(KafkaConstant.KEY_SESSION_TIMEOUT_MS,
            getIntConfig(KafkaConstant.KEY_SESSION_TIMEOUT_MS, KafkaConstant.SESSION_TIMEOUT_DEFAULT_VALUE,1000,300000));
        configs.put(KafkaConstant.KEY_CONNECTIONS_MAX_IDLE_MS,
            getIntConfig(KafkaConstant.KEY_KAFKA_CONSUMER_CONNECTIONS_MAX_IDLE_MS, 540000,3000,3600000));
        configs.put(KafkaConstant.KEY_MAX_POLL_INTERVAL_MS,
            getIntConfig(KafkaConstant.KEY_MAX_POLL_INTERVAL_MS, 300000,1000,1200000));
        configs.put(KafkaConstant.KEY_RECEIVE_BUFFER_BYTES,
            getIntConfig(KafkaConstant.KEY_KAFKA_CONSUMER_RECEIVE_BUFFER_BYTES, 65536,1,10485760));
        configs.put(KafkaConstant.KEY_SEND_BUFFER_BYTES,
            getIntConfig(KafkaConstant.KEY_KAFKA_CONSUMER_SEND_BUFFER_BYTES, 131072,1,10485760));
        String extendParams = getConfig(KAFKA_CONSUMER_EXTEND_PARAMS, null);
        configs.putAll(getExtendMap(extendParams));
        configs.put(KafkaConstant.KEY_DESERIALIZER, KafkaConstant.STRING_DESERIALIZER);
        configs.put(KafkaConstant.VALUE_DESERIALIZER, KafkaConstant.STRING_DESERIALIZER);
        configs.put(KafkaConstant.KEY_AUTO_COMMMIT, KafkaConstant.AUTO_COMMMIT_VALUE);
        return configs;
    }

    private static Map<String, Object> getExtendMap(String extendParams) {
        Map<String, Object> extendMap = new HashMap<>();
        if (StringUtils.isBlank(extendParams)) {
            return extendMap;
        }
        String[] params = extendParams.split("<;>");
        for (String param : params) {
            String[] para = param.split("<=>");
            if (para.length == 2) {
                if (StringUtils.isNumeric(para[1])) {
                    extendMap.put(para[0], Integer.parseInt(para[1]));
                } else if ("true".equalsIgnoreCase(para[1]) || "false".equalsIgnoreCase(para[1])) {
                    extendMap.put(para[0], Boolean.parseBoolean(para[1]));
                } else {
                    extendMap.put(para[0], para[1]);
                }
            }
        }
        return extendMap;
    }
}
