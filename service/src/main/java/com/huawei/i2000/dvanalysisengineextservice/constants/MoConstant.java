/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.constants;

/**
 * 网元相关常量
 *
 * <AUTHOR>
 * @since 2025/1/23
 */

public interface MoConstant {
    String DISPLAY_SPLIT = ",";
    String DISPLAY_EMPTY = "N/A";
    String ROOT_DN = "/";
    String ROOT_NAME = "ROOT";
    String MO_TYPE_VM = "com.huawei.IRes.vm";
    String MO_TYPE_DOCKER = "com.huawei.URes.docker";
    String SOLUTION_KEY = "Solution";

    /**
     * 业务资源对应的type名
     */
    String AS_RESOURCE_TYPE = "com.huawei.as";

    /**
     * 应用及服务对应的type名
     */
    String APPLICATION_RESOURCE_TYPE = "com.huawei.as.application";

    /**
     * 网元树的最大迭代深度
     */
    int MAX_LAYER_DEPTH = 20;
}
