/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.business.managedobject.dto;

import com.huawei.i2000.dvanalysisengineextservice.constants.DisplayConstant;
import com.huawei.i2000.dvanalysisengineextservice.constants.MoConstant;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/3/6
 */

@Getter
@Setter
public class TopologyGraph {
    private String graphName = StringUtils.EMPTY;
    private String tag;
    private String id = StringUtils.EMPTY;
    private String name = MoConstant.DISPLAY_EMPTY;
    private List<TopologyGraph> children = new ArrayList<>();

    public TopologyGraph(String tagKey) {
        this.tag = DisplayConstant.getDisplayByKey(tagKey);
    }
}
