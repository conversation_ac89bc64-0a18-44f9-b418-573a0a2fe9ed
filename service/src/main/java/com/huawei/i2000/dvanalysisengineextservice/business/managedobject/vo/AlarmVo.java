/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.business.managedobject.vo;

import com.huawei.i2000.dvanalysisengineextservice.business.display.AbstractVo;
import com.huawei.i2000.dvanalysisengineextservice.business.managedobject.dto.AlarmHit;
import com.huawei.i2000.dvanalysisengineextservice.constants.DisplayConstant;
import com.huawei.i2000.dvanalysisengineextservice.util.ContextUtils;
import com.huawei.i2000.dvanalysisengineextservice.business.alarm.AlarmClient;

import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.Locale;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/2/7
 */

@Getter
@Setter
public class AlarmVo extends AbstractVo {
    private String alarmId;
    private String alarmName;
    private String severity;
    private String latestOccurUtc;
    private String neName;
    private String moi;
    private Integer count;
    private String cleared;

    public AlarmVo(AlarmHit alarmHit) {
        super(DisplayConstant.COLUMN_PREFIX_ALARM);
        Locale locale = ContextUtils.getDefaultLocale();
        this.alarmId = alarmHit.getAlarmId();
        this.alarmName = alarmHit.getAlarmName();
        this.severity = AlarmClient.getDisplayValue(DisplayConstant.VALUE_PREFIX_ALARM_SEVERITY, alarmHit.getSeverity(), locale);
        this.latestOccurUtc = DisplayConstant.DATE_TIME_FORMATTER.format(Instant.ofEpochMilli(alarmHit.getLatestOccurUtc()));
        this.neName = alarmHit.getMeName();
        this.moi = alarmHit.getMoi();
        this.count = alarmHit.getCount();
        this.cleared = AlarmClient.getDisplayValue(DisplayConstant.VALUE_PREFIX_ALARM_CLEARED, alarmHit.getCleared(), locale);
    }
}
