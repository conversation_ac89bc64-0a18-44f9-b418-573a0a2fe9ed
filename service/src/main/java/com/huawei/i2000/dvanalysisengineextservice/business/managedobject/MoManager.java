/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.business.managedobject;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.cbb.fm.model.ScrollQueryResult;
import com.huawei.i2000.dvanalysisengineextservice.business.managedobject.dto.AbnormalIndicator;
import com.huawei.i2000.dvanalysisengineextservice.business.managedobject.dto.AlarmHit;
import com.huawei.i2000.dvanalysisengineextservice.business.managedobject.dto.Incident;
import com.huawei.i2000.dvanalysisengineextservice.business.managedobject.dto.MoEvent;
import com.huawei.i2000.dvanalysisengineextservice.business.kpi.AnalysisUtils;
import com.huawei.i2000.dvanalysisengineextservice.business.alarm.AlarmClient;
import com.huawei.i2000.dvanalysisengineextservice.util.resource.EamUtil;
import com.huawei.oms.eam.mo.ManagedObject;

import com.alibaba.fastjson2.JSON;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/1/9
 */

@Service
public class MoManager {
    private static final OssLog LOGGER = OssLogFactory.getLogger(MoManager.class);
    public static final String EVENT_TYPE_KPI = "KPI";
    public static final String EVENT_TYPE_ALARM = "ALARM";
    public static final int EVENT_QUERY_SIZE = 15;

    public Incident getEventList(String dn) throws ServiceException {
        Incident incident = new Incident();
        ManagedObject managedObject = EamUtil.getMoByDn(dn);

        if (Objects.isNull(managedObject)) {
            return incident;
        }

        incident.setName(managedObject.getName());
        incident.setDescription(managedObject.getDescription());
        incident.setSeverity(1);
        incident.setEvents(new ArrayList<>());
        incident.setTime(System.currentTimeMillis());


        // 查询告警
        queryAlarm(dn, incident);

        // 查询异常指标
        queryIndicator(dn, incident);


        return incident;
    }

    private void queryIndicator(String dn, Incident incident) {
        List<AbnormalIndicator> indicators = AnalysisUtils.getAbnormalKpiByDn(dn);
        if (CollectionUtils.isNotEmpty(indicators)) {
            indicators = indicators.stream().sorted((o1, o2) -> StringUtils.compare(o2.getStartTime(), o1.getStartTime()))
                .limit(3).collect(Collectors.toList());
            for (AbnormalIndicator indicator : indicators) {
                MoEvent moEvent = new MoEvent();

                moEvent.setType(EVENT_TYPE_KPI);
                moEvent.setName(indicator.getIndexName());
                moEvent.setDetail(indicator);
                incident.getEvents().add(moEvent);
            }
        }
    }

    private void queryAlarm(String dn, Incident incident) {
        ScrollQueryResult scrollQueryResult = AlarmClient.getAlarmByDn(dn, EVENT_QUERY_SIZE, AlarmClient.TYPE_CURRENT);
        if (scrollQueryResult == null) {
            LOGGER.warn("scrollQueryResult is null, dn:{}", dn);
            return;
        }
        List<AlarmHit> alarms = scrollQueryResult.getHits().stream()
            .map(hit -> JSON.parseObject(JSON.toJSONString(hit), AlarmHit.class))
            .collect(Collectors.toList());
        for (AlarmHit alarm : alarms) {
            MoEvent moEvent = new MoEvent();
            moEvent.setType(EVENT_TYPE_ALARM);
            moEvent.setName(alarm.getAlarmName());
            moEvent.setDetail(alarm);
            incident.getEvents().add(moEvent);
        }
    }
}
