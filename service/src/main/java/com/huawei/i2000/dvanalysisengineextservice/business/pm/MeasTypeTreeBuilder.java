/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.business.pm;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.dvanalysisengineextservice.business.pm.model.LanguageQuery;
import com.huawei.i2000.dvanalysisengineextservice.business.pm.model.MeasIndex;
import com.huawei.i2000.dvanalysisengineextservice.business.pm.model.MeasIndex4Page;
import com.huawei.i2000.dvanalysisengineextservice.business.pm.model.MeasIndexInfo;
import com.huawei.i2000.dvanalysisengineextservice.business.pm.model.MeasObject;
import com.huawei.i2000.dvanalysisengineextservice.business.pm.model.MeasObjectInfoNew;
import com.huawei.i2000.dvanalysisengineextservice.business.pm.model.MeasType;
import com.huawei.i2000.dvanalysisengineextservice.business.pm.model.MeasType4Page;
import com.huawei.i2000.dvanalysisengineextservice.business.pm.model.MeasUnit4Page;
import com.huawei.i2000.dvanalysisengineextservice.business.pm.model.PollerModel;
import com.huawei.i2000.dvanalysisengineextservice.business.pm.model.TaskDefinition;
import com.huawei.i2000.dvanalysisengineextservice.model.MeasObjectInfo;
import com.huawei.i2000.dvanalysisengineextservice.model.MeasObjectVO;
import com.huawei.i2000.dvanalysisengineextservice.model.MeasUnit4MV;
import com.huawei.i2000.dvanalysisengineextservice.util.ContextUtils;
import com.huawei.i2000.dvanalysisengineextservice.util.rest.RestConstant;
import com.huawei.i2000.dvanalysisengineextservice.util.rest.RestUtil;
import com.huawei.i2000.kernel.pkg.adapter.sdk.ResourceUtil;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 构造测量单元树
 *
 * <AUTHOR>
 * @since 2021/3/17
 */
@Component
public class MeasTypeTreeBuilder {
    private static OssLog LOGGER = OssLogFactory.getLogger(MeasTypeTreeBuilder.class);

    private static final String DOT_SEPARATOR = ".";

    private static final String I18N_NAME_SIGN = "name";

    private static final String I18N_UNIT_SIGN = "unit";

    private static final String DN = "dn";

    private static final String MEASUNITKEY = "measUnitKey";

    private static final int PAGE_SIZE = 1000;

    private Map<String, String> key2I18nValue = null;

    /**
     * 查询测量单元树
     *
     * @param moType 网元类型
     * @param version 版本
     * @param dn 网元dn
     * @param searchKey 搜索关键字
     * @param searchScope 搜索类型 all全部搜索，group按照测量单元名称搜索
     * @return 网元树
     */
    public List<MeasUnit4MV> getMeasUnitTree(String moType, String version, String dn, String searchKey,
        SearchScopeEnum searchScope) {
        List<TaskDefinition> tasks;
        tasks = getPmTaskNew(moType, version);

        key2I18nValue = getLanguage(getLanguageKey(tasks));
        List<MeasUnit4Page> measUnits = convertTask2MeasUnit4MeasTree(tasks);

        if (CollectionUtils.isEmpty(measUnits)) {
            return new ArrayList<>();
        } else {
            List<MeasUnit4MV> measUnit4MVs = new ArrayList<>();
            for (MeasUnit4Page measUnit : measUnits) {
                MeasUnit4MV measUnit4MV = ModelConvertUtil.searchMeasUnitTree(measUnit, dn, searchKey, searchScope);

                // 如果该测量单元下没有指标，不处理
                if (CollectionUtils.isEmpty(measUnit4MV.getChildren())) {
                    continue;
                }

                measUnit4MVs.add(measUnit4MV);
            }
            return measUnit4MVs;
        }
    }

    /**
     * 查询性能任务服务化
     *
     * @param moType 网元类型
     * @param version 版本
     * @return 返回任务对象集合
     */
    public List<TaskDefinition> getPmTaskNew(String moType, String version) {
        List<TaskDefinition> taskDefList = new ArrayList<>();
        try {
            String url = RestConstant.Url.REST_URL_PM_I2KQUERYPMTASK_NEW;
            url = RestUtil.addUrlParam(url, "moType", moType);
            url = RestUtil.addUrlParam(url, "version", version);
            RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_GET, url, null, null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("[getPmTask] getPmTaskNew response error");
                return taskDefList;
            }
            TaskDefinition.PmResult<List<PollerModel>> resultFromPm = JSONObject.parseObject(
                response.getResponseContent(), new NewResultReference());
            if (resultFromPm == null || resultFromPm.getResultCode() != TaskDefinition.PmResult.SUC) {
                LOGGER.error("[getPmTask] getPmTaskNew result error,result = {}", resultFromPm);
                return taskDefList;
            }

            // PmTask(pm)和TaskDefinition(dashboard)模型转换
            List<PollerModel> pmTaskList = resultFromPm.getResult();
            if (CollectionUtils.isEmpty(pmTaskList)) {
                return taskDefList;
            }
            for (PollerModel pmTask : pmTaskList) {
                TaskDefinition taskDefinition = ModelConvertUtil.pmTaskToTaskDefinitionNew(pmTask);
                taskDefList.add(taskDefinition);
            }
        } catch (ServiceException e) {
            LOGGER.error("Fail to do getPmTaskNew! e = {}", e.getMessage());
        }
        return taskDefList;
    }

    static class NewResultReference extends TypeReference<TaskDefinition.PmResult<List<PollerModel>>> {
        /**
         * 构造方法
         */
        public NewResultReference() {
        }
    }

    /**
     * 查询国际化信息
     *
     * @param queryCond 查询入参
     * @return 返回国际化map集合
     */
    public Map<String, String> getLanguage(LanguageQuery queryCond) {
        Map<String, String> resultMap = new HashMap<>();
        List<String> resKeys = queryCond.getResKeyList();
        if (CollectionUtils.isEmpty(resKeys)) {
            LOGGER.error("[MeasTypeTreeBuilder]resKeyList is empty");
            return resultMap;
        }

        // 设置语言类型
        String lanType = queryCond.getLanType();
        if (StringUtils.isEmpty(lanType)) {
            lanType = getLanguage();
            queryCond.setLanType(lanType);
        }

        Map<String, String> result = getLanguages(queryCond.getLanType(), resKeys);
        if (MapUtils.isNotEmpty(result) && result.values().size() == resKeys.size()) {
            LOGGER.warn("[getLanguage]query global info result end.");
            return result;
        }
        List<String> elseResKeys = new ArrayList<>();
        for (String resKey : resKeys) {
            if (!result.containsKey(resKey)) {
                elseResKeys.add(resKey);
            }
        }
        Map<String, String> newResult = new ResourceUtil().getMessages(elseResKeys, lanType);
        result.putAll(newResult);
        return result;
    }

    /**
     * 查询国际化信息
     *
     * @param lanType 语言类型
     * @param resKeys 国际化key
     * @return 返回国际化map集合
     */
    public Map<String, String> getLanguages(String lanType, List<String> resKeys) {
        Map<String, Object> paramMap = new HashMap<>();
        String url;
        url = RestConstant.Url.PM_LANGUAGE_DISPLAY_NEW;
        paramMap.put("languageType", lanType);
        paramMap.put("resKeyList", resKeys);
        RestfulResponse response;
        try {
            LOGGER.debug("query pm language start.lanType is {}, resKeys is {}", lanType, resKeys);
            RestfulParametes parameters = new RestfulParametes();
            parameters.setRawData(JSONObject.toJSONString(paramMap));
            response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, url, parameters, null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("response is null or response status is not ok");
                return new HashMap<>();
            }

            TaskDefinition.PmResult<Map<String, String>> result = JSONObject.parseObject(response.getResponseContent(),
                new Reference());
            if (result == null || result.getResultCode() != TaskDefinition.PmResult.SUC) {
                LOGGER.error("[PmUtil] getMeasObjectByMoType response status error, pmResult = {}", result);
                return new HashMap<>();
            }
            return result.getResult();
        } catch (ServiceException e) {
            LOGGER.error("request language from pm has error :{}", e.getMessage());
            return new HashMap<>();
        }
    }

    static class Reference extends TypeReference<TaskDefinition.PmResult<Map<String, String>>> {
        /**
         * 构造方法
         */
        public Reference() {
        }
    }

    /**
     * 获取语言环境
     *
     * @return Locale
     */
    private static String getLanguage() {
        String locale = "zh";
        String language = ContextUtils.getContext().getLocale().toString();
        if (StringUtils.isNotEmpty(language) && "en_US".equals(language)) {
            locale = "en";
        }
        return locale;
    }

    private LanguageQuery getLanguageKey(List<TaskDefinition> tasks) {
        LanguageQuery queryParam = new LanguageQuery();
        List<String> resKeyList = new ArrayList<String>();

        if (CollectionUtils.isEmpty(tasks)) {
            return queryParam;
        }

        for (TaskDefinition task : tasks) {
            String resType = task.getResType();
            String measUnitKey = task.getMeasUnitKey();

            resKeyList.add(getI18NNameKey(resType, measUnitKey, null));

            List<MeasType> measTypes = task.getMeasTypeList();

            if (CollectionUtils.isNotEmpty(measTypes)) {
                resKeyList.addAll(getMeasTypesKeys(resType, measUnitKey, measTypes));
            }

            if (task.getMeasObject() != null && task.getMeasObject().getMeasIndexList() != null) {
                resKeyList.addAll(getMeasIndexKeys(resType, measUnitKey, task.getMeasObject().getMeasIndexList()));
            }
        }

        queryParam.setResKeyList(resKeyList);

        return queryParam;
    }

    private List<String> getMeasIndexKeys(String resType, String measUnitKey, List<MeasIndex> measIndexs) {
        List<String> resKeyList = new ArrayList<String>();
        for (MeasIndex measIndex : measIndexs) {
            resKeyList.add(getI18NNameKey(resType, measUnitKey, measIndex.getKey()));
        }
        return resKeyList;
    }

    private List<String> getMeasTypesKeys(String resType, String measUnitKey, List<MeasType> measTypes) {
        List<String> resKeyList = new ArrayList<String>();
        for (MeasType measType : measTypes) {
            resKeyList.add(getI18NNameKey(resType, measUnitKey, measType.getKey()));
            resKeyList.add(getI18NUnitKey(resType, measUnitKey, measType.getKey()));
        }
        return resKeyList;
    }

    private String getI18NNameKey(String resType, String measUnitKey, String measType) {
        StringBuilder sb = new StringBuilder();
        if (measType == null) {
            sb.append(resType).append(DOT_SEPARATOR).append(measUnitKey).append(DOT_SEPARATOR).append(I18N_NAME_SIGN);
        } else {
            sb.append(resType)
                .append(DOT_SEPARATOR)
                .append(measUnitKey)
                .append(DOT_SEPARATOR)
                .append(measType)
                .append(DOT_SEPARATOR)
                .append(I18N_NAME_SIGN);
        }
        return sb.toString();
    }

    private String getI18NValue(String i18nKey) {
        String value = null;
        if (key2I18nValue == null) {
            LOGGER.warn("[getI18NValue]query global info from pm is null.");
            value = "!" + i18nKey + "!";
        } else {
            value = key2I18nValue.get(i18nKey);
            if (StringUtils.isEmpty(value)) {
                value = "!" + i18nKey + "!";
            }
        }
        return value;
    }

    private String getI18NUnitKey(String resType, String measUnitKey, String measType) {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isEmpty(measType)) {
            sb.append(resType).append(DOT_SEPARATOR).append(measUnitKey).append(DOT_SEPARATOR).append(I18N_UNIT_SIGN);
        } else {
            sb.append(resType)
                .append(DOT_SEPARATOR)
                .append(measUnitKey)
                .append(DOT_SEPARATOR)
                .append(measType)
                .append(DOT_SEPARATOR)
                .append(I18N_UNIT_SIGN);
        }

        return sb.toString();
    }

    private List<MeasType4Page> convertMeasType(String resType, String measUnitKey, List<MeasType> measTypes) {
        List<MeasType4Page> measType4Pages = new ArrayList<MeasType4Page>();

        if (CollectionUtils.isEmpty(measTypes)) {
            return measType4Pages;
        }

        for (MeasType measType : measTypes) {
            MeasType4Page measType4Page = new MeasType4Page();
            measType4Page.setKey(measType.getKey());
            measType4Page.setName(getI18NValue(getI18NNameKey(resType, measUnitKey, measType.getKey())));
            String unit = getI18NValue(getI18NUnitKey(resType, measUnitKey, measType.getKey()));
            if (unit.startsWith("!") && unit.endsWith("!")) {
                measType4Page.setUnit("");
            } else {
                measType4Page.setUnit(unit);
            }
            measType4Page.setColumn(measType.getColumn());

            // 同环比需过滤未设置同环比的指标
            measType4Pages.add(measType4Page);
        }
        return measType4Pages;
    }

    private MeasUnit4Page getMeasUnit4Page(TaskDefinition task) {
        MeasUnit4Page measUnit = new MeasUnit4Page();
        String measUuitKey = task.getMeasUnitKey();
        String resType = task.getResType();
        measUnit.setKey(measUuitKey);
        measUnit.setResourceTypeKey(resType);
        measUnit.setName(getI18NValue(getI18NNameKey(resType, measUuitKey, null)));
        measUnit.setMeasTypes(convertMeasType(resType, measUuitKey, task.getMeasTypeList()));
        measUnit.setMeasIndexs(null);
        return measUnit;
    }

    private List<MeasUnit4Page> convertTask2MeasUnit4MeasTree(List<TaskDefinition> tasks) {
        List<MeasUnit4Page> measUnits = new ArrayList<>();

        if (CollectionUtils.isEmpty(tasks)) {
            LOGGER.warn("[getMeasUnitTree]tasks is empty!");
            return measUnits;
        }
        for (TaskDefinition task : tasks) {
            MeasUnit4Page measUnit = getMeasUnit4Page(task);
            MeasObject measObject = task.getMeasObject();
            if (measObject != null && CollectionUtils.isNotEmpty(measObject.getMeasIndexList())) {
                List<MeasIndex4Page> measIndex4Pages = new ArrayList<>();
                measUnit.setMeasIndexs(measIndex4Pages);
            }

            measUnits.add(measUnit);
        }

        return measUnits;
    }

    /**
     * 按网元实例查询测量对象服务化
     *
     * @param dn 网元dn
     * @param measUnitKey 测量单元key
     * @return 测量对象
     * @throws ServiceException ServiceException
     */
    public MeasObjectInfo getObjectInfoNew(String dn, String measUnitKey) throws ServiceException {
        String url = RestConstant.Url.PM_MONITORVIEW_GEMEAAOBJ_NEW;
        url = RestUtil.addUrlParam(url, DN, dn);
        url = RestUtil.addUrlParam(url, MEASUNITKEY, measUnitKey);
        RestfulResponse response;
        try {
            LOGGER.info("[getObjectInfoNew] query measObject info start.dn is {}, measUnitKey is {}", dn, measUnitKey);
            response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_GET, url, null, null);

            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("[getObjectInfoNew] response is null or response status is not ok");
                throw new ServiceException("response error");
            }

            TaskDefinition.PmResult<List<MeasObjectInfoNew>> result = JSONObject.parseObject(
                response.getResponseContent(), new ReferenceResultNew());
            if (result == null || result.getResultCode() != TaskDefinition.PmResult.SUC) {
                LOGGER.error("[getObjectInfoNew]  getObjectInfoNew result error, result = {}", result);
                throw new ServiceException("response result error");
            }
            return newObjectInfoToOld(result.getResult());
        } catch (ServiceException e) {
            LOGGER.error("[getObjectInfoNew] request measobject from pm has error :{}", e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }

    private MeasObjectInfo newObjectInfoToOld(List<MeasObjectInfoNew> newObject) {
        if (CollectionUtils.isEmpty(newObject)) {
            LOGGER.error("[getObjectInfoNew] newObjectInfoToOld newObject is empty");
            return new MeasObjectInfo();
        }
        // 性能接口的新对象转成旧对象保持兼容
        Set<String> indexes = new HashSet<>();
        List<MeasObjectVO> mos = new ArrayList<>();
        for (MeasObjectInfoNew object : newObject) {
            List<String> indexValues = new ArrayList<>();
            StringBuilder originalValue = new StringBuilder();
            List<MeasIndexInfo> measIndexInfos = object.getMeasIndexInfos();
            if (CollectionUtils.isNotEmpty(measIndexInfos)) {
                for (MeasIndexInfo indexInfo : measIndexInfos) {
                    indexes.add(indexInfo.getIndexKeyName());
                    indexValues.add(indexInfo.getIndexValue());
                    originalValue.append(indexInfo.getIndexKey())
                        .append("=")
                        .append(indexInfo.getIndexValue())
                        .append(",");
                }
            }
            if (originalValue.toString().endsWith(",")) {
                originalValue.deleteCharAt(originalValue.length() - 1);
            }
            MeasObjectVO measObjectVO = new MeasObjectVO();
            measObjectVO.setDisplayValue(object.getDisplayValue());
            measObjectVO.setIndexValues(indexValues);
            measObjectVO.setOriginalValue(originalValue.toString());
            mos.add(measObjectVO);
        }
        MeasObjectInfo result = new MeasObjectInfo();
        result.setIndexes(new ArrayList<>(indexes));
        for (MeasObjectVO measObjectVO : mos) {
            measObjectVO.setIndexes(new ArrayList<>(indexes));
        }
        result.setMos(mos);
        return result;
    }

    static class ReferenceResultNew extends TypeReference<TaskDefinition.PmResult<List<MeasObjectInfoNew>>> {
        /**
         * 构造方法
         */
        public ReferenceResultNew() {
        }
    }
}
