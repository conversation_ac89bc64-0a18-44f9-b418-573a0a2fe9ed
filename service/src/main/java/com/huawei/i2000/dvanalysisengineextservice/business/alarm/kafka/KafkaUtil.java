/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.business.alarm.kafka;

import com.huawei.bsp.log.Logger;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.cloudsop.mq.api.common.TopicPartition;
import com.huawei.cloudsop.mq.api.consumer.IConsumer;
import com.huawei.cloudsop.mq.api.consumer.IConsumerRecord;
import com.huawei.cloudsop.mq.api.consumer.IConsumerRecords;
import com.huawei.cloudsop.mq.api.consumer.OffsetAndMetadata;
import com.huawei.cloudsop.mq.api.factory.ConsumerFactory;
import com.huawei.cloudsop.mq.exception.MqException;
import com.huawei.i2000.dvanalysisengineextservice.business.alarm.redis.RedisOper;
import com.huawei.i2000.dvanalysisengineextservice.cluster.dto.EntityList;
import com.huawei.i2000.dvanalysisengineextservice.impl.DVAnalysisEngineExtServiceIncidentDelegateImpl;
import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineextservice.business.alarm.ALarmWhiteListEntry;
import com.huawei.i2000.dvanalysisengineextservice.business.alarm.AddEventParam;
import com.huawei.i2000.dvanalysisengineextservice.util.rest.RestUtil;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * KafkaConfigUtil kafka消费组创建类
 *
 * <AUTHOR>
 * @since 2023-07-26
 */
@Component
public class KafkaUtil {
    private static final Logger LOGGER = OssLogFactory.getLog(KafkaUtil.class);

    private static final String KEY_SECURITY_STORAGE_CIPHER = "security.storage.cipher";

    private static final String VALUE_SECURITY_STORAGE_CIPHER = "org.apache.kafka.common.security.DefaultCipher";

    private static final String TOPIC_NAME = "FMService_Forward_Alarm";

    private static final String POST_EVENT_URL = "/rest/dvaiagentservice/v1/incident/addevents";
    private static final long ALARM_EXPIRE_TIME = 30 * 60;

    private static final String POST_ALARM_WHITELIST_URL
        = "/rest/dvaiagentservice/v1/alarms/originalarmwhitelist";

    private static final int POLL_MILLIONS = 60 * 1000;

    private static volatile IConsumer<String, String> consumer = null;

    private static volatile boolean subscribe = false;

    private static final Map<TopicPartition, OffsetAndMetadata> currentOffsets = new ConcurrentHashMap<>();

    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(1, 1, 10, TimeUnit.MINUTES,
        new LinkedBlockingQueue<>(10), new CustomizableThreadFactory("Kafka-Alarm-Task-Thread-"));

    public static final Map<String, ALarmWhiteListEntry> alarmWhitelistCache = new ConcurrentHashMap<>();

    @Autowired
    DVAnalysisEngineExtServiceIncidentDelegateImpl incidentDelegate;

    @Autowired
    RedisOper redisUtils;


    private void initConsumer(String groupId, Map<String, Object> customerConfig) {
        Map<String, Object> configs = new HashMap<>();
        if (customerConfig != null) {
            configs.putAll(customerConfig);
        }
        configs = KafkaConfigUtil.getInstance().getConsumerConfig(configs);
        configs.put(KafkaConstant.GROUP_ID, groupId);
        LOGGER.info("get kafka consumer config: {}", configs.toString());
        consumer = ConsumerFactory.getInstance().getConsumer(configs);
        while (Objects.isNull(consumer)) {
            sleep();
            try {
                consumer = ConsumerFactory.getInstance().getConsumer(configs);
            } catch (Exception e) {
                LOGGER.error("getConsumer failed, e:", e);
            }
        }
    }

    public List<AlarmInfo> getAlarmFromKafka() {
        while (!subscribe) {
            sleep();
        }
        List<AlarmInfo> result = Collections.emptyList();
        while (CollectionUtils.isEmpty(result)) {
            try {
                IConsumerRecords<String, String> records = consumer.poll(POLL_MILLIONS);
                if (Objects.isNull(records) || CollectionUtils.isEmpty(records.records())) {
                    consumer.commit();
                    continue;
                }
                result = new ArrayList<>();
                for (IConsumerRecord<String, String> record : records.records()) {
                    List<AlarmInfo> alarms = JSON.parseArray(record.value(), AlarmInfo.class);
                    result.addAll(alarms);
                    LOGGER.debug("KafkaConsumer discards data: offset = {}, key = {}, value = {}", record.offset(),
                        record.key(), record.value());
                    currentOffsets.put(new TopicPartition(record.topic(), record.partition()),
                        new OffsetAndMetadata(record.offset() + 1));
                }
                consumer.commit();
                return result;
            } catch (Throwable e) {
                // 为防止异常导致线程终止影响功能，捕获Exception
                LOGGER.error("Consumer poll kafka event failed,e=", e);
                sleep();
            }
        }
        return result;
    }

    public void init() {
        try {
            LOGGER.info("Start to init CloudSopAlarmKafkaConsumer");
            Map<String, Object> configs = new HashMap<>();
            configs.put(KEY_SECURITY_STORAGE_CIPHER, VALUE_SECURITY_STORAGE_CIPHER);
            configs.put("max.poll.records", 200);
            initConsumer(KafkaConstant.FM_GROUP_ID_NAME, configs);
            LOGGER.info("Init CloudSopAlarmKafkaConsumer successfully.");

            while (!subscribe) {
                try {
                    consumer.subscribe(Collections.singletonList(TOPIC_NAME),
                        new IConsumerRebalanceListenerImpl(consumer, currentOffsets));
                    subscribe = true;
                } catch (MqException e) {
                    LOGGER.error("subscribe catch a mqException:{}", e.getMessage());
                    sleep();
                }
            }

            LOGGER.info("subscribe FMService_Forward_Alarm successfully.");
            EXECUTOR.execute(this::handleAlarm);
        } catch (Exception e) {
            LOGGER.error("AlarmEventListener init error,", e);
        }
    }

    private void handleAlarm() {
        while (true) {
            try {
                List<AlarmInfo> alarms = getAlarmFromKafka();
                List<AlarmInfo> filedAlarms = filterdByALarmWhiteList(alarms);
                if (CollectionUtils.isNotEmpty(filedAlarms)) {
                    List<AddEventParam> events = filedAlarms.stream()
                        .filter(entry -> entry.isAdded() || entry.isClearedAlarm())
                        .map(AddEventParam::new)
                        .filter(this::filterDuplicateAlarm)
                        .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(events)) {
                        Map<String, List<AddEventParam>> params = Collections.singletonMap("events", events);
                        ResponseResult response = RestUtil.getPostResponse(POST_EVENT_URL, params);
                        if (!Objects.equals(response.getResultCode(), 0)) {
                            LOGGER.error("post events error, response = {}", response.toString());
                        }
                    }
                }
            } catch (Throwable throwable) {
                LOGGER.error("handle alarm error, e: ", throwable);
                sleep();
            }
        }
    }

    private boolean filterDuplicateAlarm(AddEventParam event) {
        // 清理告警直接返回
        if (event.getClearStatus() != 0) {
            return true;
        }
        String key = event.getEventId() +"_"+ event.getMoType();
        if(!redisUtils.isKeyExist(key, ALARM_EXPIRE_TIME)) {
            LOGGER.info("key: {} is not in redis, start to set redis", key);
            redisUtils.setRedisEx(key, ALARM_EXPIRE_TIME, "active");
            return true;
        } else {
            return false;
        }
    }

    private static void sleep() {
        try {
            Thread.sleep(1000L);
        } catch (InterruptedException e) {
            LOGGER.error("thread is interrupted, e:", e.getMessage());
        }
    }

    private List<AlarmInfo> filterdByALarmWhiteList(List<AlarmInfo> alarmInfos) throws ServiceException {
        if (MapUtils.isEmpty(alarmWhitelistCache)) {
            LOGGER.info("ALARMWHITELISTCACHE is empty, query whitelist rest interface");
            Map<String, Object> params = new HashMap<>();
            params.put("allQuery", true);
            ResponseResult response = RestUtil.getPostResponse(POST_ALARM_WHITELIST_URL, params);
            EntityList<ALarmWhiteListEntry> data = JSON.parseObject(JSON.toJSONString(response.getData()),
                new TypeReference<EntityList<ALarmWhiteListEntry>>() {});
            if (data == null || CollectionUtils.isEmpty(data.getObjects())) {
                LOGGER.warn("alarm white list rsp data  is null, rsp status is: {}, message {} ",
                    response.getResultCode(), response.getResultMessage());
                return alarmInfos;
            }

            for (ALarmWhiteListEntry entry : data.getObjects()) {
                String cacheKey = String.format("%s#%s", entry.getProductName(), entry.getAlarmId());
                alarmWhitelistCache.put(cacheKey, entry);
            }
            LOGGER.info("Updated ALARMWHITELISTCACHE success, cache size is {}", alarmWhitelistCache.size());
        }

        // 白名单为空，默认不过滤
        List<ALarmWhiteListEntry> alarmWhiteList = new ArrayList<>(alarmWhitelistCache.values());
        if (CollectionUtils.isEmpty(alarmWhiteList) || CollectionUtils.isEmpty(alarmInfos)) {
            LOGGER.info("alarmWhiteList or alarmInfos is empty,  not filter alarminfo");
            return alarmInfos;
        }
        // 获取alarmId
        Set<String> whiteListKeys = alarmWhiteList.stream()
            .map(ALarmWhiteListEntry::getAlarmId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(whiteListKeys) || CollectionUtils.isEmpty(alarmInfos)) {
            LOGGER.info("current WhiteList is empty, not filter alarminfo");
            return alarmInfos;
        }

        // Filter alarmInfos where productName + AlarmId  matches a key in whiteListKeys
        return alarmInfos.stream()
            .filter(event -> event.getAlarmId() != null)
            .filter(event -> whiteListKeys.contains(event.getAlarmId()))
            .collect(Collectors.toList());
    }
}
