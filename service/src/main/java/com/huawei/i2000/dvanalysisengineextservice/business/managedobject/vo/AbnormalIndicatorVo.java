/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.business.managedobject.vo;

import com.huawei.i2000.dvanalysisengineextservice.business.display.AbstractVo;
import com.huawei.i2000.dvanalysisengineextservice.business.managedobject.dto.AbnormalIndicator;
import com.huawei.i2000.dvanalysisengineextservice.constants.DisplayConstant;

import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/2/8
 */

@Getter
@Setter
public class AbnormalIndicatorVo extends AbstractVo {
    private String indexName;

    private String measUnitName;

    private String displayValue;

    private String moType;

    private String moName;

    private String taskName;

    private String startTime;

    private String probableCause;

    public AbnormalIndicatorVo(AbnormalIndicator indicator) {
        super(DisplayConstant.COLUMN_PREFIX_ABNORMAL_INDICATOR);
        this.indexName = indicator.getIndexName();
        this.measUnitName = indicator.getMeasUnitName();
        this.displayValue = indicator.getDisplayValue();
        this.moType = indicator.getMoType();
        this.moName = indicator.getMoName();
        this.taskName = indicator.getTaskName();
        this.startTime = DisplayConstant.DATE_TIME_FORMATTER.format(Instant.ofEpochMilli(Long.parseLong(indicator.getStartTime())));
        this.probableCause = indicator.getProbableCause();
    }
}
