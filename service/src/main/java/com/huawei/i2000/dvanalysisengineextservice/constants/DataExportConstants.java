/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.constants;

import com.huawei.bsp.deploy.util.DefaultEnvUtil;
import com.huawei.i2000.cbb.common.SystemUtils;

import java.io.File;

/**
 * 文件导出常量
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
public class DataExportConstants {
    private DataExportConstants() {
    }

    /**
     * 临时文件本地存放地址前缀
     */
    public static final String TMP_FILE_PATH_PREFIX = SystemUtils.isContainerMode()
        ? System.getenv("_APP_SHARE_DIR")
        : DefaultEnvUtil.getAppRoot();

    /**
     * 导出文件的格式
     */
    public static final String FILE_FORMAT = "csv";

    /**
     * 临时目录
     */
    public static final String FILE_PATH = TMP_FILE_PATH_PREFIX + File.separator + "temp" + File.separator + "aiAgent"
        + File.separator;

    /**
     * UTF-8编码
     */
    public static final String ENCODE_UTF8 = "UTF-8";
}
