/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.constants;

import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.dvanalysisengineextservice.util.ContextUtils;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 前台展示国际化常量
 *
 * <AUTHOR>
 * @since 2025/1/23
 */
public interface DisplayConstant {
    DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        .withZone(ZoneId.systemDefault());

    DateTimeFormatter DATE_DAY_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(ZoneId.systemDefault());

    DateTimeFormatter TIME_4_FILE_NAME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
        .withZone(ZoneId.systemDefault());

    String COLUMN_PREFIX_MO_CONNECT_STATUS = "mo.connect.status.column.";
    String COLUMN_PREFIX_MO_CONNECT_STATUS_ADMIN = "mo.connect.status.column.adminStatus.";
    String COLUMN_PREFIX_MO_CONNECT_STATUS_CONNECT = "mo.connect.status.column.connectStatus.";

    String COLUMN_PREFIX_MO_TOPO_STATUS = "mo.topo.status.column.";

    String COLUMN_PREFIX_ALARM = "alarm.column.";

    String COLUMN_PREFIX_ABNORMAL_INDICATOR = "abnormal.indicator.column.";

    String COLUMN_PREFIX_DISK_USAGE = "disk.usage.column.";

    String COLUMN_PREFIX_MEMORY_USAGE = "memory.usage.column.";

    String COLUMN_PREFIX_CPU_USAGE = "cpu.usage.column.";

    String VALUE_PREFIX_ALARM_SEVERITY = "alarm.value.severity.";

    String VALUE_PREFIX_ALARM_CLEARED = "alarm.value.cleared.";

    String VALUE_PREFIX_ALARM_EVENT_TYPE = "alarm.value.eventType.";

    String MO_CONNECT_STATUS_DESCRIPTION = "mo.connect.status.description";

    String MO_CONNECT_STATUS_DESCRIPTION_SINGLE = "mo.connect.status.description.single";

    String MO_TOPO_INFO_DESCRIPTION = "mo.topo.info.description";

    String MO_TOPO_INFO_GRAPH_NAME_PARENT = "mo.topo.status.graph.name.parent";
    String MO_TOPO_INFO_COLUMN_PARENT = "mo.topo.status.column.parent";
    String MO_TOPO_INFO_COLUMN_TARGET = "mo.topo.status.column.target";
    String MO_TOPO_INFO_COLUMN_CHILDREN = "mo.topo.status.column.children";
    String MO_TOPO_INFO_GRAPH_NAME_DEPLOY = "mo.topo.status.graph.name.deploy";
    String MO_TOPO_INFO_COLUMN_SRC = "mo.topo.status.column.deploySrcMo";
    String MO_TOPO_INFO_COLUMN_DEST = "mo.topo.status.column.deployDestMos";

    String MO_HEALTH_ALARM_DESCRIPTION_NOT_EMPTY = "mo.health.alarm.description.not.empty";

    String CURRENT_ALARM_DESCRIPTION_EMPTY = "current.alarm.description.not.empty";
    String HISTORY_ALARM_DESCRIPTION_EMPTY = "history.alarm.description.not.empty";
    String ALARM_RESULT_EMPTY = "alarm.result.empty";

    String MO_HEALTH_ALARM_DESCRIPTION_EMPTY = "mo.health.alarm.description.empty";

    String MO_HEALTH_KPI_DESCRIPTION_NOT_EMPTY = "mo.health.kpi.description.not.empty";

    String MO_HEALTH_KPI_DESCRIPTION_EMPTY = "mo.health.kpi.description.empty";

    String MO_EXPORT_COLUMN_PREFIX = "mo.column.";

    String NE_NOT_FOUND = "pm.kpi.export.mo.notFound.error";

    String INDICATOR_NOT_FOUND = "pm.kpi.export.task.notFound.byIndicator.error";
    String TIME_RANGE_NOT_CORRECT = "pm.kpi.export.time.range.noCorrect.error";
    String TASK_NOT_FOUND = "pm.kpi.export.task.notFound.error";

    String TASK_NOT_FOUND_BY_MO_TYPE = "pm.kpi.export.task.notFound.byMoType.error";

    String TASK_NO_DATA = "pm.kpi.export.task.nodata.error";

    String COMPARISON_RANGE_NO_DATA = "pm.kpi.query.comparison_nodata_error";

    String CURRENT_ALARM_EXPORT_NAME = "export.current.alarm.name";

    String MO_EXPORT_NAME = "export.mo.all.name";

    String PM_KPI_EXPORT = "export.pm.kpi.name";

    String DISK_USAGE_TITLE = "workbench.execute.result.disk.usage.title";

    String MEM_USAGE_TITLE = "workbench.execute.result.memory.usage.title";

    String CPU_USAGE_TITLE = "workbench.execute.result.cpu.usage.title";

    String FILE_STORE_TIME_LIMIT = "workbench.execute.result.file.store.time.limit";

    String MANAGED_OBJECT = "pm.kpi.export.managed.object";
    String MEASUREMENT_OBJECT = "pm.kpi.export.measurement.object";
    String PERFORMANCE_COUNTER = "pm.kpi.export.performance.counter";
    String DATA_SOURCE = "pm.kpi.export.data.source";
    String VIEW_NAME = "pm.kpi.export.view.name";
    String TIME_NAME = "pm.kpi.chart.x.axis.name";

    String INDICATOR_NOT_UNIQUE = "pm.kpi.chart.indicator.not.unique";
    String MEAS_OBJECTS_NOT_UNIQUE = "pm.kpi.chart.meas.object.not.unique";
    String DIFF_TIME_INTERVALS = "pm.kpi.chart.diff.time.intervals";
    String INVALIDS_TIME_INTERVALS= "pm.kpi.chart.invalids.time.intervals";

    String QUERY_KPI_MISS_PARAMETER = "pm.kpi.query.miss.parameter";

    String QUERY_KPI_OVERSIZE = "pm.kpi.query.oversize";

    String EXPORT_ALARM_FILE = "export.alarm.file";
    String EXPORT_ALARM_FILE_DETAIL = "export.alarm.file.detail";

    static String getDisplayByKey(String key) {
        return ResourceUtil.getMessage(key, ContextUtils.getDefaultLocale());
    }

    static String getDisplayByKey(String key, Object[] objects) {
        return ResourceUtil.getMessage(key, objects, ContextUtils.getDefaultLocale());
    }
}
