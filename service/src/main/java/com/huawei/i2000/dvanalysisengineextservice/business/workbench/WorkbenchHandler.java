/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.business.workbench;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.huawei.bsp.commonlib.hofs.HofsException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.cbb.security.zip.ZipUtils;
import com.huawei.i2000.dvanalysisengineextservice.constants.DataExportConstants;
import com.huawei.i2000.dvanalysisengineextservice.model.DownloadFileRequest;
import com.huawei.i2000.dvanalysisengineextservice.model.DownloadFileRsp;
import com.huawei.i2000.dvanalysisengineextservice.model.DownloadParameter;
import com.huawei.i2000.dvanalysisengineextservice.model.ExecuteParameter;
import com.huawei.i2000.dvanalysisengineextservice.model.ExecuteRequest;
import com.huawei.i2000.dvanalysisengineextservice.model.TaskExecuteResult;
import com.huawei.i2000.dvanalysisengineextservice.util.CommonUtils;
import com.huawei.i2000.dvanalysisengineextservice.util.FileUtil;
import com.huawei.i2000.dvanalysisengineextservice.util.HofsUtils;
import com.huawei.i2000.dvanalysisengineextservice.util.rest.RestConstant;
import com.huawei.i2000.dvanalysisengineextservice.util.rest.RestUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Service
public class WorkbenchHandler {
    private static final OssLog LOGGER = OssLogFactory.getLogger(WorkbenchHandler.class);

    private static final String EXECUTESCRIPT_URL = "/rest/dvcontrollerservice/v1/dvexecutescriptservice/executeScript";

    private static final String QUERYTASKEXECUTERESULT_URL = "/rest/dvcontrollerservice/v1/dvexecuteresultservice/queryTaskExecuteResult";

    private static final String DOWNLOADRESULT_URL = "/rest/dvcontrollerservice/v1/dvdownloadfileservice/downloadFile";

    private static final String RESULT_LOCAL_PATH = DataExportConstants.TMP_FILE_PATH_PREFIX + File.separator + "temp" + File.separator + "workbench" + File.separator + "result";

    private static final int MAX_ARRAY_SIZE = 2000;

    private static final long WAIT_TIME = 500L;

    // 检查执行结果次数，0.5s重试一次，600次5分钟
    private static final int RETRY_TIMES = 600;

    /**
     * 通过作业控制台下载执行结果文件
     *
     * @param requestParam requestParam
     * @param resultPath 目标网元执行脚本的结果文件全路径
     * @return 结果
     * @throws ServiceException ServiceException
     */
    public DownloadFileRsp downloadResult(ExecuteRequest requestParam, String resultPath) throws ServiceException {
        RestfulParametes restfulParametes = new RestfulParametes();
        DownloadFileRequest downloadRequest = new DownloadFileRequest();
        DownloadParameter downloadParameter = new DownloadParameter();
        downloadParameter.setInvoker("dvanalysisextservice");
        downloadParameter.setUsername(requestParam.getUsername());
        downloadRequest.setDownloadParameter(downloadParameter);

        downloadRequest.setIpList(Arrays.stream(requestParam.getIpList().split(",")).map(String::trim).collect(Collectors.toList()));
        downloadRequest.setFilePath(resultPath);

        restfulParametes.setRawData(JSON.toJSONString(downloadRequest));
        RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, DOWNLOADRESULT_URL, restfulParametes, null);
        if (response == null || response.getStatus() != RestConstant.STATUS_OK || response.getResponseContent() == null) {
            LOGGER.error("downloadResult failed, response is null or status is not 200");
            throw new ServiceException("download result file failed, response not ok");
        }
        DownloadFileRsp downloadFileRsp = JSON.parseObject(response.getResponseContent(), DownloadFileRsp.class);
        if (!downloadFileRsp.getFlag()) {
            LOGGER.error("downloadResult failed, flag is false");
            throw new ServiceException("download result file failed");
        }
        downloadFileRsp.setFileContent(getFileContent(downloadFileRsp.getFilePath()));
        downloadFileRsp.setFilePath("");
        return downloadFileRsp;
    }

    /**
     * 执行脚本
     *
     * @param executeParameter 执行参数
     * @throws ServiceException ServiceException
     */
    public void executeScript(ExecuteParameter executeParameter) throws ServiceException {
        RestfulParametes restfulParametes = new RestfulParametes();
        restfulParametes.setRawData(JSON.toJSONString(executeParameter));
        RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, EXECUTESCRIPT_URL, restfulParametes, null);
        String execId = response.getResponseContent();
        if (StringUtils.isEmpty(execId)) {
            LOGGER.error("rest controller error. deliver script failed, execid is null");
            throw new ServiceException("rest controller error. execid is null");
        }
        // 查看脚本执行状态
        checkScriptStatus(execId, executeParameter.getIps());
    }

    private void checkScriptStatus(String execId, List<String> execIps) throws ServiceException {
        TaskExecuteResult taskExecuteResult = null;
        RestfulParametes restfulParametes = new RestfulParametes();
        restfulParametes.setRawData(execId);
        for (int i = 0; i < RETRY_TIMES; i++) {
            try {
                RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, QUERYTASKEXECUTERESULT_URL, restfulParametes, null);
                if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                    throw new ServiceException("query script execute status error ");
                }
                taskExecuteResult = JSONObject.parseObject(response.getResponseContent(), TaskExecuteResult.class);
            } catch (ServiceException e) {
                LOGGER.error("rest controller failed, query script status error {}", e.getMessage());
                throw new ServiceException("rest controller failed, query script status error ");
            }
            if (taskExecuteResult == null) {
                LOGGER.error("get content from response error");
                waitHalfSecond();
                continue;
            }
            if (Objects.equals(taskExecuteResult.getState(), TaskExecuteResult.StateEnum.FAILED)) {
                LOGGER.error("Execute script failed, execIps = {}", execIps.toString());
                throw new ServiceException("Execute script failed");
            }
            if (!Objects.equals(taskExecuteResult.getState(), TaskExecuteResult.StateEnum.IN_PROGRESS)) {
                LOGGER.info("End to wait TaskExecuteResult, execId = {}, TaskExecuteResult = {}", execId, taskExecuteResult.toString());
                return;
            }
            waitHalfSecond();
        }
        LOGGER.error("Query taskExecuteResult attained upperLimit times, execId = {}, execIps = {}, TaskExecuteResult = {}", execId, execIps.toString(),
                taskExecuteResult != null ? taskExecuteResult.toString() : null);
        throw new ServiceException("Query taskExecuteResult attained upperLimit times");
    }

    private void waitHalfSecond() {
        try {
            TimeUnit.MILLISECONDS.sleep(WAIT_TIME);
        } catch (InterruptedException e) {
            LOGGER.error("sleep error", e);
        }
    }

    private Map<String, List<String>> getFileContent(String workbenchHofsPath) throws ServiceException {
        if (StringUtils.isEmpty(workbenchHofsPath)) {
            throw new ServiceException("workbenchHofsPath is empty");
        }
        try {
            String fileName = workbenchHofsPath.substring(workbenchHofsPath.lastIndexOf(File.separator) + 1);
            fileName = fileName.substring(0, fileName.indexOf('.') + 1) + "zip";
            String localFilePath = RESULT_LOCAL_PATH + File.separator + fileName;
            HofsUtils.downloadFileOrDirToSpecified(workbenchHofsPath, localFilePath);
            String unzipPath = RESULT_LOCAL_PATH + File.separator + CommonUtils.getUUID();
            File unzipDir = new File(unzipPath);
            if (!unzipDir.exists() && !unzipDir.mkdirs()) {
                LOGGER.error("create unzip directory failed");
                throw new ServiceException("create unzip directory failed");
            }
            ZipUtils.getInst().unzipAll(localFilePath, unzipPath, true);
            return getLineList(unzipPath);
        } catch (HofsException e) {
            LOGGER.error("download file failed", e);
            throw new ServiceException("download file failed");
        } catch (Exception e) {
            LOGGER.error("get file content failed", e);
            throw new ServiceException(e.getMessage());
        } finally {
            HofsUtils.removeRemoteFile(workbenchHofsPath, false);
            HofsUtils.removeRemoteFile(workbenchHofsPath + ".SHA", false);
        }
    }

    private Map<String, List<String>> getLineList(String unzipPath) throws ServiceException {
        if (StringUtils.isEmpty(unzipPath)) {
            LOGGER.error("unzipPath is empty");
            throw new ServiceException("unzipPath is empty");
        }
        File file = new File(unzipPath);
        if (!file.exists() || !file.isDirectory()) {
            LOGGER.error("unzipPath is not a directory");
            throw new ServiceException("unzip may have error");
        }
        Map<String, List<String>> result = new HashMap<>();
        for (String fileName : file.list()) {
            if (!fileName.endsWith(".txt")) {
                continue;
            }
            String ip = fileName.substring(0, fileName.indexOf('_'));
            File txtFile = new File(unzipPath, fileName);
            try (BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(txtFile), StandardCharsets.UTF_8))) {
                String line;
                List<String> fileContents = new ArrayList<>();
                while ((line = br.readLine()) != null) {
                    String strLine = line.trim();
                    if (StringUtils.isEmpty(strLine)) {
                        continue;
                    }
                    fileContents.add(strLine);
                    if (fileContents.size() >= MAX_ARRAY_SIZE) {
                        LOGGER.warn("Too many lines in file, may cause memory overflow");
                        break;
                    }
                }
                // 将文件名作为Map的键，对应的FileContent列表作为值
                result.put(ip, fileContents);
            } catch (IOException e) {
                LOGGER.error("Error reading file", e);
                throw new ServiceException("Error reading file");
            }
        }
        FileUtil.delete(file);
        return result;
    }
}
