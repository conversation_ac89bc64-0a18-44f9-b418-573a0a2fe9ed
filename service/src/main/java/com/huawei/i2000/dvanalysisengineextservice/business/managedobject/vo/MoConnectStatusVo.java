/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.business.managedobject.vo;

import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.dvanalysisengineextservice.business.display.AbstractVo;
import com.huawei.i2000.dvanalysisengineextservice.constants.DisplayConstant;
import com.huawei.oms.eam.mo.ConnectStatus;
import lombok.Getter;
import lombok.Setter;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/1/23
 */

@Getter
@Setter
public class MoConnectStatusVo extends AbstractVo {
    private String neName;
    private String connectStatus = ResourceUtil.getMessage(DisplayConstant.COLUMN_PREFIX_MO_CONNECT_STATUS_CONNECT + ConnectStatus.NoDetect);
    private String adminStatus;
    private String location;
    private String parentName;

    public MoConnectStatusVo() {
        super(DisplayConstant.COLUMN_PREFIX_MO_CONNECT_STATUS);
    }
}
