/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.dashboard;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.dvanalysisengineextservice.cluster.CatalogInfoModel;
import com.huawei.i2000.dvanalysisengineextservice.cluster.KpiDataDiagnosisView;
import com.huawei.i2000.dvanalysisengineextservice.cluster.MeasObjCatalogModel;
import com.huawei.i2000.dvanalysisengineextservice.cluster.MeasTypeKeyCatalogModel;
import com.huawei.i2000.dvanalysisengineextservice.constants.DisplayConstant;
import com.huawei.i2000.dvanalysisengineextservice.dashboard.dto.ConfigModel;
import com.huawei.i2000.dvanalysisengineextservice.dashboard.dto.DiagramRenderDataModel;
import com.huawei.i2000.dvanalysisengineextservice.dashboard.dto.FieldConfigModel;
import com.huawei.i2000.dvanalysisengineextservice.dashboard.dto.FieldModel;
import com.huawei.i2000.dvanalysisengineextservice.dashboard.dto.FieldTypeModel;
import com.huawei.i2000.dvanalysisengineextservice.dashboard.dto.ViewConfig;
import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineextservice.util.CommonUtils;
import com.huawei.i2000.dvanalysisengineextservice.util.ContextUtils;
import com.huawei.i2000.dvanalysisengineextservice.util.rest.ResponseResultHelper;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能描述:输出大盘图例
 *
 * <AUTHOR>
 * @since 2025/04/18
 */
@Component
public class DashboardManage {
    private static final OssLog LOGGER = OssLogFactory.getLogger(DashboardManage.class);

    private static final List<String> PM_KPI_HEADER = new ArrayList<>(
        Arrays.asList("dn", "timestamp", "date", "period", "measObjects", "displayValue"));

    private static final String SPACE = ",";

    private static final int COMPARISON_TIME_LENGTH = 4;

    private static final int QUERY_TIME_LENGTH = 2;

    private static final int ERROR_CODE = -1;

    public ResponseResult measTypeKeysComparisonInfo(List<Map<String, String>> kpiValues, CatalogInfoModel catalogInfoModel, String timeRange) {
        String[] timeRangeArray = timeRange.split(SPACE);
        HashSet<String> indicators = new HashSet<>(kpiValues.get(0).keySet());
        PM_KPI_HEADER.forEach(indicators::remove);
        Set<String> measObjects = kpiValues.stream().map(v -> v.get("displayValue")).collect(Collectors.toSet());

        ResponseResult responseResult = comparisonValid(indicators, measObjects, timeRangeArray);

        if (!Objects.isNull(responseResult.getResultCode()) && responseResult.getResultCode() == ERROR_CODE) {
            return responseResult;
        }

        int period = getPeriod(kpiValues);
        List<String> timeValues = new ArrayList<>();
        for (long time = Long.parseLong(timeRangeArray[2]); time <= Long.parseLong(timeRangeArray[3]); time += period * 1000L) {
            timeValues.add(String.valueOf(time));
        }

        List<ViewConfig> viewConfigs = new ArrayList<>();
        DiagramRenderDataModel diagramRenderDataModel = getDiagramRenderDataModel(catalogInfoModel, indicators,
            timeValues);

        Optional<MeasTypeKeyCatalogModel> measUnitKeyInfo = catalogInfoModel.getMeasTypeKeys()
            .stream()
            .filter(v -> indicators.iterator().next().equals(v.getMeasTypeKey())).findFirst();

        if (!measUnitKeyInfo.isPresent()) {
            LOGGER.error("No internationalization information about the {} indicator is found.", indicators.iterator().next());
            return ResponseResultHelper.failResponseEntity(ResourceUtil.getMessage(DisplayConstant.INDICATOR_NOT_UNIQUE, ContextUtils.getDefaultLocale()));
        }
        String finalMeasObject = StringUtils.isEmpty(measObjects.iterator().next()) ? StringUtils.EMPTY : measObjects.iterator().next();
        Optional<MeasObjCatalogModel> measObjectInfo = catalogInfoModel.getMeasObjects()
            .stream()
            .filter(v -> finalMeasObject.equals(v.getDisplayValue())).findFirst();

        if (!measObjectInfo.isPresent()) {
            LOGGER.error("No internationalization information about the {} measObjects is found.", finalMeasObject);
            return ResponseResultHelper.failResponseEntity(ResourceUtil.getMessage(DisplayConstant.MEAS_OBJECTS_NOT_UNIQUE, ContextUtils.getDefaultLocale()));
        }

        // 组装Y轴不同指标数据
        List<FieldModel> fieldModels = getComparisonYAxisFieldModels(kpiValues, responseResult, timeRangeArray,
            measUnitKeyInfo.get(), measObjectInfo.get());

        if (CollectionUtils.isEmpty(fieldModels)) {
            return ResponseResultHelper.failResponseEntity(ResourceUtil.getMessage(DisplayConstant.TASK_NO_DATA, ContextUtils.getDefaultLocale()));
        }

        if (fieldModels.size() == 1) {
            return responseResult;
        }

        // 组装X轴
        getXAxisFieldModels(timeValues, fieldModels);

        diagramRenderDataModel.setFields(fieldModels);

        ViewConfig viewConfig = new ViewConfig();
        viewConfig.setDataFrame(diagramRenderDataModel);
        viewConfig.setChartType(DashboardConstant.GRAPH_TYPE_LINE);

        viewConfigs.add(viewConfig);
        KpiDataDiagnosisView kpiDataDiagnosisView = new KpiDataDiagnosisView();
        kpiDataDiagnosisView.setItemsHaveData(viewConfigs);

        return ResponseResultHelper.correctResponseEntity(kpiDataDiagnosisView);
    }

    private static ResponseResult comparisonValid(HashSet<String> indicators, Set<String> measObjects, String[] timeRangeArray) {
        // 同环比图必须为确定的一个测量指标及测量对象
        if (indicators.size() > 1) {
            LOGGER.error("The number of measurement indicators in the chain comparison chart must be unique.");
            return ResponseResultHelper.failResponseEntity(
                ResourceUtil.getMessage(DisplayConstant.INDICATOR_NOT_UNIQUE, ContextUtils.getDefaultLocale()));
        }

        if (measObjects.size() > 1) {
            LOGGER.error("The number of measurement objects in the period-on-period comparison chart must be unique.");
            return ResponseResultHelper.failResponseEntity(
                ResourceUtil.getMessage(DisplayConstant.MEAS_OBJECTS_NOT_UNIQUE, ContextUtils.getDefaultLocale()));
        }

        // 同环比图两段时间间隔必须是以天为间隔的, 且间隔时间一致
        if (timeRangeArray.length != COMPARISON_TIME_LENGTH || Long.parseLong(timeRangeArray[1]) - Long.parseLong(timeRangeArray[0])
            != Long.parseLong(timeRangeArray[3]) - Long.parseLong(timeRangeArray[2])) {
            LOGGER.error("The interval between the two time ranges in the same period is different.");
            return ResponseResultHelper.failResponseEntity(
                ResourceUtil.getMessage(DisplayConstant.DIFF_TIME_INTERVALS, ContextUtils.getDefaultLocale()));
        }

        long newPeriodStartTime = Math.max(Long.parseLong(timeRangeArray[3]), Long.parseLong(timeRangeArray[1]));
        long oldPeriodStartTime = Math.min(Long.parseLong(timeRangeArray[3]), Long.parseLong(timeRangeArray[1]));

        if ((newPeriodStartTime - oldPeriodStartTime) % (24 * 60 * 60 * 1000L) != 0) {
            LOGGER.error("The interval between the two time ranges in the same period is one day.");
            return ResponseResultHelper.failResponseEntity(
                ResourceUtil.getMessage(DisplayConstant.INVALIDS_TIME_INTERVALS, ContextUtils.getDefaultLocale()));
        }

        return new ResponseResult();
    }

    private static DiagramRenderDataModel getDiagramRenderDataModel(CatalogInfoModel catalogInfoModel,
        Set<String> indicators, List<String> timeValues) {
        DiagramRenderDataModel diagramRenderDataModel = new DiagramRenderDataModel();
        diagramRenderDataModel.setId(CommonUtils.getUUID());
        if (CollectionUtils.isNotEmpty(indicators)) {
            Optional<MeasTypeKeyCatalogModel> measUnitKeys = catalogInfoModel.getMeasTypeKeys()
                .stream()
                .filter(v -> indicators.iterator().next().equals(v.getMeasTypeKey())).findFirst();
            diagramRenderDataModel.setName("Kpi data -" + (measUnitKeys.isPresent()? measUnitKeys.get().getColumnNameEn(): StringUtils.EMPTY));
        }
        diagramRenderDataModel.setUnitTypes(1);

        ConfigModel configModel = new ConfigModel();
        configModel.setType(DashboardConstant.GRAPH_TYPE_LINE);
        configModel.setLegendShow(true);
        diagramRenderDataModel.setConfig(configModel);

        diagramRenderDataModel.setLength(timeValues.size());
        return diagramRenderDataModel;
    }

    public ResponseResult measTypeKeysNormalInfo(List<Map<String, String>> kpiValues,
        CatalogInfoModel catalogInfoModel, String neName, String taskName, String timeRange) {
        if (StringUtils.isNotEmpty(timeRange) && timeRange.split(SPACE).length != QUERY_TIME_LENGTH) {
            LOGGER.error("The time range is incorrect.");
            return ResponseResultHelper.failResponseEntity(
                ResourceUtil.getMessage(DisplayConstant.TIME_RANGE_NOT_CORRECT, ContextUtils.getDefaultLocale()));
        }
        // 生成时间戳kpiValues.stream()
        List<String> timeValues = kpiValues.stream()
            .map(v -> v.get("timestamp"))
            .sorted().distinct()
            .collect(Collectors.toList());

        // 查询指标单位，同单位的可放入同一张图中
        Map<String, List<MeasTypeKeyCatalogModel>> measUnitKeyGroupByUnitMap = catalogInfoModel.getMeasTypeKeys()
            .stream()
            .collect(Collectors.groupingBy(MeasTypeKeyCatalogModel::getUnitEn));

        // 分图
        List<ViewConfig> viewConfigs = new ArrayList<>();
        for (List<MeasTypeKeyCatalogModel> measUnitKeyGroupByUnit : measUnitKeyGroupByUnitMap.values()) {
            DiagramRenderDataModel diagramRenderDataModel = getDiagramRenderDataModel(catalogInfoModel, Collections.emptySet(), timeValues);
            diagramRenderDataModel.setName("Kpi data -" + neName + "_" + taskName);

            // 组装Y轴不同指标数据
            List<FieldModel> fieldModels = getYAxisFieldModels(kpiValues, catalogInfoModel, timeValues,
                measUnitKeyGroupByUnit);

            if (CollectionUtils.isEmpty(fieldModels)) {
                LOGGER.info("The following indicator data is not displayed: {}", measUnitKeyGroupByUnit.toString());
                continue;
            }

            // 组装X轴
            getXAxisFieldModels(timeValues, fieldModels);

            diagramRenderDataModel.setFields(fieldModels);

            ViewConfig viewConfig = new ViewConfig();
            viewConfig.setChartType(DashboardConstant.GRAPH_TYPE_LINE);
            viewConfig.setDataFrame(diagramRenderDataModel);

            viewConfigs.add(viewConfig);
        }

        if (CollectionUtils.isEmpty(viewConfigs)) {
            return ResponseResultHelper.failResponseEntity(
                ResourceUtil.getMessage(DisplayConstant.TASK_NO_DATA, ContextUtils.getDefaultLocale()));
        }

        KpiDataDiagnosisView kpiDataDiagnosisView = new KpiDataDiagnosisView();
        kpiDataDiagnosisView.setItemsHaveData(viewConfigs);

        return ResponseResultHelper.correctResponseEntity(kpiDataDiagnosisView);
    }

    private static int getPeriod(List<Map<String, String>> kpiValues) {
        List<String> timestampList = kpiValues.stream()
            .map(v -> v.get("timestamp"))
            .sorted()
            .collect(Collectors.toList());
        return getPeriod(kpiValues, timestampList);
    }

    private static int getPeriod(List<Map<String, String>> kpiValues, List<String> timestampList) {
        int period = Integer.parseInt(kpiValues.get(0).get("period"));
        timestampList = timestampList.stream().distinct().collect(Collectors.toList());
        if (timestampList.size() > 1) {
            long minInterval = Long.MAX_VALUE;
            for (int i = 1; i < timestampList.size(); i++) {
                long current = Long.parseLong(timestampList.get(i));
                long previous = Long.parseLong(timestampList.get(i - 1));
                long interval = (current - previous) / 1000;
                if (interval < minInterval) {
                    minInterval = interval;
                }
            }
            period = Math.max((int) minInterval, period);
        }
        return period;
    }

    private static void getXAxisFieldModels(List<String> timeValues, List<FieldModel> fieldModels) {
        FieldModel fieldModel = new FieldModel();
        fieldModel.setId(CommonUtils.getUUID());
        String xName = ResourceUtil.getMessage(DisplayConstant.TIME_NAME, ContextUtils.getDefaultLocale());
        fieldModel.setRawName(xName);
        fieldModel.setName(xName);
        fieldModel.setType("time");
        List<Object> objectTimeValues = new ArrayList<>(timeValues);
        fieldModel.setValues(objectTimeValues);

        FieldConfigModel fieldConfigModel = new FieldConfigModel();
        FieldTypeModel fieldTypeModel = new FieldTypeModel();
        fieldTypeModel.setIsX(true);
        fieldConfigModel.setType(fieldTypeModel);
        fieldConfigModel.setShow(false);
        fieldConfigModel.setYIndex(0);
        fieldModel.setConfig(fieldConfigModel);
        fieldModels.add(fieldModel);
    }

    private static List<FieldModel> getComparisonYAxisFieldModels(List<Map<String, String>> kpiValues,
        ResponseResult responseResult, String[] timeRangeArray, MeasTypeKeyCatalogModel measUnitKey, MeasObjCatalogModel measObject) {
        List<FieldModel> fieldModels = new ArrayList<>();

        for (int i = 0; i < timeRangeArray.length / 2; i++) {
            long startTime = Long.parseLong(timeRangeArray[2 * i]);
            long endTime = Long.parseLong(timeRangeArray[2 * i + 1]);

            List<String> timestampList = kpiValues.stream()
                .filter(v -> Long.parseLong(v.get("timestamp")) <= endTime && Long.parseLong(v.get("timestamp")) >= startTime)
                .map(v -> v.get("timestamp"))
                .sorted()
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(timestampList)) {
                responseResult.setResultCode(ERROR_CODE);
                responseResult.setResultMessage(ResourceUtil.getMessage(DisplayConstant.COMPARISON_RANGE_NO_DATA, new Object[] {
                    DisplayConstant.DATE_TIME_FORMATTER.format(Instant.ofEpochMilli(startTime)),
                    DisplayConstant.DATE_TIME_FORMATTER.format(Instant.ofEpochMilli(endTime))
                }, ContextUtils.getDefaultLocale()));
                continue;
            }

            long newStartTime = Long.parseLong(timestampList.get(0));
            int period = getPeriod(kpiValues);
            List<String> timeValues = new ArrayList<>();
            for (long time = newStartTime; time <= endTime; time += period * 1000L) {
                timeValues.add(String.valueOf(time));
            }

            FieldModel fieldModel = getFieldModel(timeValues, measUnitKey, measObject, kpiValues, true);
            if (fieldModel == null) {
                continue;
            }
            fieldModels.add(fieldModel);
        }
        return fieldModels;
    }

    private static List<FieldModel> getYAxisFieldModels(List<Map<String, String>> kpiValues,
        CatalogInfoModel catalogInfoModel, List<String> timeValues,
        List<MeasTypeKeyCatalogModel> measUnitKeyGroupByUnit) {
        List<FieldModel> fieldModels = new ArrayList<>();
        for (MeasTypeKeyCatalogModel measUnitKeys : measUnitKeyGroupByUnit) {
            List<MeasObjCatalogModel> measObjects = catalogInfoModel.getMeasObjects();
            for (MeasObjCatalogModel measObject : measObjects) {
                List<Map<String, String>> data;
                if (measObject.getDisplayValue() == null) {
                    data = kpiValues.stream()
                        .filter(v -> v.containsKey(measUnitKeys.getMeasTypeKey())).collect(Collectors.toList());
                } else {
                    data = kpiValues.stream()
                        .filter(v -> measObject.getDisplayValue()
                            .equals(v.get("displayValue") == null ? StringUtils.EMPTY : v.get("displayValue"))
                            && v.containsKey(measUnitKeys.getMeasTypeKey()))
                        .collect(Collectors.toList());
                }

                if (CollectionUtils.isEmpty(data)) {
                    continue;
                }

                FieldModel fieldModel = getFieldModel(timeValues, measUnitKeys, measObject, data, false);
                if (fieldModel == null) {
                    continue;
                }
                fieldModels.add(fieldModel);
            }
        }
        return fieldModels;
    }

    private static FieldModel getFieldModel(List<String> timeValues, MeasTypeKeyCatalogModel measUnitKeys,
        MeasObjCatalogModel measObject, List<Map<String, String>> data, Boolean isNormal) {
        FieldModel fieldModel = new FieldModel();
        fieldModel.setId(CommonUtils.getUUID());
        StringBuilder rawName = new StringBuilder();
        rawName.append(
            Locale.US.equals(ContextUtils.getDefaultLocale())
                ? measObject.getDisplayValueEn() + "_" + measUnitKeys.getColumnNameEn()
                : measObject.getDisplayValueZh() + "_" + measUnitKeys.getColumnNameZh());
        if (isNormal) {
            rawName.append("(")
                .append(DisplayConstant.DATE_DAY_TIME_FORMATTER.format(Instant.ofEpochMilli(Long.parseLong(timeValues.get(0)))))
                .append(")");
        }
        fieldModel.setRawName(rawName.toString());
        fieldModel.setName(fieldModel.getRawName());
        fieldModel.setType("number");
        List<Object> values = new ArrayList<>();
        for (String timestamp : timeValues) {
            Optional<Map<String, String>> result = data.stream()
                .filter(v -> timestamp.equals(v.get("timestamp")))
                .findFirst();
            if (result.isPresent()) {
                values.add(result.get().getOrDefault(measUnitKeys.getMeasTypeKey(), null));
            } else {
                values.add(null);
            }
        }
        // 判断values是否都为null
        if (values.stream().allMatch(Objects::isNull)) {
            return null;
        }

        fieldModel.setValues(values);

        FieldConfigModel fieldConfigModel = new FieldConfigModel();
        fieldConfigModel.setUnit(Locale.US.equals(ContextUtils.getDefaultLocale())
            ? measUnitKeys.getUnitEn()
            : measUnitKeys.getUnitZh());
        fieldConfigModel.setYIndex(0);

        fieldModel.setConfig(fieldConfigModel);
        return fieldModel;
    }
}
