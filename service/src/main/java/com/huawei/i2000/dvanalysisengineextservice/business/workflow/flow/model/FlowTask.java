/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.Map;

/**
 * 功能描述：流程执行任务
 *
 * <AUTHOR>
 * @since 2020-12-01
 */
@NoArgsConstructor
public class FlowTask {
    /**
     * Flow ins id
     */
    @Getter
    @Setter
    private String flowInsId;

    /**
     * Type
     */
    @Getter
    @Setter
    private FlowExecuteType type;

    /**
     * Event
     */
    @Getter
    @Setter
    private String event;

    /**
     * Flow id
     */
    @Getter
    @Setter
    private String flowId;

    /**
     * Deployed id
     */
    @Getter
    @Setter
    private String deployedId;

    /**
     * Flow name
     */
    @Getter
    @Setter
    private String flowName;

    /**
     * Order by
     */
    @Getter
    @Setter
    private String orderBy;

    /**
     * Start time
     */
    private Date startTime;

    /**
     * End time
     */
    private Date endTime;

    /**
     * Status
     */
    @Getter
    @Setter
    private FlowExecuteStatus status;

    /**
     * Result
     */
    @Getter
    @Setter
    private String result;

    /**
     * Start time string
     */
    @Getter
    @Setter
    private String startTimeString;

    /**
     * End time string
     */
    @Getter
    @Setter
    private String endTimeString;

    /**
     * Executor
     */
    @Getter
    @Setter
    private String executor;

    /**
     * Tenant id
     */
    @Getter
    @Setter
    private String tenantId;

    /**
     * Heal succ
     */
    // @Getter
    @Setter
    private Integer healSucc;

    /**
     * Diag succ
     */
    // @Getter
    @Setter
    private Integer diagSucc;

    /**
     * Exist download
     */
    @Setter
    private Integer existDownload;

    /**
     * 流程上限文变量，实时刷新上下文变量值
     */
    @Getter
    @Setter
    private String processVariables;

    /**
     * Gets exist download *
     * 
     * @return the exist download
     */
    public Integer getExistDownload() {
        if (existDownload == null) {
            return 0;
        }
        return existDownload;
    }

    /**
     * Gets heal succ *
     * 
     * @return the heal succ
     */
    public Integer getHealSucc() {
        if (healSucc == null) {
            return 0;
        }
        return healSucc;
    }

    /**
     * Gets diag succ *
     * 
     * @return the diag succ
     */
    public Integer getDiagSucc() {
        if (diagSucc == null) {
            return 0;
        }
        return diagSucc;
    }

    /**
     * Gets start time *
     * 
     * @return the start time
     */
    public Date getStartTime() {
        return null == startTime ? null : (Date) startTime.clone();
    }

    /**
     * Sets start time *
     * 
     * @param startTime start time
     */
    public void setStartTime(Date startTime) {
        this.startTime = null == startTime ? null : (Date) startTime.clone();
    }

    /**
     * Gets end time *
     * 
     * @return the end time
     */
    public Date getEndTime() {
        return null == endTime ? null : (Date) endTime.clone();
    }

    /**
     * Sets end time *
     * 
     * @param endTime end time
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime == null ? null : (Date) endTime.clone();
    }

    /**
     * Variables
     */
    @Getter
    @Setter
    private Map<String, Object> variables;


}