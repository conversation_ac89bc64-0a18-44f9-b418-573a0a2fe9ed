/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.task.dataclean;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 时间维度清理策略
 *
 * <AUTHOR>
 * @since 2025/7/23
 */
@Service
public class TimeBasedCleanStrategy implements CleanStrategy {
    private static final OssLog LOGGER = OssLogFactory.getLogger(TimeBasedCleanStrategy.class);

    private final IncidentDataCleaner incidentDataCleaner;

    private final CaseReportDataCleaner caseReportDataCleaner;

    private static final long INCIDENT_EXPIRED_TIME = 7 * 24 * 60 * 60 * 1000L;

    @Autowired
    public TimeBasedCleanStrategy(
        IncidentDataCleaner incidentDataCleaner,
        CaseReportDataCleaner caseReportDataCleaner) {
        this.incidentDataCleaner = incidentDataCleaner;
        this.caseReportDataCleaner = caseReportDataCleaner;
    }

    @Override
    public void clean() {
        long clearTime = calExpirationTime();
        incidentDataCleaner.deleteByTime(clearTime);
        // 待实现清理案例报告记录
    }

    private long calExpirationTime() {
        return System.currentTimeMillis() - INCIDENT_EXPIRED_TIME;
    }
}
