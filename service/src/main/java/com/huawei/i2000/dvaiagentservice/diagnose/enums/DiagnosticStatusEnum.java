/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.diagnose.enums;

import lombok.Getter;

/**
 * 功能描述
 * 诊断状态枚举 - 完整实现人工和自动处置状态机
 *
 * <AUTHOR>
 * @since 2025/5/6
 */
@Getter
public enum DiagnosticStatusEnum {
    /**
     * 占位符
     */
    DIAGNOSTIC_STATUS_PLACEHOLDER(0, "占位符"),

    // 人工处置流程状态
    /**
     * 待人工处置状态
     */
    PENDING_MANUAL(1, "待人工处置"),

    /**
     * 人工处置中状态
     */
    MANUAL_PROCESSING(2, "人工处置中"),

    /**
     * 人工已处置状态
     */
    MANUAL_COMPLETED(3, "人工已处置"),

    /**
     * 人工处置失败状态
     */
    MANUAL_FAILED(4, "人工处置失败"),

    // 自动处置流程状态
    /**
     * 自动待处置状态
     */
    PENDING_AUTO(5, "自动待处置"),

    /**
     * 自动处置中状态
     */
    AUTO_PROCESSING(6, "自动处置中"),

    /**
     * 自动已处置状态
     */
    AUTO_COMPLETED(7, "自动已处置"),

    /**
     * 自动处置失败状态
     */
    AUTO_FAILED(8, "自动处置失败");

    /**
     * 序列枚举值
     */
    private final int code;

    /**
     * 枚举描述
     */
    private final String description;

    // 增强可读性
    DiagnosticStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过状态码解析枚举的工厂方法
     *
     * @param code 状态码（1-8）
     * @return 对应的枚举实例
     * @throws IllegalArgumentException 当传入非法状态码时抛出
     */
    public static DiagnosticStatusEnum fromCode(int code) {
        for (DiagnosticStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid diagnostic status: " + code);
    }

    /**
     * 通过枚举名称解析枚举的工厂方法
     *
     * @param name 枚举名称
     * @return 对应的枚举实例
     * @throws IllegalArgumentException 当传入非法枚举名称时抛出
     */
    public static DiagnosticStatusEnum fromName(String name) {
        for (DiagnosticStatusEnum status : values()) {
            if (status.name().equals(name)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid diagnostic status name: " + name);
    }

    /**
     * 状态类型检测方法 - 判断是否属于人工流程状态
     *
     * @return 是否处于人工流程状态
     */
    public boolean isManualProcess() {
        return code >= 1 && code <= 4;
    }

    /**
     * 状态类型检测方法 - 判断是否属于自动流程状态
     *
     * @return 是否属于自动流程状态
     */
    public boolean isAutoProcess() {
        return code >= 5 && code <= 8;
    }

    /**
     * 终态检测方法 - 判断是否是最终状态（处置完成或失败）
     *
     * @return 是否是最终状态
     */
    public boolean isFinalState() {
        return this == MANUAL_COMPLETED || this == MANUAL_FAILED
                || this == AUTO_COMPLETED || this == AUTO_FAILED;
    }


}