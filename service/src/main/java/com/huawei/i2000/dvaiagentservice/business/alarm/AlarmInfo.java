/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.alarm;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;

import lombok.Data;

import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/1/9
 */
@Data
public class AlarmInfo {
    private static final OssLog LOGGER = OssLogFactory.getLogger(AlarmInfo.class);

    private long clearUtc;
    private String probableCause;
    private String alarmId;
    private long occurUtc;
    private long occurTime;
    private long arriveUtc;
    private long latestOccurTime;
    private String alarmName;
    private int severity;
    private long csn;
    private long rootCsn;
    private int eventType;
    private String meName;
    private String deviceTypeId;
    private int acked;
    private int cleared;
    private int category;
    private int count;
    private String nativeMoDn;
    private String nativeMeDn;
    private String comment;
    private String productName;
    private String moi;
    private String meType;
    private String moDn;
    private String tenantId;
    private String domain;
    private String additionalInformation;
    private String rawData;

    public boolean isCleared() {
        return Objects.equals(this.category, 2) || this.cleared == 1;
    }

    public boolean isAdded() {
        return Objects.equals(this.category, 1) || this.cleared == 0;
    }

    public Map<String, String> toMap() {
        Class<? extends AlarmInfo> aClass = this.getClass();
        HashMap<String, String> map = new HashMap<>();
        for (Field field : aClass.getDeclaredFields()) {
            if ("LOGGER".equals(field.getName()) || "rawData".equalsIgnoreCase(field.getName())) {
                continue;
            }
            ReflectionUtils.makeAccessible(field);
            Object value = null;
            try {
                value = field.get(this);
            } catch (IllegalAccessException e) {
                LOGGER.error("get field value failed, name = {}, message = {}", field.getName(), e.getMessage());
            }
            if (Objects.nonNull(value)) {
                map.put(field.getName(), String.valueOf(value));
            }
        }
        return map;
    }
}
