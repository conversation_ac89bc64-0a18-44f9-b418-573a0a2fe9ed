/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * 功能描述 ChatEntry
 *
 * @since 2025-07-09
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatEntry {
    private String chatId;
    private String answerId;
    private Object data;
    private String type;
    private String status;
    private Date createTime; // 如果需要JSON日期格式化，可添加@JsonFormat注解
}