/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2021. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.util;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程工具类。包含sleep
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public final class ThreadUtil {
    private static final OssLog LOGGER = OssLogFactory.getLog(ThreadUtil.class);

    private ThreadUtil() {
    }

    /**
     * 线程sleep公共类
     *
     * @param timeout 等待时长
     * @param timeUnit 单位
     */
    public static void sleep(int timeout, TimeUnit timeUnit) {
        try {
            timeUnit.sleep(timeout);
        } catch (InterruptedException e) {
            LOGGER.error("Thread sleep exception.", e);
        }
    }

    /**
     * 线程sleep公共类， 单位：秒
     *
     * @param second 等待时长,单位：秒
     */
    public static void sleepSec(int second) {
        sleep(second, TimeUnit.SECONDS);
    }

    /**
     * 线程sleep公共类， 单位：毫秒
     *
     * @param milliSecond 等待时长，单位：毫秒
     */

    public static void sleepMilliSec(int milliSecond) {
        sleep(milliSecond, TimeUnit.MILLISECONDS);
    }

    /**
     * 停止线程池
     *
     * @param executor 线程池
     */
    public static void stopThreadPool(ThreadPoolExecutor executor) {
        if (executor == null) {
            return;
        }
        executor.shutdown();
        // 线程执行完成，则结束迁移
        while (!executor.isTerminated()) {
            ThreadUtil.sleepSec(1);
        }
    }

}
