/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.sftp;

import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.i2000.cbb.security.utils.CleanPWDUtil;
import com.huawei.i2000.dvaiagentservice.exception.ConsumerWithException;
import com.huawei.i2000.dvaiagentservice.exception.FunctionWithException;
import com.huawei.i2000.dvaiagentservice.util.ThreadUtil;
import com.huawei.i2000.util.network.IpEnvUtil;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.sshd.client.SshClient;
import org.apache.sshd.client.session.ClientSession;
import org.apache.sshd.common.cipher.BuiltinCiphers;
import org.apache.sshd.common.mac.BuiltinMacs;
import org.apache.sshd.sftp.client.SftpClient;
import org.apache.sshd.sftp.client.SftpClientFactory;
import org.apache.sshd.sftp.client.impl.SftpDirEntryIterator;
import org.apache.sshd.sftp.common.SftpException;
import org.springframework.util.FileCopyUtils;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InterruptedIOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

/**
 * SSHRemoteCall
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public class SftpUtils {
    private static final OssLog LOG = OssLogFactory.getLog(SftpUtils.class);

    /**
     * 当sftp操作被终止的重试次数
     */
    private static final int RETRY_TIME = FtpConstants.COLLECT_UOA_SFTP_RETRYTIME;

    /**
     * 当sftp操作被终止的重试休眠时间
     */
    private static final int FAIL_SLEEP = FtpConstants.COLLECT_UOA_SFTP_FAIL_SLEEP_TIME;

    private static final SftpUtils SFTP_UTILS = new SftpUtils();

    /**
     * 远程sftp 智能体文件夹
     */
    public static final String SFTP_DIR = "sftpAIAGENT";

    private SftpClient sftpClient;

    private SshClient client;

    private ClientSession session;

    private boolean isLogin = false;

    /**
     * 构造函数
     */
    private SftpUtils() {
    }

    public static SftpUtils getInstance() {
        return SFTP_UTILS;
    }

    private void login() throws OSSException {
        // 如果已登录，直接返回
        if (isLogin && sftpClient != null && sftpClient.isOpen()) {
            return;
        }
        closeSession();
        String identityCode = null;
        // 创建JSCH对象
        try {
            client = SshClient.setUpDefaultClient();
            if (FtpConstants.COLLECT_SFTP_CLIENT_SECURE_ALGORITHMS) {
                client.setCipherFactories(Collections.unmodifiableList(
                        Arrays.asList(BuiltinCiphers.cc20p1305_openssh, BuiltinCiphers.aes128ctr, BuiltinCiphers.aes192ctr,
                                BuiltinCiphers.aes256ctr, BuiltinCiphers.aes128gcm, BuiltinCiphers.aes256gcm)));
                client.setMacFactories(Collections.unmodifiableList(
                        Arrays.asList(BuiltinMacs.hmacsha256etm, BuiltinMacs.hmacsha512etm, BuiltinMacs.hmacsha256,
                                BuiltinMacs.hmacsha512)));
            }
            client.start();
            session = client.connect(FtpConstants.NAME, getIp(), Integer.parseInt(FtpConstants.PORT))
                    .verify()
                    .getSession();
            identityCode = SftpAccountCache.getSftpPwd();
            session.addPasswordIdentity(identityCode);
            session.auth().verify();
            SftpClientFactory factory = SftpClientFactory.instance();
            sftpClient = factory.createSftpClient(session);
            isLogin = true;
        } catch (IOException e) {
            isLogin = false;
            LOG.error("connect to sftp error", e);
            throw new OSSException("login sftp error");
        } finally {
            CleanPWDUtil.clearStrPwd(identityCode);
        }
    }

    /**
     * 上传文件
     *
     * @param src  将要上传的文件
     * @param dest 上传文件的目录
     * @throws OSSException 上传失败
     */
    public void uploadFile(String src, String dest) throws OSSException {
        acceptSftp(sftpClient -> {
            try (FileInputStream inputStream = new FileInputStream(src);
                 OutputStream outputStream = sftpClient.write(dest)) {
                FileCopyUtils.copy(inputStream, outputStream);
            }
        });
    }

    /**
     * 上传文件夹
     *
     * @param path 远程文件夹
     * @return 上传结果 boolean
     */
    public static boolean uploadDir(String path) {
        try {
            SftpUtils.getInstance().mkdir(SFTP_DIR);
            SftpUtils.getInstance().mkdir(SFTP_DIR + "/" + path);
            LOG.debug("uploadDir to sftp successfully.localDir={}", path);
        } catch (Exception e) {
            LOG.error("local uploadDir to sftp failed. remoteDir=" + path, e);
            return false;
        }
        return true;
    }

    /**
     * 下载文件 采用默认的传输模式：OVERWRITE
     *
     * @param src  linux服务器文件地址
     * @param dest 本地存放地址
     * @throws OSSException OSSException
     */
    public void downloadFile(String src, String dest) throws OSSException {
        acceptSftp(sftpClient -> {
            try (InputStream inputStream = sftpClient.read(src);
                 OutputStream outputStream = Files.newOutputStream(Paths.get(dest))) {
                FileCopyUtils.copy(inputStream, outputStream);
            }
        });
    }

    /**
     * 删除文件
     *
     * @param path 要删除的文件路径
     * @throws OSSException OSSException
     */
    public void deleteFile(String path) throws OSSException {
        acceptSftp(sftpClient -> {
            try {
                sftpClient.remove(path);
            } catch (SftpException e) {
                if (e.getMessage().contains("No such file or directory")) {
                    return;
                }
                throw e;
            }
        });
    }

    /**
     * 删除文件夹
     *
     * @param path 要删除的文件夹路径
     * @throws OSSException OSSException
     */
    public void deleteDir(String path) throws OSSException {
        acceptSftp(sftpClient -> deleteDir(sftpClient, path));
    }

    /**
     * 删除文件夹
     *
     * @param sftpClient sftpClient
     * @param path       文件名和文件夹名
     * @throws OSSException OSSException
     */
    private void deleteDir(SftpClient sftpClient, String path) throws OSSException {
        try {
            Iterable<SftpClient.DirEntry> files = sftpClient.readDir(path);
            for (SftpClient.DirEntry file : files) {
                if (".".equalsIgnoreCase(file.getFilename()) || "..".equalsIgnoreCase(file.getFilename())) {
                    continue;
                }
                if (file.getAttributes().isDirectory()) {
                    deleteDir(sftpClient, path + "/" + file.getFilename());
                } else {
                    sftpClient.remove(path + "/" + file.getFilename());
                }
            }
            sftpClient.rmdir(path);
        } catch (Exception e) {
            if (e.getMessage().contains("No such file or directory")) {
                return;
            }
            LOG.error("remove dir error", e);
            throw new OSSException("remove dir error");
        }
    }

    /**
     * 创建文件夹
     *
     * @param path 要创建的文件夹路径
     * @throws OSSException OSSException
     */
    public void mkdir(String path) throws OSSException {
        acceptSftp(sftpClient -> {
            try {
                sftpClient.mkdir(path);
            } catch (SftpException e) {
                if (e.getMessage().contains("already exists")) {
                    return;
                }
                throw e;
            }
        });
    }

    /**
     * 列出目录下的文件
     *
     * @param directory 目录
     * @return 文件列表
     * @throws OSSException OSSException
     */
    public Collection<SftpClient.DirEntry> listFiles(String directory) throws OSSException {
        FunctionWithException<SftpClient, Collection<SftpClient.DirEntry>> function = sftpClient -> {
            SftpDirEntryIterator iterator = null;
            try {
                Iterable<SftpClient.DirEntry> entries = sftpClient.readDir(directory);
                iterator = (SftpDirEntryIterator) entries.iterator();
                List<SftpClient.DirEntry> list = new LinkedList<>();
                while (iterator.hasNext()) {
                    list.add(iterator.next());
                }
                return list;
            } finally {
                if (iterator != null) {
                    iterator.close();
                }
            }
        };
        try {
            return applySftp(function, 0);
        } catch (OSSException e) {
            if (e.getMessage() != null && e.getMessage().contains("No such file or directory")) {
                LOG.info("directory {} not exist in sftp", directory);
                return null;
            }
            LOG.error("directory {} list file in sftp error", directory, e);
            throw e;
        }
    }

    private void acceptSftp(ConsumerWithException<SftpClient> consumer) throws OSSException {
        applySftp(client -> {
            consumer.accept(client);
            return null;
        }, 0);
    }

    private synchronized <T> T applySftp(FunctionWithException<SftpClient, T> function, int retryTimes)
            throws OSSException {
        try {
            login();
            return function.apply(sftpClient);
        } catch (InterruptedIOException e) {
            if (retryTimes > 5) {
                LOG.error("operate sftp error", e);
                throw new OSSException(e.getMessage());
            }
            LOG.warn("operate sftp error, retryTimes is {}", retryTimes, e);
            return applySftp(function, retryTimes + 1);
        } catch (RuntimeException | IOException e) {
            if (e.getMessage() != null && e.getMessage().contains("No such file or directory")) {
                LOG.warn("operate sftp error", e);
                throw new OSSException(e.getMessage());
            }

            if (StringUtils.containsAny(e.getMessage(), "A write operation is already pending",
                    "InterruptedIOException", "Channel is being closed") && retryTimes <= RETRY_TIME) {
                LOG.warn("operate sftp error, retryTimes is {}", retryTimes, e);
                ThreadUtil.sleepMilliSec(FAIL_SLEEP);
                return applySftp(function, retryTimes + 1);
            }
            LOG.error("operate sftp error", e);
            throw new OSSException(e.getMessage());
        } catch (Exception e) {
            LOG.error("operate sftp error", e);
            throw new OSSException("operate sftp error");
        }
    }

    /**
     * 关闭连接
     */
    private void closeSession() {
        IOUtils.closeQuietly(sftpClient);
        IOUtils.closeQuietly(session);
        IOUtils.closeQuietly(client);
        isLogin = false;
    }

    /**
     * Gets ip.
     *
     * @return the ip
     */
    public static String getIp() {
        if (IpEnvUtil.instance().getIpMode() == IpEnvUtil.IpMode.IPV4) {
            return SftpAccountCache.getSftpIpv4();
        } else {
            return SftpAccountCache.getSftpIpv6();
        }
    }
}