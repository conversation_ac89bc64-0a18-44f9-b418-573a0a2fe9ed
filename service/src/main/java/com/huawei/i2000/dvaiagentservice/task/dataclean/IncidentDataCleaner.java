/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.task.dataclean;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.i2000.dvaiagentservice.dao.IncidentDao;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * Incident表清理
 *
 * <AUTHOR>
 * @since 2025/7/23
 */
@Service
public class IncidentDataCleaner implements TableDataCleaner {
    private static final OssLog LOGGER = OssLogFactory.getLogger(IncidentDataCleaner.class);

    private final IncidentDao incidentDao;

    @Autowired
    public IncidentDataCleaner(IncidentDao incidentDao) {
        this.incidentDao = incidentDao;
    }

    @Override
    public int deleteByTime(long time) {
        try {
            LOGGER.info("schedule clear expired incident execute...");
            Set<Long> incidentCsnSet = incidentDao.getExpiredIncidentCsnSet(time);
            if (CollectionUtils.isNotEmpty(incidentCsnSet)) {
                LOGGER.info("clear expired incident, incident csn size is {}", incidentCsnSet.size());
                incidentDao.deleteIncidentByCsnSet(incidentCsnSet);
            }
        } catch (Exception e) {
            LOGGER.error("clear expired incident error, e: ", e);
        } finally {
            LOGGER.info("schedule clear expired incident finished.");
        }
        return 0;
    }

    @Override
    public int deleteByCapacity() {
        return 0;
    }
}
