/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.sftp;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulFactory;
import com.huawei.bsp.roa.util.restclient.RestfulOptions;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.cloudsop.common.tokenhelper.util.rpc.RestAdapter;
import com.huawei.i2000.dvaiagentservice.business.json.ObjectMapperFactory;
import com.huawei.oms.eam.mim.tool.LogUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * SftpRestProxy
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public class SftpRestProxy {
    private static final OssLog LOGGER = OssLogFactory.getLogger(SftpRestProxy.class);

    // 调用restful接口的超时时间，单位：ms
    private static final int REST_TIMEOUT = 60000;

    /**
     * 查询sftp密码
     */
    private static final String URI_SFTP_INFO = "/rest/i2000/cloudsopadapter/pm/getSftpInfo";


    public static Map<String, String> getSftpPwd() throws ServiceException {
        Map<String, String> sftpInfo = new HashMap<>();
        long start = System.currentTimeMillis();
        LOGGER.info("begin to get sftpPwd.");
        RestfulResponse response = new RestAdapter(RestfulFactory.getRestInstance()).get(URI_SFTP_INFO, null,
                getOptions());
        String responseContent = getResponseJson(response);
        try {
            sftpInfo = ObjectMapperFactory.getObjectMapper().readValue(responseContent, new HashMapStringReference());
            if (MapUtils.isNotEmpty(sftpInfo)) {
                return sftpInfo;
            }
        } catch (JsonProcessingException e) {
            LOGGER.error("Parse to json error", e);
            return sftpInfo;
        }
        LOGGER.info("end getSftpPwd  status:{}, time:{}", response.getStatus(), (System.currentTimeMillis() - start));
        return sftpInfo;
    }

    private static class HashMapStringReference extends TypeReference<Map<String, String>> {

    }

    /**
     * Gets options.
     *
     * @return the options
     */
    protected static RestfulOptions getOptions() {
        RestfulOptions options = new RestfulOptions();
        options.setRestTimeout(REST_TIMEOUT);
        return options;
    }

    /**
     * 从请求的响应消息中获取返回值
     * 这里的返回值仅Json over JMS层次的，而不是应用层期望的应用级返回值，需要应用层将
     * 其转化为具体的业务对象。
     *
     * @param response REST响应
     * @return 返回值(json字符串) response json
     * @throws ServiceException the service exception
     */
    public static String getResponseJson(RestfulResponse response) throws ServiceException {
        if (response == null) {
            throw new ServiceException("response is null");
        }

        // 通过判断返回状态码决定访问是否成功
        String result = response.getResponseContent();
        int status = response.getStatus();
        if (status / 100 != 2) {
            ServiceException exception = new ServiceException("response status : {" + status + "}");
            // 将状态码更新到异常信息中，为调用方判断服务状态
            exception.setHttpCode(status);
            throw exception;
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Response status : {}, content:{}", status, LogUtils.filterPassword(result));
        }
        return result;
    }
}
