/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent;

import com.huawei.i2000.dvaiagentservice.business.agent.dto.Constants;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.answer.LLMReport;
import com.huawei.i2000.dvaiagentservice.util.ContextUtils;

import java.util.Locale;
import java.util.Objects;

/**
 * 功能描述 ReportParser
 *
 * @since 2025-07-09
 */
public class ReportParser {
    /**
     * 将原始文本解析为 LLMReport
     *
     * @param report 中英文文报告
     * @return 包含解析结果的 LLMReport
     */
    public static LLMReport parse(String report) {
        LLMReport llmReport = new LLMReport();
        if (Objects.isNull(report)) {
            return llmReport;
        }
        Locale locale = ContextUtils.getContext().getLocale();
        if (locale.getLanguage().equalsIgnoreCase(Constants.LANG_ZH)) {
            llmReport = ReportParser.parseZhReport(report);
        } else {
            llmReport = ReportParser.parseEnReport(report);
        }
        return llmReport;
    }

    /**
     * 将原始文本解析为 LLMReport
     *
     * @param report 中文报告
     * @return 包含解析结果的 LLMReport
     */
    public static LLMReport parseZhReport(String report) {
        LLMReport llmReport = new LLMReport();

        llmReport.setReason(extractSection(report, Constants.REASON_ZH, Constants.MEASURES_ZH));
        llmReport.setMeasures(extractSection(report, Constants.MEASURES_ZH, Constants.CHECK_ITEMS_ZH));
        return llmReport;
    }

    /**
     * 将原始文本解析为 LLMReport
     *
     * @param report 英文报告
     * @return 包含解析结果的 LLMReport
     */
    public static LLMReport parseEnReport(String report) {
        LLMReport llmReport = new LLMReport();
        llmReport.setReason(extractSection(report, Constants.REASON_EN, Constants.MEASURES_EN));
        llmReport.setMeasures(extractSection(report, Constants.MEASURES_EN, Constants.CHECK_ITEMS_EN));
        return llmReport;
    }

    /**
     * 提取两关键字之间的内容，含起始关键字
     *
     * @param report 英文报告
     * @param start 开始
     * @param end 结束
     * @return 包含解析结果的 提取两关键字之间的内容
     */
    private static String extractSection(String report, String start, String end) {
        int startIdx = report.indexOf(start);
        return startIdx < 0 ? "" : report.substring(startIdx, findEndIndex(report, end, startIdx));
    }

    /**
     * 提取两关键字之间的内容，含起始关键字
     *
     * @param report 英文报告
     * @param end 结束
     * @param afterStart 开始
     * @return 包含解析结果的 提取两关键字之间的内容
     */
    private static int findEndIndex(String report, String end, int afterStart) {
        int endIdx = report.indexOf(end, afterStart);
        return endIdx < 0 ? report.length() : endIdx;
    }
}