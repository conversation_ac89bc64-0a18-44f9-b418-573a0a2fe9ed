/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent.chain;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.dvaiagentservice.business.agent.AgentUtils;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.ChainReq;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.conversation.ConversationReq;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.conversation.ConversationRsp;
import com.huawei.i2000.dvaiagentservice.util.rest.RestConstant;
import com.huawei.i2000.dvaiagentservice.util.rest.RestUtil;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;

/**
 * ConversationHandler
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public class ConversationHandler implements Handler {
    public static final OssLog LOGGER = OssLogFactory.getLogger(ConversationHandler.class);

    public static final String CONVERSATION = "/rest/naie/aiagentcore/v1/conversation";

    private Handler next;

    @Override
    public Handler setNext(Handler next) {
        this.next = next;
        return next;
    }

    @Override
    public ChainReq process(ChainReq chainReq) {
        // 处理逻辑
        LOGGER.info("Processing in ConversationHandler");
        ConversationRsp conversationRsp = conversation(chainReq.getQuestion());
        LOGGER.debug("Processing in ConversationHandler conversationId is {}", conversationRsp.getConversationId());
        chainReq.setConversationId(conversationRsp.getConversationId());
        // 调用下一个处理器
        if (next != null) {
            return next.process(chainReq);
        }
        return chainReq;
    }

    public static ConversationRsp conversation(String query) {
        RestfulParametes restfulParametes = AgentUtils.buildRestfulParametes();

        ConversationReq conversationReq = buildConversationReq(query);

        restfulParametes.setRawData(JSON.toJSONString(conversationReq));

        RestfulResponse post = new RestfulResponse();
        try {
            post = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, CONVERSATION, restfulParametes, null);
        } catch (ServiceException e) {
            LOGGER.info("conversation error is .", e);
        }
        if (post == null || post.getStatus() != RestConstant.STATUS_OK) {
            LOGGER.error("[ConversationHandler] conversation response error");
            return new ConversationRsp();
        }
        LOGGER.info("conversation RestfulResponse is {}", post.getResponseContent());
        ConversationRsp conversation = JSON.parseObject(post.getResponseContent(),
                new TypeReference<ConversationRsp>() {
                });
        return conversation;
    }

    private static ConversationReq buildConversationReq(String query) {
        ConversationReq conversationReq = new ConversationReq();
        conversationReq.setTitle(query);
        conversationReq.setDescription("create conversation");
        return conversationReq;
    }
}