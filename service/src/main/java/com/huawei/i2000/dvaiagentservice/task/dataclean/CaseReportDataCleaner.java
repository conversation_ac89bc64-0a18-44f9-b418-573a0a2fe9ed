/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.task.dataclean;

import org.springframework.stereotype.Service;

/**
 * 案例报告表清理
 *
 * <AUTHOR>
 * @since 2025/7/23
 */
@Service
public class CaseReportDataCleaner implements TableDataCleaner {

    @Override
    public int deleteByTime(long time) {
        return 0;
    }

    @Override
    public int deleteByCapacity() {
        return 0;
    }
}
