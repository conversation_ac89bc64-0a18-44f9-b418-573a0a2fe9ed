/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.cluster;

import com.huawei.i2000.dvaiagentservice.business.agent.dto.RecipeReq;

import lombok.Getter;

import org.apache.commons.lang3.ObjectUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Getter
public enum AlarmDiagnosisRecipe {
    ALARM_DIAGNOSIS_202610033("202610033", "alarm_diagnosis_with_workflow"),
    ALARM_DIAGNOSIS_202610034("202610034", "alarm_diagnosis_with_workflow"),
    ALARM_DIAGNOSIS_202610035("202610035", "alarm_diagnosis_with_workflow"),
    ALARM_DIAGNOSIS_202610036("202610036", "alarm_diagnosis_with_workflow"),
    ALARM_DIAGNOSIS_20210003("60210003", "alarm_diagnosis_with_workbench"),
    ALARM_DIAGNOSIS_20210027("60210027", "alarm_diagnosis_with_workbench"),
    ALARM_DIAGNOSIS_20210028("60210028", "alarm_diagnosis_with_workbench"),
    ALARM_DIAGNOSIS_20210029("60210029", "alarm_diagnosis_with_workbench"),
    ALARM_DIAGNOSIS_36("36", "alarm_diagnosis_with_workbench");

    private final String eventId;
    private final String recipeName;

    AlarmDiagnosisRecipe(String eventId, String recipeName) {
        this.eventId = eventId;
        this.recipeName = recipeName;
    }

    /**
     * 根据eventId获取对应的枚举实例
     *
     * @param eventId eventId
     * @return  AlarmDiagnosisRecipe
     */
    public static AlarmDiagnosisRecipe getByEventId(String eventId) {
        for (AlarmDiagnosisRecipe recipe : values()) {
            if (recipe.getEventId().equals(eventId)) {
                return recipe;
            }
        }
        return null;
    }

    /**
     * 检查eventId是否匹配任何配方场景
     *
     * @param event event
     * @return 是否匹配
     */
    public static boolean isRecipeScene(Event event) {
        return ObjectUtils.isNotEmpty(event) && getByEventId(event.getEventId()) != null;
    }

    /**
     * 创建recipe请求对象
     *
     * @param event event
     * @param model model
     * @return recipe
     */
    public static RecipeReq createRecipeReq(Event event, String model) {
        RecipeReq recipeReq = new RecipeReq();
        recipeReq.setQuestion(event.getEventId());

        Map<String, Object> recipeOptions = new HashMap<>();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("occurUtc", event.getOccurTime());
        params.put("moi", event.getMoi());
        params.put("alarmId", event.getEventId());
        params.put("csn", String.valueOf(event.getSourceCsn()));
        params.put("chunking_knowledge", "");

        // 获取对应的recipeName
        AlarmDiagnosisRecipe recipe = getByEventId(event.getEventId());
        if (recipe != null) {
            recipeOptions.put("recipeName", recipe.getRecipeName());
        }
        recipeOptions.put("model", model);
        recipeOptions.put("params", params);
        recipeReq.setOptions(recipeOptions);

        return recipeReq;
    }
}