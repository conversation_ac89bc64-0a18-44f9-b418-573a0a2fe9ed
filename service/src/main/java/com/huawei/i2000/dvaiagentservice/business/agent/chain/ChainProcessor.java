/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent.chain;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.ChainReq;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.RecipeReq;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.answer.DiagnosisReport;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.answer.LLMReport;

/**
 * ChainProcessor
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public class ChainProcessor {
    public static final OssLog LOGGER = OssLogFactory.getLogger(ChainProcessor.class);

    public static DiagnosisReport execute(RecipeReq recipeReq) {
        // 创建处理器链
        Handler conversationHandler = new ConversationHandler();
        Handler chatHandler = new ChatHandler();
        Handler chatQueryHandler = new ChatQueryHandler();
        Handler chatDetailHandler = new ChatDetailHandler();

        // 设置责任链
        conversationHandler.setNext(chatHandler)
                .setNext(chatQueryHandler)
                .setNext(chatDetailHandler);

        // 创建ChainReq对象
        ChainReq chainReq = ChainReq.builder()
                .question(recipeReq.getQuestion())
                .options(recipeReq.getOptions())
                .build();

        // 执行处理链
        ChainReq result = conversationHandler.process(chainReq);

        // 数据标准化
        DiagnosisReport diagnosisReport = LLMReport.convertReport(result.getReport());

        // 输出结果
        LOGGER.info("Processing in ChainProcessor end", result.getReport());
        return diagnosisReport;
    }
}