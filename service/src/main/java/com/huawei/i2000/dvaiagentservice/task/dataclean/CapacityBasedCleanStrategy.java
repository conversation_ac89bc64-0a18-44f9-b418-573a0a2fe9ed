/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.task.dataclean;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 容量清理策略类
 *
 * <AUTHOR>
 * @since 2025/7/23
 */
@Service
public class CapacityBasedCleanStrategy implements CleanStrategy {

    private final IncidentDataCleaner incidentDataCleaner;

    private final CaseReportDataCleaner caseReportDataCleaner;

    @Autowired
    public CapacityBasedCleanStrategy(IncidentDataCleaner incidentDataCleaner,
        CaseReportDataCleaner caseReportDataCleaner) {
        this.incidentDataCleaner = incidentDataCleaner;
        this.caseReportDataCleaner = caseReportDataCleaner;
    }

    @Override
    public void clean() {
        incidentDataCleaner.deleteByCapacity();
        caseReportDataCleaner.deleteByCapacity();
    }
}
