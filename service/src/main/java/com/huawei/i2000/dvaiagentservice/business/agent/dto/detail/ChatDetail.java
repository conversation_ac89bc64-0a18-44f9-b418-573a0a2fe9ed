/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent.dto.detail;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 功能描述 ChatDetail
 *
 * @since 2025-07-09
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatDetail {
    private String id;
    private String conversationId;
    private String question;
    private int feedback;
    private boolean mark;
    private List<Answer> answers;
    private Long askTime;
    private Long questionTime;
    private Long costTime;
}
