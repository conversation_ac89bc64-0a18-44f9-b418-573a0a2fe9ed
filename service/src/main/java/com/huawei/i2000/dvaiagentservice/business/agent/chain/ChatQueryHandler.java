/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent.chain;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.dvaiagentservice.business.agent.AgentUtils;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.ChainReq;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.query.ChatQueryRsp;
import com.huawei.i2000.dvaiagentservice.util.rest.RestConstant;
import com.huawei.i2000.dvaiagentservice.util.rest.RestUtil;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;

/**
 * ChatQueryHandler
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public class ChatQueryHandler implements Handler {
    public static final OssLog LOGGER = OssLogFactory.getLogger(ChatQueryHandler.class);
    public static final String CHAT_QUERY = "/rest/naie/aiagentcore/v1/chat/query";
    private Handler next;

    @Override
    public Handler setNext(Handler next) {
        this.next = next;
        return next;
    }

    @Override
    public ChainReq process(ChainReq chainReq) {
        // 处理逻辑
        LOGGER.info("Processing in ChatQueryHandler");
        String chatId = chatQuery(chainReq.getConversationId());
        LOGGER.debug("Processing in ChatQueryHandler chatId is {}",chatId);
        chainReq.setChatId(chatId);
        // 调用下一个处理器
        if (next != null) {
            return next.process(chainReq);
        }
        return chainReq;
    }

    String chatQuery(String conversationId) {
        RestfulParametes restfulParametes = AgentUtils.buildRestfulParametes();

        String url = CHAT_QUERY;
        url = RestUtil.addUrlParam(url, "conversationId", conversationId);
        url = RestUtil.addUrlParam(url, "pageNum", "0");

        try {
            RestfulResponse get = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_GET, url, restfulParametes, null);
            if (get == null || get.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("[ChatQueryHandler] chatQuery response error");
                return null;
            }
            ChatQueryRsp chatQueryRsp = JSON.parseObject(get.getResponseContent(),
                    new TypeReference<ChatQueryRsp>() {
                    });
            return chatQueryRsp.getRecords().get(0).getChatId();
        } catch (ServiceException e) {
            LOGGER.error("[ChatQueryHandler] chatQuery with exception {} ", e.getMessage());
            return null;
        }
    }
}