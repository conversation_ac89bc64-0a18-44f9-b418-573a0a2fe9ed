/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent.chain;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.dvaiagentservice.business.agent.AgentUtils;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.ChainReq;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.chat.ChatReq;
import com.huawei.i2000.dvaiagentservice.business.agent.enumeration.ChatTypeEnum;
import com.huawei.i2000.dvaiagentservice.util.rest.RestConstant;
import com.huawei.i2000.dvaiagentservice.util.rest.RestUtil;

import com.alibaba.fastjson2.JSON;

import org.eclipse.jetty.http.HttpStatus;

/**
 * ChatHandler
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public class ChatHandler implements Handler {
    public static final OssLog LOGGER = OssLogFactory.getLogger(ChatHandler.class);
    public static final String CHAT = "/rest/naie/aiagentcore/v1/chat";
    private Handler next;

    @Override
    public Handler setNext(Handler next) {
        this.next = next;
        return next;
    }

    @Override
    public ChainReq process(ChainReq chainReq) {
        // 处理逻辑
        LOGGER.info("Processing in ChatHandler");
        chat(chainReq);
        // 调用下一个处理器
        if (next != null) {
            return next.process(chainReq);
        }
        return chainReq;
    }

    public static void chat(ChainReq chainReq) {
        LOGGER.info("DVChatSseClient execute start");

        RestfulParametes restfulParametes = AgentUtils.buildRestfulParametes();
        restfulParametes.putHttpContextHeader("SUPPORT_STREAM_CONTENT_FOR_SDK", "true");

        ChatReq chatReq = buildChatReq(chainReq);

        restfulParametes.setRawData(JSON.toJSONString(chatReq));

        RestfulResponse post = null;
        try {
            post = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, CHAT, restfulParametes, null);
        } catch (ServiceException e) {
            LOGGER.info("DVChatSseClient sendStreamResult, error is .", e);
        }
        if (post == null || !HttpStatus.isSuccess(post.getStatus())) {
            LOGGER.info("ChatHandler error");
            return;
        }
        LOGGER.info("ChatHandler post Status is {}", post.getStatus());
    }

    private static ChatReq buildChatReq(ChainReq chainReq) {
        ChatReq chatReq = new ChatReq();
        chatReq.setConversationId(chainReq.getConversationId());
        chatReq.setQuestion(chainReq.getQuestion());
        chatReq.setStream(false);
        chatReq.setType(ChatTypeEnum.SPECIFY_RECIPE.toString());
        chatReq.setOptions(chainReq.getOptions());
        return chatReq;
    }
}