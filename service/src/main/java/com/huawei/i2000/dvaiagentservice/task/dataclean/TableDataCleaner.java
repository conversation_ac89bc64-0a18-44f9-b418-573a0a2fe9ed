/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.task.dataclean;

/**
 * 数据表清理接口
 *
 * <AUTHOR>
 * @since 2025/7/23
 */
public interface TableDataCleaner {
    /**
     * 根据时间删除记录
     *
     * @param time 过期时间
     * @return 删除的记录数
     */
    int deleteByTime(long time);

    /**
     * 根据记录条数删除
     *
     * @return 删除的记录数
     */
    int deleteByCapacity();
}
