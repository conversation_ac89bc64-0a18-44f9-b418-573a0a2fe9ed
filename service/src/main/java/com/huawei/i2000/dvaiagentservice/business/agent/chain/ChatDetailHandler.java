/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent.chain;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.dvaiagentservice.business.agent.AgentUtils;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.ChainReq;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.detail.ChatDetail;
import com.huawei.i2000.dvaiagentservice.util.rest.RestConstant;
import com.huawei.i2000.dvaiagentservice.util.rest.RestUtil;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;

import org.apache.commons.lang3.ObjectUtils;

/**
 * ChatDetailHandler
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public class ChatDetailHandler implements Handler {
    public static final OssLog LOGGER = OssLogFactory.getLogger(ChatDetailHandler.class);
    public static final String CHAT_DETAIL = "/rest/naie/aiagentcore/v1/chat/detail";
    private Handler next;

    private static final int MAX_RETRIES = 60;

    // 等待时间(毫秒)
    private static final long WAIT_TIMES = 5000L;

    @Override
    public Handler setNext(Handler next) {
        this.next = next;
        return next;
    }

    @Override
    public ChainReq process(ChainReq chainReq) {
        // 处理逻辑
        LOGGER.info("Processing in ChatDetailHandler");
        ChatDetail report = chatDetail(chainReq.getChatId());
        chainReq.setReport(report);
        // 调用下一个处理器
        if (next != null) {
            return next.process(chainReq);
        }
        return chainReq;
    }

    ChatDetail chatDetail(String chatId) {
        int retryCount = 0;
        RestfulParametes restfulParametes = AgentUtils.buildRestfulParametes();
        String url = CHAT_DETAIL + '/' + chatId;
        while (retryCount < MAX_RETRIES) {
            try {
                RestfulResponse get = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_GET, url, restfulParametes,
                    null);
                if (get == null || get.getStatus() != RestConstant.STATUS_OK) {
                    LOGGER.error("[ChatDetailHandler] chatQuery response error");
                    return null;
                }
                ChatDetail chatDetail = JSON.parseObject(get.getResponseContent(), new TypeReference<ChatDetail>() {});
                LOGGER.debug("[ChatDetailHandler] {}", get.getResponseContent());
                // 检查answers是否为null
                if (ObjectUtils.isNotEmpty(chatDetail.getAnswers()) && chatDetail.getAnswers().get(chatDetail.getAnswers().size() - 1).getStatus().equals("END")) {
                    return chatDetail; // 直接返回有效结果
                } else {
                    LOGGER.warn("[ChatDetailHandler] answers is null, retry count: {}", retryCount);
                }
            } catch (ServiceException e) {
                LOGGER.error("[ChatDetailHandler] chatQuery with exception {} ", e.getMessage());
            }
            retryCount++;
            if (retryCount < MAX_RETRIES) {
                try {
                    Thread.sleep(WAIT_TIMES);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    LOGGER.error("[ChatDetailHandler] retry wait interrupted", e);
                    break;
                }
            }
        }
        LOGGER.error("[ChatDetailHandler] all {} attempts failed for chatId: {}", MAX_RETRIES, chatId);
        return null;
    }
}