/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent.dto.detail;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 功能描述 Answer
 *
 * @since 2025-07-09
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Answer {
    private String id;
    private String chatId;
    private String level;
    private String type;
    private String status;
    private String data; // 支持多态结构
    private String recipeName;
    private Long costTime;
    private Long answerTime;
    private Object AIGC;
}
