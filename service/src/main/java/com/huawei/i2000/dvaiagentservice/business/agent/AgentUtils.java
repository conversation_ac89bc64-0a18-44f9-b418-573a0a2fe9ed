/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent;

import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

/**
 * Agent工具类
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
public class AgentUtils {
    /**
     * build Restful Parametes
     *
     * @return RestfulParametes
     */
    public static RestfulParametes buildRestfulParametes() {
        RestfulParametes restfulParametes = new RestfulParametes();
        restfulParametes.putHttpContextHeader("Agent-User-ID", "admin");
        restfulParametes.putHttpContextHeader("Agent-Tenant-ID", 0);
        restfulParametes.putHttpContextHeader("X-Agent-ID", "DVCopilot");
        restfulParametes.putHttpContextHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        return restfulParametes;
    }
}
