/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent.dto;

import com.huawei.i2000.dvaiagentservice.business.agent.dto.detail.ChatDetail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * 功能描述 发起问答请求体
 *
 * @since 2025-07-09
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChainReq {
    private String conversationId;

    private String chatId; // 可以为空，APPEND/RETRY/WAKE场景要求非空

    private String question;

    private Map<String, Object> options; // 用户介入场景传入，用于recipe继续执行

    private ChatDetail report;
}
