/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent.dto.answer;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.huawei.i2000.dvaiagentservice.business.agent.ReportParser;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.Constants;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.detail.Answer;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.detail.ChatDetail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 功能描述 LLMReport
 *
 * @since 2025-07-09
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LLMReport {
    private String reason;
    private String measures;
    private List<CheckItem> checkItem;
    private String rowShowData;

    public static DiagnosisReport convertReport(ChatDetail chatDetail) {
        DiagnosisReport diagnosisReport = new DiagnosisReport();
        LLMReport llmReport = new LLMReport();
        String report = "";
        List<Answer> answers = chatDetail.getAnswers()
                .stream()
                .filter(answer -> Objects.equals(answer.getLevel(), "ANSWER"))
                .collect(Collectors.toList());

        if (answers.isEmpty()) {
            return diagnosisReport;
        }
        // 如果是使用dv workbench 得到的检查项，格式不同，提前获取，并删掉answers中这一项
        handleWorkbenchCheckList(llmReport, answers);

        for (Answer answer : answers) {
            if (answer.getRecipeName().contains(Constants.ALARM_DIAGNOSIS_RECIPE_NAME_PREFIX)) {
                PiuData piuData = JSON.parseObject(answer.getData(), new TypeReference<PiuData>() {
                });
                if (Objects.nonNull(piuData) && Objects.nonNull(piuData.getData())) {
                    llmReport = piuData.getData();
                }
            }

            if (answer.getRecipeName().equals(Constants.LLM_RECIPE_NAME)) {
                report = answer.getData();
                LLMReport tempReport = ReportParser.parse(report);
                llmReport.setReason(tempReport.getReason());
                llmReport.setMeasures(tempReport.getMeasures());
            }
        }
        diagnosisReport.setLlmReport(llmReport);
        diagnosisReport.setTextReport(report);
        return diagnosisReport;
    }

    private static void handleWorkbenchCheckList(LLMReport llmReport, List<Answer> answers) {
        List<CheckItem> checkItems = new ArrayList<>();
        JSONObject rowData = null;
        Iterator<Answer> iterator = answers.iterator();
        while (iterator.hasNext()) {
            Answer answer = iterator.next();
            if (Objects.equals(answer.getRecipeName(), Constants.WORKBENCH_DIAGNOSIS_RECIPE_NAME)) {
                rowData = JSONObject.parse(answer.getData());
                iterator.remove();
            }
        }
        if (rowData == null) {
            return;
        }
        String rowShowData = JSONArray.toJSONString(rowData.get("data"));
        llmReport.setRowShowData(rowShowData);
        JSONArray data = JSONArray.parse(rowShowData);
        List<Object> checLists = data.stream().filter(item -> Objects.equals(((JSONObject) item).get("displayType"), "checkList")).collect(Collectors.toList());
        if (checLists.isEmpty()) {
            return;
        }
        JSONArray jsonArray = (JSONArray) ((JSONObject) checLists.get(0)).get("content");
        for (Object item : jsonArray) {
            JSONObject jsonObject = (JSONObject) item;
            CheckItem checkItem = new CheckItem();
            checkItem.setStatus((int) jsonObject.get("status"));
            checkItem.setDetails(JSONArray.toJSONString(jsonObject.get("content")));
            checkItem.setName((String) jsonObject.get("name"));
            checkItems.add(checkItem);
        }
        llmReport.setCheckItem(checkItems);
    }
}
