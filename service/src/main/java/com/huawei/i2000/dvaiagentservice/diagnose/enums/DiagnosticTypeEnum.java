/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.diagnose.enums;

import lombok.Getter;

/**
 * 功能描述
 * 诊断类型枚举
 *
 * <AUTHOR>
 * @since 2025/5/6
 */
@Getter
public enum DiagnosticTypeEnum {

    DIAGNOSTIC_TYPE_PLACEHOLDER(0, "占位符"),

    AUTO(1, "自动诊断"),

    MANUAL(2, "人工诊断");

    private final int code;

    private final String description;

    // 增强可读性
    DiagnosticTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据给定的代码查找对应的诊断类型枚举值
     *
     * @param code 需要查找的代码
     * @return 对应的诊断类型枚举值
     * @throws IllegalArgumentException 如果没有找到对应的诊断类型枚举值，将抛出此异常
     */
    public static DiagnosticTypeEnum fromCode(int code) {
        for (DiagnosticTypeEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid diagnostic type: " + code);
    }

    /**
     * 通过枚举名称解析枚举的工厂方法
     *
     * @param name 枚举名称
     * @return 对应的枚举实例
     * @throws IllegalArgumentException 当传入非法枚举名称时抛出
     */
    public static DiagnosticTypeEnum fromName(String name) {
        for (DiagnosticTypeEnum type : values()) {
            if (type.name().equals(name)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid diagnostic type name: " + name);
    }
}
