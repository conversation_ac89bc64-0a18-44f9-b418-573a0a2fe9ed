/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.exception;

/**
 * 功能描述:带异常的Function
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@FunctionalInterface
public interface FunctionWithException<T, R> {
    /**
     * Applies this function to the given argument.
     *
     * @param t 参数
     * @return 返回结果
     * @throws Exception 内部异常
     */
    R apply(T t) throws Exception;
}
