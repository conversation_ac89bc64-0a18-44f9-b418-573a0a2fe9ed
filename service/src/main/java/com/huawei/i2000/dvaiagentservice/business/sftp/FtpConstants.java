/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.sftp;

/**
 * FtpConstants
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public final class FtpConstants {
    /**
     * 服务器的用户名
     */
    public static final String NAME = System.getenv("DV_SFTP_USER");

    /**
     * ftp服务器的端口号
     */
    public static final String PORT = System.getenv("DV_SFTP_PORT");

    /**
     * 行状态，说明：目前UOA只支持RowStatus = 4 (添加)
     */
    public static final String ROW_STATUS = "4";

    /**
     * 行状态，说明：目前UOA只支持RowStatus = 6 (删除)
     */
    public static final String ROW_STATUS_DELETE = "6";

    /**
     * ftp服务器模式
     */
    public static final String MODE = "2";

    /**
     * 当sftp操作被终止的重试次数
     */
    public static final int COLLECT_UOA_SFTP_RETRYTIME = 10;

    /**
     * 当sftp操作被终止的重试休眠时间
     */
    public static final int COLLECT_UOA_SFTP_FAIL_SLEEP_TIME = 100;

    /**
     * 当sftp操作SECURE ALGORITHMS
     */
    public static final boolean COLLECT_SFTP_CLIENT_SECURE_ALGORITHMS = true;

    private FtpConstants() {
    }
}
