/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang3.tuple.Pair;

import java.io.IOException;

/**
 * Pair反序列化
 *
 * <AUTHOR>
 * @since 2021-08-23
 */
public class PairDeserializer extends JsonDeserializer<Pair<?, ?>> {
    @Override
    public Pair<?, ?> deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        parser.nextToken();
        String keyName = parser.getCurrentName();
        parser.nextToken();

        Pair<?, ?> pair = Pair.of(keyName, parser.getValueAsString());
        parser.nextToken();
        return pair;
    }
}