/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.task.dataclean;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 自定义调度线程池
 *
 * <AUTHOR>
 * @since 2025/7/23
 */
@Configuration
public class SchedulingConfig implements SchedulingConfigurer {

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setTaskScheduler(myTaskScheduler());
    }

    /**
     * 自定义调度线程池Bean
     *
     * @return TaskScheduler
     */
    @Bean(destroyMethod = "shutdown")
    public ThreadPoolTaskScheduler myTaskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();

        scheduler.setPoolSize(5);

        scheduler.setThreadNamePrefix("Scheduled-Task-Pool-");

        scheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());

        scheduler.setWaitForTasksToCompleteOnShutdown(true);

        scheduler.setAwaitTerminationSeconds(60);

        return scheduler;
    }
}
