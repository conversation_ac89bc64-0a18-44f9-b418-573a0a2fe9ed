/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.task.dataclean;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 定时任务调度器
 *
 * <AUTHOR>
 * @since 2025/7/23
 */
@Service
@EnableScheduling
public class DataCleanScheduler {
    private static final OssLog LOGGER = OssLogFactory.getLogger(DataCleanScheduler.class);

    private final TimeBasedCleanStrategy timeBasedCleanStrategy;

    private final CapacityBasedCleanStrategy capacityBasedCleanStrategy;

    @Autowired
    public DataCleanScheduler(TimeBasedCleanStrategy timeBasedCleanStrategy,
        CapacityBasedCleanStrategy capacityBasedCleanStrategy) {
        this.timeBasedCleanStrategy = timeBasedCleanStrategy;
        this.capacityBasedCleanStrategy = capacityBasedCleanStrategy;
    }

    @Scheduled(cron = "0 0 1 * * ?")
    public void scheduleTimeBasedCleansing() {
        try {
            timeBasedCleanStrategy.clean();
        } catch (Exception e) {
            LOGGER.error("ScheduleTimeBasedCleansing exception is {}", e);
        }
    }
}
