/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.sftp;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.cbb.security.cipher.CipherUtil;
import com.huawei.i2000.dvaiagentservice.util.ThreadUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;

import java.util.Map;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * sftp账号缓存类
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public class SftpAccountCache {
    private static final OssLog LOGGER = OssLogFactory.getLog(SftpAccountCache.class);

    private static final String SFTP_PWD = "sftpPwd";

    private static final String SFTP_IPV4 = "sftpIpv4";

    private static final String SFTP_IPV6 = "sftpIpv6";

    private static String sftpPwd = "";

    private static String sftpIpv4 = "";

    private static String sftpIpv6 = "";

    private static final int WAIT_INTERVAL = 60;

    private static volatile boolean initFlag;

    static {
        updateSftpInfo();
        ScheduledThreadPoolExecutor scheduler = new ScheduledThreadPoolExecutor(1,
            new BasicThreadFactory.Builder().namingPattern("[collect]update-sftp-%d").daemon(true).build());
        scheduler.scheduleAtFixedRate(SftpAccountCache::updateSftpInfo, WAIT_INTERVAL, WAIT_INTERVAL, TimeUnit.SECONDS);
    }

    private SftpAccountCache() {
    }

    private static void updateSftpInfo() {
        try {
            Map<String, String> sftpInfo = SftpRestProxy.getSftpPwd();
            String sftpPwdFromRsp = sftpInfo.get(SFTP_PWD);
            if (StringUtils.isEmpty(sftpPwdFromRsp)) {
                LOGGER.error("sftp pwd is empty");
            } else {
                sftpPwd = sftpPwdFromRsp;
            }
            String sftpIpv4FromRsp = sftpInfo.get(SFTP_IPV4);
            if (StringUtils.isEmpty(sftpIpv4FromRsp)) {
                LOGGER.error("sftp Ipv4 is empty");
            } else {
                sftpIpv4 = sftpIpv4FromRsp;
            }
            String sftpIpv6FromRsp = sftpInfo.get(SFTP_IPV6);
            if (StringUtils.isEmpty(sftpIpv6FromRsp)) {
                LOGGER.error("sftpIpv6 is empty");
            } else {
                sftpIpv6 = sftpIpv6FromRsp;
            }
            initFlag = true;
        } catch (ServiceException e) {
            LOGGER.error("get sftp pwd failed, ", e);
        }
    }

    /**
     * sftp密码
     *
     * @return sftp密码
     */
    public static String getSftpPwd() {
        try {
            waitForInit();
            return new String(CipherUtil.decrypt(sftpPwd));
        } catch (Exception e) {
            LOGGER.error("decode sftp pwd error", e);
        }
        return "";
    }

    /**
     * 获取sftpIpv4
     *
     * @return sftpIpv4
     */
    public static String getSftpIpv4() {
        waitForInit();
        return sftpIpv4;
    }

    /**
     * 获取sftpIpv6
     *
     * @return sftpIpv6
     */
    public static String getSftpIpv6() {
        waitForInit();
        return sftpIpv6;
    }

    private static void waitForInit() {
        while (!initFlag) {
            LOGGER.warn("wait sftp info config");
            ThreadUtil.sleepSec(5);
        }
    }

}
