/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvtopowebsite.impl;

import static com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant.DISTRIBUTE_INDICATOR_RANGE;

import com.huawei.bsp.as.util.Pair;
import com.huawei.bsp.biz.util.HttpUtil;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.cbb.sm.model.LogResult;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.pm.PmIndicatorInstanceService;
import com.huawei.i2000.dvtoposervice.business.pm.PmRequestService;
import com.huawei.i2000.dvtoposervice.business.pm.entity.DeletedHistoryData;
import com.huawei.i2000.dvtoposervice.business.pm.entity.DeletedHistoryResult;
import com.huawei.i2000.dvtoposervice.business.pm.entity.DeletedHistoryValues;
import com.huawei.i2000.dvtoposervice.business.pm.entity.MeasObjectInfo;
import com.huawei.i2000.dvtoposervice.business.pm.entity.QueryDeletedHistoryData;
import com.huawei.i2000.dvtoposervice.business.service.BusinessTopoOverViewServiceImpl;
import com.huawei.i2000.dvtoposervice.business.service.bean.TopNViewMeasPMData;
import com.huawei.i2000.dvtoposervice.business.service.bean.TopNViewMeasTypeValue;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.ModelIdGen;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.TopoImportSolutionAdapter;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessCommonModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessIndicatorDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceRelationDao;
import com.huawei.i2000.dvtoposervice.model.Business;
import com.huawei.i2000.dvtoposervice.model.BusinessIndicator;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.model.HomePageConfigurationResponse;
import com.huawei.i2000.dvtoposervice.model.Indicator;
import com.huawei.i2000.dvtoposervice.model.IndicatorDisplayResults;
import com.huawei.i2000.dvtoposervice.model.OverViewGrid;
import com.huawei.i2000.dvtoposervice.model.OverViewGridForMm;
import com.huawei.i2000.dvtoposervice.model.PerformanceIndexValue;
import com.huawei.i2000.dvtoposervice.model.SiteDistribution;
import com.huawei.i2000.dvtoposervice.model.SiteHistoryResults;
import com.huawei.i2000.dvtoposervice.model.Solution;
import com.huawei.i2000.dvtoposervice.util.AuthUtils;
import com.huawei.i2000.dvtoposervice.util.ConfigurationUtil;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.EamUtil;
import com.huawei.i2000.dvtoposervice.util.LogUtil;
import com.huawei.i2000.dvtoposervice.util.PmDataHandleUtil;
import com.huawei.i2000.dvtopowebsite.delegate.BusinessTopoWebsiteDelegate;
import com.huawei.i2000.dvtopowebsite.model.BusinessEditParam;
import com.huawei.i2000.dvtopowebsite.model.BusinessTopNConfig;
import com.huawei.i2000.dvtopowebsite.model.HomePageConfigurationParam;
import com.huawei.i2000.dvtopowebsite.model.IndicatorDataForMm;
import com.huawei.i2000.dvtopowebsite.model.IndicatorDistribution;
import com.huawei.i2000.dvtopowebsite.model.IndicatorDistributionForMm;
import com.huawei.i2000.dvtopowebsite.model.IndicatorDistributionResultForMm;
import com.huawei.i2000.dvtopowebsite.model.OverViewIndicatorParam;
import com.huawei.i2000.dvtopowebsite.model.OverViewIndicatorParamForMm;
import com.huawei.i2000.dvtopowebsite.model.OverviewEditParamForMm;
import com.huawei.i2000.dvtopowebsite.model.OverviewGridParam;
import com.huawei.i2000.dvtopowebsite.model.ResponseEntity;
import com.huawei.i2000.dvtopowebsite.model.TopIndicatorData;
import com.huawei.i2000.dvtopowebsite.model.TopIndicatorDistribution;
import com.huawei.i2000.dvtopowebsite.model.TopIndicatorName;
import com.huawei.i2000.dvtopowebsite.util.RestConstant;
import com.huawei.i2000.dvtopowebsite.util.RestUtil;
import com.huawei.oms.eam.mo.ManagedObject;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;

import lombok.Getter;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * BusinessTopoWebsiteDelegateImpl
 *
 * @since 2024-11-30 18:43:34
 */
@Component
public class BusinessTopoWebsiteDelegateImpl implements BusinessTopoWebsiteDelegate, ModelIdGen {

    private static final String COLON = ":";

    private static final OssLog LOGGER = OssLogFactory.getLogger(BusinessTopoWebsiteDelegateImpl.class);

    private static final String QUERY_OVERVIEW_INDICATOR_DATA
        = "/rest/dvtoposervice/v1/business/topo/queryindicatorhistory";

    private static final String QUERY_OVERVIEW_INDICATOR_DATA_DISTRIBUTION
        = "/rest/dvtoposervice/v1/business/topo/queryindicatordistribution";

    private static final String QUERY_OVERVIEW_GRID = "/rest/dvtoposervice/v1/business/topo/queryoverviewgrid";

    private static final String HOME_PAGE_CONFIG = "/rest/dvtoposervice/v1/business/topo/homePageConfiguration";

    private static final String INDICATOR_RANGE = "indicatorrange";

    private static final String GSU = "gsu";

    private static final String RSU = "rsu";

    private static final String CSU = "csu";

    private static final String DISPLAY_NAME_SPLIT = ",";

    private static final int TOPN_INDICATOR_TYPE = 3;

    private static final int MAIN_INDICATOR_TYPE = 1;

    private static final int COMMON_INDICATOR_TYPE = 0;

    private static final Integer LINK_INDICATOR_TYPE = 10;

    private static final String LINE = "_";

    private static final String CSU_00 = "csu00";

    private final Lock businessEditLock = new ReentrantLock();

    private static final String DITIAL_SUFFIX = "; ";

    @Autowired
    PmIndicatorInstanceService pmIndicatorInstanceService;

    @Autowired
    private PmRequestService pmRequestService;

    @Autowired
    BusinessTopoOverViewServiceImpl topoOverViewService;

    @Autowired
    private BusinessInstanceModelDao insModelDao;

    @Autowired
    private BusinessIndicatorDao indicatorDao;

    @Autowired
    private BusinessInstanceRelationDao relationDao;

    @Autowired
    private BusinessCommonModelDao businessCommonModelDao;

    @Override
    public ResponseEntity querySolution(HttpContext context, Long timestamp) throws ServiceException {
        LOGGER.debug("entry querySolution start.");
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            List<Solution> result = topoOverViewService.querySolution(timestamp);
            responseEntity.setData(result);
            responseEntity.setResultCode(RestConstant.SUCCESS_CODE);
        } catch (Exception e) {
            LOGGER.error("querySolution failed, error is ", e);
            responseEntity.setResultCode(RestConstant.FAILURE_CODE);
        }
        LOGGER.debug("entry querySolution end.");
        return responseEntity;
    }

    @Override
    public ResponseEntity queryBusiness(HttpContext context, Integer solutionId, Long timestamp)
        throws ServiceException {
        LOGGER.debug("entry queryBusiness start.");
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            List<Business> result = topoOverViewService.queryBusinessList(solutionId, timestamp);
            responseEntity.setData(result);
            responseEntity.setResultCode(RestConstant.SUCCESS_CODE);
        } catch (Exception e) {
            LOGGER.error("queryBusiness failed, error is ", e);
            responseEntity.setResultCode(RestConstant.FAILURE_CODE);
        }
        LOGGER.debug("entry queryBusiness end.");
        return responseEntity;
    }

    @Override
    public ResponseEntity queryOverViewIndicatorData(HttpContext context,
        OverViewIndicatorParam queryOverViewIndicatorData) throws ServiceException {
        LOGGER.info("entry queryOverViewIndicatorData start.");
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            RestfulParametes restfulParametes = new RestfulParametes();
            restfulParametes.setRawData(JSON.toJSONString(queryOverViewIndicatorData));
            RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
                QUERY_OVERVIEW_INDICATOR_DATA, restfulParametes, null);
            if (getResponseEntity(responseEntity, response) != null) {
                return responseEntity;
            }
            IndicatorDisplayResults result = JSON.parseObject(response.getResponseContent(),
                IndicatorDisplayResults.class, JSONReader.Feature.SupportSmartMatch);
            setResponseEntityData(responseEntity, response, result);
        } catch (ServiceException e) {
            LOGGER.error("queryOverViewIndicatorData failed, error is ", e);
        }
        LOGGER.info("entry queryOverViewIndicatorData end.");
        return responseEntity;
    }

    @Override
    public ResponseEntity queryIndicatorDistribution(HttpContext context,
        IndicatorDistribution queryIndicatorDistribution) throws ServiceException {
        LOGGER.info("entry queryIndicatorDistribution start.");
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            RestfulParametes restfulParametes = new RestfulParametes();
            restfulParametes.setRawData(JSON.toJSONString(queryIndicatorDistribution));
            RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
                QUERY_OVERVIEW_INDICATOR_DATA_DISTRIBUTION, restfulParametes, null);
            if (getResponseEntity(responseEntity, response) != null) {
                return responseEntity;
            }
            SiteDistribution result = JSON.parseObject(response.getResponseContent(), SiteDistribution.class,
                JSONReader.Feature.SupportSmartMatch);
            setResponseEntityData(responseEntity, response, result);
        } catch (ServiceException e) {
            LOGGER.error("queryIndicatorDistribution failed, error is ", e);
        }
        LOGGER.info("entry queryIndicatorDistribution end.");
        return responseEntity;
    }

    @Override
    public ResponseEntity queryIndicatorHistoryForMm(HttpContext context,
        OverViewIndicatorParamForMm param) throws ServiceException {
        if (param.getCurrentTime() == null) {
            param.setCurrentTime(0L);
        }
        ResponseEntity responseEntity = new ResponseEntity();
        // 不传modelType时查的是tips曲线
        if (param.getModelType() == null) {
            IndicatorDisplayResults result = queryTipsIndicatorHistory(param);
            responseEntity.setData(result);
            responseEntity.setResultCode(RestConstant.SUCCESS_CODE);
            responseEntity.setResultMessage(StringUtil.valueOf(RestConstant.STATUS_OK));
            return responseEntity;
        }
        // 传modelType时查的是右侧面板曲线，根据modelType分类查询
        IndicatorDisplayResults result;
        switch (param.getModelType()) {
            case BusinessTopoConstant.BUSINESS_TYPE_ID:
                result = queryBusinessOrChannelIndicatorData(param, BusinessTopoConstant.BUSINESS_TYPE_ID);
                break;
            case BusinessTopoConstant.CHANNEL_TYPE_ID:
                result = queryBusinessOrChannelIndicatorData(param, BusinessTopoConstant.CHANNEL_TYPE_ID);
                break;
            case BusinessTopoConstant.STRIPE_TYPE_ID:
                result = queryStripeIndicatorData(param);
                break;
            default:
                LOGGER.error("unknown modelType query indicator history");
                throw new ServiceException("unknown modelType");
        }
        responseEntity.setData(result);
        responseEntity.setResultCode(RestConstant.SUCCESS_CODE);
        responseEntity.setResultMessage(StringUtil.valueOf(RestConstant.STATUS_OK));
        return responseEntity;
    }

    @Override
    public ResponseEntity queryTopIndicator(HttpContext context, TopIndicatorDistribution queryTopIndicatorDistribution)
        throws ServiceException {
        LOGGER.info("queryTopIndicator start.");
        long timeStamp = queryTopIndicatorDistribution.getCurrentTime() == null
            ? 0
            : queryTopIndicatorDistribution.getCurrentTime();
        // 塞回入参，后续无需处理null值
        queryTopIndicatorDistribution.setCurrentTime(timeStamp);
        int solutionId = queryTopIndicatorDistribution.getSolutionId();
        if (queryTopIndicatorDistribution.getModelType() == null) {
            queryTopIndicatorDistribution.setModelType(BusinessTopoConstant.BUSINESS_TYPE_ID);
        }

        Map<Integer, List<Integer>> modelInsMap = getModelInsMap(timeStamp, solutionId);
        TopIndicatorData topIndicatorData = new TopIndicatorData();

        // 根据modelType分类查询
        switch (queryTopIndicatorDistribution.getModelType()) {
            case BusinessTopoConstant.BUSINESS_TYPE_ID:
                topIndicatorData.setModelType(BusinessTopoConstant.BUSINESS_TYPE_ID);
                setTopIndicatorData(topIndicatorData, modelInsMap.get(BusinessTopoConstant.BUSINESS_TYPE_ID),
                    queryTopIndicatorDistribution, null);
                break;
            case BusinessTopoConstant.CHANNEL_TYPE_ID:
                topIndicatorData.setModelType(BusinessTopoConstant.CHANNEL_TYPE_ID);
                setTopIndicatorData(topIndicatorData, modelInsMap.get(BusinessTopoConstant.CHANNEL_TYPE_ID),
                    queryTopIndicatorDistribution, null);
                break;
            case BusinessTopoConstant.STRIPE_TYPE_ID:
                // 根据前台传参区分三种场景，总览试图选中不选中，下钻条带视图
                topIndicatorData.setModelType(BusinessTopoConstant.STRIPE_TYPE_ID);
                List<Integer> stripeIdList = queryTopIndicatorDistribution.getStripeId() == null
                    ? modelInsMap.get(BusinessTopoConstant.STRIPE_TYPE_ID)
                    : Collections.singletonList(queryTopIndicatorDistribution.getStripeId());
                List<Integer> insIdList = relationDao.querySonByInsId(stripeIdList, timeStamp);
                setTopIndicatorData(topIndicatorData, insIdList, queryTopIndicatorDistribution, stripeIdList);
                //  只有系统业务需要塞指标，三种场景三种指标数据
                topIndicatorData.setIndicatorList(
                    getIndicatorList(queryTopIndicatorDistribution, stripeIdList, insIdList));
        }

        ResponseEntity responseEntity = new ResponseEntity();
        responseEntity.setData(topIndicatorData);
        responseEntity.setResultCode(RestConstant.SUCCESS_CODE);
        responseEntity.setResultMessage(StringUtil.valueOf(RestConstant.STATUS_OK));
        LOGGER.info("queryTopIndicator end.");
        return responseEntity;
    }

    private List<BusinessIndicator> getIndicatorList(TopIndicatorDistribution queryTopIndicatorDistribution,
        List<Integer> stripeIdList, List<Integer> insIdList) {
        return queryTopIndicatorDistribution.getStripeUnit() == null ? indicatorDao.queryHisIndInsIdList(
            queryTopIndicatorDistribution.getStripeId() == null ? stripeIdList : insIdList,
            queryTopIndicatorDistribution.getCurrentTime())
            // 下钻条带视图取第三层模型扩展属性里的指标列表
            : insModelDao.queryInstanceListByIdList(Collections.singletonList(insIdList.get(0)),
                    queryTopIndicatorDistribution.getCurrentTime())
                .stream()
                .map(ins -> pmIndicatorInstanceService.fillIndexNameAndUnit(JSON.parseArray(
                    ins.getStaticExtentAttr(BusinessTopoConstant.STRIPE_INDICATOR_LIST_ATTR_KEY).getStaticAttrValue(),
                    BusinessIndicator.class)))
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    private void setTopIndicatorData(TopIndicatorData topIndicatorData, List<Integer> insIdList,
        TopIndicatorDistribution queryTopIndicatorDistribution, List<Integer> stripeList) {
        try {
            TopIndicatorResult topIndicatorResult = new TopIndicatorResult();
            Map<String, String> topIndicatorMap = new HashMap<>();
            Integer modelType = topIndicatorData.getModelType();
            List<IndicatorDistributionResultForMm> indicatorList;
            switch (queryTopIndicatorDistribution.getModelType()) {
                case BusinessTopoConstant.STRIPE_TYPE_ID:
                    // 条带的饼图需特殊处理，查询的测量对象为*
                    indicatorList = getTopStripeIndicatorDataList(queryTopIndicatorDistribution, insIdList, stripeList);
                    setStripeTopIndicatorMap(indicatorList, topIndicatorMap);
                    topIndicatorData.setTopIndicator(topIndicatorMap);
                    return;
                case BusinessTopoConstant.BUSINESS_TYPE_ID:
                case BusinessTopoConstant.CHANNEL_TYPE_ID:
                    topIndicatorResult = getTopBusinessOrChannelIndicatorDataList(queryTopIndicatorDistribution,
                        insIdList, modelType);
            }
            // 为第三方和渠道的主指标设置topN的指标
            topIndicatorData.setTopIndicator(topIndicatorResult.getTopNameValueMap());
            topIndicatorData.setIndicatorList(topIndicatorResult.getTopIndicatorValueMap().keySet());
        } catch (Exception e) {
            LOGGER.error("setTopIndicatorData catch error!", e);
        }
    }

    private void setStripeTopIndicatorMap(List<IndicatorDistributionResultForMm> indicatorList, Map<String, String> topIndicatorMap) {
        for (IndicatorDistributionResultForMm indicator : indicatorList) {
            TopIndicatorName topIndicatorName = new TopIndicatorName();
            topIndicatorName.setModelName(indicator.getName());
            topIndicatorName.setOriginalValue(indicator.getOriginalValueList().get(0));
            // 避免空指针导致后续流程不进行
            if (indicator.getValue() != null && (topIndicatorName.getModelName() != null
                || topIndicatorName.getOriginalValue() != null)) {
                topIndicatorMap.put(JSON.toJSONString(topIndicatorName),
                    String.format(Locale.ROOT, "%.2f", Double.parseDouble(indicator.getValue())));
            }
        }
    }

    @Override
    public ResponseEntity queryIndicatorDistributionForMm(HttpContext context,
        IndicatorDistributionForMm queryIndicatorDistribution) {
        LOGGER.info("queryOverViewIndicatorDataForMm start.");

        IndicatorDataForMm indicatorDataForMm = new IndicatorDataForMm();
        long timeStamp = queryIndicatorDistribution.getCurrentTime() == null
            ? 0
            : queryIndicatorDistribution.getCurrentTime();
        int solutionId = queryIndicatorDistribution.getSolutionId();
        Map<Integer, List<Integer>> modelInsMap = getModelInsMap(timeStamp, solutionId);

        // 查询指标
        if (queryIndicatorDistribution.getInstanceId() == null) {
            // 默认查询全量
            setDefaultAllIndicator(indicatorDataForMm, timeStamp, modelInsMap);
        } else {
            // 选中某个实例时查询
            setSelectBusinessIndicator(indicatorDataForMm, queryIndicatorDistribution.getInstanceId(), timeStamp,
                modelInsMap);
        }

        // 设置返回结果
        ResponseEntity responseEntity = new ResponseEntity();
        responseEntity.setData(indicatorDataForMm);
        responseEntity.setResultCode(RestConstant.SUCCESS_CODE);
        responseEntity.setResultMessage(StringUtil.valueOf(RestConstant.STATUS_OK));

        LOGGER.info("queryOverViewIndicatorDataForMm end.");
        return responseEntity;
    }

    /**
     * MM, 根据给定的时间戳和解决方案ID，获取挂载在解决方案下的四层实例。
     *
     * @param timeStamp 时间戳，用于查询模型实例的时间点
     * @param solutionId 解决方案ID，用于唯一标识一个解决方案
     * @return 返回一个映射，其中键是MM第1层、第101层、第2层、第401层的ID，值是模型实例的列表
     */
    private Map<Integer, List<Integer>> getModelInsMap(long timeStamp, int solutionId) {
        // 0代表查询当前时间，否则是时间回溯
        List<BusinessInstanceModelDB> solutionIns = timeStamp == 0
            ? insModelDao.queryNextLevelInstance(solutionId)
            : insModelDao.queryNextLevelInstanceByTimeLine(solutionId, timeStamp);

        // 过滤隐藏的第三方和渠道
        solutionIns = solutionIns.stream()
            .filter(ins ->
                StringUtils.isEmpty(ins.getExtentAttr(BusinessTopoConstant.BUSINESS_IS_DISPLAY).getAttrValue())
                    || Boolean.parseBoolean(ins.getExtentAttr(BusinessTopoConstant.BUSINESS_IS_DISPLAY).getAttrValue()))
            .collect(Collectors.toList());

        // 四层模型的insId列表
        Map<Integer, List<Integer>> modelInsMap = solutionIns.stream()
            .collect(Collectors.groupingBy(BusinessInstanceModelDB::getModelType,
                Collectors.mapping(BusinessInstanceModelDB::getInstanceId, Collectors.toList())));

        // 检查是否存在四层，不存在则补空列表
        checkModelInsMap(modelInsMap);
        return modelInsMap;
    }

    private void checkModelInsMap(Map<Integer, List<Integer>> modelInsMap) {
        if (!modelInsMap.containsKey(BusinessTopoConstant.BUSINESS_TYPE_ID) || !modelInsMap.containsKey(
            BusinessTopoConstant.STRIPE_TYPE_ID) || !modelInsMap.containsKey(BusinessTopoConstant.CHANNEL_TYPE_ID)) {
            LOGGER.error("modelInsMap is not contains all ins type, modelInsMap key is {}", modelInsMap.keySet());
            modelInsMap.computeIfAbsent(BusinessTopoConstant.BUSINESS_TYPE_ID, a -> new ArrayList<>());
            modelInsMap.computeIfAbsent(BusinessTopoConstant.CHANNEL_TYPE_ID, a -> new ArrayList<>());
            modelInsMap.computeIfAbsent(BusinessTopoConstant.STRIPE_TYPE_ID, a -> new ArrayList<>());
        }
    }

    private List<IndicatorDistributionResultForMm> getIndicatorDataList(long timeStamp, List<Integer> insIdList) {
        // modelType字段只在获取top数据时用到，用来获取条带一层的指标
        // todo 这里存在一个问题，如果一个第三层有多个为1的测量对象，汇总会出错，待解决
        return insIdList.stream().map(insId -> {
            try {
                return getSelectIndicatorResult(timeStamp, insId, null);
            } catch (ServiceException e) {
                LOGGER.error("get select business indicator error! instance id is {}", insId, e);
            }
            // 出异常时只返回insId
            IndicatorDistributionResultForMm result = new IndicatorDistributionResultForMm();
            // 这个字段top饼图接口用不到
            result.setTo(insId);
            return result;
        }).collect(Collectors.toList());
    }

    private List<IndicatorDistributionResultForMm> getSelectThirdIndicatorDataList(long timeStamp, Integer insId) {
        // 选中第三方时暂定查第三方的指标，每个第三方目前只有一个接入渠道
        Integer channelId = null;
        IndicatorDistributionResultForMm result = new IndicatorDistributionResultForMm();
        try {
            channelId = getAssociatedChannelList(insId, timeStamp).get(0);
            result = getSelectIndicatorResult(timeStamp, insId, null);
        } catch (Exception e) {
            LOGGER.error("get select business indicator error! instance id is {}", insId, e);
            result = new IndicatorDistributionResultForMm();
        } finally {
            result.setTo(channelId);
        }
        return Collections.singletonList(result);
    }

    private List<IndicatorDistributionResultForMm> getTopStripeIndicatorDataList(
        TopIndicatorDistribution queryTopIndicatorDistribution, List<Integer> insIdList, List<Integer> stripeList) {
        try {
            Integer stripeId = queryTopIndicatorDistribution.getStripeId();
            long timeStamp = queryTopIndicatorDistribution.getCurrentTime() == null
                ? 0
                : queryTopIndicatorDistribution.getCurrentTime();
            String stripeUnit = queryTopIndicatorDistribution.getStripeUnit();

            // 获取指标
            List<BusinessIndicator> indicators = getIndicatorList(insIdList, stripeList, stripeId, stripeUnit,
                timeStamp);
            if (CollectionUtils.isEmpty(indicators)) {
                LOGGER.error("query stripe indicator is empty, stripeTeamId = {}", stripeId);
                return new ArrayList<>();
            }

            BusinessIndicator indicator = indicators.get(0);
            if (stripeUnit != null) {
                // 获取top指标挂载的dn
                String dn = getTopIndicatorDn(queryTopIndicatorDistribution, timeStamp, indicator, stripeUnit);
                if (dn == null) {
                    return new ArrayList<>();
                }
                indicator.setDn(dn);
            }

            String stripe = getStripeNameFromId(insIdList, stripeId, timeStamp);

            // 构造参数查询top性能数据
            List<Indicator> indicatorList = JSONArray.parseArray(
                JSON.toJSONString(Collections.singletonList(indicator)), Indicator.class,
                JSONReader.Feature.SupportSmartMatch);
            // 调用性能topN接口获取topN指标
            List<TopNViewMeasPMData> topNPmDataResults = PmDataHandleUtil.getClusterIndicatorTopNResultForStripe(indicatorList,
                timeStamp == 0 ? null : timeStamp, true, BusinessTopoConstant.TOPN.TOP_N_TYPE_STRIPE, stripe, stripeUnit);
            return getIndicatorDistributionResultList(topNPmDataResults);

        } catch (Exception e) {
            LOGGER.error("getTopStripeIndicatorDataList getIndicatorList catch error!", e);
            return new ArrayList<>();
        }
    }

    private TopIndicatorResult getTopBusinessOrChannelIndicatorDataList(
        TopIndicatorDistribution queryTopIndicatorDistribution, List<Integer> insIdList, int modelType) {
        long timeStamp = queryTopIndicatorDistribution.getCurrentTime() == null
            ? 0
            : queryTopIndicatorDistribution.getCurrentTime();

        // 获取指标
        List<BusinessIndicator> indicators = getMainIndicator(insIdList, timeStamp,
            queryTopIndicatorDistribution.getModelType());

        if (!ContextUtils.getContext().getAdmin()) {
            indicators = indicators.stream()
                .filter(indicator -> AuthUtils.getAuthDns().contains(indicator.getDn()))
                .collect(Collectors.toList());
        }
        if (BusinessTopoConstant.CHANNEL_TYPE_ID == modelType || BusinessTopoConstant.BUSINESS_TYPE_ID == modelType) {
            // 接入渠道要排除sftp
            indicators = getMaxQuantityBusinessIndicators(indicators);
        }

        if (CollectionUtils.isEmpty(indicators)) {
            LOGGER.warn("query indicator is empty, instance ids is {}", insIdList);
            return new TopIndicatorResult();
        }

        List<Indicator> indicatorList = JSONArray.parseArray(JSON.toJSONString(indicators), Indicator.class,
            JSONReader.Feature.SupportSmartMatch);
        // 调用性能topN接口获取topN指标
        String indicatorType = modelType == BusinessTopoConstant.BUSINESS_TYPE_ID
            ? BusinessTopoConstant.TOPN.TOP_N_TYPE_BUSINESS
            : BusinessTopoConstant.TOPN.TOP_N_TYPE_CHANNEL;

        // 性能指标挂所有网元之后，会出现第三方dn不一致的情况，找出出现次数最多的 dn
        handleSameDn(indicatorList);

        List<TopNViewMeasPMData> topNPmDataResults = PmDataHandleUtil.getTopNResult(indicatorList,
            timeStamp == 0 ? null : timeStamp, PmDataHandleUtil.getTopNum(indicatorType));

        // indicatorId去除moType作为key
        Map<String, String> indicatorValueMap = getIndicatorValueMap(topNPmDataResults);

        // 过滤并设置返回值
        TopIndicatorResult topIndicatorResult = filterAndSetIndicator(indicatorList, indicatorValueMap, timeStamp, modelType);

        // 将result按value的值从大到小进行排序，保留topN
        sortAndKeepTopN(topIndicatorResult, indicatorType);

        return topIndicatorResult;

    }

    private TopIndicatorResult filterAndSetIndicator(List<Indicator> indicatorList,
        Map<String, String> indicatorValueMap, long timeStamp, int modelType) {
        TopIndicatorResult topIndicatorResult = new TopIndicatorResult();
        Map<String, String> result = new HashMap<>();
        Map<Indicator, String> indicatorResult = new HashMap<>();
        Map<Integer, BusinessInstanceModelDB> insMap = insModelDao.queryInstanceListByIdList(
                indicatorList.stream().map(Indicator::getInstanceId).collect(Collectors.toList()), timeStamp)
            .stream()
            .collect(Collectors.toMap(BusinessInstanceModelDB::getInstanceId, a -> a));
        for (Indicator indicator : indicatorList) {
            String key = indicator.getDn() + indicator.getMeasUnitKey() + indicator.getMeasTypeKey()
                + indicator.getDisplayValue();
            if (indicatorValueMap.containsKey(key)) {
                TopIndicatorName topIndicatorName = new TopIndicatorName();
                topIndicatorName.setInstanceId(indicator.getInstanceId());
                BusinessInstanceModelDB businessInstanceModelDB = insMap.get(indicator.getInstanceId());
                String modelName = getModelName(modelType, businessInstanceModelDB);
                topIndicatorName.setModelName(modelName);
                result.put(JSON.toJSONString(topIndicatorName), indicatorValueMap.get(key));
                indicatorResult.put(indicator, indicatorValueMap.get(key));
            }
        }
        topIndicatorResult.setTopNameValueMap(result);
        topIndicatorResult.setTopIndicatorValueMap(indicatorResult);
        return topIndicatorResult;
    }

    private String getModelName(int modelType, BusinessInstanceModelDB businessInstanceModelDB) {
        String modelName = "";
        if (Objects.isNull(businessInstanceModelDB)) {
            LOGGER.error("businessInstanceModelDB is null, cannot get modelName, modelType = {}", modelType);
            return modelName;
        }
        if (BusinessTopoConstant.BUSINESS_TYPE_ID == modelType) {
            modelName = businessInstanceModelDB.getStaticExtentAttr("businessName").getStaticAttrValue();
        } else if (BusinessTopoConstant.CHANNEL_TYPE_ID == modelType) {
            modelName = businessInstanceModelDB.getStaticExtentAttr("channelName").getStaticAttrValue();
        } else {
            LOGGER.error("modelType invalid! {}", modelType);
        }
        return modelName;
    }

    private Map<String, String> getIndicatorValueMap(List<TopNViewMeasPMData> topNPmDataResults) {
        Map<String, String> indicatorValueMap = new HashMap<>();
        for (TopNViewMeasPMData pmData : topNPmDataResults) {
            String dn = pmData.getDn();
            String displayValue = pmData.getDisplayValue();
            for (TopNViewMeasTypeValue value : pmData.getValues()) {
                if (value.getValue() != null) {
                    indicatorValueMap.put(dn + value.getMeasUnitKey() + value.getMeasTypeKey() + displayValue,
                        value.getValue());
                }
            }
        }
        return indicatorValueMap;
    }

    private void sortAndKeepTopN(TopIndicatorResult topIndicatorResult, String indicatorType) {

        Map<String, String> result = topIndicatorResult.getTopNameValueMap();
        Map<Indicator, String> indicatorResult = topIndicatorResult.getTopIndicatorValueMap();

        // 检查输入是否为空
        if (result == null || result.isEmpty()) {
            return;
        }

        if (result.keySet().size() > PmDataHandleUtil.getTopNum(indicatorType)) {
            // 将Map的条目转换为List
            List<Map.Entry<String, String>> entryList = sortEntry(result);
            List<Map.Entry<String, String>> topEntries = entryList.subList(0,
                Math.min(PmDataHandleUtil.getTopNum(indicatorType), entryList.size()));

            // 将排序后的结果转换回Map
            Map<String, String> sortedMap = new LinkedHashMap<>();
            for (Map.Entry<String, String> entry : topEntries) {
                sortedMap.put(entry.getKey(), entry.getValue());
            }
            topIndicatorResult.setTopNameValueMap(sortedMap);
        }

        if (indicatorResult.keySet().size() > PmDataHandleUtil.getTopNum(indicatorType)) {
            // 将Map的条目转换为List
            List<Map.Entry<Indicator, String>> indicatorEntryList = sortEntry(indicatorResult);
            List<Map.Entry<Indicator, String>> topIndicatorEntries = indicatorEntryList.subList(0,
                Math.min(PmDataHandleUtil.getTopNum(indicatorType), indicatorEntryList.size()));

            // 将排序后的结果转换回Map
            Map<Indicator, String> sortedIndicatorMap = new LinkedHashMap<>();
            for (Map.Entry<Indicator, String> entry : topIndicatorEntries) {
                sortedIndicatorMap.put(entry.getKey(), entry.getValue());
            }
            topIndicatorResult.setTopIndicatorValueMap(sortedIndicatorMap);
        }

    }

    private static <K> List<Map.Entry<K, String>> sortEntry(Map<K, String> result) {
        List<Map.Entry<K, String>> entryList = new ArrayList<>(result.entrySet());

        // 定义比较器，根据值（double类型的字符串）从大到小排序
        entryList.sort((entry1, entry2) -> {
            try {
                // 将值从String转换为Double
                double value1 = Double.parseDouble(entry1.getValue());
                double value2 = Double.parseDouble(entry2.getValue());
                // 从大到小排序
                return Double.compare(value2, value1);
            } catch (NumberFormatException e) {
                // 这里假设无法转换的值视为最小值
                return 1;
            }
        });
        return entryList;
    }

    private List<BusinessIndicator> getIndicatorList(List<Integer> insIdList, List<Integer> stripeList,
        Integer stripeId, String stripeUnit, long timeStamp) {
        List<BusinessIndicator> indicators;
        if (stripeUnit != null) {
            // 获取条带层
            List<BusinessInstanceModelDB> indicatorModels = getStripeSiteModels(insIdList, stripeId, timeStamp);
            if (CollectionUtils.isEmpty(indicatorModels)) {
                LOGGER.error("query stripeSite models is null, stripeTeamId = {}", stripeId);
                return new ArrayList<>();
            }
            // 过滤出topN主指标，取第0个使用
            indicators = getIndicatorsFromModelAttr(indicatorModels,
                BusinessTopoConstant.STRIPE_INDICATOR_LIST_ATTR_KEY, false).stream().filter(Objects::nonNull)
                .filter(indicator -> Objects.equals(indicator.getIndicatorDisplayType(), TOPN_INDICATOR_TYPE))
                .collect(Collectors.toList());
        } else if (stripeId == null) {
            // 第二层所有挂载的gsu主指标，是同一个网元的
            List<BusinessIndicator> businessIndicators = indicatorDao.queryIndicatorByInstanceId(
                    insModelDao.queryInstanceListByIdList(stripeList, timeStamp).get(0).getInstanceId(), timeStamp)
                .stream()
                .filter(indicator -> Objects.equals(indicator.getIndicatorDisplayType(), TOPN_INDICATOR_TYPE))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(businessIndicators)) {
                LOGGER.error("query stripeSite indicators is empty, stripeList = {}", stripeList);
                return Collections.emptyList();
            }
            indicators = Collections.singletonList(businessIndicators.get(0));
        } else {
            // 总览视图，选中某个条带时, 过滤出第三层site非csu的实例, 然后获取其指标
            BusinessInstanceModelDB ins = insModelDao.queryInstanceListByIdList(insIdList, timeStamp)
                .stream()
                .filter(site -> !site.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE)
                    .getAttrValue()
                    .toLowerCase(Locale.ROOT)
                    .contains(CSU))
                .collect(Collectors.toList())
                .get(0);
            indicators = Collections.singletonList(
                indicatorDao.queryIndicatorByInstanceId(ins.getInstanceId(), timeStamp)
                    .stream()
                    .filter(indicator -> Objects.equals(indicator.getIndicatorDisplayType(), TOPN_INDICATOR_TYPE))
                    .collect(Collectors.toList())
                    .get(0));
        }
        return indicators;
    }

    private String getTopIndicatorDn(TopIndicatorDistribution queryTopIndicatorDistribution, long timeStamp,
        BusinessIndicator indicator, String stripeUnit) {
        Integer stripeId = queryTopIndicatorDistribution.getStripeId();
        // 根据网元类型查出对应条带和条带单元下的网元，取第0个查性能
        List<BusinessInstanceModelDB> stripeGroupModels = insModelDao.queryInstanceListByIdList(
            Collections.singletonList(stripeId), timeStamp);
        if (CollectionUtils.isEmpty(stripeGroupModels)) {
            LOGGER.error("query stripe group models is null, stripeTeamId = {}", stripeId);
            return null;
        }
        BusinessInstanceModelDB stripeGroupModel = stripeGroupModels.get(0);
        String stripeName = stripeGroupModel
            .getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE_GROUP_NAME)
            .getAttrValue();

        // 过滤项从第三层取
        BusinessInstanceModelDB stripeModels = insModelDao.queryNextLevelInstanceByTimeLineBatch(
            Collections.singletonList(stripeId), timeStamp).get(0);
        String siteName = stripeModels.getExtentAttr(BusinessTopoConstant.MM_SITE_NAME).getAttrValue();
        String groupName = stripeModels.getExtentAttr(BusinessTopoConstant.MM_HWS_GROUP_NAME).getAttrValue();

        if (indicator.getIsAggregateByAllSolution() != null && indicator.getIsAggregateByAllSolution()) {
            // 找到性能挂载的那个网元
            String unitDn = PmDataHandleUtil.getStripeUnitDn(indicator, siteName, groupName, stripeName, stripeUnit);
            if (StringUtils.isEmpty(unitDn)) {
                Map<String, String> measUnitDnMap = TopoImportSolutionAdapter.getMmMeasUnitDnMap(indicator.getMoType());
                unitDn = measUnitDnMap.get(indicator.getMeasUnitKey());
            }
            if (StringUtils.isNotEmpty(unitDn)) {
                return unitDn;
            }
        }

        // 根据moType和条带单元过滤网元
        List<ManagedObject> insMos = EamUtil.queryMosByMoType(indicator.getMoType());
        insMos = insMos.stream()
            .filter(mo -> stripeName.equals(mo.getHwsStripe()) && queryTopIndicatorDistribution.getStripeUnit()
                .equals(mo.getHwsAppSite()) && Objects.equals(siteName, mo.getHwsSiteName()) && Objects.equals(
                groupName, mo.getHwsGroupName()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(insMos)) {
            LOGGER.error("query stripe unit mos is null, stripeTeamId = {}", stripeId);
            return null;
        }
        return insMos.get(0).getDN().getValue();
    }

    private List<BusinessInstanceModelDB> getStripeSiteModels(List<Integer> insIdList, Integer stripeId,
        long timeStamp) {
        List<BusinessInstanceModelDB> stripeSiteModels;
        if (stripeId != null) {
            stripeSiteModels = insModelDao.queryNextLevelInstanceByTimeLineBatch(Collections.singletonList(stripeId),
                timeStamp);
        } else {
            stripeSiteModels = insModelDao.queryInstanceListByIdList(insIdList, timeStamp);
        }
        return stripeSiteModels;
    }

    private List<IndicatorDistributionResultForMm> getIndicatorDistributionResultList(
        List<TopNViewMeasPMData> resultList) {
        List<IndicatorDistributionResultForMm> resultIndicatorList = new ArrayList<>();
        for (TopNViewMeasPMData data : resultList) {
            String originalValue = null;
            if (data.getMeasObject() != null && CollectionUtils.isNotEmpty(data.getMeasObject().getKeys())) {
                ArrayList<String> originalValueList = new ArrayList<>();
                for (int i = 0; i < data.getMeasObject().getKeys().size(); i++) {
                    originalValueList.add(
                        data.getMeasObject().getKeys().get(i) + "<=>" + data.getMeasObject().getValues().get(i));
                }
                // 拼接所有的值
                originalValue = String.join("<,>", originalValueList);
            } else {
                LOGGER.error("get MeasObject key is null! ");
            }

            // 接口入参包含测量单元，只会返回一个value
            TopNViewMeasTypeValue value = data.getValues().get(0);
            IndicatorDistributionResultForMm resultIndicator = new IndicatorDistributionResultForMm();
            resultIndicator.setValue(value.getValue());
            resultIndicator.setOriginalValueList(Collections.singletonList(originalValue));
            resultIndicator.setName(filterDisplayValue(data.getDisplayValue()));

            resultIndicatorList.add(resultIndicator);
        }

        return resultIndicatorList;
    }

    private String filterDisplayValue(String displayValue) {
        if (ConfigurationUtil.getConfigDataByName(BusinessTopoConstant.TOP_INDICATOR_NAME_REGEX) == null
            || ConfigurationUtil.getConfigDataByName(BusinessTopoConstant.TOP_INDICATOR_NAME_REGEX).getValue()
            == null) {
            LOGGER.error("topIndicatorNameRegex is null!");
            return displayValue;
        }
        if (displayValue == null) {
            LOGGER.error("displayValue is null!");
            return "";
        }
        String siteName = ConfigurationUtil.getConfigDataByName(BusinessTopoConstant.MM_SITE_NAME_CONFIG).getValue();
        return Arrays.stream(displayValue.split(DISPLAY_NAME_SPLIT))
            .filter(value -> !Arrays.asList(
                ConfigurationUtil.getConfigDataByName(BusinessTopoConstant.TOP_INDICATOR_NAME_REGEX)
                    .getValue()
                    .split(DISPLAY_NAME_SPLIT)).contains(value))
            .filter(value -> !value.contains(siteName))
            .collect(Collectors.joining(DISPLAY_NAME_SPLIT));
    }

    private List<BusinessIndicator> getIndicatorsFromModelAttr(List<BusinessInstanceModelDB> stripeSiteModels,
        String indicatorListType, boolean hasMultipleSameLayerModel) {
        List<BusinessIndicator> indicators = new ArrayList<>();
        if (!hasMultipleSameLayerModel) {
            String stripeIndicatorJson = stripeSiteModels.get(0)
                .getStaticExtentAttr(indicatorListType)
                .getStaticAttrValue();
            try {
                indicators = JSON.parseArray(stripeIndicatorJson, BusinessIndicator.class);
            } catch (JSONException e) {
                LOGGER.error("parse stripeUnitIndicatorList error", e);
            }
        } else {
            for (BusinessInstanceModelDB model : stripeSiteModels) {
                String stripeIndicatorJson = model.getStaticExtentAttr(indicatorListType).getStaticAttrValue();
                try {
                    List<BusinessIndicator> jsonIndicators = JSON.parseArray(stripeIndicatorJson,
                        BusinessIndicator.class);
                    indicators.addAll(jsonIndicators);
                } catch (JSONException e) {
                    LOGGER.error("parse stripeUnitIndicatorList error", e);
                }
            }
        }
        return indicators;
    }

    private List<IndicatorDistributionResultForMm> getAllStripeIndicatorDataList(long timeStamp, List<Integer> insIdList) {
        List<IndicatorDistributionResultForMm> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(insIdList)) {
            return Collections.emptyList();
        }
        String moType = getStripeIndicatorMoType(insIdList.get(0), timeStamp);
        Map<String, String> measUnitDnMap = TopoImportSolutionAdapter.getMmMeasUnitDnMap(moType);
        List<BusinessInstanceModelDB> siteInsModel = insModelDao.queryNextLevelInstanceByTimeLineBatch(insIdList, timeStamp);
        siteInsModel = siteInsModel.stream()
            .filter(site -> StringUtils.isNotEmpty(site.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue())
                && !site.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue().toLowerCase(Locale.ROOT).contains(CSU))
            .collect(Collectors.toList());

        Map<Integer, BusinessInstanceModelDB> stripeGroupToStripeMap = siteInsModel.stream()
            .collect(Collectors.toMap(BusinessInstanceModelDB::getTargetInstanceId, model -> model,
                (existingValue, newValue) -> newValue));

        for (int insId : insIdList) {
            IndicatorDistributionResultForMm result = new IndicatorDistributionResultForMm();
            try {
                IndicatorDistributionResultForMm indicator = getAllSelectIndicatorResult(timeStamp, insId, stripeGroupToStripeMap,
                    BusinessTopoConstant.SITE_GROUP_TYPE_ID, measUnitDnMap);

                result = indicator;
                if (Objects.nonNull(result.getValue())) {
                    result.setValue(String.format(Locale.ROOT, "%.2f",
                        Double.parseDouble(result.getValue())));
                }

            } catch (Exception e) {
                LOGGER.error("get select business indicator error! instance id is {}", insId, e);
            } finally {
                result.setTo(insId);
                resultList.add(result);
            }
        }
        return resultList;
    }

    private String getStripeIndicatorMoType(Integer stripeGroupInstanceId, Long currentTime) {
        // 条带指标从下一层中获取,过滤掉csu条带的
        BusinessInstanceModelDB siteInsModel = insModelDao.queryNextLevelInstanceByTimeLine(stripeGroupInstanceId,
                currentTime)
            .stream()
            .filter(site -> StringUtils.isNotEmpty(site.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue()) && !site.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE)
                .getAttrValue()
                .toLowerCase(Locale.ROOT)
                .contains(CSU))
            .findFirst()
            .orElse(null);
        if (siteInsModel == null) {
            return StringUtils.EMPTY;
        }
        // 如果为跨解决方案汇聚任务，判断测量对象中是否含有对应条带
        // 不为跨解决方案汇聚任务，通过Dn进行过滤
        // 先从站点层中取出displayValue为10的指标，并找出测量对象
        List<BusinessIndicator> indicators = indicatorDao.queryIndicatorByInstanceId(siteInsModel.getInstanceId(), currentTime)
            .stream()
            .filter(ind -> Objects.nonNull(ind.getIndicatorDisplayType()) && LINK_INDICATOR_TYPE.equals(ind.getIndicatorDisplayType()))
            .collect(Collectors.toList());
        // 如果不存在则从原始地方查询
        if (CollectionUtils.isEmpty(indicators)) {
            String stripeIndicatorJson = siteInsModel.getStaticExtentAttr(
                BusinessTopoConstant.LINK_INDICATOR_LIST_ATTR_KEY).getStaticAttrValue();
            try {
                indicators = JSON.parseArray(stripeIndicatorJson, BusinessIndicator.class);
            } catch (JSONException e) {
                LOGGER.error("parse stripeUnitIndicatorList error", e);
                return StringUtils.EMPTY;
            }
            return indicators.get(0).getMoType();
        }
        return indicators.get(0).getMoType();
    }

    private IndicatorDistributionResultForMm getAllSelectIndicatorResult(long timeStamp, int insId,
        Map<Integer, BusinessInstanceModelDB> stripeGroupToStripeMap, Integer modelType, Map<String, String> measUnitDnMap) {
        IndicatorDistributionResultForMm result = new IndicatorDistributionResultForMm();
        result.setStatus("normal");
        // 这个字段top饼图接口用不到
        result.setTo(insId);
        // 获取主指标列表
        List<BusinessIndicator> indicators = getMainIndicatorWithAggregateDnMap(stripeGroupToStripeMap.get(insId), timeStamp, measUnitDnMap);
        // 过滤没权限的指标
        if (!ContextUtils.getContext().getAdmin()) {
            indicators = indicators.stream()
                .filter(indicator -> AuthUtils.getAuthDns().contains(indicator.getDn()))
                .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(indicators)) {
            LOGGER.warn("[queryIndicatorDistributionForMm] could not find any indicator.");
            return result;
        }
        result.setName(indicators.get(0).getIndexName());
        // 从性能获取指标值
        List<BusinessIndicator> filledIndicators = pmIndicatorInstanceService.fillIndexNameAndUnit(indicators);
        Map<String, Long> timeRanges = PmDataHandleUtil.getIndTPSTimeRangeInPmFormat(timeStamp == 0 ? null : timeStamp,
            DISTRIBUTE_INDICATOR_RANGE);
        List<Indicator> indicatorList = JSON.parseArray(JSON.toJSONString(filledIndicators), Indicator.class,
            JSONReader.Feature.SupportSmartMatch);
        List<QueryDeletedHistoryData> queryDeletedHistoryData = pmRequestService.getQueryDeletedHistoryData(
            indicatorList, timeRanges);
        DeletedHistoryData performanceResult = pmRequestService.getDeletedHistoryDataFormPerformance(
            queryDeletedHistoryData);
        if (CollectionUtils.isEmpty(performanceResult.getResult()) || CollectionUtils.isEmpty(
            performanceResult.getResult().get(0).getDatas())) {
            LOGGER.warn("[queryIndicatorDistributionForMm] There is no indicator data.");
            return result;
        }

        Map<String, DeletedHistoryValues> nameValueMap = getHistoryValuesMap(performanceResult, modelType);
        if (indicators.stream().anyMatch(ind -> ind.getIndicatorStatus() != null && ind.getIndicatorStatus())) {
            result.setStatus("alarm");
        } else {
            result.setStatus("normal");
        }
        result.setValue(getValue(nameValueMap));
        result.setName(indicators.get(0).getIndexName());
        return result;
    }

    private List<BusinessIndicator> getMainIndicatorWithAggregateDnMap(BusinessInstanceModelDB siteInsModel, Long currentTime, Map<String, String> measUnitDnMap) {
        if (siteInsModel == null) {
            return new ArrayList<>();
        }
        // 如果为跨解决方案汇聚任务，判断测量对象中是否含有对应条带
        // 不为跨解决方案汇聚任务，通过Dn进行过滤
        // 先从站点层中取出displayValue为10的指标，并找出测量对象
        List<BusinessIndicator> indicators = indicatorDao.queryIndicatorByInstanceId(siteInsModel.getInstanceId(), currentTime)
            .stream()
            .filter(ind -> Objects.nonNull(ind.getIndicatorDisplayType()) && LINK_INDICATOR_TYPE.equals(ind.getIndicatorDisplayType()))
            .collect(Collectors.toList());
        // 如果不存在则从原始地方查询
        if (CollectionUtils.isEmpty(indicators)) {
            String stripeIndicatorJson = siteInsModel.getStaticExtentAttr(
                BusinessTopoConstant.LINK_INDICATOR_LIST_ATTR_KEY).getStaticAttrValue();
            try {
                indicators = JSON.parseArray(stripeIndicatorJson, BusinessIndicator.class);
            } catch (JSONException e) {
                LOGGER.error("parse stripeUnitIndicatorList error", e);
                return new ArrayList<>();
            }
        }
        if (Objects.nonNull(indicators.get(0).getIsAggregateByAllSolution()) && indicators.get(0).getIsAggregateByAllSolution()) {
            indicators.get(0).setDn(measUnitDnMap.get(indicators.get(0).getMeasUnitKey()));
        } else {
            indicators.get(0).setDn(siteInsModel.getDn());
        }
        return indicators;
    }

    private void setSelectBusinessIndicator(IndicatorDataForMm indicatorDataForMm, int insId, long timeStamp,
        Map<Integer, List<Integer>> insMap) {
        // 接入渠道的指标需要根据选中的指标判断
        List<IndicatorDistributionResultForMm> lineThird2ChannelList = new ArrayList<>();
        if (insMap.get(BusinessTopoConstant.BUSINESS_TYPE_ID).contains(insId)) {
            lineThird2ChannelList = getSelectThirdIndicatorDataList(timeStamp, insId);
        } else if (insMap.get(BusinessTopoConstant.STRIPE_TYPE_ID).contains(insId)) {
            lineThird2ChannelList = getIndicatorDataList(timeStamp, insMap.get(BusinessTopoConstant.CHANNEL_TYPE_ID));
        } else if (insMap.get(BusinessTopoConstant.CHANNEL_TYPE_ID).contains(insId)) {
            lineThird2ChannelList = getIndicatorDataList(timeStamp, Collections.singletonList(insId));
        } else {
            LOGGER.error("insId is not in mm solution ins!, ins id is {}", insId);
        }
        indicatorDataForMm.setLineThird2Channel(lineThird2ChannelList);
    }

    private List<Integer> getAssociatedChannelList(int insId, long timeStamp) {
        try {
            String modelIds = insModelDao.queryAttrValue(insId, timeStamp, BusinessTopoConstant.ASSOCIATED_CHANNEL);
            JSONArray jsonArray = JSONArray.parseArray(modelIds);
            if (jsonArray == null) {
                return new ArrayList<>();
            }
            List<String> modelIdList = jsonArray.toJavaList(String.class);
            return insModelDao.queryInstanceIdByModelId(modelIdList);
        } catch (Exception e) {
            LOGGER.error("getAssociatedChannelList catch error!", e);
            return new ArrayList<>();
        }
    }

    private void setDefaultAllIndicator(IndicatorDataForMm indicatorDataForMm, long timeStamp,
        Map<Integer, List<Integer>> modelInsMap) {
        LOGGER.info("set default indicator distribution.");
        List<IndicatorDistributionResultForMm> lineThird2ChannelList = new ArrayList<>();
        List<IndicatorDistributionResultForMm> lineChannel2StripList = new ArrayList<>();
        try {
            lineThird2ChannelList = getIndicatorDataList(timeStamp,
                modelInsMap.get(BusinessTopoConstant.CHANNEL_TYPE_ID));
            lineChannel2StripList = getAllStripeIndicatorDataList(timeStamp,
                modelInsMap.get(BusinessTopoConstant.STRIPE_TYPE_ID));
        } catch (Exception e) {
            LOGGER.error("setDefaultAllIndicator catch error!", e);
        }
        indicatorDataForMm.setLineThird2Channel(lineThird2ChannelList);
        indicatorDataForMm.setLineChannel2StripIns(lineChannel2StripList);
    }

    private IndicatorDistributionResultForMm getSelectIndicatorResult(long timeStamp, int insId, Integer modelType)
        throws ServiceException {
        IndicatorDistributionResultForMm result = new IndicatorDistributionResultForMm();
        result.setStatus("normal");
        // 这个字段top饼图接口用不到
        result.setTo(insId);
        // 获取主指标列表
        List<BusinessIndicator> indicators = getMainIndicator(Collections.singletonList(insId), timeStamp, modelType);
        // 过滤没权限的指标
        if (!ContextUtils.getContext().getAdmin()) {
            indicators = indicators.stream()
                .filter(indicator -> AuthUtils.getAuthDns().contains(indicator.getDn()))
                .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(indicators)) {
            LOGGER.warn("[queryIndicatorDistributionForMm] could not find any indicator.");
            return result;
        }
        result.setName(indicators.get(0).getIndexName());
        // 从性能获取指标值
        List<BusinessIndicator> filledIndicators = pmIndicatorInstanceService.fillIndexNameAndUnit(indicators);
        Map<String, Long> timeRanges = PmDataHandleUtil.getIndTPSTimeRangeInPmFormat(timeStamp == 0 ? null : timeStamp,
            DISTRIBUTE_INDICATOR_RANGE);
        List<Indicator> indicatorList = JSON.parseArray(JSON.toJSONString(filledIndicators), Indicator.class,
            JSONReader.Feature.SupportSmartMatch);
        List<QueryDeletedHistoryData> queryDeletedHistoryData = pmRequestService.getQueryDeletedHistoryData(
            indicatorList, timeRanges);
        DeletedHistoryData performanceResult = pmRequestService.getDeletedHistoryDataFormPerformance(
            queryDeletedHistoryData);
        if (CollectionUtils.isEmpty(performanceResult.getResult()) || CollectionUtils.isEmpty(
            performanceResult.getResult().get(0).getDatas())) {
            LOGGER.warn("[queryIndicatorDistributionForMm] There is no indicator data.");
            return result;
        }

        Map<String, DeletedHistoryValues> nameValueMap = getHistoryValuesMap(performanceResult, modelType);
        if (indicators.stream().anyMatch(ind -> ind.getIndicatorStatus() != null && ind.getIndicatorStatus())) {
            result.setStatus("alarm");
        } else {
            result.setStatus("normal");
        }
        result.setValue(getValue(nameValueMap));
        result.setName(indicators.get(0).getIndexName());
        return result;
    }

    private List<BusinessIndicator> getMaxQuantityBusinessIndicators(List<BusinessIndicator> indicators) {
        // 从性能获取指标值
        List<BusinessIndicator> filledIndicators = pmIndicatorInstanceService.fillIndexNameAndUnit(indicators);
        Map<String, List<BusinessIndicator>> grouped = filledIndicators.stream()
            .collect(Collectors.groupingBy(
                indicator -> indicator.getMoType() + indicator.getMeasUnitKey() + indicator.getMeasTypeKey()));
        // 找到数量最多的分组
        return grouped.entrySet()
            .stream()
            .max(Comparator.comparingInt(e -> e.getValue().size()))
            .map(Map.Entry::getValue)
            .orElse(new ArrayList<>());
    }

    public String getValue(Map<String, DeletedHistoryValues> dnValueMap) {
        // 若一个网元有多个测量对象都是主指标，加和
        List<String> valueList = dnValueMap.values()
            .stream()
            .filter(Objects::nonNull)
            .flatMap(
                deletedHistoryValues -> deletedHistoryValues.getValues().values().stream().filter(Objects::nonNull)).collect(
                Collectors.toList());
        if (CollectionUtils.isEmpty(valueList)) {
            return null;
        }
        return String.format(Locale.ROOT, "%.2f", valueList.stream()
            .mapToDouble(Double::parseDouble)
            .sum());
    }

    private List<BusinessIndicator> getMainIndicator(List<Integer> instanceIds, Long currentTime, Integer modelType) {
        // 条带层单独处理
        if (Objects.equals(BusinessTopoConstant.STRIPE_TYPE_ID, modelType)) {
            return getStripeMainIndicators(instanceIds, currentTime);
        }

        List<BusinessIndicator> indicators = indicatorDao.queryHisIndInsIdList(instanceIds, currentTime);
        if (CollectionUtils.isEmpty(indicators)) {
            LOGGER.error("[queryIndicatorResultsForMm] could not find any indicator by instanceIds: {}", instanceIds);
            return Collections.emptyList();
        }
        // 获取所有主指标
        List<BusinessIndicator> mainIndList = indicators.stream()
            .filter(ind -> BusinessTopoConstant.INDICATOR_TYPE_TPS.equals(ind.getIndicatorDisplayType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mainIndList)) {
            LOGGER.error("[queryIndicatorResultsForMm] could not find any main indicator by instanceIds: {}",
                instanceIds);
            return Collections.emptyList();
        }
        return mainIndList;
    }

    private List<BusinessIndicator> getStripeMainIndicators(List<Integer> instanceIds, Long currentTime) {
        // 条带指标从下一层中获取,过滤掉csu条带的
        BusinessInstanceModelDB siteInsModel = insModelDao.queryNextLevelInstanceByTimeLineBatch(instanceIds,
                currentTime)
            .stream()
            .filter(site -> StringUtils.isNotEmpty(site.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue()) && !site.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE)
                .getAttrValue()
                .toLowerCase(Locale.ROOT)
                .contains(CSU))
            .findFirst()
            .orElse(null);
        if (siteInsModel == null) {
            return new ArrayList<>();
        }
        // 如果为跨解决方案汇聚任务，判断测量对象中是否含有对应条带
        // 不为跨解决方案汇聚任务，通过Dn进行过滤
        // 先从站点层中取出displayValue为10的指标，并找出测量对象
        List<BusinessIndicator> indicators = indicatorDao.queryIndicatorByInstanceId(siteInsModel.getInstanceId(), currentTime)
            .stream()
            .filter(ind -> Objects.nonNull(ind.getIndicatorDisplayType()) && LINK_INDICATOR_TYPE.equals(ind.getIndicatorDisplayType()))
            .collect(Collectors.toList());
        // 如果不存在则从原始地方查询
        if (CollectionUtils.isEmpty(indicators)) {
            String stripeIndicatorJson = siteInsModel.getStaticExtentAttr(
                BusinessTopoConstant.LINK_INDICATOR_LIST_ATTR_KEY).getStaticAttrValue();
            try {
                indicators = JSON.parseArray(stripeIndicatorJson, BusinessIndicator.class);
            } catch (JSONException e) {
                LOGGER.error("parse stripeUnitIndicatorList error", e);
                return new ArrayList<>();
            }
        }
        if (Objects.nonNull(indicators.get(0).getIsAggregateByAllSolution()) && indicators.get(0).getIsAggregateByAllSolution()) {
            Map<String, String> measUnitDnMap = TopoImportSolutionAdapter.getMmMeasUnitDnMap(indicators.get(0).getMoType());
            indicators.get(0).setDn(measUnitDnMap.get(indicators.get(0).getMeasUnitKey()));
        } else {
            indicators.get(0).setDn(siteInsModel.getDn());
        }
        return indicators;
    }

    private static Map<String, DeletedHistoryValues> getHistoryValuesMap(DeletedHistoryData performanceResult,
        Integer modelType) {
        Map<String, DeletedHistoryValues> dnValueMap = new HashMap<>();
        // todo 这里需要按时间戳过滤，十分钟以内有数据可以展示
        DeletedHistoryResult result = performanceResult.getResult().get(0);
        if (Objects.isNull(result.getDatas())) {
            return dnValueMap;
        }
        // 若为top5接系统服务数据汇总，需要获取displayValue作为key
        boolean isTopStripe = modelType != null && BusinessTopoConstant.STRIPE_TYPE_ID == modelType;
        for (DeletedHistoryValues value : result.getDatas()) {
            if (value.getValues() == null) {
                continue;
            }
            String keyValue = isTopStripe ? value.getDisplayValue() : value.getDn();
            if (!dnValueMap.containsKey(keyValue)) {
                dnValueMap.put(keyValue, value);
            } else if (value.getTimestamp().compareTo(dnValueMap.get(keyValue).getTimestamp()) > 0) {
                dnValueMap.put(keyValue, value);
            }
        }
        return dnValueMap;
    }

    @Override
    public ResponseEntity queryOverviewGrid(HttpContext context, OverviewGridParam queryOverviewGrid)
        throws ServiceException {
        LOGGER.info("entry queryOverviewGrid start.");
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            RestfulParametes restfulParametes = new RestfulParametes();
            restfulParametes.setRawData(JSON.toJSONString(queryOverviewGrid));
            RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, QUERY_OVERVIEW_GRID,
                restfulParametes, null);
            if (getResponseEntity(responseEntity, response) != null) {
                return responseEntity;
            }
            OverViewGrid result = JSON.parseObject(response.getResponseContent(), OverViewGrid.class,
                JSONReader.Feature.SupportSmartMatch);
            setResponseEntityData(responseEntity, response, result);
        } catch (ServiceException e) {
            LOGGER.error("queryOverviewGrid failed, error is ", e);
        }
        LOGGER.info("entry queryOverviewGrid end.");
        return responseEntity;
    }

    @Override
    public ResponseEntity queryOverviewGridForMm(HttpContext context, OverviewGridParam param) {
        ResponseEntity responseEntity = new ResponseEntity();
        OverViewGridForMm overViewGrid = new OverViewGridForMm();
        try {
            // 根据solutionIns组装overView
            overViewGrid = topoOverViewService.queryOverviewGridForMm(
                JSONObject.parseObject(JSONObject.toJSONString(param),
                    com.huawei.i2000.dvtoposervice.model.OverviewGridParam.class));

        } catch (Exception e) {
            LOGGER.error("get over view catch error!", e);
            responseEntity.setData(overViewGrid);
            responseEntity.setResultCode(RestConstant.FAILURE_CODE);
            responseEntity.setResultMessage(String.valueOf(RestConstant.SERVICE_ERROR_CODE));
            return responseEntity;
        }
        responseEntity.setData(overViewGrid);
        responseEntity.setResultCode(RestConstant.SUCCESS_CODE);
        responseEntity.setResultMessage(String.valueOf(RestConstant.STATUS_OK));
        return responseEntity;
    }

    private ResponseEntity getResponseEntity(ResponseEntity responseEntity, RestfulResponse response) {
        if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
            responseEntity.setResultCode(RestConstant.FAILURE_CODE);
            responseEntity.setResultMessage("service response is not 200");
            LOGGER.error("error,response = {}", response);
            return responseEntity;
        }
        return null;
    }

    private void setResponseEntityData(ResponseEntity responseEntity, RestfulResponse response, Object result) {
        responseEntity.setData(result);
        responseEntity.setResultCode(RestConstant.SUCCESS_CODE);
        responseEntity.setResultMessage(StringUtil.valueOf(response.getStatus()));
    }

    @Override
    public ResponseEntity queryInitEditPage(HttpContext context, BusinessEditParam queryInitEditPage)
        throws ServiceException {
        LOGGER.debug("entry queryInitEditPage()");
        ResponseEntity result = new ResponseEntity();
        // 根据不同解决方案载入不同的编辑页面 -> 根据解决方案查询解决方案类型
        Integer solType = topoOverViewService.querySolutionTypeBySolIns(queryInitEditPage.getSolutionId());
        // 根据解决方案查询站点配置
        switch (solType) {
            case BusinessTopoConstant.SolutionTypeLabel.CBS_TYPE:
            case BusinessTopoConstant.SolutionTypeLabel.BES_TYPE:
                result.setData(topoOverViewService.queryInitEditPage(queryInitEditPage.getSolutionId()));
                break;
            case BusinessTopoConstant.SolutionTypeLabel.MM_TYPE:
                result.setData(topoOverViewService.queryInitEditPageForMm(queryInitEditPage.getSolutionId()));
                break;
        }
        result.setResultCode(RestConstant.SUCCESS_CODE);
        LOGGER.debug("entry queryInitEditPage() end.");
        return result;
    }

    @Override
    public ResponseEntity editBusiness(HttpContext context, BusinessEditParam editBusiness) throws ServiceException {
        LOGGER.debug("entry editBusiness()");
        ResponseEntity result = new ResponseEntity();
        try {
            result = topoOverViewService.editOverViewPage(context, editBusiness.getSolutionId(),
                editBusiness.getSolutionName(), JSONObject.toJSONString(editBusiness.getBusinessList()),
                JSONObject.toJSONString(editBusiness.getSiteList()));
        } catch (ServiceException e) {
            result.setResultCode(RestConstant.FAILURE_CODE);
            result.setResultMessage(
                ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.business.failed.advice"));
        }
        LOGGER.debug("entry queryInitEditPage() end.");
        return result;
    }


    public ResponseEntity editOverviewForMm(HttpContext context, OverviewEditParamForMm editOverview) throws ServiceException {
        LOGGER.debug("entry editOverviewForMm()");
        ResponseEntity result = new ResponseEntity();
        try {
            result = topoOverViewService.editOverviewForMm(context, editOverview.getSolutionId(),
                    editOverview.getSolutionName(), JSONObject.toJSONString(editOverview.getBusinessList()),
                    JSONObject.toJSONString(editOverview.getChannelList()), JSONObject.toJSONString(editOverview.getStripeTeamList()));
        } catch (ServiceException e) {
            result.setResultCode(RestConstant.FAILURE_CODE);
            result.setResultMessage(
                    ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.business.failed.advice"));
        }
        LOGGER.debug("entry editOverviewForMm() end.");
        return result;
    }

    @Override
    public ResponseEntity saveHomePageConfigurationData(HttpContext context,
        HomePageConfigurationParam saveHomePageConfigurationData) throws ServiceException {
        LOGGER.info("saveHomePageConfigurationData start.");
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            RestfulParametes restfulParametes = new RestfulParametes();
            restfulParametes.setRawData(JSON.toJSONString(saveHomePageConfigurationData));
            RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, HOME_PAGE_CONFIG,
                restfulParametes, null);
            if (getResponseEntity(responseEntity, response) != null) {
                return responseEntity;
            }
            HomePageConfigurationResponse result = JSON.parseObject(response.getResponseContent(),
                HomePageConfigurationResponse.class, JSONReader.Feature.SupportSmartMatch);
            setResponseEntityData(responseEntity, response, result);
        } catch (ServiceException e) {
            LOGGER.error("queryOverviewGrid failed, error is ", e);
        }
        LOGGER.info("entry queryOverviewGrid end.");
        return responseEntity;
    }

    private IndicatorDisplayResults queryTipsIndicatorHistory(OverViewIndicatorParamForMm param) {
        List<BusinessIndicator> indicators;
        if (param.getStripeId() != null) {
            // 条带的tps指标取第三层linkIndicatorList，加上rsu或者gsu的dn
            return getStripeIndicators(param);
        } else {
            // 第三方和接入渠道的指标通过同类匹配获得
            indicators = getBusinessOrChannelIndicators(param);
        }

        if (CollectionUtils.isEmpty(indicators)) {
            LOGGER.error("[queryTipsIndicatorHistory] could not find any indicator, indicatorId = {}",
                param.getIndicatorId());
            return new IndicatorDisplayResults();
        }

        List<SiteHistoryResults> siteHistoryResultList = getPmDataAndAggregate(param.getCurrentTime(), indicators, new HashMap<>(), true);
        IndicatorDisplayResults result = new IndicatorDisplayResults();
        result.getSiteHistoryList().addAll(siteHistoryResultList);
        return result;
    }

    private IndicatorDisplayResults queryCommonIndicatorHistory(OverViewIndicatorParamForMm param, int type) {
        // 查前topN的instanceId
        Map<Integer, List<Integer>> modelInsMap = getModelInsMap(param.getCurrentTime(), param.getInstanceIds().get(0));
        // 获取普通指标
        List<BusinessIndicator> commonIndList = getCommonIndList(param, type, modelInsMap);
        if (BusinessTopoConstant.CHANNEL_TYPE_ID == type) {
            // 接入渠道要排除sftp
            commonIndList = getMaxQuantityBusinessIndicators(commonIndList);
        }
        if (CollectionUtils.isEmpty(commonIndList)) {
            LOGGER.error("[queryIndicatorResultsForMm] could not find any common indicator by instanceIds: {}",
                commonIndList);
            return new IndicatorDisplayResults();
        }

        List<Indicator> commonIndicatorList = JSONArray.parseArray(JSON.toJSONString(commonIndList), Indicator.class,
            JSONReader.Feature.SupportSmartMatch);

        // 性能指标挂所有网元之后，会出现第三方dn不一致的情况，使用出现次数最多的dn，避免排序时受到dn数量影响
        handleSameDn(commonIndicatorList);

        // 调用性能topN接口获取topN指标
        String indicatorType = type == BusinessTopoConstant.BUSINESS_TYPE_ID ? BusinessTopoConstant.TOPN.TOP_N_TYPE_BUSINESS : BusinessTopoConstant.TOPN.TOP_N_TYPE_CHANNEL;
        List<TopNViewMeasPMData> topNPmDataResults = PmDataHandleUtil.getTopNResult(commonIndicatorList,
            param.getCurrentTime() == 0 ? null : param.getCurrentTime(), PmDataHandleUtil.getTopNum(indicatorType));
        // indicatorId去除moType作为key
        Map<String, String> indicatorValueMap = getIndicatorValueMap(topNPmDataResults);
        // 过滤并设置返回值
        TopIndicatorResult topIndicatorResult = filterAndSetIndicator(commonIndicatorList, indicatorValueMap,
            param.getCurrentTime(), type);
        // 将result按value的值从大到小进行排序，保留topN
        sortAndKeepTopN(topIndicatorResult, indicatorType);

        getIndicatorDistributionResultList(topNPmDataResults);

        Map<Integer, List<Indicator>> indicatorsMap = topIndicatorResult.getTopIndicatorValueMap().keySet().stream()
            .collect(Collectors.groupingBy(Indicator::getInstanceId));
        IndicatorDisplayResults result = new IndicatorDisplayResults();
        Map<Integer, String> insIdNameMap = type == BusinessTopoConstant.CHANNEL_TYPE_ID
            ? insModelDao.queryInstanceListByIdList(modelInsMap.get(type), param.getCurrentTime())
            .stream()
            .collect(Collectors.toMap(BusinessInstanceModelDB::getInstanceId,
                model -> model.getExtentAttr(BusinessTopoConstant.CHANNEL_NAME_ATTR_KEY).getAttrValue()))
            : insModelDao.queryInstanceListByIdList(modelInsMap.get(type), param.getCurrentTime())
                .stream()
                .collect(Collectors.toMap(BusinessInstanceModelDB::getInstanceId,
                    model -> model.getExtentAttr(BusinessTopoConstant.BUSINESS_NAME_ATTR_KEY).getAttrValue()));
        for (List<Indicator> indicatorGroup : indicatorsMap.values()) {
            List<BusinessIndicator> businessIndicatorGroup = JSONArray.parseArray(JSON.toJSONString(indicatorGroup),
                BusinessIndicator.class, JSONReader.Feature.SupportSmartMatch);
            List<SiteHistoryResults> resultGroup = getPmDataAndAggregate(param.getCurrentTime(), businessIndicatorGroup,
                insIdNameMap, false);
            resultGroup = resultGroup.stream()
                .filter(siteResult -> StringUtils.isNotEmpty(siteResult.getSiteName()))
                .collect(Collectors.toList());
            result.getSiteHistoryList().addAll(resultGroup);
        }
        return result;
    }

    private void handleSameDn(List<Indicator> commonIndicatorList) {
        List<String> dns = commonIndicatorList.stream().map(Indicator::getDn).collect(Collectors.toList());
        Optional<String> mostFrequentDn = dns.stream()
            .collect(Collectors.groupingBy(s -> s, Collectors.counting()))
            .entrySet()
            .stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey);

        mostFrequentDn.ifPresent(dn -> commonIndicatorList.forEach(indicator -> indicator.setDn(dn)));
    }

    private List<BusinessIndicator> getCommonIndList(OverViewIndicatorParamForMm param, int type,
        Map<Integer, List<Integer>> modelInsMap) {
        // 获取所有普通指标
        List<BusinessIndicator> commonIndList = indicatorDao.queryHisIndInsIdList(modelInsMap.get(type),
                param.getCurrentTime())
            .stream()
            .filter(ind -> BusinessTopoConstant.INDICATOR_TYPE_COMMON.equals(ind.getIndicatorDisplayType()))
            .filter(ind -> Objects.equals(param.getMeasTypeKey(), ind.getMeasTypeKey()) && Objects.equals(
                param.getMeasUnitKey(), ind.getMeasUnitKey()) && Objects.equals(param.getMoType(), ind.getMoType()))
            .collect(Collectors.toList());
        if (!ContextUtils.getContext().getAdmin()) {
            commonIndList = commonIndList.stream()
                .filter(indicator -> AuthUtils.getAuthDns().contains(indicator.getDn()))
                .collect(Collectors.toList());
        }
        return commonIndList;
    }

    private List<SiteHistoryResults> getPmDataAndAggregate(long currentTime, List<BusinessIndicator> indicators, Map<Integer, String> insIdNameMap, boolean needLastWeek) {
        // 构造入参，查性能数据
        List<BusinessIndicator> filledIndicators = pmIndicatorInstanceService.fillIndexNameAndUnit(indicators);
        Map<String, Long> timeRanges = PmDataHandleUtil.getIndQueryTimeRangeInPmFormat(currentTime,
            INDICATOR_RANGE, needLastWeek);
        List<Indicator> indicatorList = JSONArray.parseArray(JSON.toJSONString(filledIndicators), Indicator.class,
            JSONReader.Feature.SupportSmartMatch);
        List<QueryDeletedHistoryData> queryDeletedHistoryData = pmRequestService.getQueryDeletedHistoryData(
            indicatorList, timeRanges);
        DeletedHistoryData performanceResult = pmRequestService.getDeletedHistoryDataFormPerformance(
            queryDeletedHistoryData);

        // 处理性能数据，进行汇聚
        Map<String, List<PerformanceIndexValue>> indexValues = pmRequestService.getPerformanceIndexValueList(
            performanceResult, filledIndicators, false);
        return aggregateStripeIndexDatas(filledIndicators, indexValues, currentTime, insIdNameMap);
    }

    private List<BusinessIndicator> getBusinessOrChannelIndicators(OverViewIndicatorParamForMm param) {
        Integer instanceId = param.getBusinessId() == null
            ? param.getChannelId()
            : param.getBusinessId();
        if (instanceId == null) {
            LOGGER.error("business instance is null, indicatorId = {}", param.getIndicatorId());
            return Collections.emptyList();
        }
        BusinessIndicator businessIndicator = indicatorDao.queryByIndicatorIdAndInstanceId(
            param.getIndicatorId(), instanceId, param.getCurrentTime());
        if (businessIndicator == null) {
            LOGGER.error("query business indicator is null, indicatorId = {}, instanceId = {}",
                param.getIndicatorId(), instanceId);
            return Collections.emptyList();
        }
        // 找出gsu,rsu01,rsu02……的指标，用于汇聚加和
        List<BusinessIndicator> indicators = indicatorDao.queryHisIndInsIdList(
            Collections.singletonList(instanceId), param.getCurrentTime());
        indicators = indicators.stream()
            .filter(indicator -> Objects.equals(indicator.getMeasUnitKey(), businessIndicator.getMeasUnitKey())
                && Objects.equals(indicator.getMeasTypeKey(), businessIndicator.getMeasTypeKey())
                && Objects.equals(indicator.getOriginalValue(), businessIndicator.getOriginalValue())
                && Objects.equals(indicator.getMoType(), businessIndicator.getMoType()))
            .collect(Collectors.toList());

        if (!ContextUtils.getContext().getAdmin()) {
            indicators = indicators.stream()
                .filter(indicator -> AuthUtils.getAuthDns().contains(indicator.getDn()))
                .collect(Collectors.toList());
        }
        return indicators;
    }

    private IndicatorDisplayResults getStripeIndicators(OverViewIndicatorParamForMm param) {
        // 前台传的stripeId是条带分组的id，需要向下查询出rsu+csu实例
        List<BusinessInstanceModelDB> stripes = insModelDao.queryNextLevelInstanceByTimeLine(param.getStripeId(), param.getCurrentTime());
        stripes = stripes.stream()
            .filter(modelDb -> StringUtils.isNotEmpty(modelDb.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue())
                && (modelDb.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue().toLowerCase(Locale.ROOT).contains(RSU))
                || modelDb.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue().toLowerCase(Locale.ROOT).contains(GSU))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stripes)) {
            LOGGER.error("query model indicator is null, indicatorId = {}, stripeTeamId = {}",
                param.getIndicatorId(), param.getStripeId());
            return new IndicatorDisplayResults();
        }
        BusinessInstanceModelDB siteModel = stripes.get(0);

        // 查出该类型的tips指标，只会查出来1条
        List<BusinessIndicator> indicators = getIndicatorsFromModelAttr(stripes, BusinessTopoConstant.LINK_INDICATOR_LIST_ATTR_KEY, false);
        if (CollectionUtils.isEmpty(indicators)) {
            LOGGER.error("query stripe indicator is empty, stripeTeamId = {}", param.getInstanceIds());
            return new IndicatorDisplayResults();
        }
        BusinessIndicator indicator = indicators.get(0);
        indicator.setDn(siteModel.getDn());

        if (Objects.nonNull(indicator.getIsAggregateByAllSolution()) && indicator.getIsAggregateByAllSolution()) {
            String siteName = siteModel.getExtentAttr(BusinessTopoConstant.MM_SITE_NAME).getAttrValue();
            String groupName = siteModel.getExtentAttr(BusinessTopoConstant.MM_HWS_GROUP_NAME).getAttrValue();
            String stripeName = siteModel.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue();
            indicator.setOriginalValue(indicator.getOriginalValue() + siteName + LINE + groupName + LINE + stripeName);
            Map<String, String> measUnitDnMap = TopoImportSolutionAdapter.getMmMeasUnitDnMap(indicator.getMoType());
            indicator.setDn(measUnitDnMap.get(indicator.getMeasUnitKey()));
        }

        List<SiteHistoryResults> siteHistoryResultList = getPmDataAndAggregate(param.getCurrentTime(), indicators, new HashMap<>(), true);
        IndicatorDisplayResults result = new IndicatorDisplayResults();
        result.getSiteHistoryList().addAll(siteHistoryResultList);
        return result;
    }

    private IndicatorDisplayResults queryIndicatorByOriginalValues(OverViewIndicatorParamForMm param,
        BusinessIndicator indicator) {
        if (!ContextUtils.getContext().getAdmin() && !AuthUtils.getAuthDns().contains(indicator.getDn())) {
            LOGGER.error("query stripe indicator is empty, stripeTeamId = {}",
                param.getInstanceIds());
            return new IndicatorDisplayResults();
        }

        // 获取测量对象
        List<String> orignalValueList = new ArrayList<>();
        boolean needLastWeek;
        if ("*".equals(indicator.getOriginalValue())) {
            orignalValueList = getOriginalValueList(param, indicator);
            needLastWeek = false;
        } else {
            needLastWeek = true;
            if (indicator.getOriginalValue() != null) {
                orignalValueList = handleOriginalValues(Collections.singletonList(indicator.getOriginalValue()));
            }
        }

        IndicatorDisplayResults result = new IndicatorDisplayResults();
        List<BusinessIndicator> indicators = Collections.singletonList(indicator);
        if (orignalValueList.isEmpty()) {
            List<SiteHistoryResults> siteHistoryResultList = getPmDataAndAggregate(param.getCurrentTime(), indicators,
                new HashMap<>(), true);
            result.getSiteHistoryList().addAll(siteHistoryResultList);
            return result;
        }

        // 根据每个测量对象进行查询
        List<MeasObjectInfo> objectInfo = pmRequestService.getMeasObjectByMoType(indicator.getMoType(),
            indicator.getMeasUnitKey());
        for (String measObject : orignalValueList) {
            try {
                indicators.forEach(ind -> {
                    ind.setOriginalValue(measObject);
                    ind.setDisplayValue(pmIndicatorInstanceService.getDisplayValue(objectInfo, measObject, true));
                });
                List<SiteHistoryResults> siteHistoryResultList = getPmDataAndAggregate(param.getCurrentTime(),
                    indicators, new HashMap<>(), needLastWeek);
                result.getSiteHistoryList().addAll(siteHistoryResultList);
            } catch (Exception e) {
                LOGGER.error("getPmDataAndAggregate catch error!", e);
            }
        }
        return result;
    }

    private List<String> getOriginalValueList(OverViewIndicatorParamForMm param, BusinessIndicator indicator) {
        List<String> orignalValueList = new ArrayList<>();
        // 调用性能topN接口获取topN指标,从中获取originalValue
        List<TopNViewMeasPMData> topNPmDataResults = PmDataHandleUtil.getClusterIndicatorTopNResult(
            JSONArray.parseArray(JSON.toJSONString(Collections.singletonList(indicator)), Indicator.class,
                JSONReader.Feature.SupportSmartMatch),
            (param.getCurrentTime() == null || param.getCurrentTime() == 0) ? null : param.getCurrentTime(), true, BusinessTopoConstant.TOPN.TOP_N_TYPE_STRIPE, null, null);
        for (TopNViewMeasPMData data : topNPmDataResults) {
            String originalValue;
            if (data.getMeasObject() != null && CollectionUtils.isNotEmpty(data.getMeasObject().getKeys())) {
                List<String> oriValueList = new ArrayList<>();
                for (int i = 0; i < data.getMeasObject().getKeys().size(); i++) {
                    oriValueList.add(
                        data.getMeasObject().getKeys().get(i) + "=" + data.getMeasObject().getValues().get(i));
                }
                // 拼接所有的值
                originalValue = String.join(",", oriValueList);
                orignalValueList.add(originalValue);
            } else {
                LOGGER.error("get MeasObject key is null! ");
            }
        }
        return orignalValueList;
    }

    private List<SiteHistoryResults> aggregateStripeIndexDatas(List<BusinessIndicator> indicators,
        Map<String, List<PerformanceIndexValue>> indexValues, long currentTime, Map<Integer, String> insIdNameMap) {
        List<PerformanceIndexValue> aggregateIndexValues = PmDataHandleUtil.aggregateSiteHistoryData(indexValues);
        // 设置汇聚结果
        BusinessIndicator indicator = indicators.get(0);
        SiteHistoryResults siteHistoryResult = new SiteHistoryResults();
        if (StringUtils.isEmpty(indicator.getDisplayValue()) && StringUtils.isNotEmpty(indicator.getOriginalValue())) {
            List<MeasObjectInfo> objectInfo = pmRequestService.getMeasObjectByMoType(indicator.getMoType(),
                indicator.getMeasUnitKey());
            String originalValue = indicator.getOriginalValue().replace("=", "<=>").replace(",", "<,>");
            indicator.setDisplayValue(pmIndicatorInstanceService.getDisplayValue(objectInfo, originalValue, true));
        }
        siteHistoryResult.setSiteName(filterDisplayValue(getIndicatorDisplayName(indicator, insIdNameMap)));
        siteHistoryResult.setIndexName(indicator.getIndexName());
        long endTime = 0 == currentTime ? System.currentTimeMillis() : currentTime;
        ConfigData configData = ConfigurationUtil.getConfigDataByName(INDICATOR_RANGE);
        siteHistoryResult.setStartTime(String.valueOf(endTime - Long.parseLong(configData.getValue())));
        siteHistoryResult.setEndTime(String.valueOf(endTime));
        siteHistoryResult.setIndexUnit(indicator.getUnit());
        siteHistoryResult.setHistoryTotalCount(aggregateIndexValues.size());

        // 获取同环比列表
        long todayStartTime = endTime - Long.parseLong(configData.getValue());
        Map<String, Pair<Long, Long>> rangeNameMap = PmDataHandleUtil.getTimeRange(todayStartTime, endTime);
        Map<String, List<PerformanceIndexValue>> rangeIndexValue = PmDataHandleUtil.indexValueGroupBy(
            aggregateIndexValues, rangeNameMap);
        siteHistoryResult.setComparativeValueMap(rangeIndexValue);
        siteHistoryResult.setMoType(indicator.getMoType());
        siteHistoryResult.setMeasUnitKey(indicator.getMeasUnitKey());
        siteHistoryResult.setMeasTypeKey(indicator.getMeasTypeKey());
        if (StringUtils.isNotEmpty(indicator.getOriginalValue())) {
            String replace = indicator.getOriginalValue().replace("=", "<=>").replace(",", "<,>");
            siteHistoryResult.setOriginalValue(replace);
        }
        List<String> dnList = indicators.stream().map(BusinessIndicator::getDn).collect(Collectors.toList());
        siteHistoryResult.setDnList(dnList);
        return Collections.singletonList(siteHistoryResult);
    }

    private String getIndicatorDisplayName(BusinessIndicator indicator, Map<Integer, String> insIdNameMap) {
        // 接入渠道和第三方返回对应的businessName
        if (insIdNameMap.containsKey(indicator.getInstanceId())) {
            return insIdNameMap.get(indicator.getInstanceId());
        }
        // 系统业务返回displayValue
        return indicator.getDisplayValue();
    }

    private IndicatorDisplayResults queryBusinessOrChannelIndicatorData(OverViewIndicatorParamForMm param, int type) {
        // 如果传了indicatorId，说明选中的是单个第三方或接入渠道，与tips曲线展示相同
        if (StringUtils.isNotEmpty(param.getIndicatorId())) {
            return queryTipsIndicatorHistory(param);
        }

        // 如果查的是普通指标，先查指标，再查topN, 再查数据
        if (Objects.equals(param.getIndicatorDisplayType(), BusinessTopoConstant.INDICATOR_TYPE_COMMON)) {
            return queryCommonIndicatorHistory(param, type);
        }

        // 如果没传instanceIds说明没找到topN，直接返回空结果
        if (CollectionUtils.isEmpty(param.getInstanceIds())) {
            return new IndicatorDisplayResults();
        }

        // 如果没传indicatorId，说明未选中，查前台返回的topN的instanceId
        List<BusinessInstanceModelDB> instances = insModelDao.queryInstanceListByIdList(param.getInstanceIds(), param.getCurrentTime());
        Map<Integer, String> insIdNameMap = type == BusinessTopoConstant.CHANNEL_TYPE_ID
            ? instances.stream().collect(Collectors.toMap(BusinessInstanceModelDB::getInstanceId,
                model -> model.getExtentAttr(BusinessTopoConstant.CHANNEL_NAME_ATTR_KEY).getAttrValue()))
            : instances.stream().collect(Collectors.toMap(BusinessInstanceModelDB::getInstanceId,
                model -> model.getExtentAttr(BusinessTopoConstant.BUSINESS_NAME_ATTR_KEY).getAttrValue()));

        List<BusinessIndicator> indicators = JSONArray.parseArray(param.getIndicatorList(), BusinessIndicator.class)
            .stream()
            .filter(indicator -> Objects.equals(indicator.getMoType(), param.getMoType()) && Objects.equals(
                indicator.getMeasUnitKey(), param.getMeasUnitKey()) && Objects.equals(indicator.getMeasTypeKey(),
                param.getMeasTypeKey()))
            .collect(Collectors.toList());

        Map<Integer, List<BusinessIndicator>> indicatorsMap = indicators.stream()
            .collect(Collectors.groupingBy(BusinessIndicator::getInstanceId));
        IndicatorDisplayResults result = new IndicatorDisplayResults();
        for (List<BusinessIndicator> indicatorGroup : indicatorsMap.values()) {
            List<SiteHistoryResults> resultGroup = getPmDataAndAggregate(param.getCurrentTime(), indicatorGroup,
                insIdNameMap, false);
            resultGroup = resultGroup.stream()
                .filter(siteResult -> StringUtils.isNotEmpty(siteResult.getSiteName()))
                .collect(Collectors.toList());
            result.getSiteHistoryList().addAll(resultGroup);
        }
        return result;
    }

    private IndicatorDisplayResults queryStripeIndicatorData(OverViewIndicatorParamForMm param) {
        // 如果传入的是条带单元指标，查条带单元下面对应实例的指标
        if (param.getAppSite() != null) {
            handleOriginalValues(param);
            return queryStripeUnitIndicatorHistory(param);
        }

        // 无论传入的是不是主指标，只要查的是全部条带的指标，就查第2层关联的指标
        if (param.getInstanceIds().size() != 1) {
            return queryNormalMutipleIndicatorHistory(param);
        }

        // 如果传入的不是主指标，而且查的是单个条带的指标，就查第3层关联的指标
        if (param.getIndicatorDisplayType() != null && param.getIndicatorDisplayType() != 1 && param.getInstanceIds().size() == 1) {
            return queryNormalSingleIndicatorHistory(param);
        }

        // 如果传入的是主指标，查第3层的linkIndicatorList
        return queryMainIndicatorHistory(param);
    }

    private IndicatorDisplayResults queryMainIndicatorHistory(OverViewIndicatorParamForMm param) {
        List<BusinessInstanceModelDB> stripeModels = insModelDao.queryNextLevelInstanceByTimeLineBatch(
            param.getInstanceIds(), param.getCurrentTime());
        stripeModels = stripeModels.stream()
            .filter(modelDb ->
                StringUtils.isNotEmpty(modelDb.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue())
                    && (modelDb.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE)
                    .getAttrValue()
                    .toLowerCase(Locale.ROOT)
                    .contains(RSU)) || modelDb.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue().toLowerCase(Locale.ROOT).contains(GSU))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stripeModels)) {
            LOGGER.error("query stripe models is null, stripeTeamId = {}", param.getInstanceIds());
            return new IndicatorDisplayResults();
        }
        BusinessInstanceModelDB siteModel = stripeModels.get(0);

        // 查出该类型的tips指标，只会查出来1条
        List<BusinessIndicator> indicators = getIndicatorsFromModelAttr(stripeModels, BusinessTopoConstant.LINK_INDICATOR_LIST_ATTR_KEY, false);
        if (CollectionUtils.isEmpty(indicators)) {
            LOGGER.error("query stripe indicator is empty, stripeTeamId = {}", param.getInstanceIds());
            return new IndicatorDisplayResults();
        }
        BusinessIndicator indicator = indicators.get(0);
        List<BusinessIndicator> indicatorList = new ArrayList<>();
        BusinessIndicator copyIndicator = JSON.parseObject(JSON.toJSONString(indicator), BusinessIndicator.class);
        copyIndicator.setDn(siteModel.getDn());
        if (Objects.nonNull(copyIndicator.getIsAggregateByAllSolution()) && copyIndicator.getIsAggregateByAllSolution()) {
            String siteName = siteModel.getExtentAttr(BusinessTopoConstant.MM_SITE_NAME).getAttrValue();
            String groupName = siteModel.getExtentAttr(BusinessTopoConstant.MM_HWS_GROUP_NAME).getAttrValue();
            String stripeName = siteModel.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue();
            copyIndicator.setOriginalValue(copyIndicator.getOriginalValue() + siteName + LINE + groupName + LINE + stripeName);
            Map<String, String> measUnitDnMap = TopoImportSolutionAdapter.getMmMeasUnitDnMap(indicator.getMoType());
            copyIndicator.setDn(measUnitDnMap.get(copyIndicator.getMeasUnitKey()));
        }
        indicatorList.add(copyIndicator);

        List<SiteHistoryResults> siteHistoryResultList = getPmDataAndAggregate(param.getCurrentTime(), indicatorList, new HashMap<>(), true);
        IndicatorDisplayResults result = new IndicatorDisplayResults();
        result.getSiteHistoryList().addAll(siteHistoryResultList);
        return result;
    }

    private static void handleOriginalValues(OverViewIndicatorParamForMm param) {
        List<String> processOriginalValues = param.getOriginalValues().stream()
            .map(value -> value.replace("<=>", "=").replace("<,>", ","))
            .collect(Collectors.toList());
        param.setOriginalValues(processOriginalValues);
    }

    private static List<String> handleOriginalValues(List<String> originalValueList) {
        return originalValueList.stream()
            .map(value -> value.replace("<=>", "=").replace("<,>", ","))
            .collect(Collectors.toList());
    }

    private IndicatorDisplayResults queryNormalMutipleIndicatorHistory(OverViewIndicatorParamForMm param) {
        List<BusinessIndicator> indicators = indicatorDao.queryHisIndInsIdList(param.getInstanceIds(), param.getCurrentTime());
        indicators = indicators.stream()
            .filter(indicator -> Objects.equals(indicator.getMoType(), param.getMoType())
                && Objects.equals(indicator.getMeasUnitKey(), param.getMeasUnitKey())
                && Objects.equals(indicator.getMeasTypeKey(), param.getMeasTypeKey()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(indicators)) {
            LOGGER.error("query stripe indicator is empty, stripeTeamId = {}", param.getInstanceIds());
            return new IndicatorDisplayResults();
        }
        return queryIndicatorByOriginalValues(param, indicators.get(0));
    }

    private IndicatorDisplayResults queryNormalSingleIndicatorHistory(OverViewIndicatorParamForMm param) {
        List<BusinessInstanceModelDB> stripeModels = insModelDao.queryNextLevelInstanceByTimeLineBatch(
            param.getInstanceIds(), param.getCurrentTime());
        stripeModels = stripeModels.stream()
            .filter(modelDb -> StringUtils.isNotEmpty(modelDb.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue())
                && (modelDb.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue().toLowerCase(Locale.ROOT).contains(RSU))
                || modelDb.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue().toLowerCase(Locale.ROOT).contains(GSU))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stripeModels)) {
            LOGGER.error("query stripe models is null, stripeTeamId = {}", param.getInstanceIds());
            return new IndicatorDisplayResults();
        }
        Integer stripeInstanceId = stripeModels.get(0).getInstanceId();
        List<BusinessIndicator> indicators = indicatorDao.queryHisIndInsIdList(Collections.singletonList(stripeInstanceId), param.getCurrentTime());
        indicators = indicators.stream()
            .filter(indicator -> Objects.equals(indicator.getMoType(), param.getMoType())
                && Objects.equals(indicator.getMeasUnitKey(), param.getMeasUnitKey())
                && Objects.equals(indicator.getMeasTypeKey(), param.getMeasTypeKey()))
            .collect(Collectors.toList());
        return queryIndicatorByOriginalValues(param, indicators.get(0));
    }

    private IndicatorDisplayResults queryStripeUnitIndicatorHistory(OverViewIndicatorParamForMm param) {
        List<BusinessInstanceModelDB> stripeModels = insModelDao.queryNextLevelInstanceByTimeLineBatch(
            param.getInstanceIds(), param.getCurrentTime());
        if (CollectionUtils.isEmpty(stripeModels)) {
            LOGGER.error("query stripe models is null, stripeTeamId = {}", param.getInstanceIds());
            return new IndicatorDisplayResults();
        }

        // 过滤出和传入的指标相同的指标，取第0个使用
        List<BusinessIndicator> indicators = getIndicatorsFromModelAttr(stripeModels, BusinessTopoConstant.STRIPE_INDICATOR_LIST_ATTR_KEY, false).stream()
            .filter(indicator -> indicator.getMoType().equals(param.getMoType()) && indicator.getMeasUnitKey()
                .equals(param.getMeasUnitKey()) && indicator.getMeasTypeKey().equals(param.getMeasTypeKey()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(indicators)) {
            LOGGER.error("query stripe indicator is empty, stripeTeamId = {}", param.getInstanceIds());
            return new IndicatorDisplayResults();
        }

        BusinessIndicator indicator = indicators.get(0);

        // 根据网元类型查出对应条带和条带单元下的网元，取第0个查性能
        List<BusinessInstanceModelDB> stripeGroupModel = insModelDao.queryInstanceListByIdList(param.getInstanceIds(), param.getCurrentTime());
        if (CollectionUtils.isEmpty(stripeGroupModel)) {
            LOGGER.error("query stripe group models is null, stripeTeamId = {}", param.getInstanceIds());
            return new IndicatorDisplayResults();
        }
        String stripeName = stripeGroupModel.get(0).getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE_GROUP_NAME).getAttrValue();
        String siteName = stripeModels.get(0).getExtentAttr(BusinessTopoConstant.MM_SITE_NAME).getAttrValue();
        String groupName = stripeModels.get(0).getExtentAttr(BusinessTopoConstant.MM_HWS_GROUP_NAME).getAttrValue();

        // 如果是跨解决方案汇聚任务，需要获取性能设置的dn，并拼接测量对象
        if (Objects.nonNull(indicator.getIsAggregateByAllSolution()) && indicator.getIsAggregateByAllSolution()) {
            // 找到性能挂载的那个网元
            String unitDn = PmDataHandleUtil.getStripeUnitDn(indicator, siteName, groupName, stripeName, param.getAppSite());
            if (StringUtils.isEmpty(unitDn)) {
                Map<String, String> measUnitDnMap = TopoImportSolutionAdapter.getMmMeasUnitDnMap(indicator.getMoType());
                unitDn = measUnitDnMap.get(indicator.getMeasUnitKey());
            }
            if (Objects.nonNull(indicator.getOriginalValue()) && !"*".equals(indicator.getOriginalValue())) {
                indicator.setOriginalValue(indicator.getOriginalValue() + siteName + "_" + groupName + "_" + stripeName + "_" + param.getAppSite());
            }
            indicator.setDn(unitDn);
        } else {
            List<ManagedObject> insMos = EamUtil.queryMosByMoType(indicator.getMoType());
            insMos = insMos.stream()
                .filter(mo -> stripeName.equals(mo.getHwsStripe()) && param.getAppSite().equals(mo.getHwsAppSite())
                    && Objects.equals(siteName, mo.getHwsSiteName()) && Objects.equals(groupName, mo.getHwsGroupName()))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(insMos)) {
                LOGGER.error("query stripe unit mos is null, stripeTeamId = {}", param.getInstanceIds());
                return new IndicatorDisplayResults();
            }
            String dn = insMos.get(0).getDN().getValue();
            indicator.setDn(dn);
        }
        return queryIndicatorByOriginalValues(param, indicator);
    }

    @Getter
    static class TopIndicatorResult {

        public void setTopNameValueMap(Map<String, String> topNameValueMap) {
            this.topNameValueMap = topNameValueMap;
        }

        public void setTopIndicatorValueMap(Map<Indicator, String> topIndicatorValueMap) {
            this.topIndicatorValueMap = topIndicatorValueMap;
        }

        Map<String, String> topNameValueMap = new HashMap<>();

        Map<Indicator, String> topIndicatorValueMap = new HashMap<>();
    }

    @Override
    public ResponseEntity queryMmTopNConfig(HttpContext context) {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            BusinessTopNConfig businessTopNConfig = new BusinessTopNConfig();
            businessTopNConfig.setBusinessTopNConfig(Integer.parseInt(ConfigurationUtil.getConfigDataByName(BusinessTopoConstant.TOPN.TOP_N_BUSINESS).getValue()));
            businessTopNConfig.setChannelTopNConfig(Integer.parseInt(ConfigurationUtil.getConfigDataByName(BusinessTopoConstant.TOPN.TOP_N_CHANNEL).getValue()));
            businessTopNConfig.setStripeTopNConfig(Integer.parseInt(ConfigurationUtil.getConfigDataByName(BusinessTopoConstant.TOPN.TOP_N_STRIPE).getValue()));
            responseEntity.setResultCode(RestConstant.SUCCESS_CODE);
            responseEntity.setData(businessTopNConfig);
        } catch (Exception e) {
            LOGGER.error("queryMmTopNConfig error!", e);
            responseEntity.setResultCode(RestConstant.FAILURE_CODE);
            responseEntity.setResultMessage("query topN config error");
        }
        return responseEntity;
    }

    @Override
    public ResponseEntity editMmTopNConfig(HttpContext context, BusinessTopNConfig businessTopNConfig) throws ServiceException {
        // 记录操作日志
        String remoteAddr = HttpUtil.getRemoteAddr(context.getHttpServletRequest());
        ResponseEntity responseEntity = new ResponseEntity();
        StringBuilder logDetail = new StringBuilder();
        if (businessEditLock.tryLock()) {
            try {
                if (businessTopNConfig.getBusinessTopNConfig() != null) {
                    updateConfigDataIfNecessary(BusinessTopoConstant.TOPN.TOP_N_BUSINESS, businessTopNConfig.getBusinessTopNConfig(), logDetail);
                }
                if (businessTopNConfig.getChannelTopNConfig() != null) {
                    updateConfigDataIfNecessary(BusinessTopoConstant.TOPN.TOP_N_CHANNEL, businessTopNConfig.getChannelTopNConfig(), logDetail);
                }
                if (businessTopNConfig.getStripeTopNConfig() != null) {
                    updateConfigDataIfNecessary(BusinessTopoConstant.TOPN.TOP_N_STRIPE, businessTopNConfig.getStripeTopNConfig(), logDetail);
                }
                responseEntity.setResultCode(RestConstant.SUCCESS_CODE);
                responseEntity.setResultMessage("edit topN config success");
                recordOpLog(false, logDetail, remoteAddr);
            } catch (Exception e) {
                LOGGER.error("editMmTopNConfig error!", e);
                responseEntity.setResultCode(RestConstant.FAILURE_CODE);
                responseEntity.setResultMessage("edit topN config error");
                recordOpLog(true, logDetail, remoteAddr);
            } finally {
                businessEditLock.unlock();
            }
        } else {
            responseEntity.setResultCode(RestConstant.FAILURE_CODE);
            responseEntity.setResultMessage(ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.business.waiting"));
        }
        return responseEntity;
    }

    private void updateConfigDataIfNecessary(String configName, Integer configValue, StringBuilder logDetail) {
        ConfigData configData = ConfigurationUtil.getConfigDataByName(configName);
        String currentValue = configData.getValue();
        String newValue = String.valueOf(configValue);
        if (!currentValue.equals(newValue)) {
            configData.setValue(newValue);
            ConfigurationUtil.updateConfigDataByName(configData);
            logDetail.append(configName).append(COLON).append(configValue).append(DITIAL_SUFFIX);
        }
    }

    private void recordOpLog(boolean success, StringBuilder logDetail, String remoteAddr) throws ServiceException {
        // 如果没有修改topN配置，不记录操作日志
        if (StringUtils.isEmpty(logDetail)) {
            return;
        }
        if (logDetail.toString().endsWith(DITIAL_SUFFIX)) {
            logDetail.delete(logDetail.length() - DITIAL_SUFFIX.length(), logDetail.length());
        }
        LogResult result = success ? LogResult.SUCCESSFUL : LogResult.FAILURE;
        LogUtil.recordCreateOperationLog(ResourceUtil.getMessage("com.huawei.i2000.topo.service.source"),
            ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.topn.success") + logDetail, result,
            ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.topn.operation"), remoteAddr);
    }

    private String getStripeNameFromId(List<Integer> insIdList, Integer stripeId, long timeStamp) {
        List<BusinessInstanceModelDB> indicatorModels = getStripeSiteModels(insIdList, stripeId, timeStamp);
        if (CollectionUtils.isEmpty(indicatorModels)) {
            LOGGER.error("getStripeNameFromId stripeSite models is empty, stripeTeamId = {}", stripeId);
            return null;
        }
        String name = indicatorModels.get(0).getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue();
        if (StringUtils.isEmpty(name)) {
            LOGGER.error("cannot find stripe name, dn = {}", indicatorModels.get(0).getDn());
            return null;
        }
        if (name.equals(CSU_00)) {
            return GSU;
        }
        return name.replace(CSU, RSU);
    }

}

