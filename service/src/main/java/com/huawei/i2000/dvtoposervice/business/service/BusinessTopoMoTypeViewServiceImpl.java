/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.TypeReference;
import com.huawei.bsp.as.util.Pair;
import com.huawei.bsp.deploy.util.DefaultEnvUtil;
import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.cbb.common.CollectionUtil;
import com.huawei.i2000.dvtoposervice.bean.pm.HistoryDatas;
import com.huawei.i2000.dvtoposervice.bean.pm.HistoryIndexValue;
import com.huawei.i2000.dvtoposervice.bean.pm.HistoryQueryData;
import com.huawei.i2000.dvtoposervice.bean.pm.HistoryQueryValue;
import com.huawei.i2000.dvtoposervice.bean.pm.IndicatorDetail;
import com.huawei.i2000.dvtoposervice.bean.pm.IndicatorUnit;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInsExtentAttrDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceRelationModel;
import com.huawei.i2000.dvtoposervice.business.database.BusinessStepModelDB;
import com.huawei.i2000.dvtoposervice.business.pm.PmIndicatorInstanceService;
import com.huawei.i2000.dvtoposervice.business.pm.PmRequestService;
import com.huawei.i2000.dvtoposervice.business.pm.entity.DeletedHistoryData;
import com.huawei.i2000.dvtoposervice.business.pm.entity.DeletedHistoryResult;
import com.huawei.i2000.dvtoposervice.business.pm.entity.DeletedHistoryValues;
import com.huawei.i2000.dvtoposervice.business.pm.entity.MoObject;
import com.huawei.i2000.dvtoposervice.business.pm.entity.QueryDeletedHistoryData;
import com.huawei.i2000.dvtoposervice.business.service.bean.TopNViewMeasPMData;
import com.huawei.i2000.dvtoposervice.business.topo.AdminAuthorizedHelper;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.constant.TimeConstant;
import com.huawei.i2000.dvtoposervice.convert.ClusterConvert;
import com.huawei.i2000.dvtoposervice.convert.PodConvert;
import com.huawei.i2000.dvtoposervice.convert.VmConvert;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessIndicatorDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceRelationDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessMoTypeDao;
import com.huawei.i2000.dvtoposervice.impl.TopoServiceDelegateImpl;
import com.huawei.i2000.dvtoposervice.model.BusinessClusterData;
import com.huawei.i2000.dvtoposervice.model.ClusterDisplayResult;
import com.huawei.i2000.dvtoposervice.model.ClusterInsCommonParam;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.model.GrayscaleParam;
import com.huawei.i2000.dvtoposervice.model.GrayscaleResult;
import com.huawei.i2000.dvtoposervice.model.Indicator;
import com.huawei.i2000.dvtoposervice.model.IndicatorIdName;
import com.huawei.i2000.dvtoposervice.model.MoTypeViewCommonParam;
import com.huawei.i2000.dvtoposervice.model.MoTypeViewGrid;
import com.huawei.i2000.dvtoposervice.model.PerformanceIndexValue;
import com.huawei.i2000.dvtoposervice.model.PipelineDisplayResult;
import com.huawei.i2000.dvtoposervice.model.PodData;
import com.huawei.i2000.dvtoposervice.model.PodDeployResult;
import com.huawei.i2000.dvtoposervice.model.PodIndicatorDisplayResults;
import com.huawei.i2000.dvtoposervice.model.PodViewCommonParam;
import com.huawei.i2000.dvtoposervice.model.PodViewHistoryResults;
import com.huawei.i2000.dvtoposervice.model.VmData;
import com.huawei.i2000.dvtoposervice.model.VmDeployResult;
import com.huawei.i2000.dvtoposervice.model.VmIndicatorDisplayResults;
import com.huawei.i2000.dvtoposervice.util.AuthUtils;
import com.huawei.i2000.dvtoposervice.util.ConfigurationUtil;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.EamUtil;
import com.huawei.i2000.dvtoposervice.util.PerformanceClient;
import com.huawei.i2000.dvtoposervice.util.PmDataHandleUtil;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.dvtoposervice.util.dto.EamFootPrint;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.oms.eam.mim.MORelation;
import com.huawei.oms.eam.mim.RelationType;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.MOType;
import com.huawei.oms.eam.mo.ManagedElement;
import com.huawei.oms.eam.mo.ManagedObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.Vector;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.math.RoundingMode.HALF_UP;

/**
 * 业务网元下钻视图相关类
 *
 * <AUTHOR>
 * @since 2024/3/10
 */
@Service
public class BusinessTopoMoTypeViewServiceImpl implements BusinessTopoMoTypeViewService {

    private static final OssLog LOGGER = OssLogFactory.getLogger(BusinessTopoMoTypeViewServiceImpl.class);

    protected static final String PROPERTIES = DefaultEnvUtil.getAppRoot() + File.separator + "etc" + File.separator
        + "config" + File.separator + "toposervice.properties";

    private static final String VM_CLASS = "com.huawei.IRes.vm";

    private static final String GAUSS_TYPE = "com.huawei.gaussdb";

    private static final String ORACLE_TYPE = "inf.db.oracle";

    private static final String MYSQL_TYPE = "com.huawei.mysql";

    private static final List<String> DATABASE_TYP_LIST = Arrays.asList(GAUSS_TYPE, ORACLE_TYPE, MYSQL_TYPE);

    private static final String INDICATOR_RANGE = "indicatorrange";

    private static final String COMMA = ",";

    private static final String OWN_CSN = "OwnCsn";

    private static final String DELIMITER = ",";

    private static final String REMOVED = "Removed";

    private static final String MEAS_UNIT_STAR = "*";

    private static Pattern pattern = Pattern.compile("\\d+");

    private static final int SINGEL_INDICATOR = 1;

    private static final String GSU = "gsu";

    private static final String CSU_00 = "csu00";

    private static final String RSU = "rsu";

    private static final String CSU = "csu";

    @Autowired
    BusinessInstanceModelDao modelDao;

    @Autowired
    BusinessMoTypeDao moTypeDao;

    @Autowired
    TopoServiceDelegateImpl topoServiceDelegateImpl;

    @Autowired
    BusinessInstanceRelationDao relationDao;

    @Autowired
    PmRequestService pmRequestService;

    @Autowired
    PmIndicatorInstanceService pmIndicatorInstanceService;

    @Autowired
    private AdminAuthorizedHelper adminAuthorizedHelper;

    @Autowired
    private BusinessIndicatorDao businessIndicatorDao;

    @Override
    public MoTypeViewGrid queryMoTypeViewGrid(MoTypeViewCommonParam param) {
        long timeStamp = Objects.isNull(param.getTimestamp()) ? 0L : param.getTimestamp();
        List<BusinessInstanceModelDB> subInsList = new ArrayList<>();
        // cbs/bes场景只查一层，mm场景需要查询另一个泳道的
        if (StringUtils.isEmpty(param.getStripeUnit())) {
            subInsList = modelDao.queryNextLevelInstanceByTimeLine(param.getInstanceId(),
                timeStamp);
        } else {
            if (Objects.isNull(param.getTimestamp())) {
                param.setTimestamp(0L);
            }
            BusinessInstanceModelDB instanceType = modelDao.queryInstanceByInstanceIdAndTimeLine(param.getInstanceId(), param.getTimestamp());
            List<BusinessInstanceModelDB> instanceTypeModels = modelDao.queryHistoryInstanceByModelId(instanceType.getModelId(), param.getTimestamp());
            List<Integer> instanceTypeIds = instanceTypeModels.stream()
                .map(BusinessInstanceModelDB::getInstanceId)
                .collect(Collectors.toList());
            List<BusinessInstanceModelDB> siteModelDbs = modelDao.queryMMSiteInstanceByTime(param.getInstanceId(), param.getTimestamp());
            if (CollectionUtils.isEmpty(siteModelDbs)) {
                LOGGER.error("[queryMoTypeViewGrid] find site ins is empty, ins id is {}", param.getInstanceId());
                return new MoTypeViewGrid();
            }
            String stripe = siteModelDbs.get(0).getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue();
            subInsList = modelDao.queryNextLevelInstanceByTimeLineBatch(instanceTypeIds, param.getTimestamp());
            subInsList = subInsList.stream()
                .filter(model -> isSameStripe(model, stripe))
                .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(subInsList)) {
            LOGGER.debug("[queryMoTypeViewGrid] could not find any sub ins, ins id is {}", param.getInstanceId());
            return new MoTypeViewGrid();
        }
        // 过滤条带单元
        if (StringUtils.isNotEmpty(param.getStripeUnit())) {
            subInsList = subInsList.stream().filter(ins -> {
                BusinessInsExtentAttrDB stripeAttr = ins.getExtentAttr(BusinessTopoConstant.MM_HWS_UNIT);
                return param.getStripeUnit().equals(stripeAttr.getAttrValue());
            }).collect(Collectors.toList());
        }
        // 取一个集群层实例获取
        BusinessInstanceModelDB subClusterIns = subInsList.get(0);
        String moType = subClusterIns.getStaticExtentAttr(BusinessTopoConstant.MO_TYPE_MAPPING_ATTR_KEY)
            .getStaticAttrValue();
        Integer applicationType = StringUtils.isNotEmpty(
            subClusterIns.getStaticExtentAttr(BusinessTopoConstant.APPLICATION_TYPE).getStaticAttrValue())
            ? Integer.parseInt(
            subClusterIns.getStaticExtentAttr(BusinessTopoConstant.APPLICATION_TYPE).getStaticAttrValue())
            : 0;
        // 当应用类型为数据库类型时，展示数据库类型面板
        if (DATABASE_TYP_LIST.contains(moType) || BusinessTopoConstant.DATABASE_APPLICATION_TYPE.equals(applicationType)) {
            // 展示数据库视图
            return queryDataBaseViewGrid(param, subInsList);
        } else {
            // 展示分层视图
            return getClusterMotypeViewGrid(param, subInsList);
        }
    }

    private MoTypeViewGrid getClusterMotypeViewGrid(MoTypeViewCommonParam param, List<BusinessInstanceModelDB> subInsList) {
        long timeStamp = Objects.isNull(param.getTimestamp()) ? 0L : param.getTimestamp();
        List<BusinessInstanceModelDB> filterList = AuthUtils.permissionFilter(subInsList);
        List<BusinessClusterData> businessClusterList = new ArrayList<>();
        Map<Integer, List<BusinessInstanceModelDB>> podInsMap = getPodInsMap(timeStamp, filterList);
        Map<Integer, List<BusinessInstanceModelDB>> relationalVmIns = getRelationalVmIns(podInsMap, timeStamp);
        ClusterConvert clusterConvert = new ClusterConvert();
        for (BusinessInstanceModelDB manageObjectModelDB : filterList) {

            BusinessClusterData businessClusterData = clusterConvert.convert(manageObjectModelDB);
            List<BusinessInstanceModelDB> podInsList = new ArrayList<>();
            // 构建BusinessInstanceModel
            buildBusinessInstanceModelData(manageObjectModelDB, businessClusterData, podInsList, podInsMap);
            if (CollectionUtil.isEmpty(podInsList)) {
                businessClusterList.add(businessClusterData);
                continue;
            }
            // 获取Pod信息
            getPodData(podInsList, businessClusterData);
            List<Integer> podInsIdList = podInsList.stream()
                .map(BusinessInstanceModelDB::getInstanceId)
                .collect(Collectors.toList());
            List<BusinessInstanceModelDB> vmInsList = new ArrayList<>();
            podInsIdList.forEach(insId -> {
                if (relationalVmIns.containsKey(insId)) {
                    vmInsList.addAll(relationalVmIns.get(insId));
                }
            });
            // 获取Vm信息
            getVmData(businessClusterData, vmInsList);
            businessClusterList.add(businessClusterData);
        }
        setGrayFlag(businessClusterList, param.getInstanceId(), timeStamp);
        MoTypeViewGrid moTypeViewGrid = new MoTypeViewGrid();
        setGridEnvironmentType(filterList, moTypeViewGrid);
        moTypeViewGrid.setBusinessClusterList(businessClusterList);
        if (CollectionUtils.isNotEmpty(filterList)) {
            BusinessInstanceModelDB instanceModelDB = filterList.get(0);
            String applicationType = instanceModelDB.getStaticExtentAttr(BusinessTopoConstant.APPLICATION_TYPE).getStaticAttrValue();
            if (StringUtils.isNotEmpty(applicationType)) {
                moTypeViewGrid.setApplicationType(Integer.parseInt(applicationType));
            }
        }

        return moTypeViewGrid;
    }

    /**
     * 查询数据库应用视图，包含数据库应用与数据库部署主机
     *
     * @param param 分层视图入参 instanceId->网元类型Id timestamp->时间戳
     * @param subInsList 数据库实例列表
     * @return MoTypeViewGrid 分层视图
     */
    @Override
    public MoTypeViewGrid queryDataBaseViewGrid(MoTypeViewCommonParam param, List<BusinessInstanceModelDB> subInsList) {
        long timeStamp = Objects.isNull(param.getTimestamp()) ? 0L : param.getTimestamp();
        // 根据网元权限过滤
        List<BusinessInstanceModelDB> filterSubInsList = AuthUtils.permissionFilter(subInsList);

        List<BusinessClusterData> businessClusterList = new ArrayList<>();
        Map<Integer, List<BusinessInstanceModelDB>> databaseInsMap = new HashMap<>();
        ClusterConvert clusterConvert = new ClusterConvert();
        databaseInsMap.put(0, filterSubInsList);
        Map<Integer, List<BusinessInstanceModelDB>> relationalVmIns = getRelationalVmIns(databaseInsMap, timeStamp);
        for (BusinessInstanceModelDB manageObjectModelDB : filterSubInsList) {
            // 构建BusinessInstanceModel
            BusinessClusterData businessClusterData = clusterConvert.convert(manageObjectModelDB);
            // 没有下层Pod，不需要查询
            buildBusinessInstanceModelData(manageObjectModelDB, businessClusterData, new ArrayList<>(), new HashMap<>());
            businessClusterList.add(businessClusterData);

            List<BusinessInstanceModelDB> vmInsList = new ArrayList<>();
            if (relationalVmIns.containsKey(manageObjectModelDB.getInstanceId())) {
                vmInsList.addAll(relationalVmIns.get(manageObjectModelDB.getInstanceId()));
            }

            // 获取Vm信息
            getVmData(businessClusterData, vmInsList);
        }
        MoTypeViewGrid moTypeViewGrid = new MoTypeViewGrid();
        moTypeViewGrid.setApplicationType(BusinessTopoConstant.DATABASE_APPLICATION_TYPE);
        setGridEnvironmentType(filterSubInsList, moTypeViewGrid);
        moTypeViewGrid.setBusinessClusterList(businessClusterList);
        return moTypeViewGrid;
    }

    private void setGridEnvironmentType(List<BusinessInstanceModelDB> filterList, MoTypeViewGrid moTypeViewGrid) {
        if (CollectionUtils.isNotEmpty(filterList)) {
            moTypeViewGrid.setEnvironmentType(EamUtil.isContainerEnvironment(filterList.get(0).getDn()));
        }
    }

    private Map<Integer, List<BusinessInstanceModelDB>> getRelationalVmIns(
        Map<Integer, List<BusinessInstanceModelDB>> podInsMap, long timeStamp) {
        ArrayList<BusinessInstanceModelDB> podIns = new ArrayList<>();
        for (List<BusinessInstanceModelDB> value : podInsMap.values()) {
            podIns.addAll(value);
        }

        // 查询所有的关联虚机
        List<Integer> podInsIdList = podIns.stream()
            .map(BusinessInstanceModelDB::getInstanceId)
            .collect(Collectors.toList());
        return getRelationalVmIns(podInsIdList, timeStamp);
    }

    private Map<Integer, List<BusinessInstanceModelDB>> getPodInsMap(Long timeStamp, List<BusinessInstanceModelDB> filterList) {
        if (CollectionUtils.isEmpty(filterList)) {
            return Collections.emptyMap();
        }

        // 查询所有的pod
        Set<Integer> instanceIds = filterList.stream()
            .map(BusinessInstanceModelDB::getInstanceId)
            .collect(Collectors.toSet());
        timeStamp = Objects.isNull(timeStamp) ? Long.valueOf(0L) : timeStamp;
        List<BusinessInstanceModelDB> podIns = modelDao.queryNextLevelInstanceByTimeLineBatch(instanceIds, timeStamp);
        podIns = AuthUtils.permissionFilter(podIns);
        Map<Integer, List<BusinessInstanceModelDB>> podInsMap = podIns.stream()
            .collect(Collectors.groupingBy(BusinessInstanceModelDB::getTargetInstanceId));
        Map<Integer, List<BusinessInstanceModelDB>> result = new HashMap<>(podInsMap);
        for (BusinessInstanceModelDB businessInstanceModelDB : filterList) {
            if (!result.containsKey(businessInstanceModelDB.getInstanceId())) {
                result.put(businessInstanceModelDB.getInstanceId(), Collections.emptyList());
            }
        }
        return result;
    }

    @Override
    public VmDeployResult queryVmDeployData(PodViewCommonParam queryVmDeployData) throws ServiceException {
        Integer instanceId = queryVmDeployData.getInstanceId();
        LOGGER.info("begin to queryVmDeployData:{}", instanceId);
        boolean historyFlag = queryVmDeployData.getEndTime() == null;
        BusinessInstanceModelDB vmData;
        if (historyFlag) {
            vmData = modelDao.queryInstanceByInstanceId(instanceId);
        } else {
            vmData = modelDao.queryInstanceByInstanceIdAndTimeLine(instanceId, queryVmDeployData.getEndTime());
        }
        if (vmData == null || StringUtils.isEmpty(vmData.getDn())) {
            LOGGER.error("vmData or dn is null:{}", instanceId);
            return new VmDeployResult();
        }
        String dnId = vmData.getDn();
        VmDeployResult vmDeployResult = new VmDeployResult();
        vmDeployResult.setDnId(String.valueOf(instanceId));
        String alarmCsn = vmData.getExtentAttr(OWN_CSN).getAttrValue();
        vmDeployResult.setCsnState(StringUtils.isEmpty(alarmCsn) ? 0 : 1);

        setPodList(dnId, historyFlag, queryVmDeployData, vmDeployResult);
        // 设置vm下所有pod的instanceId
        List<Integer> instanceIdList;
        if (historyFlag) {
            instanceIdList = moTypeDao.queryPodInstanceIdByVmInstanceId(instanceId);
        } else {
            instanceIdList = moTypeDao.queryPodInstanceIdByVmInstanceIdAndTimeLine(instanceId,
                queryVmDeployData.getEndTime());
        }
        if (CollectionUtils.isEmpty(instanceIdList)) {
            LOGGER.error("instanceId is empty:{}", instanceId);
            return vmDeployResult;
        }
        vmDeployResult.setPodInstanceIdList(instanceIdList);

        LOGGER.info("success to queryVmDeployData {}", dnId);
        return vmDeployResult;
    }

    private Map<Integer, List<BusinessInstanceModelDB>> getRelationalVmIns(List<Integer> insList, long timeStamp) {
        List<BusinessInstanceRelationModel> instanceParentRelList
            = relationDao.getInsRelByInsIdAndRelationTypeForTimeLine(insList, timeStamp,
            BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT, BusinessTopoConstant.COLUMN_INSTANCE_ID);

        if (CollectionUtils.isEmpty(instanceParentRelList)) {
            return Collections.emptyMap();
        }
        // 保存关联关系, 存在一个数据库应用对应对个部署主机的情况
        Map<Integer, List<Integer>> relationMap = instanceParentRelList.stream()
            .collect(Collectors.groupingBy(BusinessInstanceRelationModel::getInstanceId,
                Collectors.mapping(BusinessInstanceRelationModel::getTargetInstanceId, Collectors.toList())));

        // 查询所有被关联的实例
        List<Integer> vmInsIdList = relationMap.values()    // 获取所有虚机
            .stream().flatMap(List::stream).collect(Collectors.toList());
        List<BusinessInstanceModelDB> businessInstanceModelDBS = modelDao.queryInstanceListByIdList(vmInsIdList,
            timeStamp);

        // 将所有被关联的实例转为map
        Map<Integer, BusinessInstanceModelDB> relationInsMap = businessInstanceModelDBS.stream()
            .collect(Collectors.toMap(BusinessInstanceModelDB::getInstanceId, Function.identity(), (v1, v2) -> v1));

        // 构造instanceId->被关联实例的结果map
        Map<Integer, List<BusinessInstanceModelDB>> result = new HashMap<>();
        for (Map.Entry<Integer, List<Integer>> entry : relationMap.entrySet()) {
            List<Integer> vmInsList = entry.getValue();
            for (Integer vmInsId : vmInsList) {
                if (relationInsMap.containsKey(vmInsId)) {
                    // 从 relationInsMap 中获取对应的业务实例对象
                    BusinessInstanceModelDB instance = relationInsMap.get(vmInsId);
                    // 将对象添加到 result 中对应父实例的列表中（自动初始化空列表）
                    result.computeIfAbsent(entry.getKey(), k -> new ArrayList<>()).add(instance);
                }
            }
        }
        return result;
    }

    private void setPodList(String dnId, boolean historyFlag, PodViewCommonParam queryVmDeployData,
        VmDeployResult vmDeployResult) throws ServiceException {
        if (historyFlag) {
            Vector<DN> srcMOList = null;
            try {
                srcMOList = MITManagerClient.newInstance().getSrcMOList(new DN(dnId), RelationType.Deployment);
            } catch (OSSException e) {
                LOGGER.error("get mo list failed", e);
                throw new ServiceException();
            }

            // 设置vm关联的pod
            List<ManagedObject> openApiNodeVMList = MITManagerClient.newInstance().getMoByDns(srcMOList);
            List<PodData> podList = new LinkedList<>();
            openApiNodeVMList.forEach(node -> {
                PodData podData = new PodData();
                podData.setDnId(node.getDN().getValue());
                podData.setPodName(node.getName());
                podList.add(podData);
            });
            vmDeployResult.setPodDataList(podList);
        } else {
            List<BusinessInstanceModelDB> businessInstanceModelDBS = modelDao.queryPodInstanceByTimeLine(
                queryVmDeployData.getInstanceId(), queryVmDeployData.getEndTime());
            List<PodData> podList = new LinkedList<>();
            businessInstanceModelDBS.forEach(pod -> {
                PodData podData = new PodData();
                podData.setDnId(pod.getDn());
                podData.setPodName(pod.getExtentAttr("podName").getAttrValue());
                podList.add(podData);
            });
            vmDeployResult.setPodDataList(podList);
        }
    }

    @Override
    public PodDeployResult queryPodDeployData(PodViewCommonParam queryPodDeployData) throws ServiceException {
        LOGGER.info("begin to queryPodDeployData:{}", queryPodDeployData.getInstanceId());
        boolean historyFlag = queryPodDeployData.getEndTime() == null;
        Integer instanceId = queryPodDeployData.getInstanceId();
        BusinessInstanceModelDB podData;
        BusinessInstanceModelDB upIns;
        if (historyFlag) {
            podData = modelDao.queryInstanceByInstanceId(instanceId);
            upIns = modelDao.queryUpLevelInstance(instanceId);
        } else {
            podData = modelDao.queryInstanceByInstanceIdAndTimeLine(instanceId, queryPodDeployData.getEndTime());
            upIns = modelDao.queryUpLevelInstance(instanceId, queryPodDeployData.getEndTime());
        }
        if (Objects.isNull(podData) || StringUtils.isEmpty(podData.getDn())) {
            LOGGER.error("podData or dn is null:{}", instanceId);
            return new PodDeployResult();
        }
        String dnId = podData.getDn();
        List<DN> dnPodList = new ArrayList<>(Collections.singletonList(new DN(dnId)));
        PodDeployResult podDeployResult = new PodDeployResult();
        String alarmCsn = podData.getExtentAttr(BusinessTopoConstant.ATTR_KEY_ALARM_CSN).getAttrValue();
        podDeployResult.setCsnState(StringUtils.isEmpty(alarmCsn) ? 0 : 1);
        podDeployResult.setEnvironmentType(EamUtil.isContainerEnvironment(upIns.getDn()));
        setPodResult(podDeployResult, historyFlag, instanceId, dnId, queryPodDeployData);

        Integer vmInstanceId;
        if (historyFlag) {
            List<ManagedObject> destNodes = getDestNode(dnPodList);
            if (CollectionUtil.isEmpty(destNodes)) {
                LOGGER.error("getAllRelationsManagedObjectByDns destNodes is empty{}", destNodes);
                return podDeployResult;
            }
            vmInstanceId = moTypeDao.queryVmInstanceIdByPodInstanceId(instanceId);
            setVmResult(destNodes, vmInstanceId, podDeployResult);
        } else {
            vmInstanceId = relationDao.queryAntiRelationByTypeAndTimeLine(instanceId, 1,
                queryPodDeployData.getEndTime());
            setVmResult(vmInstanceId, queryPodDeployData.getEndTime(), podDeployResult);
        }

        LOGGER.info("success to queryPodDeployData {}", dnId);
        return podDeployResult;
    }

    @Override
    public ClusterDisplayResult queryClusterDisplayResults(ClusterInsCommonParam param) throws ServiceException {
        // 非admin角色禁止查看时间轴回溯
        if (Boolean.TRUE.equals(!ContextUtils.getContext().getAdmin()
                && (Objects.nonNull(param.getTimestamp()) && param.getTimestamp() != 0L))
                && !adminAuthorizedHelper.nonAdminAuthorize(param.getInstanceId(), param.getTimestamp())) {
            LOGGER.error("only admin user can look time line!");
            return new ClusterDisplayResult();
        }
        // 校验一下用户对该实例的权限
        if (!AuthUtils.isInstanceAuth(param.getInstanceId())) {
            LOGGER.error("don't have permission of this instance, id = {}", param.getInstanceId());
            return new ClusterDisplayResult();
        }
        ClusterDisplayResult clusterDisplayResult = new ClusterDisplayResult();
        BusinessInstanceModelDB clusterIns;
        if (Objects.isNull(param.getTimestamp()) || param.getTimestamp() == 0L) {
            clusterIns = modelDao.queryInstanceByInstanceId(param.getInstanceId());
        } else {
            clusterIns = modelDao.queryInstanceByInstanceIdAndTimeLine(param.getInstanceId(), param.getTimestamp());
        }
        loadClusterData(clusterIns, clusterDisplayResult, param);
        return clusterDisplayResult;
    }

    private void loadClusterData(BusinessInstanceModelDB clusterIns, ClusterDisplayResult clusterDisplayResult,
        ClusterInsCommonParam param) {
        if (Objects.isNull(param.getTimestamp()) || param.getTimestamp() == 0L) {
            try {
                ManagedObject mo = MITManagerClient.newInstance().getMO(clusterIns.getDn());
                MOType moTypeData = MITManagerClient.newInstance()
                    .getMOType(clusterIns.getStaticExtentAttr("moTypeMapping").getStaticAttrValue());
                if (Objects.isNull(mo) || Objects.isNull(moTypeData)) {
                    LOGGER.error(
                        "[queryClusterDisplayResults] query mo or moType error, invalid date, the ins id is {}",
                        clusterIns.getInstanceId());
                    return;
                }
                if (mo instanceof ManagedElement) {
                    ManagedElement managedElement = (ManagedElement) mo;
                    clusterDisplayResult.setInsIP(managedElement.getIPAddress());
                    LOGGER.debug("noIp is {}", managedElement.getIPAddress());
                    clusterDisplayResult.setAvailableStatus(Objects.isNull(managedElement.getOperationalStatus())
                        ? StringUtils.EMPTY
                        : managedElement.getOperationalStatus().name());
                }
                clusterDisplayResult.setDn(clusterIns.getDn());
                clusterDisplayResult.setInsVersion(mo.getVersion());
                clusterDisplayResult.setAppName(mo.getName());
                clusterDisplayResult.setInsType(moTypeData.getDisplayType());
                clusterDisplayResult.setCsnState(StringUtils.isEmpty(
                    clusterIns.getExtentAttr(BusinessTopoConstant.ATTR_KEY_ALARM_CSN).getAttrValue()) ? 0 : 1);
            } catch (OSSException e) {
                LOGGER.error("[queryClusterDisplayResults] query managedObject error. the dn is {}", clusterIns.getDn(),
                    e);
            }
        } else {
            clusterDisplayResult.setDn(clusterIns.getDn());
            clusterDisplayResult.setAvailableStatus(
                clusterIns.getExtentAttr(BusinessTopoConstant.ATTR_KEY_NODE_STATUS).getAttrValue());
            clusterDisplayResult.setInsIP(
                clusterIns.getExtentAttr(BusinessTopoConstant.ATTR_KEY_NODE_IP).getAttrValue());
            clusterDisplayResult.setAppName(
                clusterIns.getExtentAttr(BusinessTopoConstant.ATTR_KEY_APP_NAME).getAttrValue());
            clusterDisplayResult.setInsVersion(
                clusterIns.getExtentAttr(BusinessTopoConstant.ATTR_KEY_VERSION).getAttrValue());
            clusterDisplayResult.setInsType(
                clusterIns.getExtentAttr(BusinessTopoConstant.ATTR_KEY_DISPLAY_TYPE).getAttrValue());
            clusterDisplayResult.setCsnState(
                StringUtils.isEmpty(clusterIns.getExtentAttr(BusinessTopoConstant.ATTR_KEY_ALARM_CSN).getAttrValue())
                    ? 0
                    : 1);
        }
    }

    private void setVmResult(Integer vmInstanceId, Long endTime, PodDeployResult podDeployResult) {
        VmData vmData = new VmData();
        BusinessInstanceModelDB businessInstanceModelDB = modelDao.queryInstanceByInstanceIdAndTimeLine(vmInstanceId,
            endTime);
        String nodeIp = businessInstanceModelDB.getExtentAttr("nodeIp").getAttrValue();
        vmData.setVmIp(nodeIp);
        String vmName = businessInstanceModelDB.getExtentAttr("vmName").getAttrValue();
        vmData.setVmName(vmName);
        vmData.setDnId(String.valueOf(vmInstanceId));
        vmData.setInstanceId(vmInstanceId);
        List<VmData> vmDataList = new LinkedList<>();
        vmDataList.add(vmData);
        podDeployResult.setVmDataList(vmDataList);
    }

    private void setVmResult(List<ManagedObject> destNodes, Integer vmInstanceId, PodDeployResult podDeployResult) {
        List<VmData> vmDataList = new LinkedList<>();
        for (ManagedObject destNode : destNodes) {
            if (VM_CLASS.equals(destNode.getType())) {
                VmData vmData = new VmData();
                vmData.setDnId(String.valueOf(vmInstanceId));
                vmData.setInstanceId(vmInstanceId);
                vmData.setVmName(destNode.getName());
                if (destNode instanceof ManagedElement) {
                    ManagedElement managedElement = (ManagedElement) destNode;
                    vmData.setVmIp(managedElement.getIPAddress());
                }
                vmDataList.add(vmData);
            }
        }

        podDeployResult.setVmDataList(vmDataList);
    }

    private List<ManagedObject> getDestNode(List<DN> dnPodList) throws ServiceException {
        List<ManagedObject> destNodes = new ArrayList<>();
        List<MORelation> moRelations;
        try {
            moRelations = MITManagerClient.newInstance()
                .getAllRelationsByDns(dnPodList, RelationType.Deployment.name(), false);
        } catch (OSSException e) {
            LOGGER.error("getAllRelationsByDns error", e);
            throw new ServiceException();
        }
        if (CollectionUtil.isEmpty(moRelations)) {
            LOGGER.error("Deployment Nodes is null,dns{},pDN={}", dnPodList);
            return destNodes;
        }

        List<DN> destNodeList = moRelations.stream()
            .map(MORelation::getDestNode)
            .distinct()
            .collect(Collectors.toList());

        try {
            destNodes = MITManagerClient.newInstance().getMOList(destNodeList);
        } catch (OSSException e) {
            LOGGER.error("getAllRelationsManagedObjectByDns error", e);
            throw new ServiceException();
        }
        return destNodes;
    }

    private void setPodResult(PodDeployResult podDeployResult, boolean historyFlag, Integer instanceId, String dnId,
        PodViewCommonParam queryPodDeployData) {
        if (historyFlag) {
            ManagedObject mo = MITManagerClient.newInstance().getMO(new DN(dnId));
            if (mo instanceof ManagedElement) {
                ManagedElement managedElement = (ManagedElement) mo;
                podDeployResult.setPodIp(managedElement.getIPAddress());
                LOGGER.debug("noIp is {}", managedElement.getIPAddress());
                podDeployResult.setPodName(managedElement.getName());
                podDeployResult.setAvailableStatus(managedElement.getOperationalStatus().name());
                podDeployResult.setCreateTime(String.valueOf(managedElement.getCreatedTime()));
                List<DN> dockerDns = managedElement.getChildren();
                List<ManagedObject> dockerMos = EamUtil.getMoByDns(dockerDns);
                List<String> dockerNames = dockerMos.stream()
                    .map(ManagedObject::getName)
                    .sorted()
                    .collect(Collectors.toList());
                podDeployResult.setDockerName(String.join(DELIMITER, dockerNames));
            }
            return;
        }
        getPodResultByTimeLine(podDeployResult, instanceId, queryPodDeployData);
    }

    private void getPodResultByTimeLine(PodDeployResult podDeployResult, Integer instanceId, PodViewCommonParam queryPodDeployData) {
        BusinessInstanceModelDB businessInstanceModelDB = modelDao.queryInstanceByInstanceIdAndTimeLine(instanceId,
            queryPodDeployData.getEndTime());
        String nodeServiceStatus = businessInstanceModelDB.getExtentAttr(BusinessTopoConstant.NODE_SERVICE_STATUS).getAttrValue();
        String podName = businessInstanceModelDB.getExtentAttr(BusinessTopoConstant.ATTR_KEY_POD_NAME).getAttrValue();
        String nodeIp = businessInstanceModelDB.getExtentAttr(BusinessTopoConstant.ATTR_KEY_NODE_IP).getAttrValue();
        String nodeCreateTime = businessInstanceModelDB.getExtentAttr(BusinessTopoConstant.NODE_CREATE_TIME).getAttrValue();
        podDeployResult.setPodIp(nodeIp);
        podDeployResult.setAvailableStatus(nodeServiceStatus);
        podDeployResult.setPodName(podName);
        podDeployResult.setCreateTime(nodeCreateTime);
        // dockerName不是从历史备份表查的，是直接查资源和网元足迹得到的
        List<BusinessInstanceModelDB> dockerIns = modelDao.queryNextLevelInstanceByTimeLine(instanceId, queryPodDeployData.getEndTime());
        Set<String> dockerDns = dockerIns.stream()
            .map(BusinessInstanceModelDB::getDn)
            .collect(Collectors.toSet());
        List<MoObject> dockerMos = EamUtil.getMoListByDnList(new ArrayList<>(dockerDns));
        List<String> dockerNames = dockerMos.stream()
            .map(MoObject::getName)
            .collect(Collectors.toList());
        // 如果有从资源查不到的docker说明已经消亡，去网元足迹查
        dockerMos.stream().map(MoObject::getDn).collect(Collectors.toList()).forEach(dockerDns::remove);
        if (CollectionUtils.isEmpty(dockerDns)) {
            podDeployResult.setDockerName(String.join(DELIMITER, dockerNames));
            return;
        }
        List<String> eventTypes = new ArrayList<>();
        eventTypes.add(REMOVED);
        long endTime = System.currentTimeMillis();
        for (String deleteDn : dockerDns) {
            List<EamFootPrint> mos = EamUtil.queryFootPrintByDn(eventTypes, deleteDn, endTime - TimeConstant.ONE_DAY, endTime);
            if (CollectionUtils.isNotEmpty(mos)) {
                dockerNames.add(mos.get(0).getMoName());
            }
        }
        podDeployResult.setDockerName(String.join(DELIMITER, dockerNames));
    }

    @Override
    public PodIndicatorDisplayResults queryIndicatorListAndData(PodViewCommonParam indicatorParam)
        throws ServiceException {
        BusinessInstanceModelDB businessInstanceModelDB;
        if (indicatorParam.getEndTime() == null) {
            businessInstanceModelDB = modelDao.queryInstanceByInstanceId(indicatorParam.getInstanceId());
        } else {
            businessInstanceModelDB = modelDao.queryInstanceByInstanceIdAndTimeLine(indicatorParam.getInstanceId(),
                indicatorParam.getEndTime());
        }
        PodIndicatorDisplayResults indicatorDisplayResults = new PodIndicatorDisplayResults();
        if (Objects.isNull(businessInstanceModelDB)) {
            LOGGER.warn("[queryPodIndicator] could not find ins, instance id is {}", indicatorParam.getInstanceId());
            return indicatorDisplayResults;
        }
        List<Indicator> indicatorList;
        String indicatorId = indicatorParam.getIndicatorId();
        List<PodViewHistoryResults> displayIndicatorList = new ArrayList<>();
        if (businessInstanceModelDB.getModelType() == BusinessTopoConstant.MOTYPE_CLUSTER_TYPE_ID) {
            indicatorList = getIndicators(indicatorParam);
            if (CollectionUtils.isEmpty(indicatorList)) {
                return indicatorDisplayResults;
            }
            if (StringUtils.isNotEmpty(indicatorId)) {
                List<Indicator> indicators = indicatorList.stream()
                    .filter(indicator -> indicator.getIndicatorId().equalsIgnoreCase(indicatorId))
                    .collect(Collectors.toList());
                getIndicators(indicatorParam, indicatorList, sortAndGetFirstIndicator(indicators), displayIndicatorList,
                    false);
            } else {
                getIndicators(indicatorParam, indicatorList, sortAndGetFirstIndicator(indicatorList),
                    displayIndicatorList, false);
            }
        } else {
            indicatorList = getIndicators(indicatorParam, indicatorId);
            if (CollectionUtils.isEmpty(indicatorList)) {
                return indicatorDisplayResults;
            }
            sortAndGetFirstIndicator(indicatorList);
            List<Indicator> result;
            if (StringUtils.isNotEmpty(indicatorId)) {
                result = indicatorList.stream()
                    .filter(indicator -> indicator.getIndicatorId().equalsIgnoreCase(indicatorId))
                    .collect(Collectors.toList());
            } else {
                result = indicatorList;
            }
            getPodIndicateResult(indicatorParam, Collections.singletonList(result.get(0)), displayIndicatorList, false);
        }

        indicatorDisplayResults.setPodIndicatorList(displayIndicatorList);
        if (indicatorId == null) {
            setIndicatorIdList(indicatorDisplayResults, indicatorList);
        }

        LOGGER.info("success to queryPodIndicator:{}", indicatorDisplayResults);
        return indicatorDisplayResults;
    }

    public PodIndicatorDisplayResults queryMmIndicatorListAndData(PodViewCommonParam indicatorParam)
        throws ServiceException {
        PodIndicatorDisplayResults indicatorDisplayResults = new PodIndicatorDisplayResults();
        Long endTime = indicatorParam.getEndTime();
        int insId = indicatorParam.getInstanceId();
        String indicatorId = indicatorParam.getIndicatorId();
        // 查本层的网元
        BusinessInstanceModelDB businessInstanceModelDB = endTime == null
            ? modelDao.queryInstanceByInstanceId(insId)
            : modelDao.queryInstanceByInstanceIdAndTimeLine(insId, endTime);
        if (Objects.isNull(businessInstanceModelDB)) {
            LOGGER.warn("[queryPodIndicator] could not find ins, instance id is {}", insId);
            return indicatorDisplayResults;
        }

        // 查本层的指标
        List<Indicator> indicatorList = getIndicators(indicatorParam, indicatorId);
        if (CollectionUtils.isEmpty(indicatorList)) {
            return indicatorDisplayResults;
        }

        List<Indicator> filteredIndicatorList = StringUtils.isNotEmpty(indicatorId) ? indicatorList.stream()
            .filter(indicator -> indicator.getIndicatorId().equalsIgnoreCase(indicatorId))
            .collect(Collectors.toList()) : indicatorList;

        List<PodViewHistoryResults> displayIndicatorList = new ArrayList<>();
        // 获取指标的值
        getPodIndicateResult(indicatorParam, Collections.singletonList(sortAndGetFirstIndicator(filteredIndicatorList)),
            displayIndicatorList, true);
        indicatorDisplayResults.setPodIndicatorList(displayIndicatorList);

        // 获取指标id和名称
        if (indicatorId == null) {
            setIndicatorIdList(indicatorDisplayResults, filteredIndicatorList);
        }

        LOGGER.info("success to queryPodIndicator:{}", indicatorDisplayResults);
        return indicatorDisplayResults;
    }

    private <T> void setIndicatorIdList(T targetClass, List<Indicator> indicatorList) {
        Map<String, Indicator> indicatorMap = indicatorList.stream().collect(Collectors.toMap(
            indicator -> indicator.getMeasUnitKey() + indicator.getMeasTypeKey(), indicator -> indicator,
            (existing, replacement) -> replacement));
        List<IndicatorIdName> indicatorIdNames = indicatorMap.values().stream().map(indicator -> {
            IndicatorIdName indicatorIdName = new IndicatorIdName();
            indicatorIdName.setIndicatorId(indicator.getIndicatorId());
            indicatorIdName.setIndexName(indicator.getIndexName());
            indicatorIdName.setIndicatorSortNumber(indicator.getIndicatorSortNumber());
            return indicatorIdName;
        }).collect(Collectors.toList());
        indicatorIdNames.sort(
            Comparator.comparing(IndicatorIdName::getIndicatorSortNumber, Comparator.nullsLast(Integer::compareTo))
                .thenComparing(IndicatorIdName::getIndexName, Comparator.nullsLast(String::compareTo))
        );
        try {
            Method method = targetClass.getClass().getMethod("setIndicatorIdList", List.class);
            method.invoke(targetClass, indicatorIdNames);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            LOGGER.error("set indicator names error", e);
        }
    }

    private List<Indicator> getIndicators(PodViewCommonParam queryPodIndicatorParam, String indicatorId)
        throws ServiceException {
        if (!AuthUtils.isInstanceAuth(queryPodIndicatorParam.getInstanceId())) {
            LOGGER.error("don't have permission of this ins, id = {}", queryPodIndicatorParam.getInstanceId());
            return Collections.emptyList();
        }
        List<Indicator> indicatorList;
        if (StringUtils.isNotEmpty(indicatorId)) {
            indicatorList = fillEmptyIndexName(
                moTypeDao.queryMoTypeIndicatorByIndicatorId(indicatorId, queryPodIndicatorParam.getInstanceId(),
                    queryPodIndicatorParam.getEndTime()));
        } else {
            indicatorList = fillEmptyIndexName(
                moTypeDao.queryMoTypeIndicatorByInstanceId(queryPodIndicatorParam.getInstanceId(),
                    queryPodIndicatorParam.getEndTime()));
        }
        if (CollectionUtils.isEmpty(indicatorList)) {
            indicatorList = getIndicators(queryPodIndicatorParam);
        }
        return indicatorList;
    }

    private List<Indicator> getIndicators(PodViewCommonParam queryPodIndicatorParam) throws ServiceException {
        List<Indicator> indicatorList;
        List<Integer> instanceIdList = new ArrayList<>();
        List<BusinessInstanceModelDB> businessInstanceModelDBS;
        if (queryPodIndicatorParam.getEndTime() == null) {
            businessInstanceModelDBS = modelDao.queryNextLevelInstance(queryPodIndicatorParam.getInstanceId());
        } else {
            businessInstanceModelDBS = modelDao.queryNextLevelInstanceByTimeLine(queryPodIndicatorParam.getInstanceId(),
                queryPodIndicatorParam.getEndTime());
        }
        if (StringUtils.isNotEmpty(queryPodIndicatorParam.getStripeUnit())) {
            businessInstanceModelDBS = businessInstanceModelDBS.stream().filter(model -> queryPodIndicatorParam.getStripeUnit().equals(model.getExtentAttr(BusinessTopoConstant.MM_HWS_UNIT).getAttrValue())).collect(Collectors.toList());
        }
        List<BusinessInstanceModelDB> filterIns = AuthUtils.permissionFilter(businessInstanceModelDBS);
        for (BusinessInstanceModelDB instanceModelDB : filterIns) {
            instanceIdList.add(instanceModelDB.getInstanceId());
        }
        indicatorList = fillEmptyIndexName(
            moTypeDao.queryMoTypeIndicatorListByInstanceIdList(instanceIdList, queryPodIndicatorParam.getEndTime()));
        return indicatorList;
    }

    private void getIndicators(PodViewCommonParam queryPodIndicatorParam, List<Indicator> indicatorList,
        Indicator needIndicator, List<PodViewHistoryResults> podIndicatorList, boolean isMm) {
        List<Indicator> indicators = indicatorList.stream()
            .filter(indicator -> indicator.getMeasTypeKey().equals(needIndicator.getMeasTypeKey())
                && indicator.getMeasUnitKey().equals(needIndicator.getMeasUnitKey()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(indicators)) {
            return;
        }
        // 单个实例直接查指标
        if (indicators.size() == 1) {
            getPodIndicateResult(queryPodIndicatorParam, indicators, podIndicatorList, isMm);
            return;
        }
        // mm场景查询topN方式不同，因实例的测量对象可能为*
        if (isMm) {
            getPodIndicateResult(queryPodIndicatorParam, indicators, podIndicatorList, true);
        } else {
            getIndicators(queryPodIndicatorParam, podIndicatorList, indicators);
        }
    }

    private void getIndicators(PodViewCommonParam queryPodIndicatorParam, List<PodViewHistoryResults> podIndicatorList,
        List<Indicator> indicators) {
        // 多个实例先查询TopN再查指标
        List<TopNViewMeasPMData> topNPmDataResults = PmDataHandleUtil.getClusterIndicatorTopNResult(indicators,
            queryPodIndicatorParam.getEndTime(), false, BusinessTopoConstant.TOPN.TOP_N_TYPE_NORMAL, null, null);
        Set<String> dns = topNPmDataResults.stream()
            .map(TopNViewMeasPMData::getDn)
            .filter(StringUtils::isNotEmpty)
            .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(dns)) {
            // 所有指标都没数据时默认展示前20个指标
            dns.addAll(indicators.stream()
                .sorted(Comparator.comparing(Indicator::getDnName))
                .limit(20)
                .map(Indicator::getDn)
                .collect(Collectors.toSet()));
        }
        indicators = indicators.stream()
            .filter(indicator -> dns.contains(indicator.getDn()))
            .collect(Collectors.toList());
        getPodIndicateResult(queryPodIndicatorParam, indicators, podIndicatorList, false);
    }

    private void getPodIndicateResult(PodViewCommonParam queryPodIndicatorParam, List<Indicator> indicators,
        List<PodViewHistoryResults> podIndicatorList, boolean needTopN) {
        if (CollectionUtils.isEmpty(indicators)) {
            LOGGER.error("indicator is null! instanceId:{}", queryPodIndicatorParam.getInstanceId());
            return;
        }

        long endTime = queryPodIndicatorParam.getEndTime() != null && queryPodIndicatorParam.getEndTime() != 0L
            ? queryPodIndicatorParam.getEndTime()
            : System.currentTimeMillis();
        ConfigData configData = ConfigurationUtil.getConfigDataByName(INDICATOR_RANGE);
        long startTime = endTime - Long.parseLong(configData.getValue());

        if (needTopN) {
            // mm场景，先查topn
            String originalValue = indicators.get(0).getOriginalValue();
            if ("*".equals(originalValue)) {
                getPodIndicateResultForMm(queryPodIndicatorParam, indicators, podIndicatorList, endTime, startTime);
            } else {
                getPodIndicateResult(indicators, podIndicatorList, endTime, startTime);
            }
        } else {
            // 非mm场景
            getPodIndicateResult(indicators, podIndicatorList, endTime, startTime);
        }

    }

    private void getPodIndicateResultForMm(PodViewCommonParam queryPodIndicatorParam, List<Indicator> indicators,
        List<PodViewHistoryResults> podIndicatorList, long endTime, long startTime) {
        // 获取测量对象
        List<Indicator> orignalValueIndicatorList = getOriginalValueList(queryPodIndicatorParam.getEndTime(),
            indicators);
        if (!orignalValueIndicatorList.isEmpty()) {
            getPodIndicateResult(orignalValueIndicatorList, podIndicatorList, endTime, startTime);
        }
    }

    private void getPodIndicateResult(List<Indicator> indicators, List<PodViewHistoryResults> podIndicatorList,
        long endTime, long startTime) {
        Set<String> dns = indicators.stream().map(Indicator::getDn).collect(Collectors.toSet());
        boolean needLastWeek = dns.size() == SINGEL_INDICATOR;
        Map<String, Long> timeRanges = PmDataHandleUtil.getIndQueryTimeRangeInPmFormat(endTime, INDICATOR_RANGE, needLastWeek);
        List<QueryDeletedHistoryData> queryDeletedHistoryData = pmRequestService.getQueryDeletedHistoryData(indicators,
            timeRanges);
        DeletedHistoryData performanceResult = pmRequestService.getDeletedHistoryDataFormPerformance(
            queryDeletedHistoryData);
        Map<String, List<PerformanceIndexValue>> indexValues = pmRequestService.getPerformanceIndexValueList(
            performanceResult, indicators, true);
        List<String> dnList = indicators.stream().map(Indicator::getDn).collect(Collectors.toList());
        List<MoObject> mos = EamUtil.getMoListByDnList(dnList);
        Map<String, String> dnNameMap = mos.stream().collect(Collectors.toMap(MoObject::getDn, MoObject::getName));
        for (Indicator indicator : indicators) {
            String keyName = indicator.getDisplayValue() != null
                ? indicator.getDn() + ":" + indicator.getDisplayValue()
                : indicator.getDn();
            PodViewHistoryResults podViewHistoryResults = getPodViewHistoryResults(indicator,
                indexValues.get(keyName), endTime, startTime);
            if (dnNameMap.containsKey(indicator.getDn())) {
                podViewHistoryResults.setPodName(StringUtils.isEmpty(indicator.getDisplayValue())
                    ? dnNameMap.get(indicator.getDn())
                    : dnNameMap.get(indicator.getDn()) + ":" + indicator.getDisplayValue());
            }
            podIndicatorList.add(podViewHistoryResults);
        }
        podIndicatorList.sort(Comparator.comparing(PodViewHistoryResults::getPodName, Comparator.nullsLast(String::compareTo)));
    }

    private List<Indicator> getOriginalValueList(Long time, List<Indicator> indicators) {
        List<Indicator> indicatorList = new ArrayList<>();
        // 调用性能topN接口获取topN指标,从中获取originalValue
        List<TopNViewMeasPMData> topNPmDataResults = PmDataHandleUtil.getClusterIndicatorTopNResult(
            JSONArray.parseArray(JSON.toJSONString(indicators), Indicator.class, JSONReader.Feature.SupportSmartMatch),
            (time == null || time == 0) ? null : time, true, BusinessTopoConstant.TOPN.TOP_N_TYPE_NORMAL, null, null);
        // 约束为相同网元类型
        String moType = indicators.get(0).getMoType();
        String unit = indicators.get(0).getUnit();
        Map<String, Indicator> indicatorMap = indicators.stream()
            .collect(Collectors.toMap(a -> a.getDn() + moType + a.getMeasUnitKey() + a.getMeasTypeKey(), a -> a));
        for (TopNViewMeasPMData data : topNPmDataResults) {
            String originalValue = null;
            Indicator indicator = new Indicator();
            if (data.getMeasObject() != null && CollectionUtils.isNotEmpty(data.getMeasObject().getKeys())) {
                List<String> oriValueList = new ArrayList<>();
                for (int i = 0; i < data.getMeasObject().getKeys().size(); i++) {
                    oriValueList.add(
                        data.getMeasObject().getKeys().get(i) + "=" + data.getMeasObject().getValues().get(i));
                }
                // 拼接所有的值
                originalValue = String.join(",", oriValueList);
            }
            // 设置指标的值
            setIndicator(indicatorMap.get(
                data.getDn() + moType + data.getValues().get(0).getMeasUnitKey() + data.getValues()
                    .get(0)
                    .getMeasTypeKey()), unit, data, indicator, originalValue);
            indicatorList.add(indicator);
        }
        return indicatorList;
    }

    private void setIndicator(Indicator oriIndicator, String unit, TopNViewMeasPMData data, Indicator indicator,
        String originalValue) {
        indicator.setDisplayValue(data.getDisplayValue());
        indicator.setMeasTypeKey(data.getValues().get(0).getMeasTypeKey());
        indicator.setMeasUnitKey(data.getValues().get(0).getMeasUnitKey());
        indicator.setOriginalValue(originalValue);
        indicator.setMoType(oriIndicator == null ? null : oriIndicator.getMoType());
        indicator.setDn(data.getDn());
        indicator.setUnit(oriIndicator == null ? unit : oriIndicator.getUnit());
    }

    public List<PerformanceIndexValue> getPmData(Indicator indicator, long startTime, long endTime) {
        LOGGER.info("executePm start time={}", startTime);
        Map<String, Long> timeRanges = PmDataHandleUtil.getIndQueryTimeRangeInPmFormat(endTime, INDICATOR_RANGE, true);
        List<Indicator> indicatorList = new ArrayList<>();
        indicatorList.add(indicator);
        List<QueryDeletedHistoryData> queryDeletedHistoryData = pmRequestService.getQueryDeletedHistoryData(
            indicatorList, timeRanges);
        DeletedHistoryData performanceResult = pmRequestService.getDeletedHistoryDataFormPerformance(
            queryDeletedHistoryData);
        if (CollectionUtils.isEmpty(performanceResult.getResult())) {
            LOGGER.error("query data is empty,moType={}, measUnitKey={}.", indicator.getMoType(),
                indicator.getMeasUnitKey());
            return new ArrayList<>();
        }

        List<PerformanceIndexValue> indexValues = new ArrayList<>();
        List<DeletedHistoryResult> deletedHistoryResults = performanceResult.getResult();
        for (DeletedHistoryResult deletedHistoryResult : deletedHistoryResults) {
            List<DeletedHistoryValues> datas = deletedHistoryResult.getDatas();
            if (CollectionUtils.isEmpty(datas)) {
                continue;
            }
            for (DeletedHistoryValues historyValue : datas) {
                PerformanceIndexValue indexValue = new PerformanceIndexValue();
                Map<String, String> values = historyValue.getValues();
                indexValue.setTimestampStr(String.valueOf(historyValue.getTimestamp()));
                indexValue.setIndexValue(values.get(indicator.getMeasTypeKey()));
                indexValues.add(indexValue);
            }
        }

        LOGGER.info("executePm cost time={}", Math.subtractExact(System.currentTimeMillis(), startTime));
        return indexValues;
    }

    @Override
    public VmIndicatorDisplayResults queryVMIndicator(PodViewCommonParam queryVMIndicatorParam)
        throws ServiceException {
        List<String> dnPods = new ArrayList<>();
        long endTime = queryVMIndicatorParam.getEndTime() != null
            ? queryVMIndicatorParam.getEndTime()
            : System.currentTimeMillis();
        ConfigData configData = ConfigurationUtil.getConfigDataByName(INDICATOR_RANGE);
        long startTime = endTime - Long.parseLong(configData.getValue());
        long timestamp = Objects.isNull(queryVMIndicatorParam.getEndTime()) ? 0L : queryVMIndicatorParam.getEndTime();
        BusinessInstanceModelDB businessInstanceModelDB = modelDao.queryInstanceByInstanceIdAndTimeLine(
            queryVMIndicatorParam.getInstanceId(), timestamp);
        dnPods.add(businessInstanceModelDB.getDn());
        List<ManagedObject> managedObjects = getVmMo(dnPods);
        // 查询Dn的网元类型，并根据网元类型生成指标
        String moType = managedObjects.stream().limit(1).map(ManagedObject::getType).collect(Collectors.toList()).get(0);
        List<Indicator> indicatorList = PropertiesUtil.buildVmIndicate(moType);
        pmIndicatorInstanceService.fillIndexNameAndUnit(indicatorList);
        
        VmIndicatorDisplayResults vmIndicatorDisplayResults = new VmIndicatorDisplayResults();

        loadMemoryTotalData(indicatorList, dnPods, startTime, endTime, vmIndicatorDisplayResults);
        loadMemoryUsageData(indicatorList, dnPods, startTime, endTime, vmIndicatorDisplayResults);

        vmIndicatorDisplayResults.setCpuNum(PerformanceClient.getInstance().taskVmCpuNmu(dnPods.get(0), moType));
        if (CollectionUtil.isNotEmpty(managedObjects)) {
            ManagedObject mo = managedObjects.get(0);
            if (mo instanceof ManagedElement) {
                ManagedElement managedElement = (ManagedElement) mo;
                vmIndicatorDisplayResults.setVmName(mo.getName());
                vmIndicatorDisplayResults.setVmIp(managedElement.getIPAddress());
                vmIndicatorDisplayResults.setAvailableStatus(managedElement.getOperationalStatus().name());
                vmIndicatorDisplayResults.setCsnState(
                    StringUtils.isEmpty(businessInstanceModelDB.getExtentAttr(OWN_CSN).getAttrValue()) ? 0 : 1);
            }
        }
        // vm实例关联指标时，要展示关联的指标而非默认指标
        PodIndicatorDisplayResults indicatorResults = queryMmIndicatorListAndData(queryVMIndicatorParam);
        if (CollectionUtils.isNotEmpty(indicatorResults.getPodIndicatorList())) {
            vmIndicatorDisplayResults.setVmIndicatorList(indicatorResults.getPodIndicatorList());
            vmIndicatorDisplayResults.setIndicatorIdList(indicatorResults.getIndicatorIdList());
        } else {
            vmIndicatorDisplayResults.setVmIndicatorList(getPodIndicatorList(indicatorList, dnPods, startTime, endTime));
        }
        return vmIndicatorDisplayResults;
    }

    private void loadMemoryUsageData(List<Indicator> indicatorList, List<String> dnPods, long startTime, long endTime,
        VmIndicatorDisplayResults vmIndicatorDisplayResults) {
        List<Indicator> memoryUsageRateIndicators = indicatorList.stream()
            .filter(in -> "Memory".equals(in.getMeasUnitKey()) && "MemUsage".equals(in.getMeasTypeKey()))
            .collect(Collectors.toList());
        for (Indicator indicator : memoryUsageRateIndicators) {
            List<IndicatorUnit> indicatorUnitList = PmDataHandleUtil.convertPerformanceQueryModelForDn(indicator,
                dnPods);
            List<HistoryDatas> historyDataList = getHistoryDataList(indicatorUnitList, startTime, endTime);
            HistoryQueryValue value = getVmMemDataForDn(historyDataList.get(0)).get(dnPods.get(0));
            vmIndicatorDisplayResults.setMemoryUsageRateIndex(value.getIndexName());
            if (CollectionUtil.isNotEmpty(value.getIndexValues())) {
                vmIndicatorDisplayResults.setMemoryUsageRate(
                    value.getIndexValues().get(value.getIndexValues().size() - 1).getIndexValue());
            }
        }
    }

    private void loadMemoryTotalData(List<Indicator> indicatorList, List<String> dnPods, long startTime, long endTime,
        VmIndicatorDisplayResults vmIndicatorDisplayResults) {
        List<Indicator> memoryIndicators = indicatorList.stream()
            .filter(in -> "Memory".equals(in.getMeasUnitKey()) && "MemTotal".equals(in.getMeasTypeKey()))
            .collect(Collectors.toList());
        for (Indicator indicator : memoryIndicators) {
            List<IndicatorUnit> indicatorUnitList = PmDataHandleUtil.convertPerformanceQueryModelForDn(indicator,
                dnPods);
            List<HistoryDatas> historyDataList = getHistoryDataList(indicatorUnitList, startTime, endTime);
            if (CollectionUtil.isNotEmpty(historyDataList)) {
                HistoryQueryValue value = getVmMemDataForDn(historyDataList.get(0)).get(dnPods.get(0));
                if (CollectionUtil.isNotEmpty(value.getIndexValues())) {
                    vmIndicatorDisplayResults.setMemoryIndex(value.getIndexName());
                    BigDecimal memoryTotal = new BigDecimal(
                        value.getIndexValues().get(value.getIndexValues().size() - 1).getIndexValue());
                    String memoryTotalStr = "0";
                    if ("KB".equals(value.getIndexUnit())) {
                        memoryTotalStr = memoryTotal.divide(new BigDecimal(1024 * 1024), 2, HALF_UP).toPlainString();
                    }
                    vmIndicatorDisplayResults.setMemoryTotal(memoryTotalStr);
                }
            }
        }
    }

    @Override
    public PipelineDisplayResult queryPipeLine(GrayscaleParam grayDisplayFlag) throws ServiceException {
        LOGGER.debug("begin to queryPipeLine");
        PipelineDisplayResult pipelineDisplayResult = new PipelineDisplayResult();

        // 前台灰度不展示
        if (grayDisplayFlag.getInstanceId() != null && grayDisplayFlag.getInstanceId() == 1) {
            moTypeDao.savePipelineDisplay(1);
            return pipelineDisplayResult;
        }
        BusinessStepModelDB businessStepModelDB = moTypeDao.queryPipelineStatus();
        if (businessStepModelDB == null) {
            LOGGER.error("can not find pipeline data");
            return pipelineDisplayResult;
        }
        // 处理灰度用户百分比映射
        ConfigData configDataMinus = ConfigurationUtil.getConfigDataByName("grayPercentageMinus");
        Map<Integer, Integer> grayPercentMap;
        try {
            grayPercentMap = JSON.parseObject(configDataMinus.getValue(),
                new TypeReference<Map<Integer, Integer>>() {}, JSONReader.Feature.SupportSmartMatch);
        } catch (JSONException e) {
            LOGGER.error("parse grayPercentageMinus error, use default value.", e);
            grayPercentMap = JSON.parseObject(configDataMinus.getDefaultValue(),
                new TypeReference<Map<Integer, Integer>>() {}, JSONReader.Feature.SupportSmartMatch);
        }
        Integer garyPercent = businessStepModelDB.getGrayPercentage();
        if (grayPercentMap.containsKey(garyPercent)) {
            businessStepModelDB.setGrayPercentage(grayPercentMap.get(garyPercent));
        }
        pipelineDisplayResult.setStepId(businessStepModelDB.getStepId());
        pipelineDisplayResult.setGrayPercentage(businessStepModelDB.getGrayPercentage());
        pipelineDisplayResult.setGrayDisplayFlag(businessStepModelDB.getGrayDisplayFlag());
        pipelineDisplayResult.setIsUpgrade(businessStepModelDB.getIsUpgrade());
        return pipelineDisplayResult;
    }

    @Override
    public GrayscaleResult queryGrayscale(GrayscaleParam queryGrayscaleParam) throws ServiceException {
        LOGGER.debug("begin to queryGrayscale");
        GrayscaleResult grayscaleResult = new GrayscaleResult();
        int instanceId = queryGrayscaleParam.getInstanceId();
        BusinessInstanceModelDB businessInstanceModelDB = modelDao.queryInstanceByInstanceId(instanceId);
        if (businessInstanceModelDB == null) {
            LOGGER.error("can't find businessInstanceModelDB by instanceId:{}", instanceId);
            return grayscaleResult;
        }
        if (!BusinessTopoConstant.CLASS_TYPE_CBS.equalsIgnoreCase(businessInstanceModelDB
            .getStaticExtentAttr(BusinessTopoConstant.CLASS_TYPE_ATTR_KEY).getStaticAttrValue())
            && !BusinessTopoConstant.CLASS_TYPE_CBS.equalsIgnoreCase(businessInstanceModelDB
            .getStaticExtentAttr(BusinessTopoConstant.SITE_MO_TYPE_ATTR_KEY).getStaticAttrValue())) {
            return grayscaleResult;
        }
        List<Integer> grayInstance = new ArrayList<>();
        if (businessInstanceModelDB.getModelType() == BusinessTopoConstant.SOLUTION_TYPE_ID) {
            overViewGray(grayInstance, instanceId);
        } else if (businessInstanceModelDB.getModelType() == BusinessTopoConstant.SITE_TYPE_ID) {
            List<BusinessInstanceModelDB> levelFourModelDBS = modelDao.queryNextLevelInstance(instanceId);
            Map<Integer, List<BusinessInstanceModelDB>> podInsMap = getPodInsMap(null, levelFourModelDBS);
            for (BusinessInstanceModelDB levelFourModelDB : levelFourModelDBS) {
                dealLevelModel(levelFourModelDB, grayInstance, true, podInsMap);
            }
        } else {
            LOGGER.error("Incorrect level:{}", businessInstanceModelDB.getModelType());
        }

        grayscaleResult.setGrayscaleList(grayInstance);

        return grayscaleResult;
    }

    private void overViewGray(List<Integer> grayInstance, int instanceId) {
        // 通过解决方案id获取所有站点实例
        List<BusinessInstanceModelDB> siteModelDBS = modelDao.querySiteBySolutionInsId(instanceId);
        Set<Integer> siteInsIds = siteModelDBS.stream()
            .map(BusinessInstanceModelDB::getInstanceId)
            .collect(Collectors.toSet());

        // 通过站点实例Id列表获取所有第六层实例
        List<BusinessInstanceModelDB> allLevelSixModelDBS = modelDao.getLevelSixModelDBSBySiteInsId(siteInsIds);

        // 根据站点id 进行分组，站点Id -> 6层实例列表
        Map<Integer, List<BusinessInstanceModelDB>> sixGroupModelMap = allLevelSixModelDBS.stream()
            .collect(Collectors.groupingBy(BusinessInstanceModelDB::getTargetInstanceId));

        // 对每个站点遍历判断是否存在灰度
        for (Map.Entry<Integer, List<BusinessInstanceModelDB>> entry : sixGroupModelMap.entrySet()) {
            // 对站点的实例列表根据modelId分组
            Map<String, List<BusinessInstanceModelDB>> sixGroupModel = entry.getValue()
                .stream()
                .collect(Collectors.groupingBy(BusinessInstanceModelDB::getModelId));
            // 任何一个分组存在灰度，就将站点id放入结果列表
            if (sixGroupModel.values().stream().anyMatch(this::isGray)) {
                grayInstance.add(entry.getKey());
            }
        }
    }

    private boolean dealLevelModel(BusinessInstanceModelDB levelModelDB, List<Integer> resultList, boolean isSite,
        Map<Integer, List<BusinessInstanceModelDB>> podInsMap) {
        Integer modelType = levelModelDB.getModelType();
        switch (modelType) {
            case 2:
                List<BusinessInstanceModelDB> levelThreeModelDBS = podInsMap.getOrDefault(levelModelDB.getInstanceId(),
                    Collections.emptyList());
                podInsMap = getPodInsMap(null, levelThreeModelDBS);
                for (BusinessInstanceModelDB levelThreeModelDB : levelThreeModelDBS) {
                    boolean result = dealLevelModel(levelThreeModelDB, resultList, isSite, podInsMap);
                    if (result) {
                        resultList.add(levelThreeModelDB.getInstanceId());
                    }
                }
                return CollectionUtils.isNotEmpty(resultList);
            case 3:
                List<BusinessInstanceModelDB> levelFourModelDBS = podInsMap.getOrDefault(levelModelDB.getInstanceId(),
                    Collections.emptyList());
                podInsMap = getPodInsMap(null, levelFourModelDBS);
                for (BusinessInstanceModelDB levelFourModelDB : levelFourModelDBS) {
                    boolean result = dealLevelModel(levelFourModelDB, resultList, isSite, podInsMap);
                    if (result) {
                        return true;
                    }
                }
                return false;
            case 4:
                List<BusinessInstanceModelDB> levelFiveModelDBS = podInsMap.getOrDefault(levelModelDB.getInstanceId(),
                    Collections.emptyList());
                podInsMap = getPodInsMap(null, levelFiveModelDBS);
                for (BusinessInstanceModelDB levelFiveModelDB : levelFiveModelDBS) {
                    boolean result = dealLevelModel(levelFiveModelDB, resultList, isSite, podInsMap);
                    if (result && isSite) {
                        resultList.add(levelFiveModelDB.getInstanceId());
                    } else if (result) {
                        return true;
                    }
                }
                return false;
            case 5:
                List<BusinessInstanceModelDB> levelSixModelDBS = podInsMap.getOrDefault(levelModelDB.getInstanceId(),
                    Collections.emptyList());
                return isGray(levelSixModelDBS);

            default:
                return false;
        }
    }

    private boolean isGray(List<BusinessInstanceModelDB> businessClusterList) {
        if (CollectionUtils.isEmpty(businessClusterList)) {
            return false;
        }
        if (businessClusterList.size() == 1) {
            return false;
        }

        if (businessClusterList.size() > 2) {
            return false;
        }

        boolean emptyFlag = false;
        for (BusinessInstanceModelDB businessClusterData : businessClusterList) {
            if (StringUtils.isEmpty(businessClusterData.getVersion())) {
                emptyFlag = true;
            }
        }
        if (emptyFlag) {
            return false;
        }

        Set<String> versionList = new HashSet<>();
        for (BusinessInstanceModelDB businessClusterData : businessClusterList) {
            List<BusinessInsExtentAttrDB> attrDBList = businessClusterData.getAttrDBList();
            Map<String, String> attrMap = attrDBList.stream()
                .filter(attr -> attr.getAttrName() != null && attr.getAttrValue() != null)
                .collect(Collectors.toMap(BusinessInsExtentAttrDB::getAttrName, BusinessInsExtentAttrDB::getAttrValue));
            String version = attrMap.get(BusinessTopoConstant.NODE_VERSION);
            if (StringUtils.isNotEmpty(version)) {
                versionList.add(version);
            }
        }

        return versionList.size() != 1;
    }

    private List<PodViewHistoryResults> getPodIndicatorList(List<Indicator> indicatorList, List<String> dnPods,
        long startTime, long endTime) {
        List<Indicator> queryIndicators = indicatorList.stream()
            .filter(in -> !"Memory".equals(in.getMeasUnitKey()) || !("MemTotal".equals(in.getMeasTypeKey())))
            .collect(Collectors.toList());
        List<PodViewHistoryResults> podIndicatorList = new ArrayList<>();
        for (Indicator indicator : queryIndicators) {
            List<IndicatorUnit> indicatorUnitList = PmDataHandleUtil.convertPerformanceQueryModelForDn(indicator,
                dnPods);
            List<HistoryDatas> historyDataList = getHistoryDataList(indicatorUnitList, startTime, endTime);
            List<PerformanceIndexValue> indexValues = getPerformance(historyDataList);
            indicator.setDn(dnPods.get(0));
            PodViewHistoryResults podViewHistoryResults = getPodViewHistoryResults(indicator, indexValues, endTime,
                startTime);
            podIndicatorList.add(podViewHistoryResults);
        }
        return podIndicatorList;
    }

    private List<ManagedObject> getVmMo(List<String> dnPods) throws ServiceException {
        List<DN> dnPodList = dnPods.stream().map(DN::new).collect(Collectors.toList());
        List<ManagedObject> managedObjects;
        try {
            managedObjects = MITManagerClient.newInstance().getMOList(dnPodList);
        } catch (OSSException e) {
            LOGGER.error("getAllRelationsByDns error", e);
            throw new ServiceException("get pod mo failed");
        }
        if (CollectionUtil.isEmpty(managedObjects)) {
            LOGGER.error("Deployment Nodes is null,dns{},pDN={}", dnPodList);
            throw new ServiceException("Deployment Nodes is null");
        }
        return managedObjects;
    }

    private HashMap<String, HistoryQueryValue> getVmMemDataForDn(HistoryDatas queryCondition) {
        List<HistoryQueryData> queryData = PerformanceClient.getInstance()
            .getHistoryDataFormPerformance(queryCondition);
        HashMap<String, HistoryQueryValue> dnLastValue = new HashMap<>();
        for (HistoryQueryData historyQueryData : queryData) {
            List<HistoryQueryValue> resultData = historyQueryData.getResultData();
            for (HistoryQueryValue resultDatum : resultData) {
                dnLastValue.putIfAbsent(resultDatum.getDn(), resultDatum);
            }
        }
        return dnLastValue;
    }

    public PodViewHistoryResults getPodViewHistoryResults(Indicator indicator, List<PerformanceIndexValue> indexValues,
        long endTime, long startTime) {
        PodViewHistoryResults podViewHistoryResults = new PodViewHistoryResults();
        podViewHistoryResults.setIndexName(indicator.getIndexName());
        podViewHistoryResults.setStartTime(String.valueOf(startTime));
        podViewHistoryResults.setEndTime(String.valueOf(endTime));
        podViewHistoryResults.setIndexUnit(indicator.getUnit());
        podViewHistoryResults.setHistoryTotalCount(indexValues.size());
        ConfigData configData = ConfigurationUtil.getConfigDataByName(INDICATOR_RANGE);
        Map<String, Pair<Long, Long>> rangeNameMap = PmDataHandleUtil.getTimeRange(
            endTime - Long.parseLong(configData.getValue()), endTime);
        Map<String, List<PerformanceIndexValue>> rangeIndexValue = PmDataHandleUtil.indexValueGroupBy(indexValues,
            rangeNameMap);
        podViewHistoryResults.setComparativeValueMap(rangeIndexValue);
        podViewHistoryResults.setDn(indicator.getDn());
        podViewHistoryResults.setMeasTypeKey(indicator.getMeasTypeKey());
        podViewHistoryResults.setMeasUnitKey(indicator.getMeasUnitKey());
        if (StringUtils.isNotEmpty(indicator.getOriginalValue())) {
            String replace = indicator.getOriginalValue().replace("=", "<=>").replace(",", "<,>");
            podViewHistoryResults.setOriginalValue(replace);
        }
        podViewHistoryResults.setMoType(indicator.getMoType());
        return podViewHistoryResults;
    }

    /**
     * getPerformance
     *
     * @param historyDataList historyDataList
     * @return List<PerformanceIndexValue>
     */
    public List<PerformanceIndexValue> getPerformance(List<HistoryDatas> historyDataList) {
        List<PerformanceIndexValue> totalData = new ArrayList<>();
        for (HistoryDatas historyData : historyDataList) {
            // 查询存活网元的性能数据
            int period = historyData.getPeriod();
            List<PerformanceIndexValue> queryData = getPmData(historyData, period);
            LOGGER.debug("[getPerformance] The data is {}", queryData.size());
            // 数据去重
            List<PerformanceIndexValue> distinct = new ArrayList<>(queryData.stream()
                .collect(Collectors.toMap(PerformanceIndexValue::getTimestampStr, Function.identity(),
                    (oldValue, newValue) -> (
                        Double.parseDouble(oldValue.getIndexValue()) > Double.parseDouble(newValue.getIndexValue())
                            ? oldValue
                            : newValue)))
                .values());
            totalData.addAll(distinct);
        }
        return totalData.stream()
            .sorted(Comparator.comparing(PerformanceIndexValue::getTimestampStr))
            .collect(Collectors.toList());
    }

    private List<PerformanceIndexValue> getPmData(HistoryDatas queryCondition, int period) {
        long startTime = System.currentTimeMillis();
        LOGGER.debug("executePm start time={}", startTime);
        List<HistoryQueryData> queryData = PerformanceClient.getInstance()
            .getHistoryDataFormPerformance(queryCondition);
        if (CollectionUtils.isEmpty(queryData)) {
            LOGGER.error("query data is empty,moType={}, measUnitKey={}.", queryCondition.getMoType(),
                queryCondition.getMeasUnitKey());
            return new ArrayList<>();
        }

        List<PerformanceIndexValue> indexValues = new ArrayList<>();
        for (HistoryQueryData historyQueryData : queryData) {
            int dataSize = historyQueryData.getResultData().size();
            for (int i = 0; i < dataSize; i++) {
                HistoryQueryValue queryValue = historyQueryData.getResultData().get(i);
                List<HistoryIndexValue> value = handleMultiValue(queryValue.getIndexValues());
                LOGGER.debug("get data count = {}", value.size());
                String kpiId = queryValue.getDn() + queryValue.getIndexGroup() + queryValue.getIndexKey();
                if (StringUtils.isNotEmpty(queryValue.getDisplayValue())) {
                    kpiId += queryValue.getDisplayValue();
                }
                if (StringUtils.isNotEmpty(kpiId)) {
                    kpiId = kpiId.replace(COMMA, "");
                }
                LOGGER.debug("get kpiId = {}", kpiId);
                for (HistoryIndexValue valueArray : value) {
                    // 每一行拼接时间戳, kpiId, 指标值, 指标单位, 采集周期, unitedId
                    PerformanceIndexValue indexValue = new PerformanceIndexValue();
                    indexValue.setTimestampStr(String.valueOf(valueArray.getTimestamp()));
                    indexValue.setIndexValue(valueArray.getIndexValue());
                    indexValue.setIndexName(queryValue.getIndexName());
                    indexValues.add(indexValue);
                }
            }
        }
        LOGGER.debug("executePm cost time={}", Math.subtractExact(System.currentTimeMillis(), startTime));
        return indexValues;
    }

    protected static List<HistoryIndexValue> handleMultiValue(List<HistoryIndexValue> value) {
        List<HistoryIndexValue> result = new ArrayList<>();
        LinkedHashMap<String, List<HistoryIndexValue>> listMapByTime = value.stream()
            .collect(
                Collectors.groupingBy(HistoryIndexValue::getTimestampStr, LinkedHashMap::new, Collectors.toList()));
        try {
            for (Map.Entry<String, List<HistoryIndexValue>> entry : listMapByTime.entrySet()) {
                List<HistoryIndexValue> indexValueList = entry.getValue();
                indexValueList = indexValueList.stream()
                    .filter((HistoryIndexValue indexValue) -> StringUtils.isNotEmpty(indexValue.getIndexValue()))
                    .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(indexValueList)) {
                    // 性能查过来的数据value可能是null
                    continue;
                }
                if (indexValueList.size() > 1) {
                    // 倒序后取第一个值，即为最大值
                    indexValueList = indexValueList.stream()
                        .sorted(Comparator.comparing(HistoryIndexValue::getIndexValue,
                            Comparator.comparingDouble(Double::parseDouble)).reversed())
                        .collect(Collectors.toList());
                    result.add(indexValueList.get(0));
                } else {
                    result.addAll(indexValueList);
                }
            }
        } catch (NumberFormatException exception) {
            // 该指标的性能数据转换异常
            LOGGER.error("[handleMultiValue] format indexValue fail! error is {}", exception);
        }
        return result;
    }

    /**
     * getHistoryDataList
     *
     * @param indicatorUnitList indicatorUnitList
     * @param startTime startTime
     * @param endTime endTime
     * @return List<HistoryDatas>
     */
    public List<HistoryDatas> getHistoryDataList(List<IndicatorUnit> indicatorUnitList, long startTime, long endTime) {
        List<HistoryDatas> historyDataList = new ArrayList<>(indicatorUnitList.size());
        for (IndicatorUnit indicatorUnit : indicatorUnitList) {
            int period = PerformanceClient.getInstance()
                .getPeriod(indicatorUnit.getMoType(), indicatorUnit.getMeasUnitKey());
            if (period == -1) {
                LOGGER.error("get task periods error,  = {}", indicatorUnit.getIndicatorId());
                continue;
            }
            for (int i = 0; i < indicatorUnit.getIndicatorDetailList().size(); ) {
                List<IndicatorDetail> indicators = indicatorUnit.getIndicatorDetailList();
                HistoryDatas historyData = new HistoryDatas();
                historyData.setPeriod(period);
                historyData.setBeginTime(startTime);
                historyData.setEndTime(endTime);
                historyData.setMoType(indicatorUnit.getMoType());
                historyData.setMeasUnitKey(indicatorUnit.getMeasUnitKey());
                historyData.setMo2Index(JSON.toJSONString(indicators));
                historyData.setSiteId(indicatorUnit.getSiteId());
                historyDataList.add(historyData);
                // 当前取第一个
                break;
            }
            break;
        }
        return historyDataList;
    }

    private void getVmData(BusinessClusterData businessClusterData, List<BusinessInstanceModelDB> vmInsList) {
        List<BusinessInstanceModelDB> filterIns = AuthUtils.permissionFilter(new ArrayList<>(vmInsList));
        Set<VmData> vmDataList = new HashSet<>();
        Map<Integer, List<BusinessInstanceModelDB>> vmInsGroupList = filterIns.stream()
            .collect(Collectors.groupingBy(BusinessInstanceModelDB::getInstanceId));
        VmConvert vmConvert = new VmConvert();
        for (Map.Entry<Integer, List<BusinessInstanceModelDB>> entry : vmInsGroupList.entrySet()) {
            BusinessInstanceModelDB vmInstanceModelDB = entry.getValue().get(0);
            VmData vmData = vmConvert.convert(vmInstanceModelDB);
            vmDataList.add(vmData);
        }
        businessClusterData.setVmDataList(new ArrayList<>(vmDataList));
    }

    private void buildBusinessInstanceModelData(BusinessInstanceModelDB manageObjectModelDB,
        BusinessClusterData businessClusterData, List<BusinessInstanceModelDB> podInsList,
        Map<Integer, List<BusinessInstanceModelDB>> podInsMap) {

        List<BusinessInstanceModelDB> filterIns = podInsMap.getOrDefault(manageObjectModelDB.getInstanceId(),
            Collections.emptyList());
        businessClusterData.setPodTotalCount(filterIns.size());
        podInsList.addAll(filterIns);
    }

    private void getPodData(List<BusinessInstanceModelDB> podInsList, BusinessClusterData businessClusterData) {
        List<PodData> podDataList = new ArrayList<>();
        PodConvert podConvert = new PodConvert();
        for (BusinessInstanceModelDB podModelDB : podInsList) {
            PodData podData = podConvert.convert(podModelDB);
            podDataList.add(podData);
        }
        businessClusterData.setPodDataList(podDataList);
    }

    private void setGrayFlag(List<BusinessClusterData> businessClusterList, int instanceId, long timeStamp) {
        if (setGray(businessClusterList, instanceId, timeStamp)) {
            return;
        }

        boolean emptyFlag = false;
        for (BusinessClusterData businessClusterData : businessClusterList) {
            if (StringUtils.isEmpty(businessClusterData.getVersion())) {
                emptyFlag = true;
            }
        }
        if (emptyFlag) {
            for (BusinessClusterData businessClusterData : businessClusterList) {
                businessClusterData.setIsGray(2);
            }
            return;
        }

        sortList(businessClusterList);

        Set<String> versionList = new HashSet<>();
        for (BusinessClusterData businessClusterData : businessClusterList) {
            versionList.add(businessClusterData.getVersion());
        }

        // 所有版本号都相同 1灰度区 0生产区 2不区分
        if (versionList.size() == 1) {
            for (BusinessClusterData businessClusterData : businessClusterList) {
                businessClusterData.setIsGray(2);
            }
        } else {
            for (int i = 0; i < businessClusterList.size(); i++) {
                if (i == 0) {
                    businessClusterList.get(i).setIsGray(0);
                } else {
                    businessClusterList.get(i).setIsGray(1);
                }
            }
        }
    }

    private boolean setGray(List<BusinessClusterData> businessClusterList, int instanceId, long timeStamp) {
        BusinessInstanceModelDB businessInstanceModelDB = modelDao.queryInstanceByInstanceIdAndTimeLine(instanceId,
            timeStamp);
        if (Objects.isNull(businessInstanceModelDB) || Objects.isNull(businessInstanceModelDB.getInstanceId())) {
            return false;
        }
        String supportGrey = businessInstanceModelDB.getStaticExtentAttr(
            BusinessTopoConstant.SUPPORT_GRAY_UPGRADE_ATTR_KEY).getStaticAttrValue();
        boolean result = Objects.nonNull(supportGrey) && Boolean.parseBoolean(supportGrey);
        if (!result) {
            for (BusinessClusterData businessClusterData : businessClusterList) {
                businessClusterData.setIsGray(2);
            }
            return true;
        }

        if (businessClusterList.size() == 1) {
            for (BusinessClusterData businessClusterData : businessClusterList) {
                businessClusterData.setIsGray(0);
            }
            return true;
        }

        if (businessClusterList.size() > 2) {
            for (BusinessClusterData businessClusterData : businessClusterList) {
                businessClusterData.setIsGray(2);
            }
            return true;
        }
        return false;
    }

    private void sortList(List<BusinessClusterData> businessClusterList) {
        businessClusterList.sort((busOne, busTwo) -> {
            Matcher matcherOne = pattern.matcher(busOne.getVersion());
            Matcher matcherTwo = pattern.matcher(busTwo.getVersion());
            StringBuilder one = new StringBuilder();
            StringBuilder two = new StringBuilder();
            while (matcherOne.find()) {
                one.append(matcherOne.group());
            }
            while (matcherTwo.find()) {
                two.append(matcherTwo.group());
            }
            return Integer.compare(Integer.parseInt(one.toString()), Integer.parseInt(two.toString()));
        });
    }

    private List<Indicator> fillEmptyIndexName(List<Indicator> indicatorList) {
        // 导入后没有indexName和unit，需要补齐
        return pmIndicatorInstanceService.fillIndexNameAndUnit(indicatorList);
    }

    public PodIndicatorDisplayResults queryIndicatorListAndDataForMm(PodViewCommonParam param) throws ServiceException {
        if (param.getEndTime() == null) {
            param.setEndTime(0L);
        }
        BusinessInstanceModelDB moTypeModel = modelDao.queryInstanceByInstanceIdAndTimeLine(param.getInstanceId(), param.getEndTime());
        if (moTypeModel == null) {
            LOGGER.error("query moType instance is null, instanceId = {}", param.getInstanceId());
            return new PodIndicatorDisplayResults();
        }
        BusinessInstanceModelDB stripeGroupModel = modelDao.queryInstanceByInstanceIdAndTimeLine(param.getStripeId(), param.getEndTime());
        if (stripeGroupModel == null) {
            LOGGER.error("query stripe group instance is null, instanceId = {}", param.getInstanceId());
            return new PodIndicatorDisplayResults();
        }

        // 根据第五层modelId拼出第六层modelId
        String typeModelId = moTypeModel.getModelId();
        String instanceModelId = BusinessTopoConstant.CLUSTER_INSTANCE_ID + typeModelId.substring(1);
        String stripeGroupName = stripeGroupModel.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE_GROUP_NAME).getAttrValue();
        Set<String> stripeNames = new HashSet<>();
        if (GSU.equals(stripeGroupName)) {
            stripeNames.add(GSU);
            stripeNames.add(CSU_00);
        } else {
            stripeNames.add(stripeGroupName);
            stripeNames.add(stripeGroupName.replace(RSU, CSU));
        }

        // 根据第六层modelId查询实例，再使用条带名称和条带单元过滤
        List<Indicator> indicatorList = getIndicatorsByModelIdAndStripe(param, instanceModelId, stripeNames);
        if (CollectionUtils.isEmpty(indicatorList)) {
            return new PodIndicatorDisplayResults();
        }

        // 根据是否传入indicatorId过滤指标
        List<PodViewHistoryResults> displayIndicatorList = new ArrayList<>();
        if (StringUtils.isNotEmpty(param.getIndicatorId())) {
            List<Indicator> indicators = indicatorList.stream()
                .filter(indicator -> indicator.getIndicatorId().equalsIgnoreCase(param.getIndicatorId()))
                .collect(Collectors.toList());
            getIndicators(param, indicatorList, sortAndGetFirstIndicator(indicators), displayIndicatorList, true);
        } else {
            getIndicators(param, indicatorList, sortAndGetFirstIndicator(indicatorList), displayIndicatorList, true);
        }
        PodIndicatorDisplayResults indicatorDisplayResults = new PodIndicatorDisplayResults();
        indicatorDisplayResults.setPodIndicatorList(displayIndicatorList);
        if (param.getIndicatorId() == null) {
            setIndicatorIdList(indicatorDisplayResults, indicatorList);
        }
        return indicatorDisplayResults;
    }

    private List<Indicator> getIndicatorsByModelIdAndStripe(PodViewCommonParam param, String instanceModelId, Set<String> stripeNames)
        throws ServiceException {
        List<BusinessInstanceModelDB> instances = modelDao.queryInstanceWithAttrByModelIdTimeLine(
            instanceModelId, param.getEndTime());
        instances = instances.stream()
            .filter(ins -> param.getStripeUnit().equals(ins.getExtentAttr(BusinessTopoConstant.MM_HWS_UNIT).getAttrValue())
                && stripeNames.contains(ins.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue()))
            .collect(Collectors.toList());

        List<BusinessInstanceModelDB> filterIns = AuthUtils.permissionFilter(instances);
        List<Integer> instanceIdList = filterIns.stream().map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList());
        return fillEmptyIndexName(moTypeDao.queryMoTypeIndicatorListByInstanceIdList(instanceIdList, param.getEndTime()));
    }

    private Indicator sortAndGetFirstIndicator(List<Indicator> indicatorList) {
        indicatorList.sort(Comparator.comparing(Indicator::getIndicatorSortNumber, Comparator.nullsLast(Integer::compareTo))
            .thenComparing(Indicator::getIndexName, Comparator.nullsLast(String::compareTo))
        );
        return indicatorList.get(0);
    }

    public PodIndicatorDisplayResults queryNewIndicatorList(PodViewCommonParam indicatorParam) throws ServiceException {
        BusinessInstanceModelDB businessInstanceModelDB;
        if (indicatorParam.getEndTime() == null) {
            businessInstanceModelDB = modelDao.queryInstanceByInstanceId(indicatorParam.getInstanceId());
        } else {
            businessInstanceModelDB = modelDao.queryInstanceByInstanceIdAndTimeLine(indicatorParam.getInstanceId(),
                indicatorParam.getEndTime());
        }
        PodIndicatorDisplayResults indicatorDisplayResults = new PodIndicatorDisplayResults();
        if (Objects.isNull(businessInstanceModelDB)) {
            LOGGER.warn("[queryPodIndicator] could not find ins, instance id is {}", indicatorParam.getInstanceId());
            return indicatorDisplayResults;
        }
        List<Indicator> indicatorList = getIndicators(indicatorParam, null);
        if (CollectionUtils.isEmpty(indicatorList)) {
            return indicatorDisplayResults;
        }
        sortAndGetFirstIndicator(indicatorList);
        setIndicatorIdList(indicatorDisplayResults, indicatorList);
        return indicatorDisplayResults;
    }

    private boolean isSameStripe(BusinessInstanceModelDB model, String stripe) {
        String thisStripe = model.getExtentAttr(BusinessTopoConstant.MM_HWS_STRIPE).getAttrValue();
        if (stripe.contains(GSU)) {
            return GSU.equals(thisStripe) || CSU_00.equals(thisStripe);
        }
        return thisStripe.replace(CSU, RSU).equals(stripe.replace(CSU, RSU));
    }
}
