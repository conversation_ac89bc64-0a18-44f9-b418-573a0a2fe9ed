/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.openapi.relation;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.business.openapi.relation.model.AssociationOfMo;
import com.huawei.i2000.dvtoposervice.business.openapi.relation.model.DnRelation;
import com.huawei.i2000.dvtoposervice.business.openapi.relation.model.MoOfGroup;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmDetail;
import com.huawei.i2000.dvtoposervice.model.HisAlarmQueryParam;
import com.huawei.i2000.dvtoposervice.model.Solution;
import com.huawei.i2000.dvtoposervice.model.SolutionExParam;
import com.huawei.i2000.dvtoposervice.model.UserParam;

import java.util.List;

/**
 * Topo关联关系查询
 *
 * <AUTHOR>
 * @since 2025/6/23
 */
public interface BusinessTopoRelationService {

    List<AssociationOfMo> getApplicationRelation(SolutionExParam param) throws ServiceException;

    List<DnRelation> getDnRelationShip(SolutionExParam param) throws ServiceException;

    List<MoOfGroup> getGroupPartition(SolutionExParam param) throws ServiceException;

    List<AlarmDetail> queryHistoryAlarm(HisAlarmQueryParam param);

    List<Solution> getPermissionsSolList(UserParam param);
}
