/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.impl;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmDetail;
import com.huawei.i2000.dvtoposervice.business.openapi.relation.BusinessTopoRelationServiceImpl;
import com.huawei.i2000.dvtoposervice.business.openapi.relation.model.AssociationOfMo;
import com.huawei.i2000.dvtoposervice.business.openapi.relation.model.DnRelation;
import com.huawei.i2000.dvtoposervice.business.openapi.relation.model.MoOfGroup;
import com.huawei.i2000.dvtoposervice.delegate.BusinessExternalServiceDelegate;
import com.huawei.i2000.dvtoposervice.model.HisAlarmQueryParam;
import com.huawei.i2000.dvtoposervice.model.ResponseResult;
import com.huawei.i2000.dvtoposervice.model.Solution;
import com.huawei.i2000.dvtoposervice.model.SolutionExParam;
import com.huawei.i2000.dvtoposervice.model.UserParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * BusinessExternalServiceDelegateImpl
 *
 * @since 2025-8-2 18:33:53
 */
@Component
public class BusinessExternalServiceDelegateImpl implements BusinessExternalServiceDelegate {

    @Autowired
    private BusinessTopoRelationServiceImpl businessTopoRelationService;

    @Override
    public ResponseResult getDnRelation(HttpContext context, SolutionExParam param)
        throws ServiceException {
        ResponseResult responseResult = new ResponseResult();
        List<DnRelation> dnRelations = businessTopoRelationService.getDnRelationShip(param);
        responseResult.setData(dnRelations);
        return responseResult;
    }

    @Override
    public ResponseResult getAppRelation(HttpContext context, SolutionExParam param) throws ServiceException {
        ResponseResult responseResult = new ResponseResult();
        List<AssociationOfMo> associationOfMoList = businessTopoRelationService.getApplicationRelation(param);
        responseResult.setData(associationOfMoList);
        return responseResult;
    }

    @Override
    public ResponseResult getGroupPartition(HttpContext context, SolutionExParam param) throws ServiceException {
        ResponseResult responseResult = new ResponseResult();
        List<MoOfGroup> moOfGroupList = businessTopoRelationService.getGroupPartition(param);
        responseResult.setData(moOfGroupList);
        return responseResult;
    }

    @Override
    public ResponseResult getHistoryAlarmByCondition(HttpContext context, HisAlarmQueryParam param) throws ServiceException {
        ResponseResult responseResult = new ResponseResult();
        List<AlarmDetail> alarmDetails = businessTopoRelationService.queryHistoryAlarm(param);
        responseResult.setData(alarmDetails);
        return responseResult;
    }

    @Override
    public ResponseResult getPermissionSol(HttpContext context, UserParam param) throws ServiceException {
        ResponseResult responseResult = new ResponseResult();
        List<Solution> permissionSolList = businessTopoRelationService.getPermissionsSolList(param);
        responseResult.setData(permissionSolList);
        return responseResult;
    }
}

