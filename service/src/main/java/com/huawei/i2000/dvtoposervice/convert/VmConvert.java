/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.convert;

import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.model.VmData;
import org.apache.commons.lang3.StringUtils;

/**
 * Vm转换类
 *
 * <AUTHOR>
 * @since 2025/8/1
 */
public class VmConvert {

    public VmData convert(BusinessInstanceModelDB instanceModelDB) {
        VmData vmData = new VmData();
        convertStatic(vmData, instanceModelDB);
        convertDynamic(vmData, instanceModelDB);
        return vmData;
    }

    public void convertStatic(VmData vmIns, BusinessInstanceModelDB instance) {

    }

    public void convertDynamic(VmData vmIns, BusinessInstanceModelDB instance) {
        vmIns.setDnId(String.valueOf(instance.getInstanceId()));
        vmIns.setVmName(instance.getExtentAttr("vmName").getAttrValue());
        vmIns.setVmIp(instance.getExtentAttr("nodeIp").getAttrValue());
        String nodeServiceStatus = instance.getExtentAttr(BusinessTopoConstant.NODE_SERVICE_STATUS)
                .getAttrValue();
        vmIns.setAvailableStatus(StringUtils.isEmpty(nodeServiceStatus) ? "Enabled" : nodeServiceStatus);
        String alarmCsn = instance.getExtentAttr("OwnCsn").getAttrValue();
        vmIns.setCsnState(StringUtils.isEmpty(alarmCsn) ? 0 : 1);
    }
}
