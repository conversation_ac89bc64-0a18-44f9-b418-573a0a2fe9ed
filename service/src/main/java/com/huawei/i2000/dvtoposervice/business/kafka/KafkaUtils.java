/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.kafka;

import com.alibaba.fastjson2.JSON;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmDetail;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessMoKafkaEvent;
import com.huawei.i2000.dvtoposervice.constant.KafkaConstant;
import com.huawei.i2000.dvtoposervice.util.dto.CustomRejectedExecutionHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/6/11
 */
public class KafkaUtils {
    private static volatile KafkaProducer alarmProducer = null;

    private static volatile KafkaProducer moEventProducer = null;

    private static final Object PRODUCER_LOCK = new Object();

    private static final Object PRODUCER_MO_LOCK = new Object();

    private static final Map<String, Object> PRODUCER_CONFIGS = new HashMap<>();

    private static final ThreadPoolExecutor ALARM_SEND_EXECUTOR = new ThreadPoolExecutor(1, 1, 10L, TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(200), new CustomizableThreadFactory("Send-Alarm-Kafka-Thread-"), new CustomRejectedExecutionHandler());

    private static final ThreadPoolExecutor MO_EVENT_SEND_EXECUTOR = new ThreadPoolExecutor(1, 1, 10L, TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(200), new CustomizableThreadFactory("Send-Mo_Event-Kafka-Thread-"), new CustomRejectedExecutionHandler());

    static {
        PRODUCER_CONFIGS.put(KafkaConstant.KAFKA_KEY_SERIALIZER, KafkaConstant.KAFKA_SERIALIZER);
        PRODUCER_CONFIGS.put(KafkaConstant.KAFKA_VALUE_SERIALIZER, KafkaConstant.KAFKA_SERIALIZER);
        PRODUCER_CONFIGS.put(KafkaConstant.MAX_REQUEST_SIZE_KEY, KafkaConstant.MAX_REQUEST_SIZE_DEFAULT_VALUE);
    }

    private KafkaUtils() {}

    public static void sendAlarm(String solutionName, Set<AlarmDetail> alarmList) {
        if (CollectionUtils.isNotEmpty(alarmList)) {
            ALARM_SEND_EXECUTOR.execute(() -> {
                KafkaProducer producer = KafkaUtils.getAlarmProducer();
                for (List<AlarmDetail> alarms : ListUtils.partition(new ArrayList<>(alarmList), 200)) {
                    producer.send(JSON.toJSONString(Collections.singletonMap(solutionName, alarms)));
                }
            });
        }
    }

    public static KafkaProducer getAlarmProducer() {
        if (Objects.isNull(alarmProducer)) {
            synchronized (PRODUCER_LOCK) {
                if (Objects.isNull(alarmProducer)) {
                    alarmProducer = new KafkaProducer(KafkaConstant.TOPIC_TOPO_ALARM, PRODUCER_CONFIGS);
                }
            }
        }
        return alarmProducer;
    }

    public static void sendMoEvent(Set<BusinessMoKafkaEvent> businessMoEvents) {
        if (CollectionUtils.isNotEmpty(businessMoEvents)) {
            MO_EVENT_SEND_EXECUTOR.execute(() -> {
                KafkaProducer producer = KafkaUtils.getMoEventProducer();
                for (List<BusinessMoKafkaEvent> moEvents : ListUtils.partition(new ArrayList<>(businessMoEvents), 200)) {
                    producer.send(JSON.toJSONString(moEvents));
                }
            });
        }
    }

    public static KafkaProducer getMoEventProducer() {
        if (Objects.isNull(moEventProducer)) {
            synchronized (PRODUCER_MO_LOCK) {
                if (Objects.isNull(moEventProducer)) {
                    moEventProducer = new KafkaProducer(KafkaConstant.TOPIC_TOPO_MO_EVENT, PRODUCER_CONFIGS);
                }
            }
        }
        return moEventProducer;
    }
}
