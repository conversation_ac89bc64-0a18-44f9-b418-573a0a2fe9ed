/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.util.dto;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 功能描述：线程池拒绝策略
 *
 * <AUTHOR>
 * @since 2025/6/11
 */
public class CustomRejectedExecutionHandler implements RejectedExecutionHandler {
    private static final OssLog LOGGER = OssLogFactory.getLogger(CustomRejectedExecutionHandler.class);

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        // 打印日志并丢弃任务
        LOGGER.error("Task {}  rejected from {}", r.toString(), executor.toString());
    }
}