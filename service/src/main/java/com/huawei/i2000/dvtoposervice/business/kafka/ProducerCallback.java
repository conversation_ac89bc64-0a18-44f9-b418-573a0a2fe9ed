/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.kafka;

import com.huawei.bsp.log.Logger;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cloudsop.mq.api.common.IRecordMetadata;
import com.huawei.cloudsop.mq.api.producer.IProducerCallback;
import com.huawei.cloudsop.mq.api.producer.IProducerRecord;

import java.util.Objects;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/6/11
 */
public class ProducerCallback extends IProducerCallback<String, String> {
    private static final Logger LOGGER = OssLogFactory.getLogger(ProducerCallback.class);

    /**
     * 构造函数
     *
     * @param record record
     */
    public ProducerCallback(IProducerRecord<String, String> record) {
        super(record);
    }

    @Override
    public void onCompletion(IRecordMetadata metadata, Exception exception, IProducerRecord<String, String> iProducerRecord) {
        if (Objects.nonNull(exception)) {
            LOGGER.error("Fail to produce, e: ", exception);
        } else {
            LOGGER.info("Success to produce, offset is {}, partition is {}", metadata.offset(), metadata.partition());
        }
    }
}
