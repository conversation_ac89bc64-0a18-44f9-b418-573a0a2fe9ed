/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.convert;

import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.model.PodData;
import org.apache.commons.lang3.StringUtils;

/**
 * Pod转换类
 *
 * <AUTHOR>
 * @since 2025/8/1
 */
public class PodConvert {

    public PodData convert(BusinessInstanceModelDB instanceModelDB) {
        PodData podData = new PodData();
        convertStatic(podData, instanceModelDB);
        convertDynamic(podData, instanceModelDB);
        return podData;
    }

    public void convertStatic(PodData podData, BusinessInstanceModelDB instance) {

    }

    public void convertDynamic(PodData podData, BusinessInstanceModelDB instance) {
        podData.setDnId(String.valueOf(instance.getInstanceId()));
        podData.setPodName(instance.getExtentAttr("podName").getAttrValue());
        podData.setCreateTime(instance.getExtentAttr(BusinessTopoConstant.NODE_CREATE_TIME).getAttrValue());
        String nodeServiceStatus = instance.getExtentAttr(BusinessTopoConstant.NODE_SERVICE_STATUS)
                .getAttrValue();
        podData.setAvailableStatus(StringUtils.isEmpty(nodeServiceStatus) ? "Enabled" : nodeServiceStatus);
        String alarmCsn = instance.getExtentAttr("AlarmCsn").getAttrValue();
        podData.setCsnState(StringUtils.isEmpty(alarmCsn) ? 0 : 1);
    }
}
