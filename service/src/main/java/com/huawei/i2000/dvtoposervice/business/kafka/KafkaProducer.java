/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.kafka;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cloudsop.mq.api.factory.ProducerFactory;
import com.huawei.cloudsop.mq.api.producer.IProducer;
import com.huawei.cloudsop.mq.api.producer.IProducerRecord;
import com.huawei.cloudsop.mq.exception.MqException;
import com.huawei.i2000.dvtoposervice.util.DateUtil;

import java.util.Map;
import java.util.Objects;

/**
 * 功能描述：Mq生产者对象
 *
 * <AUTHOR>
 * @since 2025/6/11
 */
public class KafkaProducer {
    private static final OssLog LOGGER = OssLogFactory.getLogger(KafkaProducer.class);

    private IProducer<String, String> producer = null;

    private final String topicName;

    /**
     * 构造一个MQ生产者
     *
     * @param topicName 生产者生产消息的topic名称
     * @param configs configs
     */
    public KafkaProducer(String topicName, Map<String, Object> configs) {
        this.topicName = topicName;
        initProducer(configs);
    }

    /**
     * 初始化生产者
     *
     * @param configs configs
     */
    private void initProducer(Map<String, Object> configs) {
        LOGGER.info("start to init producer, topic = {}", this.topicName);
        // 服务还没有启动获取其他原因下，创建失败生产者失败后重试
        do {
            try {
                this.producer = ProducerFactory.getInstance().getProducer(configs);
                if (Objects.isNull(this.producer)) {
                    LOGGER.error("init kafka producer failed, topic: {}", this.topicName);
                    DateUtil.sleep(5);
                }
            } catch (Throwable throwable) {
                LOGGER.info("init kafka producer error, e: ", throwable);
                DateUtil.sleep(5);
            }
        } while (Objects.isNull(this.producer));
        LOGGER.info("init producer finished, topic = {}", this.topicName);
    }

    /**
     * 生产消息
     *
     * @param message 消息
     */
    public void send(String message) {
        IProducerRecord<String, String> record = ProducerFactory.getInstance().createProducerRecord(this.topicName, message);
        try {
            this.producer.send(record, new ProducerCallback(record));
        } catch (MqException exception) {
            LOGGER.error("MqException occurs when produce message, message is {}, exception is {}", message, exception.getMessage());
        }
    }
}