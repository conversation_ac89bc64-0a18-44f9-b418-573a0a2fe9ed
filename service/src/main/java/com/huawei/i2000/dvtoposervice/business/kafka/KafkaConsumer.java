/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.kafka;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cloudsop.mq.api.consumer.IConsumer;

import javax.management.timer.Timer;
import java.util.List;

/**
 * 功能描述：KafkaConsumer生产者对象
 *
 * <AUTHOR>
 * @since 2025/8/7
 */
public class KafkaConsumer {

    private static final OssLog LOGGER = OssLogFactory.getLogger(KafkaConsumer.class);

    public void subscribeTopic(IConsumer<String, String> consumer, List<String> topicList, String topicName) {
        while (true) {
            try {
                consumer.subscribe(topicList);
                LOGGER.info("subject topic {} success", topicName);
                break;
            } catch (Exception e) {
                LOGGER.error("catch a mqException", e);
                try {
                    LOGGER.warn("mqException, wait 3s.");
                    Thread.sleep(Timer.ONE_SECOND * 3);
                } catch (InterruptedException ex) {
                    LOGGER.error("Thread.sleep failed, ", ex);
                }
            }
        }
    }
}
