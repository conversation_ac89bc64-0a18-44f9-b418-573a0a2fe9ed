/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.event;

import com.alibaba.fastjson2.JSON;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cloudsop.mq.api.common.TopicPartition;
import com.huawei.cloudsop.mq.api.consumer.IConsumer;
import com.huawei.cloudsop.mq.api.consumer.IConsumerRecord;
import com.huawei.cloudsop.mq.api.consumer.IConsumerRecords;
import com.huawei.cloudsop.mq.api.consumer.OffsetAndMetadata;
import com.huawei.cloudsop.mq.api.factory.ConsumerFactory;
import com.huawei.cloudsop.mq.exception.MqException;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmDetail;
import com.huawei.i2000.dvtoposervice.business.kafka.IConsumerRebalanceListenerImpl;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.BusinessThresholdAlarmStorageService;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.BusinessTopoAlarmStorageService;
import com.huawei.i2000.dvtoposervice.constant.KafkaConstant;
import com.huawei.i2000.dvtoposervice.impl.DVTopoServiceDelegateImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 告警订阅监听
 *
 * <AUTHOR>
 * @since 2025/8/2
 */
@Component
public class AlarmConsumer implements InitializingBean {

    private static final OssLog LOGGER = OssLogFactory.getLogger(AlarmConsumer.class);

    private static final int POLL_MILLIONS = 60 * 1000;

    private Map<String, Object> configs = new HashMap<>();

    private IConsumer<String, String> consumer;

    private Map<TopicPartition, OffsetAndMetadata> currentOffsets = new ConcurrentHashMap<>();

    private final ExecutorService consumerKafkaExecutors = new ThreadPoolExecutor(1, 1, 1, TimeUnit.MINUTES,
            new SynchronousQueue<>(), new CustomizableThreadFactory("Incident-consumerKafka-Thread-"), new ThreadPoolExecutor.DiscardPolicy());

    @Autowired
    private BusinessTopoAlarmStorageService businessTopoAlarmStorageService;

    @Autowired
    private BusinessThresholdAlarmStorageService businessThresholdAlarmStorageService;

    @Autowired
    private DVTopoServiceDelegateImpl dvTopoServiceDelegate;

    @Override
    public void afterPropertiesSet() {
        try {
            initConsumer();
        } catch (Throwable e) {
            LOGGER.error("AlarmConsumer init error,", e);
        }
    }

    private void initConsumer() {
        initConsumerConfig();
        // 异步启用订阅流程
        ExecutorService executors = Executors.newSingleThreadExecutor();
        executors.execute(this::subscribe);
    }

    private void initConsumerConfig() {
        configs.put(KafkaConstant.KEY_DESERIALIZER, KafkaConstant.STRING_DESERIALIZER);
        configs.put(KafkaConstant.VALUE_DESERIALIZER, KafkaConstant.STRING_DESERIALIZER);
        configs.put(KafkaConstant.GROUP_ID, KafkaConstant.GROUP_ID_NAME);
        configs.put(KafkaConstant.MAX_POLL_RECORDS, 100);

        this.consumer = ConsumerFactory.getInstance().getConsumer(configs);
    }

    private void subscribe() {
        List<String> topicList = new ArrayList<>();
        topicList.add(KafkaConstant.TOPIC_ALARM);
        boolean subscribe = false;
        while (!subscribe) {
            try {
                consumer.subscribe(topicList, new IConsumerRebalanceListenerImpl(consumer, currentOffsets));
                subscribe = true;
            } catch (MqException e) {
                LOGGER.error("subscribe catch a mqException:{}", e.getMessage());
                sleep();
            }
        }
        // 消费kafka传入的消息
        consumerKafkaExecutors.execute(this::consumerAlarmFromKafka);
    }

    private void consumerAlarmFromKafka() {
        LOGGER.info("[BusinessTopoAlarm] enter BusinessWorker.");
        Thread.currentThread().setName("kafkaDeme-consumer-kafka-" + KafkaConstant.TOPIC_ALARM);
        while (true) {
            try {
                IConsumerRecords<String, String> records = consumer.poll(POLL_MILLIONS);
                List<AlarmDetail> alarmDetails = convertAlarmFromKafka(records);
                // 处理多个告警队列
                businessTopoAlarmStorageService.receiveAlarmList(alarmDetails);
                businessThresholdAlarmStorageService.receiveAlarmList(alarmDetails);
                dvTopoServiceDelegate.offerAlarmList(alarmDetails);
                consumer.commit();
            } catch (Throwable e) {
                LOGGER.error("consumerAlarmFromKafka error", e);
                sleep();
            }
        }
    }

    public List<AlarmDetail> convertAlarmFromKafka(IConsumerRecords<String, String> records) {
        if (Objects.isNull(records) || CollectionUtils.isEmpty(records.records())) {
            return Collections.emptyList();
        }
        return filterAndTransformAlarm(records.records());

    }

    private List<AlarmDetail> filterAndTransformAlarm(List<IConsumerRecord<String, String>> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        List<AlarmDetail> result = new ArrayList<>();
        for (IConsumerRecord<String, String> record : records) {
            List<AlarmDetail> alarms = JSON.parseArray(record.value(), AlarmDetail.class);
            result.addAll(alarms);
            LOGGER.debug("KafkaConsumer discards data: offset = {}, key = {}, value = {}", record.offset(), record.key(), record.value());
            this.currentOffsets.put(new TopicPartition(record.topic(), record.partition()), new OffsetAndMetadata(record.offset() + 1));
        }
        return result;
    }


    private static void sleep() {
        sleep(1000L);
    }

    private static void sleep(long time) {
        try {
            Thread.sleep(time);
        } catch (InterruptedException e) {
            LOGGER.error("thread is interrupted, e:", e.getMessage());
        }
    }
}
