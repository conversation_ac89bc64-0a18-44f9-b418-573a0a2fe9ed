/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import static com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant.DISTRIBUTE_INDICATOR_RANGE;

import com.huawei.bsp.as.util.Pair;
import com.huawei.bsp.biz.util.HttpUtil;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.cbb.sm.model.LogResult;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.NonLeafModel;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.OverView;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.OverViewForMm;
import com.huawei.i2000.dvtoposervice.builder.ViewAssemble;
import com.huawei.i2000.dvtoposervice.business.database.BusinessCommonModel;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInsExtentAttrDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceRelationModel;
import com.huawei.i2000.dvtoposervice.business.edit.BusinessEditOperation;
import com.huawei.i2000.dvtoposervice.business.edit.ChannelEditOperation;
import com.huawei.i2000.dvtoposervice.business.openapi.alarm.BusinessAlarmInitService;
import com.huawei.i2000.dvtoposervice.business.pm.PmIndicatorInstanceService;
import com.huawei.i2000.dvtoposervice.business.pm.PmRequestService;
import com.huawei.i2000.dvtoposervice.business.pm.entity.DeletedHistoryData;
import com.huawei.i2000.dvtoposervice.business.pm.entity.DeletedHistoryResult;
import com.huawei.i2000.dvtoposervice.business.pm.entity.DeletedHistoryValues;
import com.huawei.i2000.dvtoposervice.business.pm.entity.QueryDeletedHistoryData;
import com.huawei.i2000.dvtoposervice.business.service.bean.ToDistinguishingList;
import com.huawei.i2000.dvtoposervice.business.service.bean.ToUpdateObjectSet;
import com.huawei.i2000.dvtoposervice.business.service.bean.TopNViewMeasPMData;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessConfigEnum;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.business.topo.redis.RedisExOper;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.BusinessTopoAlarmStorageService;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.HomePageConfigurationService;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.ModelIdGen;
import com.huawei.i2000.dvtoposervice.business.validate.BusinessInstanceModifyValidate;
import com.huawei.i2000.dvtoposervice.constant.LogConstant;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.convert.BusinessConvert;
import com.huawei.i2000.dvtoposervice.convert.BusinessConvertForMm;
import com.huawei.i2000.dvtoposervice.convert.ChannelConvert;
import com.huawei.i2000.dvtoposervice.convert.SolutionConvert;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessCommonModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessIndicatorDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceRelationDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessMoTypeDao;
import com.huawei.i2000.dvtoposervice.model.Business;
import com.huawei.i2000.dvtoposervice.model.BusinessEditInitView;
import com.huawei.i2000.dvtoposervice.model.BusinessIndicator;
import com.huawei.i2000.dvtoposervice.model.Channel;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.model.GroupOfMo;
import com.huawei.i2000.dvtoposervice.model.Indicator;
import com.huawei.i2000.dvtoposervice.model.IndicatorDisplayResults;
import com.huawei.i2000.dvtoposervice.model.IndicatorDistribution;
import com.huawei.i2000.dvtoposervice.model.OverViewGrid;
import com.huawei.i2000.dvtoposervice.model.OverViewGridForMm;
import com.huawei.i2000.dvtoposervice.model.OverViewIndicatorParam;
import com.huawei.i2000.dvtoposervice.model.OverviewGridParam;
import com.huawei.i2000.dvtoposervice.model.PerformanceIndexValue;
import com.huawei.i2000.dvtoposervice.model.QueryUpperLayerParam;
import com.huawei.i2000.dvtoposervice.model.Site;
import com.huawei.i2000.dvtoposervice.model.SiteDistribution;
import com.huawei.i2000.dvtoposervice.model.SiteHistoryResults;
import com.huawei.i2000.dvtoposervice.model.SiteTPSValue;
import com.huawei.i2000.dvtoposervice.model.SiteTeam;
import com.huawei.i2000.dvtoposervice.model.Solution;
import com.huawei.i2000.dvtoposervice.model.StripeTeam;
import com.huawei.i2000.dvtoposervice.model.UpperLayerInfo;
import com.huawei.i2000.dvtoposervice.util.AuthUtils;
import com.huawei.i2000.dvtoposervice.util.ConfigurationUtil;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.EamUtil;
import com.huawei.i2000.dvtoposervice.util.LogUtil;
import com.huawei.i2000.dvtoposervice.util.ModelConvertUtil;
import com.huawei.i2000.dvtoposervice.util.PmDataHandleUtil;
import com.huawei.i2000.dvtoposervice.util.SiteIdUtil;
import com.huawei.i2000.dvtoposervice.util.SiteNameUtils;
import com.huawei.i2000.dvtopowebsite.model.ResponseEntity;
import com.huawei.i2000.dvtopowebsite.util.RestConstant;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.ManagedObject;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONReader;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * BusinessTopoOverViewServiceImpl
 *
 * <AUTHOR>
 * @since 2024/2/28
 */
@Service
public class BusinessTopoOverViewServiceImpl implements BusinessTopoOverViewService, ModelIdGen {

    private static final String ACTIVE = "1";

    private static OssLog LOGGER = OssLogFactory.getLogger(BusinessTopoOverViewServiceImpl.class);

    private static final String INDICATOR_RANGE = "indicatorrange";

    private static final String AGGREGATE_TYPE = "aggregatetype";

    private static final String DITIAL_SUFFIX = "; ";

    private final Lock businessEditLock = new ReentrantLock();

    private static final RedisExOper REDIS_EX_OPER = new RedisExOper(RedisConstant.MO_STORE_LIST,
        RedisConstant.REDIS_NAME);

    @Autowired
    private PmRequestService pmRequestService;

    @Autowired
    private BusinessIndicatorDao indicatorDao;

    @Autowired
    private BusinessInstanceModelDao modelDao;

    @Autowired
    private BusinessInstanceRelationDao relationDao;

    @Autowired
    private BusinessCommonModelDao commonModelDao;

    @Autowired
    private BusinessEditOperation businessEditOperationService;

    @Autowired
    private ChannelEditOperation channelEditOperationService;

    @Autowired
    PmIndicatorInstanceService pmIndicatorInstanceService;

    @Autowired
    BusinessTopoAlarmStorageService storageService;

    @Autowired
    BusinessMoTypeDao businessMoTypeDao;

    @Autowired
    private HomePageConfigurationService homePageConfigurationService;

    @Autowired
    private BusinessAlarmInitService businessAlarmInitService;

    @Autowired
    private BusinessInstanceModelDao businessInstanceModelDao;

    @Override
    public List<Solution> querySolution(Long timestamp) {
        SolutionConvert solutionConvert = new SolutionConvert();
        timestamp = Objects.isNull(timestamp) ? Long.valueOf(0L) : timestamp;
        List<BusinessInstanceModelDB> allSolutionList = modelDao.queryAllInstanceOfTypeHistory(
            BusinessTopoConstant.SOLUTION_TYPE_ID, timestamp);

        // 根据权限判断，如果有单个站点权限，视为可以查看
        List<BusinessInstanceModelDB> authSolutionList = obtainAuthSolutionIns(timestamp, allSolutionList);

        List<SolutionWrapper> solutionWrapperList = new ArrayList<>();
        for (BusinessInstanceModelDB solution : authSolutionList) {
            Solution solutionResult = solutionConvert.convert(solution);
            solutionResult.setSolutionType(commonModelDao.querySolutionTypeByModelId(solution.getModelId()));
            SolutionWrapper solutionWrapper = new SolutionWrapper();
            solutionWrapperList.add(solutionWrapper);
            solutionWrapper.setSolution(solutionResult);
            setSolutionName(solution.getModelId(), solutionWrapper);

            String isImporting = REDIS_EX_OPER.get(BusinessTopoConstant.CONFIGURATION_IMPORT_TASK);
            // 字段有值，为正在导入
            solutionResult.setIsImporting(StringUtils.isNotEmpty(isImporting));
        }
        if (CollectionUtils.isEmpty(solutionWrapperList)) {
            return Collections.emptyList();
        }
        return homePageConfigurationService.sortForSolutionList(solutionWrapperList);
    }

    private List<BusinessInstanceModelDB> obtainAuthSolutionIns(Long timestamp, List<BusinessInstanceModelDB> allSolutionList) {
        List<BusinessInstanceModelDB> authSolutionList = new ArrayList<>();
        if (ContextUtils.getContext() == null || ContextUtils.getContext().getAdmin() == null) {
            return Collections.emptyList();
        }
        if (!ContextUtils.getContext().getAdmin()) {
            Set<String> authDns = AuthUtils.getAuthDns();
            for (BusinessInstanceModelDB solution : allSolutionList) {
                // 查询所有下层站点实例
                List<String> subSiteInsList = relationDao.queryInsIdOfOneLvFromUp(solution.getInstanceId(),
                        BusinessTopoConstant.RELATION_TYPE_TOP_LEVEL, BusinessTopoConstant.SITE_TYPE_ID, timestamp)
                    .stream()
                    .map(BusinessInstanceModelDB::getDn)
                    .collect(Collectors.toList());
                // 存在交集则添加
                if (CollectionUtils.isNotEmpty(authDns)) {
                    if (!Collections.disjoint(authDns, subSiteInsList)) {
                        authSolutionList.add(solution);
                    }
                }
            }
        } else {
            authSolutionList = allSolutionList;
        }
        return authSolutionList;
    }

    private void setSolutionName(String modelId, SolutionWrapper solutionWrapper) {
        BusinessCommonModel commonModel = commonModelDao.queryCommonModelByModelId(modelId);
        if (Objects.isNull(commonModel)) {
            LOGGER.warn("[querySolution] getSolutionInsName the model id could not find model, model id is {}", modelId);
            return;
        }
        String solutionInsName = commonModel.getSolutionName();
        solutionWrapper.setRealSolutionName(solutionInsName);
        ConfigData configData = ConfigurationUtil.getConfigDataByName(solutionInsName);
        if (configData != null && configData.getValue() != null) {
            solutionInsName = configData.getValue();
        }
        solutionWrapper.getSolution().setSolutionName(solutionInsName);
    }

    @Override
    public List<Business> queryBusinessList(Integer solutionId, Long timestamp) {
        timestamp = Objects.isNull(timestamp) ? Long.valueOf(0L) : timestamp;
        List<BusinessInstanceModelDB> businessDBList = modelDao.queryNextLevelInstanceByTimeLine(solutionId, timestamp)
            .stream()
            .filter(ins -> ins.getModelType() == 1)
            .collect(Collectors.toList());
        // 去除不展示的实例
        return queryBusinessList(businessDBList, timestamp, true).stream()
            .filter(Business::getIsDisplay)
            .collect(Collectors.toList());
    }

    private List<Business> queryBusinessList(List<BusinessInstanceModelDB> businessDBList, Long timestamp,
        Boolean filterDn) {
        BusinessConvert businessConvert = new BusinessConvert();
        List<Business> businesses = new ArrayList<>();
        // Topo网元的指标
        List<Integer> businessInsList = businessDBList.stream()
            .map(BusinessInstanceModelDB::getInstanceId)
            .collect(Collectors.toList());
        List<BusinessIndicator> indicatorsOfAll = indicatorDao.queryHisIndInsIdList(businessInsList, timestamp);

        for (BusinessInstanceModelDB business : businessDBList) {
            Business businessResult = businessConvert.convert(business);
            List<BusinessIndicator> indicatorsOfBu = indicatorsOfAll.stream()
                .filter(ind -> ind.getInstanceId().equals(business.getInstanceId()))
                .collect(Collectors.toList());
            // 总览视图查询去除不能展示的指标
            if (CollectionUtils.isEmpty(indicatorsOfBu)) {
                businesses.add(businessResult);
                continue;
            }
            businessResult.setIndicatorList(
                ModelConvertUtil.getIndicatorList(indicatorsOfBu, pmIndicatorInstanceService));
            businesses.add(businessResult);
        }
        return businesses;
    }

    @Override
    public IndicatorDisplayResults queryIndicatorResults(OverViewIndicatorParam param) throws ServiceException {
        IndicatorDisplayResults results = new IndicatorDisplayResults();
        List<BusinessIndicator> indicators = getDisplayIndicator(param.getBusinessId(), param.getIndicatorId(),
            param.getSiteId(), param.getCurrentTime());
        if (!ContextUtils.getContext().getAdmin()) {
            indicators = indicators.stream()
                .filter(indicator -> AuthUtils.getAuthDns().contains(indicator.getDn()))
                .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(indicators)) {
            LOGGER.error("[queryIndicatorResults] could not find any indicator.");
            return results;
        }
        List<BusinessIndicator> filledIndicators = pmIndicatorInstanceService.fillIndexNameAndUnit(indicators);
        Map<String, Long> timeRanges = PmDataHandleUtil.getIndQueryTimeRangeInPmFormat(param.getCurrentTime(),
            INDICATOR_RANGE, true);
        List<Indicator> indicatorList = JSONArray.parseArray(JSON.toJSONString(filledIndicators), Indicator.class, JSONReader.Feature.SupportSmartMatch);
        // 如果不需要汇聚，就取topN指标
        if (!needAggregate(indicators.get(0))) {
            List<TopNViewMeasPMData> topNPmDataResults = PmDataHandleUtil.getClusterIndicatorTopNResult(indicatorList,
                param.getCurrentTime(), false, BusinessTopoConstant.TOPN.TOP_N_TYPE_NORMAL, null, null);
            Set<String> dns = topNPmDataResults.stream().map(TopNViewMeasPMData::getDn).collect(Collectors.toSet());
            indicatorList = indicatorList.stream()
                .filter(indicator -> dns.contains(indicator.getDn()))
                .collect(Collectors.toList());
            if (dns.size() > 1) {
                timeRanges = PmDataHandleUtil.getIndQueryTimeRangeInPmFormat(param.getCurrentTime(),
                    INDICATOR_RANGE, false);
            }
        }
        List<QueryDeletedHistoryData> queryDeletedHistoryData = pmRequestService.getQueryDeletedHistoryData(
            indicatorList, timeRanges);
        DeletedHistoryData performanceResult = pmRequestService.getDeletedHistoryDataFormPerformance(
            queryDeletedHistoryData);
        Map<String, List<PerformanceIndexValue>> indexValues = pmRequestService.getPerformanceIndexValueList(performanceResult,
            filledIndicators, false);
        // set results
        List<SiteHistoryResults> siteHistoryResultList = getLoadSiteHistoryResults(filledIndicators, indexValues,
            param);
        results.getSiteHistoryList().addAll(siteHistoryResultList);
        BusinessInstanceModelDB businessInstance = modelDao.queryInstanceByInstanceIdAndTimeLine(param.getBusinessId(),
            Objects.isNull(param.getCurrentTime()) ? 0L : param.getCurrentTime());
        results.setBusinessId(businessInstance.getInstanceId());
        results.setBusinessName(
            businessInstance.getExtentAttr(BusinessTopoConstant.BUSINESS_NAME_ATTR_KEY).getAttrValue());
        return results;
    }

    private List<SiteHistoryResults> getLoadSiteHistoryResults(List<BusinessIndicator> indicators,
        Map<String, List<PerformanceIndexValue>> indexValues, OverViewIndicatorParam param) {
        List<SiteHistoryResults> siteHistoryResultsList = new ArrayList<>();
        indicators.sort(Comparator.comparing(BusinessIndicator::getDnName, Comparator.nullsLast(Comparator.naturalOrder())));
        if (needAggregate(indicators.get(0))) {
            List<PerformanceIndexValue> aggregateIndexValues = PmDataHandleUtil.aggregateSiteHistoryData(indexValues);
            // set aggregate results
            SiteHistoryResults siteHistoryResult = getSiteHistoryResults(indicators,
                Objects.isNull(param.getSiteId()) ? null : String.valueOf(param.getSiteId()), aggregateIndexValues,
                param.getCurrentTime());
            siteHistoryResultsList.add(siteHistoryResult);
        } else {
            for (BusinessIndicator eachSiteIndicator : indicators) {
                List<PerformanceIndexValue> eachSiteIndHistoryValues = indexValues.getOrDefault(
                    eachSiteIndicator.getDn(), new ArrayList<>());
                SiteHistoryResults siteHistoryResult = getSiteHistoryResults(Collections.singletonList(eachSiteIndicator),
                    eachSiteIndicator.getSiteId(), eachSiteIndHistoryValues, param.getCurrentTime());
                siteHistoryResultsList.add(siteHistoryResult);
            }
        }
        return siteHistoryResultsList;
    }

    private boolean needAggregate(BusinessIndicator indicator) {
        return (!Objects.isNull(indicator.getAggrType()) && indicator.getAggrType() == 1)
            || indicator.getIndicatorDisplayType() == 1;
    }

    @Override
    public SiteDistribution queryOverViewDistribution(IndicatorDistribution param) throws ServiceException {
        SiteDistribution distributionResult = new SiteDistribution();
        List<BusinessIndicator> indicators = getMainIndicator(param.getInstanceId(), param.getCurrentTime());
        if (!ContextUtils.getContext().getAdmin()) {
            indicators = indicators.stream()
                .filter(indicator -> AuthUtils.getAuthDns().contains(indicator.getDn()))
                .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(indicators)) {
            LOGGER.warn("[queryOverViewDistribution] could not find any indicator.");
            return distributionResult;
        }
        List<BusinessIndicator> filledIndicators = pmIndicatorInstanceService.fillIndexNameAndUnit(indicators);
        List<String> dns = filledIndicators.stream().map(BusinessIndicator::getDn).collect(Collectors.toList());
        BusinessCommonModel commonModel = getSiteCommonModelByBusinessInstanceId(param.getInstanceId(), param.getCurrentTime());
        Map<String, String> dnBelongSiteMap = getIndBelongToSiteMap(dns, commonModel);
        if (dnBelongSiteMap.isEmpty()) {
            LOGGER.warn("[queryOverViewDistribution] There is no site indicator.");
            return distributionResult;
        }

        Long currentTime = param.getCurrentTime();
        Map<String, Long> timeRanges = PmDataHandleUtil.getIndTPSTimeRangeInPmFormat(currentTime,
            DISTRIBUTE_INDICATOR_RANGE);
        List<Indicator> indicatorList = JSON.parseArray(JSON.toJSONString(filledIndicators), Indicator.class, JSONReader.Feature.SupportSmartMatch);
        List<QueryDeletedHistoryData> queryDeletedHistoryData = pmRequestService.getQueryDeletedHistoryData(
            indicatorList, timeRanges);
        DeletedHistoryData performanceResult = pmRequestService.getDeletedHistoryDataFormPerformance(
            queryDeletedHistoryData);
        if (CollectionUtils.isEmpty(performanceResult.getResult())) {
            LOGGER.error("[queryOverViewDistribution] There is no indicator data.");
            return distributionResult;
        }

        Map<String, DeletedHistoryValues> dnValueMap = getHistoryValuesMap(performanceResult);
        Map<String, BusinessIndicator> dnIndMap = filledIndicators.stream()
            .collect(Collectors.toMap(BusinessIndicator::getDn, Function.identity(), (existing, replacement) -> replacement));
        String measTypeKey = filledIndicators.get(0).getMeasTypeKey();
        List<SiteTPSValue> tpsValues = new ArrayList<>();
        Double totalValue = getTotalValue(dnBelongSiteMap, dnValueMap, dnIndMap, measTypeKey, tpsValues, currentTime);
        distributionResult.setTotalData(totalValue);
        distributionResult.setSiteDataList(
            tpsValues.stream().sorted(Comparator.comparing(SiteTPSValue::getSiteName)).collect(Collectors.toList()));
        return distributionResult;
    }

    private Double getTotalValue(Map<String, String> dnBelongSiteMap, Map<String, DeletedHistoryValues> dnValueMap,
        Map<String, BusinessIndicator> dnIndMap, String measTypeKey, List<SiteTPSValue> tpsValues, Long currentTime) {
        List<BusinessInstanceModelDB> siteIns = modelDao.queryAllInstanceOfTypeHistory(
            BusinessTopoConstant.SITE_TYPE_ID, currentTime);
        Map<String, Integer> siteInsMap = siteIns.stream()
            .collect(Collectors.toMap(site -> site.getExtentAttr(BusinessTopoConstant.ATTR_SITE_ID).getAttrValue(),
                BusinessInstanceModelDB::getInstanceId, (v1, v2) -> v2));
        Double totalValue = 0d;
        boolean existAnyValue = false;
        Set<Integer> siteInsIds = new HashSet<>();
        for (Map.Entry<String, String> entry : dnBelongSiteMap.entrySet()) {
            SiteTPSValue tpsValue = new SiteTPSValue();
            tpsValue.setSiteId(siteInsMap.get(entry.getKey()));
            tpsValue.setSiteName(entry.getKey());
            if (dnValueMap.get(entry.getValue()) != null) {
                DeletedHistoryValues historyValues = dnValueMap.get(entry.getValue());
                String indicatorValue = historyValues.getValues().get(measTypeKey);
                if (Objects.nonNull(indicatorValue)) {
                    tpsValue.setIndicatorValue(Double.parseDouble(indicatorValue));
                    totalValue += tpsValue.getIndicatorValue();
                    existAnyValue = true;
                }
            }
            tpsValue.setIndicatorStatus(getIndicatorStatus(tpsValue, dnIndMap.get(entry.getValue())));
            tpsValues.add(tpsValue);
            siteInsIds.add(tpsValue.getSiteId());
        }
        // 站点名称重新赋值 性能差，非正式方案，后边可以再优化
        renameSiteName(tpsValues, siteInsIds);
        totalValue = existAnyValue ? Double.parseDouble(String.format(Locale.ROOT, "%.2f", totalValue)) : null;
        return totalValue;
    }

    public void renameSiteName(List<SiteTPSValue> tpsValues, Set<Integer> siteInsIds) {
        // 查询实例拓展属性表是否有siteName拓展字段，有的话替换为这个字段的值，没有的话拼一下siteId
        List<BusinessInsExtentAttrDB> attrs = modelDao.queryAttrValueByInstanceIds(BusinessTopoConstant.ATTR_KEY_SITE_DISPLAY_NAME, siteInsIds);
        Map<Integer, String> attrMap = attrs.stream().collect(Collectors.toMap(BusinessInsExtentAttrDB::getInstanceId, BusinessInsExtentAttrDB::getAttrValue, (v1, v2) -> v2));
        for (SiteTPSValue siteTPSValue : tpsValues) {
            if (attrMap.containsKey(siteTPSValue.getSiteId())) {
                siteTPSValue.setSiteName(attrMap.get(siteTPSValue.getSiteId()));
            } else {
                siteTPSValue.setSiteName(BusinessTopoConstant.SITE_PREFIX + siteTPSValue.getSiteName());
            }
        }
    }

    private Boolean getIndicatorStatus(SiteTPSValue tpsValue, BusinessIndicator businessIndicator) {
        ConfigData openBusinessThresholdAlarm = ConfigurationUtil.getConfigDataByName("openBusinessThresholdsAlarm");
        if (Objects.nonNull(openBusinessThresholdAlarm) && StringUtils.isNotEmpty(openBusinessThresholdAlarm.getValue())
            && Boolean.parseBoolean(openBusinessThresholdAlarm.getValue())) {
            if (Objects.nonNull(businessIndicator.getIndicatorStatus())) {
                return businessIndicator.getIndicatorStatus();
            } else {
                return false;
            }
        } else {
            // 为0设置为异常
            return Objects.isNull(tpsValue.getIndicatorValue()) || tpsValue.getIndicatorValue() == 0;
        }
    }

    private static Map<String, DeletedHistoryValues> getHistoryValuesMap(DeletedHistoryData performanceResult) {
        Map<String, DeletedHistoryValues> dnValueMap = new HashMap<>();
        DeletedHistoryResult result = performanceResult.getResult().get(0);
        if (Objects.isNull(result.getDatas())) {
            return dnValueMap;
        }
        for (DeletedHistoryValues value : result.getDatas()) {
            if (!dnValueMap.containsKey(value.getDn())) {
                dnValueMap.put(value.getDn(), value);
            } else if (value.getTimestamp().compareTo(dnValueMap.get(value.getDn()).getTimestamp()) > 0) {
                dnValueMap.put(value.getDn(), value);
            }
        }
        return dnValueMap;
    }

    private List<BusinessIndicator> getDisplayIndicator(Integer businessId, String indicatorId, Integer siteId,
        Long timestamp) throws ServiceException {
        timestamp = Objects.isNull(timestamp) ? Long.valueOf(0L) : timestamp;
        List<BusinessIndicator> indicators = indicatorDao.queryHisIndInsIdList(Collections.singletonList(businessId),
            timestamp);
        if (CollectionUtils.isEmpty(indicators)) {
            LOGGER.error("[queryIndicatorResults] could not find any indicator by businessId: {}", businessId);
            return new ArrayList<>();
        }

        // 找到所有和传入指标类型相同的
        Optional<BusinessIndicator> matchedInd = indicators.stream()
            .filter(ind -> ind.getIndicatorId().equals(indicatorId))
            .findFirst();
        if (!matchedInd.isPresent()) {
            LOGGER.error("[queryIndicatorResults] could not find any indicator by indicatorId: {}", indicatorId);
            return new ArrayList<>();
        }

        List<BusinessIndicator> matchedIndicatorList = new ArrayList<>();
        List<BusinessIndicator> siteIndicatorList = indicators.stream()
            .filter(ind -> ind.getMoType().equals(matchedInd.get().getMoType()) && ind.getMeasUnitKey()
                .equals(matchedInd.get().getMeasUnitKey()) && ind.getMeasTypeKey()
                .equals(matchedInd.get().getMeasTypeKey()))
            .collect(Collectors.toList());

        BusinessCommonModel commonModel = getSiteCommonModelByBusinessInstanceId(businessId, timestamp);
        filterAndLoadIndSite(siteId, siteIndicatorList, matchedIndicatorList, timestamp, commonModel);
        return matchedIndicatorList;
    }

    private void filterAndLoadIndSite(Integer siteId, List<BusinessIndicator> siteIndicatorList,
        List<BusinessIndicator> matchedIndicatorList, Long timestamp, BusinessCommonModel commonModel) {
        List<String> dns = siteIndicatorList.stream().map(BusinessIndicator::getDn).collect(Collectors.toList());
        List<BusinessInstanceModelDB> instanceModelDBS = modelDao.queryInstanceByDnsWithoutExtendsTimeLine(dns,
            timestamp);
        Map<String, BusinessInstanceModelDB> instanceModelMap = instanceModelDBS.stream()
            .collect(Collectors.toMap(BusinessInstanceModelDB::getDn, Function.identity(), (v1, v2) -> v1));

        List<Integer> instanceIds = instanceModelMap.values()
            .stream()
            .map(BusinessInstanceModelDB::getInstanceId)
            .collect(Collectors.toList());
        Map<Integer, Integer> relationMap = relationDao.queryRelationByTypeBatchTimeLine(instanceIds,
            BusinessTopoConstant.RELATION_TYPE_SITE, timestamp);

        List<Integer> relationInstanceIds = new ArrayList<>(relationMap.values());
        List<BusinessInstanceModelDB> relationInstances = modelDao.queryInstanceListByIdList(relationInstanceIds,
            Objects.isNull(timestamp) ? 0L : timestamp);
        Map<Integer, BusinessInstanceModelDB> relationInstanceMap = relationInstances.stream()
            .collect(Collectors.toMap(BusinessInstanceModelDB::getInstanceId, Function.identity(), (v1, v2) -> v1));
        // 在Topo范围内
        if (CollectionUtils.isNotEmpty(instanceIds)) {
            for (BusinessIndicator siteInd : siteIndicatorList) {
                BusinessInstanceModelDB indIns = instanceModelMap.get(siteInd.getDn());
                if (isMatchedToSite(indIns, siteInd, relationMap, relationInstanceMap, siteId)) {
                    matchedIndicatorList.add(siteInd);
                }
            }
        } else {
            BusinessInstanceModelDB selectSiteIns = null;
            if (Objects.nonNull(siteId)) {
                selectSiteIns = modelDao.queryInstanceByInstanceIdAndTimeLine(siteId,
                    Objects.isNull(timestamp) ? 0L : timestamp);
            }
            // 获取站点和指标的
            Map<String, String> indMapSite = getIndBelongToSiteMap(dns, commonModel);
            for (BusinessIndicator siteInd : siteIndicatorList) {
                if (isDnMatchedToSite(indMapSite, siteInd, selectSiteIns)) {
                    matchedIndicatorList.add(siteInd);
                }
            }
        }
    }

    public boolean isMatchedToSite(BusinessInstanceModelDB indIns, BusinessIndicator siteInd,
        Map<Integer, Integer> relationMap, Map<Integer, BusinessInstanceModelDB> relationInstanceMap, Integer siteId) {
        if (Objects.isNull(indIns)) {
            LOGGER.warn("[queryIndicatorResults] query instance is null, dn = {}", siteInd.getDn());
            return false;
        }
        Integer indBelongSite = relationMap.get(indIns.getInstanceId());
        if (Objects.isNull(indBelongSite)) {
            LOGGER.warn("[queryIndicatorResults] query site instance is null, indIns = {}", indIns.getInstanceId());
            return false;
        }
        BusinessInstanceModelDB siteIns = relationInstanceMap.get(indBelongSite);
        if (Objects.isNull(siteId) || Objects.equals(indBelongSite, siteId)) {
            setSiteAttr(siteInd, siteIns);
            return true;
        }
        return false;
    }

    private static void setSiteAttr(BusinessIndicator siteInd, BusinessInstanceModelDB siteIns) {
        String siteId = siteIns.getExtentAttr(BusinessTopoConstant.ATTR_SITE_ID).getAttrValue();
        siteInd.setSiteId(siteId);
        String siteName = siteIns.getExtentAttr(BusinessTopoConstant.ATTR_KEY_SITE_DISPLAY_NAME).getAttrValue();
        if (StringUtils.isEmpty(siteName)) {
            siteName = BusinessTopoConstant.SITE_PREFIX + siteId;
        }
        siteInd.setSiteName(siteName);
    }

    /**
     * 当指标实例不存在于Topo树中时，指标与站点的匹配方式
     *
     * @param indMapSite 站点和网元映射
     * @param siteInd 需要匹配的指标
     * @param selectSiteIns 站点实例
     * @return 是否能匹配至站点
     */
    public boolean isDnMatchedToSite(Map<String, String> indMapSite, BusinessIndicator siteInd,
        BusinessInstanceModelDB selectSiteIns) {
        // 未传入站点Id，进行全量匹配
        if (Objects.isNull(selectSiteIns)) {
            for (Map.Entry<String, String> entry : indMapSite.entrySet()) {
                if (entry.getValue().equals(siteInd.getDn())) {
                    siteInd.setSiteId(entry.getKey());
                    siteInd.setSiteName(BusinessTopoConstant.SITE_PREFIX + entry.getKey());
                    return true;
                }
            }
        } else {
            String siteId = selectSiteIns.getExtentAttr(BusinessTopoConstant.ATTR_SITE_ID).getAttrValue();
            String siteName = selectSiteIns.getExtentAttr(BusinessTopoConstant.ATTR_KEY_SITE_DISPLAY_NAME).getAttrValue();
            if (StringUtils.isEmpty(siteName)) {
                siteName = BusinessTopoConstant.SITE_PREFIX + siteId;
            }
            // 如果从映射中能够找到对应的站点，则认为匹配
            String matchedDn = indMapSite.getOrDefault(siteId, StringUtils.EMPTY);
            if (matchedDn.equals(siteInd.getDn())) {
                siteInd.setSiteId(siteId);
                siteInd.setSiteName(siteName);
                return true;
            }
        }
        return false;
    }

    private Map<String, String> getIndBelongToSiteMap(List<String> dns, BusinessCommonModel commonModel) {
        Map<String, String> indMapSite = new HashMap<>();
        String siteColumn = commonModel.getStaticExtentAttr(BusinessTopoConstant.SITE_ID_COLUMN_ATTR_KEY).getStaticAttrValue();
        List<DN> dnList = new ArrayList<>();
        for (String dn : dns) {
            DN dnIns = new DN(dn);
            dnList.add(dnIns);
        }
        List<ManagedObject> managedObjects = EamUtil.getMoByDns(dnList);
        // 根据不同解决方案去分别站点的获取方式
        // 0->如何获取站点模型
        List<BusinessCommonModel> siteCommonModelList = commonModelDao.queryCommonModelByType(BusinessTopoConstant.SITE_TYPE_ID);

        // 1->获取站点模型
        // 2->根据站点模型获取到字段，并根据字段做匹配


        for (ManagedObject managedObject : managedObjects) {
            String siteId = SiteIdUtil.getSiteIdByMo(managedObject, siteColumn);
            if (StringUtils.isNotEmpty(siteId)) {
                indMapSite.put(siteId, managedObject.getDN().getValue());
            }
        }
        if (indMapSite.size() != dns.size()) {
            LOGGER.error("[getIndBelongToSiteMap] some indicator not find site info.");
        }
        return indMapSite;
    }

    private List<BusinessIndicator> getMainIndicator(Integer businessId, Long currentTime) throws ServiceException {
        List<BusinessIndicator> indicators = indicatorDao.queryIndicatorByInstanceId(businessId, currentTime);
        if (CollectionUtils.isEmpty(indicators)) {
            LOGGER.error("[queryIndicatorResults] could not find any indicator by businessId: {}", businessId);
            return Collections.emptyList();
        }

        BusinessIndicator mainInd = indicators.stream()
            .filter(ind -> ind.getIndicatorDisplayType() == 1)
            .findFirst()
            .orElse(null);
        if (Objects.isNull(mainInd)) {
            LOGGER.error("[queryIndicatorResults] could not find any main indicator by businessId: {}", businessId);
            return Collections.emptyList();
        }
        return getDisplayIndicator(businessId, mainInd.getIndicatorId(), null, currentTime);
    }

    public SiteHistoryResults getSiteHistoryResults(List<BusinessIndicator> indicators, String siteId,
        List<PerformanceIndexValue> indexValues, Long endTime) {
        BusinessIndicator indicator = indicators.get(0);
        SiteHistoryResults siteHistoryResult = new SiteHistoryResults();
        String siteName = indicator.getSiteName();
        String prefix = StringUtils.isEmpty(siteName) ? BusinessTopoConstant.SITE_PREFIX + siteId : siteName;
        siteHistoryResult.setSiteName(
            siteId != null ? prefix + BusinessTopoConstant.DASH + indicator.getDnName() : "All");
        siteHistoryResult.setIndexName(indicator.getIndexName());

        endTime = null == endTime ? Long.valueOf(System.currentTimeMillis()) : endTime;
        ConfigData configData = ConfigurationUtil.getConfigDataByName(INDICATOR_RANGE);
        siteHistoryResult.setStartTime(String.valueOf(endTime - Long.parseLong(configData.getValue())));
        siteHistoryResult.setEndTime(String.valueOf(endTime));
        siteHistoryResult.setIndexUnit(indicator.getUnit());
        siteHistoryResult.setHistoryTotalCount(indexValues.size());
        // 获取同环比列表
        long todayStartTime = endTime - Long.parseLong(configData.getValue());
        Map<String, Pair<Long, Long>> rangeNameMap = PmDataHandleUtil.getTimeRange(todayStartTime, endTime);
        Map<String, List<PerformanceIndexValue>> rangeIndexValue = PmDataHandleUtil.indexValueGroupBy(indexValues,
            rangeNameMap);
        siteHistoryResult.setComparativeValueMap(rangeIndexValue);
        siteHistoryResult.setMoType(indicator.getMoType());
        siteHistoryResult.setMeasUnitKey(indicator.getMeasUnitKey());
        siteHistoryResult.setMeasTypeKey(indicator.getMeasTypeKey());
        if (StringUtils.isNotEmpty(indicator.getOriginalValue())) {
            String replace = indicator.getOriginalValue().replace("=", "<=>").replace(",", "<,>");
            siteHistoryResult.setOriginalValue(replace);
        }
        List<String> dnList = indicators.stream().map(BusinessIndicator::getDn).collect(Collectors.toList());
        siteHistoryResult.setDnList(dnList);
        return siteHistoryResult;
    }

    @Override
    public OverViewGrid queryOverviewGrid(OverviewGridParam param) {
        ViewAssemble assemble = new ViewAssemble();
        Integer solutionId = param.getSolutionId();
        BusinessInstanceModelDB solutionIns = null;
        // 0代表查询当前时间，否则是时间回溯
        if (Objects.isNull(param.getTimestamp()) || param.getTimestamp() == 0) {
            solutionIns = modelDao.queryInstanceByInstanceId(solutionId);
        } else {
            solutionIns = modelDao.queryInstanceByInstanceIdAndTimeLine(solutionId, param.getTimestamp());
        }
        if (Objects.isNull(solutionIns) || Objects.isNull(solutionIns.getInstanceId())) {
            LOGGER.error("solutionIns is empty");
            return new OverViewGrid();
        }
        NonLeafModel nonLeafModel = new NonLeafModel(solutionIns);
        OverView overView = (OverView) assemble.constructOverView(nonLeafModel, param.getTimestamp());
        OverViewGrid overViewGrid = new OverViewGrid();
        overViewGrid.setBusinessList(overView.getBusinessList());
        overViewGrid.setSiteTeamList(overView.getSiteTeamList());

        // 如果不是管理员，需要检查站点下全部实例的权限，缺权限时需要前台提示分域不合理
        if (!ContextUtils.getContext().getAdmin()) {
            checkAllInstancePermission(overViewGrid, solutionIns.getSolutionName());
        }
        return overViewGrid;
    }

    @Override
    public OverViewGridForMm queryOverviewGridForMm(OverviewGridParam param) {
        Integer solutionId = param.getSolutionId();
        // 0代表查询当前时间，否则是时间回溯
        BusinessInstanceModelDB solutionIns = (Objects.isNull(param.getTimestamp()) || param.getTimestamp() == 0)
            ? modelDao.queryInstanceByInstanceId(solutionId)
            : modelDao.queryInstanceByInstanceIdAndTimeLine(solutionId, param.getTimestamp());
        if (Objects.isNull(solutionIns) || Objects.isNull(solutionIns.getInstanceId())) {
            LOGGER.error("solutionIns is empty");
            return null;
        }

        // 根据solutionIns组装overView
        OverViewGridForMm overViewGrid = new OverViewGridForMm();
        NonLeafModel nonLeafModel = new NonLeafModel(solutionIns);
        ViewAssemble assemble = new ViewAssemble();
        // 组装数据
        OverViewForMm overView = assemble.constructMmOverViewBatch(nonLeafModel, param.getTimestamp());
        overViewGrid.setBusinessList(overView.getBusinessList());
        overViewGrid.setStripeTeamList(overView.getStripeTeamList());
        overViewGrid.setChannelList(overView.getChannelList());

        // 如果不是管理员，需要检查站点下全部实例的权限，缺权限时需要前台提示分域不合理
        if (!ContextUtils.getContext().getAdmin()) {
            checkAllInstancePermission(overViewGrid, param.getTimestamp() == null ? 0 : param.getTimestamp(),
                solutionId);
        }
        return overViewGrid;
    }

    public OverViewForMm queryInitEditPageForMm(Integer solutionId) {
        BusinessInstanceModelDB solutionIns = modelDao.queryInstanceByInstanceId(solutionId);
        if (Objects.isNull(solutionIns) || Objects.isNull(solutionIns.getInstanceId())) {
            LOGGER.error("solution Instance is empty");
            return null;
        }
        NonLeafModel nonLeafModel = new NonLeafModel(solutionIns);
        ViewAssemble assemble = new ViewAssemble();
        return assemble.constructMmOverView(nonLeafModel);
    }

    private void checkAllInstancePermission(OverViewGrid overViewGrid, String solutionName) {
        // 对dv实例鉴权
        List<BusinessInstanceModelDB> dvModels = modelDao.queryInstanceWithAttrByModelId(createDvModelId(BusinessTopoConstant.DV_APP_INS_MODEL_ID, solutionName));
        Set<String> dvSiteIds = dvModels.stream().map(model -> model.getExtentAttr(BusinessTopoConstant.ATTR_SITE_ID).getAttrValue()).collect(Collectors.toSet());
        for (SiteTeam siteTeam : overViewGrid.getSiteTeamList()) {
            boolean isBreak = false;
            for (Site site : siteTeam.getSiteList()) {
                if (dvSiteIds.contains(site.getSiteName()) && !ContextUtils.getContext().getAdmin() && !AuthUtils.getAuthDns().contains(BusinessTopoConstant.OSS_DN)) {
                    overViewGrid.setHasFiltered(BusinessTopoConstant.IS_FILTERED);
                    isBreak = true;
                    break;
                }
                List<BusinessInstanceModelDB> models = modelDao.queryInstanceByRelationType(site.getSiteId(),
                    BusinessTopoConstant.RELATION_TYPE_SITE);
                Set<String> dns = models.stream()
                    .map(BusinessInstanceModelDB::getDn)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toSet());
                // 对vm鉴权
                List<Integer> podInstanceIds = models.stream()
                    .filter(model -> model.getModelType() == BusinessTopoConstant.POD_TYPE_ID)
                    .map(BusinessInstanceModelDB::getInstanceId)
                    .collect(Collectors.toList());
                List<BusinessInstanceModelDB> vmModels = modelDao.queryInstancesByRelationType(podInstanceIds, BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT);
                List<String> vmDns = vmModels.stream().map(BusinessInstanceModelDB::getDn).collect(Collectors.toList());
                dns.addAll(vmDns);
                if (!AuthUtils.getAuthDns().containsAll(dns)) {
                    overViewGrid.setHasFiltered(BusinessTopoConstant.IS_FILTERED);
                    isBreak = true;
                    break;
                }
            }
            if (isBreak) {
                break;
            }
        }
    }

    private void checkAllInstancePermission(OverViewGridForMm overViewGrid, long timeStamp, int solutionId) {
        Set<String> authDns = AuthUtils.getAuthDns();

        // 先鉴权管理类
        Set<String> manageDns = modelDao.queryNextLevelInstanceBySelectTypeByTimeLine(
                Collections.singletonList(solutionId), BusinessTopoConstant.RELATION_TYPE_TOP_LEVEL, timeStamp)
            .stream()
            .filter(ins -> Objects.equals(BusinessTopoConstant.MANAGEMENT_INSTANCE_ID, ins.getModelType()))
            .map(BusinessInstanceModelDB::getDn)
            .collect(Collectors.toSet());
        if (!authDns.containsAll(manageDns)) {
            overViewGrid.setHasFiltered(BusinessTopoConstant.IS_FILTERED);
            return;
        }

        // 查出第三层,鉴权过滤
        List<BusinessInstanceModelDB> sites = modelDao.queryNextLevelInstanceByTimeLineBatch(
            overViewGrid.getStripeTeamList().stream().map(StripeTeam::getStripeTeamId).collect(Collectors.toList()),
            timeStamp);
        if (!authDns.containsAll(sites.stream().map(BusinessInstanceModelDB::getDn).collect(Collectors.toList()))) {
            overViewGrid.setHasFiltered(BusinessTopoConstant.IS_FILTERED);
            return;
        }

        // 查第三层子实例
        List<BusinessInstanceModelDB> models = modelDao.queryNextLevelInstanceBySelectTypeByTimeLine(
            sites.stream().map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList()),
            BusinessTopoConstant.RELATION_TYPE_SITE, timeStamp);
        Set<String> dns = models.stream()
            .map(BusinessInstanceModelDB::getDn)
            .filter(StringUtils::isNotEmpty)
            .collect(Collectors.toSet());

        // 对vm鉴权
        List<Integer> podInstanceIds = models.stream()
            .filter(model -> model.getModelType() == BusinessTopoConstant.POD_TYPE_ID)
            .map(BusinessInstanceModelDB::getInstanceId)
            .collect(Collectors.toList());
        List<BusinessInstanceModelDB> vmModels = modelDao.queryNextLevelInstanceBySelectTypeByTimeLine(podInstanceIds,
            BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT, timeStamp);
        List<String> vmDns = vmModels.stream()
            .map(BusinessInstanceModelDB::getDn)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        dns.addAll(vmDns);

        if (!authDns.containsAll(dns)) {
            overViewGrid.setHasFiltered(BusinessTopoConstant.IS_FILTERED);
        }
    }

    @Override
    public BusinessEditInitView queryInitEditPage(Integer instanceId) {
        BusinessEditInitView editInitView = new BusinessEditInitView();
        // 获取解决方案实例下一层实例
        List<BusinessInstanceModelDB> secondLevelIns = modelDao.queryNextLevelInstance(instanceId);
        // 设置南北向业务实例
        List<BusinessInstanceModelDB> businessDBList = secondLevelIns
                .stream().filter(ins -> ins.getModelType() == 1).collect(Collectors.toList());
        List<Business> businessList = queryBusinessList(businessDBList, 0L, false);
        editInitView.setBusinessList(businessList);

        // 站点分组实例id列表
        List<Integer> siteGroupInsIds = secondLevelIns
                .stream().filter(ins -> ins.getModelType() == 2).map(BusinessInstanceModelDB::getInstanceId)
                .collect(Collectors.toList());

        List<Site> siteList = getSiteListBySiteGroups(instanceId, siteGroupInsIds, true);
        siteList.sort(Comparator.comparing(Site::getSiteName));
        editInitView.setSiteList(siteList);

        return editInitView;
    }

    private List<Site> getSiteListBySiteGroups(Integer solutionId, List<Integer> siteGroupInsIds, boolean isInit) {
        List<Site> sites = new ArrayList<>();
        // 站点实例
        List<BusinessInstanceModelDB> siteDBList = getPermissionInstanceList(siteGroupInsIds);
        Map<Integer, BusinessInstanceModelDB> siteMap = siteDBList.stream()
                .collect(Collectors.toMap(BusinessInstanceModelDB::getInstanceId, Function.identity(), (v1, v2) -> v2));
        // 应用类型分组实例
        List<BusinessInstanceModelDB> groupOfMoDBList = modelDao.queryNextLevelInstanceBatch(siteMap.keySet());
        Map<Integer, List<GroupOfMo>> groupSiteMap = groupOfMoDBList
                .stream().collect(Collectors.groupingBy(BusinessInstanceModelDB::getTargetInstanceId,
                        Collectors.mapping(this::convertInstanceModelToGroup, Collectors.toList())));
        if (isInit){
            checkAndFillGroupName(solutionId, groupSiteMap);
        }
        siteMap.forEach((siteInsId, value) -> {
            Site site = new Site();
            site.setSiteId(siteInsId);
            site.setSiteName(SiteNameUtils.getSiteName(value, isInit));
            site.setGroupList(groupSiteMap.get(siteInsId));
            sites.add(site);
        });
        return sites;
    }

    private void checkAndFillGroupName(Integer solutionId, Map<Integer, List<GroupOfMo>> groupSiteMap) {
        List<GroupOfMo> groupOfMos = new ArrayList<>();
        groupSiteMap.values().forEach(groupList -> {
            List<GroupOfMo> lackNameGroups = groupList.stream().filter(groupOfMo -> StringUtils.isEmpty(groupOfMo.getGroupName())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(lackNameGroups)) {
                groupOfMos.addAll(lackNameGroups);
            }
        });
        if (CollectionUtils.isEmpty(groupOfMos)) {
            return;
        }
        BusinessInstanceModelDB solutionIns = modelDao.queryInstanceByInstanceId(solutionId);
        BusinessCommonModel solutionModel = commonModelDao.queryCommonModelByModelId(solutionIns.getModelId());
        BusinessCommonModel groupQueryCondition = createEditQueryCondition(solutionModel,
                BusinessTopoConstant.BUSINESS_GROUP_TYPE_ID);
        List<BusinessCommonModel> groupCommonModels = commonModelDao.queryCommonModelByCondition(groupQueryCondition);
        Map<String, String> groupName = groupCommonModels.stream()
                .collect(Collectors.toMap(BusinessCommonModel::getModelId, model -> model.getStaticExtentAttr("groupName").getStaticAttrValue(), (v1, v2) -> v2));
        groupOfMos.forEach(groupOfMo -> groupOfMo.setGroupName(groupName.get(groupOfMo.getModelId())));
    }

    private BusinessCommonModel createEditQueryCondition(BusinessCommonModel solutionModel, Integer modelType) {
        BusinessCommonModel condition = new BusinessCommonModel();
        condition.setSolutionName(solutionModel.getSolutionName());
        condition.setModelType(modelType);
        return condition;
    }

    private GroupOfMo convertInstanceModelToGroup(BusinessInstanceModelDB groupOfMoDB) {
        GroupOfMo groupOfMo = new GroupOfMo();
        groupOfMo.setSiteId(groupOfMoDB.getTargetInstanceId());
        groupOfMo.setGroupId(groupOfMoDB.getInstanceId());
        groupOfMo.setGroupName(groupOfMoDB.getExtentAttr(BusinessTopoConstant.GROUP_NAME_ATTR_KEY).getAttrValue());
        groupOfMo.setModelId(groupOfMoDB.getModelId());
        return groupOfMo;
    }

    @Override
    @Transactional(rollbackFor = ServiceException.class)
    public ResponseEntity editOverViewPage(HttpContext context, Integer solutionId, String solutionName,
                                           String businessContent, String siteContent) throws ServiceException {
        ResponseEntity responseEntity = new ResponseEntity();
        // 记录操作日志
        String remoteAddr = HttpUtil.getRemoteAddr(context.getHttpServletRequest());
        ToDistinguishingList toDistinguishingList = new ToDistinguishingList();
        if (businessEditLock.tryLock()) {
            StringBuilder logDetail = new StringBuilder();
            boolean siteEditeSuccess = false;
            try {
                siteEditeSuccess = updateSiteInfo(solutionId, siteContent, logDetail);
                // 保存解决方案名
                saveSolutionName(solutionId, solutionName);
                // 判断参数是否合法
                BusinessInstanceModifyValidate businessInstanceModifyValidate = new BusinessInstanceModifyValidate();
                businessInstanceModifyValidate.validate(businessContent);
                List<Business> businessList = JSONArray.parseArray(businessContent, Business.class, JSONReader.Feature.SupportSmartMatch);

                // 根据前台传输的业务列表与后台业务列表对应
                toDistinguishingList = distinguishingBusinessOperationType(solutionId, businessList);
                recordOpLog(false, siteEditeSuccess, logDetail, toDistinguishingList.getLogDetail(), remoteAddr);
                responseEntity.setResultCode(RestConstant.SUCCESS_CODE);
            } catch (ServiceException e) {
                LOGGER.error("[editBusinessList] edit business error, the error detail is ", e);
                recordOpLog(true, siteEditeSuccess, logDetail, toDistinguishingList.getLogDetail(), remoteAddr);
                throw new ServiceException(e.getMessage());
            } finally {
                businessEditLock.unlock();
            }
        } else {
            responseEntity.setResultCode(RestConstant.FAILURE_CODE);
            responseEntity.setResultMessage(
                ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.business.waiting"));
        }
        return responseEntity;
    }

    @Override
    @Transactional(rollbackFor = ServiceException.class)
    public ResponseEntity editOverviewForMm(HttpContext context, Integer solutionId, String solutionName,
                                            String businessContent, String channelContent, String stripeTeamContent) throws ServiceException {
        ResponseEntity responseEntity = new ResponseEntity();
        // 记录操作日志
        String remoteAddr = HttpUtil.getRemoteAddr(context.getHttpServletRequest());
        ToDistinguishingList toDistinguishingList = new ToDistinguishingList();
        if (businessEditLock.tryLock()) {
            StringBuilder logDetail = new StringBuilder();
            boolean stripeTeamEditeSuccess = false;
            try {
                // 保存解决方案名
                saveSolutionName(solutionId, solutionName);
                // 处理条带分组名称
                stripeTeamEditeSuccess = updateStripeTeamInfo(solutionId, stripeTeamContent, logDetail);
                List<Business> thirdPartyList = JSONArray.parseArray(businessContent, Business.class, JSONReader.Feature.SupportSmartMatch);
                List<Channel> channelList = JSONArray.parseArray(channelContent, Channel.class, JSONReader.Feature.SupportSmartMatch);
                // 处理第三方和接入渠道
                toDistinguishingList = distinguishingBusinessOperationType(solutionId, thirdPartyList, channelList);
                recordOpLog(false, stripeTeamEditeSuccess, logDetail, toDistinguishingList.getLogDetail(), remoteAddr);
                responseEntity.setResultCode(RestConstant.SUCCESS_CODE);
            } catch (ServiceException e) {
                LOGGER.error("[editBusinessList] edit business error, the error detail is ", e);
                recordOpLog(true, stripeTeamEditeSuccess, logDetail, toDistinguishingList.getLogDetail(), remoteAddr);
                throw new ServiceException(e.getMessage());
            } finally {
                businessEditLock.unlock();
            }
        } else {
            responseEntity.setResultCode(RestConstant.FAILURE_CODE);
            responseEntity.setResultMessage(ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.business.waiting"));
        }
        return responseEntity;
    }

    private boolean updateStripeTeamInfo(Integer solutionId, String stripeTeamContent, StringBuilder logDetail) throws ServiceException {
        if (StringUtils.isEmpty(stripeTeamContent)) {
            LOGGER.info("stripe team content from web is empty, skip update site info");
            return true;
        }
        try {
            // 条带分组实例列表
            List<BusinessInstanceModelDB> stripeTeamIns = modelDao.queryNextLevelInstance(solutionId).stream().filter(ins -> ins.getModelType() == 2).collect(Collectors.toList());
            List<Integer> stripeTeamIds = stripeTeamIns.stream().map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList());
            List<BusinessInstanceModelDB> siteInsList = getPermissionInstanceList(stripeTeamIds);
            // 某组中一个site有权限，对这个组的名称就有权限修改
            Map<Integer, List<BusinessInstanceModelDB>> siteGroup = siteInsList.stream().collect(Collectors.groupingBy(BusinessInstanceModelDB::getTargetInstanceId));
            // 过滤出有权限的实例
            stripeTeamIns = stripeTeamIns.stream().filter(ins -> siteGroup.containsKey(ins.getInstanceId())).collect(Collectors.toList());
            List<StripeTeam> originStripeTeamList = convertInstanceModelToStripeTeam(stripeTeamIns);
            List<StripeTeam> curStripGroupList = JSONArray.parseArray(stripeTeamContent, StripeTeam.class, JSONReader.Feature.SupportSmartMatch);
            List<Integer> permissionIds = originStripeTeamList.stream().map(StripeTeam::getStripeTeamId).collect(Collectors.toList());
            List<Integer> noPermissionIds = curStripGroupList.stream().map(StripeTeam::getStripeTeamId).filter(curId -> !permissionIds.contains(curId)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noPermissionIds)) {
                String detail = noPermissionIds.stream().sorted().map(String::valueOf).collect(Collectors.joining(", "));
                LOGGER.error("don't have permission of this Stripe Team, id = {}", detail);
                LogUtil.sendAuthFailedSecurityLog(LogConstant.USER_AUTHENTICATION_FAILED_STRIPETEAM, new Object[]{detail});
                throw new ServiceException(ResourceUtil.getMessage(LogConstant.USER_AUTHENTICATION_FAILED_STRIPETEAM, new Object[]{detail}));
            }
            List<BusinessInsExtentAttrDB> attrList = getDiffirentStripeTeam(originStripeTeamList, curStripGroupList, logDetail);
            if (CollectionUtils.isEmpty(attrList)) {
                LOGGER.info("no stripe team modified");
                return true;
            }
            modelDao.insertInstanceAttr(attrList);
            logDetail.deleteCharAt(logDetail.length() - 1);
            logDetail.insert(0, ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.stripeteamname.detail.prefix")).append(DITIAL_SUFFIX);
            return true;
        } catch (Exception e) {
            logDetail.delete(0, logDetail.length());
            logDetail.append(ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.stripeteamname.failed"));
            LOGGER.error("edit stripe team error", e);
            throw new ServiceException(e.getMessage());
        }
    }

    private List<BusinessInstanceModelDB> getPermissionInstanceList(List<Integer> parentIds) {
        List<BusinessInstanceModelDB> siteInsList = modelDao.queryNextLevelInstanceBatch(parentIds);
        // 校验权限
        if (!Boolean.TRUE.equals(ContextUtils.getContext().getAdmin())) {
            Set<String> authDns = AuthUtils.getAuthDns();
            if (CollectionUtils.isEmpty(authDns)) {
                siteInsList = Collections.emptyList();
            } else {
                siteInsList = siteInsList.stream().filter(insModel -> authDns.contains(insModel.getDn())).collect(Collectors.toList());
            }
        }
        return siteInsList;
    }

    private List<BusinessInsExtentAttrDB> getDiffirentStripeTeam(List<StripeTeam> originStripeTeamList, List<StripeTeam> curStripGroupList, StringBuilder logDetail) {
        List<BusinessInsExtentAttrDB> attrList = new ArrayList<>();
        Map<Integer, StripeTeam> oriStripeTeamMap = originStripeTeamList.stream().collect(Collectors.toMap(StripeTeam::getStripeTeamId, Function.identity(), (oldVal, newVal) -> newVal));
        Map<Integer, StripeTeam> curStripeTeamMap = curStripGroupList.stream().collect(Collectors.toMap(StripeTeam::getStripeTeamId, Function.identity(), (oldVal, newVal) -> newVal));
        curStripeTeamMap.forEach((insId, stripeTeam) -> {
            StripeTeam oriSiteInfo = oriStripeTeamMap.get(insId);
            if (!Objects.equals(stripeTeam.getStripeTeamName(), oriSiteInfo.getStripeTeamName())) {
                BusinessInsExtentAttrDB extentAttrDB = new BusinessInsExtentAttrDB(insId, BusinessTopoConstant.STRIPE_NAME, BusinessTopoConstant.STRING_CLASS_TYPE, stripeTeam.getStripeTeamName());
                attrList.add(extentAttrDB);
                logDetail.append(oriSiteInfo.getStripeTeamName()).append("->").append(stripeTeam.getStripeTeamName()).append(",");
            }
        });
        return attrList;
    }

    private List<StripeTeam> convertInstanceModelToStripeTeam(List<BusinessInstanceModelDB> stripeTeamIns) {
        List<StripeTeam> stripeTeams = new ArrayList<>();
        stripeTeamIns.forEach(ins -> {
            StripeTeam stripeTeam = new StripeTeam();
            stripeTeam.setStripeTeamId(ins.getInstanceId());
            stripeTeam.setStripeTeamName(ins.getExtentAttr(BusinessTopoConstant.STRIPE_NAME).getAttrValue());
            stripeTeams.add(stripeTeam);
        });
        return stripeTeams;
    }

    private void recordOpLog(boolean hasException, boolean preEditeSuccess, StringBuilder logDetail, String bussinessDetail, String remoteAddr) throws ServiceException {
        if (StringUtils.isNotEmpty(bussinessDetail)) {
            logDetail.append(ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.business.detail.prefix")).append(bussinessDetail);
        }
        // 如果站点和业务都没有修改，不记录操作日志
        if (StringUtils.isEmpty(logDetail)) {
            return;
        }
        if (logDetail.toString().endsWith(DITIAL_SUFFIX)) {
            logDetail.delete(logDetail.length() - DITIAL_SUFFIX.length(), logDetail.length());
        }
        if (!hasException) {
            LogUtil.recordCreateOperationLog(ResourceUtil.getMessage("com.huawei.i2000.topo.service.source"),
                    ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.business.success") + logDetail, LogResult.SUCCESSFUL,
                    ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.business.operation"), remoteAddr);
            return;
        }
        // 部分成功，修改站点名称(或条带分组名称)成功，但是修改业务失败
        if (preEditeSuccess) {
            LogUtil.recordCreateOperationLog(ResourceUtil.getMessage("com.huawei.i2000.topo.service.source"),
                    ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.business.partsuccess") +
                            logDetail.append(bussinessDetail), LogResult.PARTIAL_SUCCESS,
                    ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.business.operation"), remoteAddr);
            return;
        }
        LogUtil.recordCreateOperationLog(ResourceUtil.getMessage("com.huawei.i2000.topo.service.source"),
                ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.business.failed") +
                        logDetail.append(bussinessDetail), LogResult.FAILURE,
                ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.business.operation"), remoteAddr);
    }

    private boolean updateSiteInfo(Integer solutionId, String siteContent, StringBuilder logDetail) throws ServiceException {
        if (StringUtils.isEmpty(siteContent)){
            LOGGER.info("site content from web is empty, skip update site info");
            return true;
        }
        try {
            // 站点分组实例id列表
            List<Integer> siteGroupInsIds = modelDao.queryNextLevelInstance(solutionId)
                    .stream().filter(ins -> ins.getModelType() == 2).map(BusinessInstanceModelDB::getInstanceId)
                    .collect(Collectors.toList());
            List<Site> originSiteList = getSiteListBySiteGroups(solutionId, siteGroupInsIds, false);
            List<Site> curSiteList = JSONArray.parseArray(siteContent, Site.class, JSONReader.Feature.SupportSmartMatch);
            checkSitePermission(originSiteList, curSiteList);
            List<BusinessInsExtentAttrDB> attrList = getDiffirentExtentAttrList(originSiteList, curSiteList, logDetail);
            if (CollectionUtils.isEmpty(attrList)) {
                LOGGER.info("no site modified");
                return true;
            }
            modelDao.insertInstanceAttr(attrList);
            logDetail.deleteCharAt(logDetail.length() - 1);
            logDetail.insert(0, ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.sitename.detail.prefix")).append(DITIAL_SUFFIX);
            return true;
        } catch (Exception e) {
            logDetail.delete(0, logDetail.length());
            logDetail.append(ResourceUtil.getMessage("com.huawei.i2000.topo.service.nlive.edit.sitename.failed"));
            LOGGER.error("[updateSiteInfo] edit site error", e);
            throw new ServiceException(e.getMessage());
        }
    }

    private void checkSitePermission(List<Site> originSiteList, List<Site> curSiteList) throws ServiceException {
        Set<Integer> originSiteIds = originSiteList.stream().map(Site::getSiteId).collect(Collectors.toSet());
        List<Integer> noPermissionSites = curSiteList.stream().map(Site::getSiteId).filter(siteId -> !originSiteIds.contains(siteId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noPermissionSites)) {
            return;
        }
        String noPermissionSiteIdStrs = noPermissionSites.stream().sorted().map(String::valueOf).collect(Collectors.joining(", "));
        LOGGER.error("don't have permission of this site, siteId = {}", noPermissionSiteIdStrs);
        LogUtil.sendAuthFailedSecurityLog(LogConstant.USER_AUTHENTICATION_FAILED_SITE, new Object[]{noPermissionSiteIdStrs});
        throw new ServiceException(ResourceUtil.getMessage(LogConstant.USER_AUTHENTICATION_FAILED_SITE, new Object[]{noPermissionSiteIdStrs}));
    }

    private List<BusinessInsExtentAttrDB> getDiffirentExtentAttrList(List<Site> originSiteList, List<Site> curSiteList, StringBuilder logDetail) {
        List<BusinessInsExtentAttrDB> attrList = new ArrayList<>();
        Map<Integer, Site> oriSiteMap = originSiteList.stream().collect(Collectors.toMap(Site::getSiteId, Function.identity(), (oldVal, newVal) -> newVal));
        Map<Integer, Site> curSiteMap = curSiteList.stream().collect(Collectors.toMap(Site::getSiteId, Function.identity(), (oldVal, newVal) -> newVal));
        curSiteMap.forEach((insId, site) -> {
            Site oriSiteInfo = oriSiteMap.get(insId);
            if (!Objects.equals(site.getSiteName(), oriSiteInfo.getSiteName())) {
                BusinessInsExtentAttrDB extentAttrDB = new BusinessInsExtentAttrDB(insId, BusinessTopoConstant.ATTR_KEY_SITE_DISPLAY_NAME, BusinessTopoConstant.STRING_CLASS_TYPE, site.getSiteName());
                attrList.add(extentAttrDB);
                logDetail.append(oriSiteInfo.getSiteName()).append("->").append(site.getSiteName()).append(",");
            }
            List<GroupOfMo> curGroupOfMoList = site.getGroupList();
            List<GroupOfMo> oriGroupOfMoList = oriSiteInfo.getGroupList();
            List<BusinessInsExtentAttrDB> groupDBList = getDiffGroupOfMoList(curGroupOfMoList, oriGroupOfMoList, logDetail);
            if (CollectionUtils.isNotEmpty(groupDBList)) {
                attrList.addAll(groupDBList);
            }
        });
        return attrList;
    }

    private List<BusinessInsExtentAttrDB> getDiffGroupOfMoList(List<GroupOfMo> curGroupOfMoList, List<GroupOfMo> oriGroupOfMoList, StringBuilder logDetail) {
        List<BusinessInsExtentAttrDB> attrList = new ArrayList<>();
        Map<Integer, GroupOfMo> oriGroupMap = oriGroupOfMoList.stream().collect(Collectors.toMap(GroupOfMo::getGroupId, Function.identity(), (oldVal, newVal) -> newVal));
        Map<Integer, GroupOfMo> curGroupMap = curGroupOfMoList.stream().collect(Collectors.toMap(GroupOfMo::getGroupId, Function.identity(), (oldVal, newVal) -> newVal));
        curGroupMap.forEach((insId, group) -> {
            GroupOfMo oriGroup = oriGroupMap.get(insId);
            if (!Objects.equals(group.getGroupName(), oriGroup.getGroupName())) {
                BusinessInsExtentAttrDB extentAttrDB = new BusinessInsExtentAttrDB(insId, BusinessTopoConstant.GROUP_NAME_ATTR_KEY, BusinessTopoConstant.STRING_CLASS_TYPE, group.getGroupName());
                attrList.add(extentAttrDB);
                logDetail.append(group.getGroupName()).append("->").append(group.getGroupName()).append(",");
            }
        });
        return attrList;
    }

    private void saveSolutionName(Integer solutionId, String solutionModifyName) throws ServiceException {
        BusinessInstanceModelDB solutionIns = modelDao.queryInstanceByInstanceId(solutionId);
        BusinessCommonModel solutionModel = commonModelDao.queryCommonModelByModelId(solutionIns.getModelId());
        String solutionInsName = solutionModel.getSolutionName();

        if (StringUtils.isNotEmpty(solutionModifyName)) {
            ConfigData config = ConfigurationUtil.getConfigDataByName(solutionInsName);
            ConfigData newConfig = new ConfigData();
            newConfig.setConfigItemName(solutionInsName);
            newConfig.setDataType("String");
            newConfig.setValue(solutionModifyName);
            if (config == null) {
                ConfigurationUtil.saveConfigData(newConfig);
            } else {
                ConfigurationUtil.updateConfigDataByName(newConfig);
            }
        }
    }

    private ToDistinguishingList distinguishingBusinessOperationType(Integer solutionId,
        List<Business> toUpdateBusinessList) throws ServiceException {
        List<Business> currentBusinessesList = getCurrentBusinessesList(solutionId);
        ToDistinguishingList toDistinguishingList = new ToDistinguishingList(currentBusinessesList,
            toUpdateBusinessList);
        new BusinessInstanceModifyValidate().validate(
            getBusinessListPreUpdate(currentBusinessesList, toDistinguishingList));

        ToUpdateObjectSet toUpdateObjectSet = initToUpdateObjectSet(solutionId);
        // 添加业务
        LOGGER.debug("[editBusinessList] The add list size is {}, the model name list is {}",
            toDistinguishingList.getToAddBusinessList().size(),
                toDistinguishingList.getToAddBusinessList()
                    .stream()
                    .map(Business::getBusinessName)
                    .collect(Collectors.toList()));
        for (Business toAddBusiness : toDistinguishingList.getToAddBusinessList()) {
            businessEditOperationService.addBusiness(toAddBusiness, toUpdateObjectSet, false);
        }
        // 编辑业务
        LOGGER.debug("[editBusinessList] The edit list size is {}, the model name list is {}",
            toDistinguishingList.getToEditBusinessList().size(),
                toDistinguishingList.getToEditBusinessList()
                    .stream()
                    .map(Business::getBusinessName)
                    .collect(Collectors.toList()));
        for (Business toEditBusiness : toDistinguishingList.getToEditBusinessList()) {
            businessEditOperationService.editBusiness(toEditBusiness, toUpdateObjectSet, false);
        }
        // 删除业务
        LOGGER.debug("[editBusinessList] The delete list size is {}, the model name list is {}",
            toDistinguishingList.getToDeleteBusinessList(),
                toDistinguishingList.getToDeleteBusinessList()
                    .stream()
                    .map(Business::getBusinessName)
                    .collect(Collectors.toList()));
        // 通用操作
        for (Business business : toUpdateBusinessList) {
            businessEditOperationService.saveCommonExtentAttr(business, toUpdateObjectSet);
        }
        // 通过toUpdateObjectSet更新
        businessEditOperationService.updateAndClearData(toUpdateObjectSet,
                toDistinguishingList.getToDeleteBusinessList().stream().map(Business::getBusinessId).collect(Collectors.toList()));
        LOGGER.debug("[editBusinessList] update modify data end.");
        // 异步刷新阈值告警
        businessAlarmInitService.initEditAlarmImmediately(toUpdateObjectSet.getSyncRefreshInsList());
        return toDistinguishingList;
    }

    private ToDistinguishingList distinguishingBusinessOperationType(Integer solutionId, List<Business> updateThirdPartyList, List<Channel> updateChannelList) throws ServiceException {
        List<Business> thirdPartyListFromDB = new ArrayList<>();
        List<Channel> channelListFromDB = new ArrayList<>();
        initBusinessesListFromDB(solutionId, thirdPartyListFromDB, channelListFromDB);
        ToDistinguishingList distinguishedList = new ToDistinguishingList(thirdPartyListFromDB, channelListFromDB, updateThirdPartyList, updateChannelList);
        List<Business> thirdPartyList = getBusinessListPreUpdate(thirdPartyListFromDB, distinguishedList);
        List<Channel> channelList = getChannelListPreUpdate(channelListFromDB, distinguishedList);
        new BusinessInstanceModifyValidate().validate(thirdPartyList, channelList);
        ToUpdateObjectSet toUpdateObjectSet = initToUpdateObjectSet(solutionId);
        // 设置新增和修改channel的对象参数
        initChannelInUpdateSet(distinguishedList, toUpdateObjectSet);
        Map<String, Channel> addChannelNameMap = distinguishedList.getAddChannelList().stream().collect(Collectors.toMap(Channel::getChannelName, Function.identity(), (v1, v2) -> v1));

        // 新增和修改第三方
        initThirdPartyInUpdateSet(distinguishedList, toUpdateObjectSet, addChannelNameMap);
        // 删除业务
        if (LOGGER.isDebugEnabled()){
            LOGGER.debug("The delete third party list size is {}, the model name list is {}",
                    distinguishedList.getToDeleteBusinessList(),
                    distinguishedList.getToDeleteBusinessList().stream().map(Business::getBusinessName).collect(Collectors.toList()));
            LOGGER.debug("The delete channel list size is {}, the model name list is {}",
                    distinguishedList.getDeleteChannelList(),
                    distinguishedList.getDeleteChannelList().stream().map(Channel::getChannelName).collect(Collectors.toList()));
        }
        List<Integer> deleteInstanceIds = distinguishedList.getDeleteChannelList().stream().map(Channel::getChannelId).collect(Collectors.toList());
        List<String> deleteModelIds = distinguishedList.getDeleteChannelList().stream().map(Channel::getModelId).collect(Collectors.toList());
        List<Integer> deleteThirdPartyIds = distinguishedList.getToDeleteBusinessList().stream().map(Business::getBusinessId).collect(Collectors.toList());
        List<String> deleteThirdPartyModelIds = distinguishedList.getToDeleteBusinessList().stream().map(Business::getModelId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteThirdPartyIds)) {
            deleteInstanceIds.addAll(deleteThirdPartyIds);
        }
        if (CollectionUtils.isNotEmpty(deleteThirdPartyModelIds)) {
            deleteModelIds.addAll(deleteThirdPartyModelIds);
        }
        updateSortOrderForInstance(updateThirdPartyList, updateChannelList, toUpdateObjectSet);
        // 通过toUpdateObjectSet更新
        businessEditOperationService.updateAndClearData(toUpdateObjectSet, deleteInstanceIds, deleteModelIds);
        LOGGER.debug("[editBusinessList] update modify data end.");
        // 异步刷新第三方、接入渠道告警状态
        businessAlarmInitService.initEditAlarmImmediately(toUpdateObjectSet.getSyncRefreshInsList());
        return distinguishedList;
    }

    private void updateSortOrderForInstance(List<Business> updateThirdPartyList, List<Channel> updateChannelList, ToUpdateObjectSet toUpdateObjectSet) {
        List<BusinessInsExtentAttrDB> insExtentAttrList = new ArrayList<>();
        for (Business business : updateThirdPartyList) {
            if (business.getBusinessId() == null || Objects.equals(business.getEditType(), 1)) {
                continue;
            }
            BusinessInstanceModelDB instanceModel = new BusinessInstanceModelDB();
            instanceModel.setInstanceId(business.getBusinessId());
            BusinessInsExtentAttrDB insExtentAttr = businessEditOperationService.createInsExtentAttr(instanceModel, BusinessTopoConstant.BUSINESS_SORT_ORDER, business);
            if (insExtentAttr != null) {
                insExtentAttrList.add(insExtentAttr);
            }
        }
        for (Channel channel : updateChannelList) {
            if (channel.getChannelId() == null || Objects.equals(channel.getEditType(), 1)) {
                continue;
            }
            BusinessInstanceModelDB instanceModel = new BusinessInstanceModelDB();
            instanceModel.setInstanceId(channel.getChannelId());
            BusinessInsExtentAttrDB insExtentAttr = channelEditOperationService.createInsExtentAttr(instanceModel, BusinessTopoConstant.BUSINESS_SORT_ORDER, channel);
            if (insExtentAttr != null) {
                insExtentAttrList.add(insExtentAttr);
            }
        }
        toUpdateObjectSet.addInstanceExtentAttrs(insExtentAttrList);
    }

    private void initThirdPartyInUpdateSet(ToDistinguishingList distinguishedList, ToUpdateObjectSet toUpdateObjectSet, Map<String, Channel> addChannelNameMap) throws ServiceException {
        if (LOGGER.isDebugEnabled()){
            LOGGER.debug("The add third party list size is {}, the model name list is {}",
                    distinguishedList.getToAddBusinessList().size(),
                    distinguishedList.getToAddBusinessList().stream().map(Business::getBusinessName).collect(Collectors.toList()));
            LOGGER.debug("The edit third party list size is {}, the model name list is {}",
                    distinguishedList.getToEditBusinessList().size(),
                    distinguishedList.getToEditBusinessList().stream().map(Business::getBusinessName).collect(Collectors.toList()));
        }
        for (Business toAddBusiness : distinguishedList.getToAddBusinessList()) {
            // 实例已经入库过一次了,toAddBusiness有了实例id
            businessEditOperationService.addBusiness(toAddBusiness, toUpdateObjectSet, addChannelNameMap);
        }
        for (Business toEditBusiness : distinguishedList.getToEditBusinessList()) {
            businessEditOperationService.editBusiness(toEditBusiness, toUpdateObjectSet, addChannelNameMap);
        }
    }

    private void initChannelInUpdateSet(ToDistinguishingList distinguishedList, ToUpdateObjectSet toUpdateObjectSet) throws ServiceException {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("The add channel list size is {}, the model name list is {}",
                    distinguishedList.getAddChannelList().size(),
                    distinguishedList.getAddChannelList().stream().map(Channel::getChannelName).collect(Collectors.toList()));
            LOGGER.debug("The edit channel list size is {}, the model name list is {}",
                    distinguishedList.getEditChannelList().size(),
                    distinguishedList.getEditChannelList().stream().map(Channel::getChannelName).collect(Collectors.toList()));
        }
        for (Channel addChannel : distinguishedList.getAddChannelList()) {
            // 实例已经入库过一次了,addChannel有了实例id
            channelEditOperationService.addChannel(addChannel, toUpdateObjectSet);
        }
        for (Channel editChannel : distinguishedList.getEditChannelList()) {
            channelEditOperationService.editChannel(editChannel, toUpdateObjectSet);
        }
    }

    private void initBusinessesListFromDB(Integer solutionId, List<Business> thirdPartyList, List<Channel> channelList) {
        List<BusinessInstanceModelDB> firstLayerInstanceList = modelDao.queryNextLevelInstance(solutionId);
        List<BusinessInstanceModelDB> thirdPartyModelList = firstLayerInstanceList.stream()
                .filter(ins -> Objects.equals(ins.getModelType(), BusinessConfigEnum.BUSINESS_CONFIG_ENUM.getModeType()))
                .collect(Collectors.toList());
        List<BusinessInstanceModelDB> channelModelList = firstLayerInstanceList.stream()
                .filter(ins -> Objects.equals(ins.getModelType(), BusinessConfigEnum.CHANNEL_CONFIG_ENUM.getModeType()))
                .collect(Collectors.toList());
        BusinessConvertForMm businessConvert = new BusinessConvertForMm();
        // 获取指标是一个实例一个实例做的，可以优化批量查询
        thirdPartyList.addAll(thirdPartyModelList.stream().map(businessConvert::convertForMm).collect(Collectors.toList()));
        businessConvert.initAssociatedChannelList(thirdPartyList, thirdPartyModelList);
        ChannelConvert channelConvert = new ChannelConvert(businessConvert);
        channelList.addAll(channelModelList.stream().map(channelConvert::convertForMm).collect(Collectors.toList()));
    }

    private List<Business> getBusinessListPreUpdate(List<Business> currentBusinessesList,
        ToDistinguishingList toDistinguishingList) {
        List<Business> businesses = new ArrayList<>();
        Map<Integer, Business> editBusinesses = new HashMap<>();
        List<Integer> deleteBusinessIds = toDistinguishingList.getToDeleteBusinessList()
            .stream()
            .map(Business::getBusinessId)
            .collect(Collectors.toList());
        toDistinguishingList.getToEditBusinessList()
            .forEach(editBusiness -> editBusinesses.put(editBusiness.getBusinessId(), editBusiness));
        for (Business business : currentBusinessesList) {
            if (deleteBusinessIds.contains(business.getBusinessId())) {
                continue;
            }
            if (editBusinesses.containsKey(business.getBusinessId())) {
                businesses.add(editBusinesses.get(business.getBusinessId()));
                continue;
            }
            businesses.add(business);
        }
        businesses.addAll(toDistinguishingList.getToAddBusinessList());
        return businesses;
    }

    private List<Channel> getChannelListPreUpdate(List<Channel> currentChannelList, ToDistinguishingList distinguishedList) {
        List<Channel> channels = new ArrayList<>();
        Map<Integer, Channel> editChannels = new HashMap<>();
        List<Integer> deleteChannelIds = distinguishedList.getDeleteChannelList().stream().map(Channel::getChannelId).collect(Collectors.toList());
        distinguishedList.getEditChannelList().forEach(channel -> editChannels.put(channel.getChannelId(), channel));
        for (Channel channel : currentChannelList) {
            if (deleteChannelIds.contains(channel.getChannelId())) {
                continue;
            }
            if (editChannels.containsKey(channel.getChannelId())) {
                channels.add(editChannels.get(channel.getChannelId()));
                continue;
            }
            channels.add(channel);
        }
        channels.addAll(distinguishedList.getAddChannelList());
        return channels;
    }

    private List<Business> getCurrentBusinessesList(Integer solutionId) {
        List<BusinessInstanceModelDB> currentBusinessModelList = modelDao.queryNextLevelInstance(solutionId)
            .stream()
            .filter(ins -> Objects.equals(ins.getModelType(), BusinessConfigEnum.BUSINESS_CONFIG_ENUM.getModeType()))
            .collect(Collectors.toList());
        BusinessConvert businessConvert = new BusinessConvert();
        return currentBusinessModelList.stream().map(businessConvert::convert).collect(Collectors.toList());
    }

    private ToUpdateObjectSet initToUpdateObjectSet(Integer solutionId) {
        ToUpdateObjectSet toUpdateObjectSet = new ToUpdateObjectSet();
        BusinessInstanceModelDB solutionIns = modelDao.queryInstanceByInstanceId(solutionId);
        BusinessCommonModel solutionModel = commonModelDao.queryCommonModelByModelId(solutionIns.getModelId());
        toUpdateObjectSet.setSolutionId(solutionId);
        toUpdateObjectSet.setSolutionName(solutionModel.getSolutionName());
        return toUpdateObjectSet;
    }

    public Integer querySolutionTypeBySolIns(Integer solutionInsId) {
        BusinessInstanceModelDB solutionIns = modelDao.queryInstanceByInstanceId(solutionInsId);
        String solutionName = solutionIns.getSolutionName();
        BusinessCommonModel siteQueryModel = commonModelDao.querySiteModelBySolutionName(solutionName);
        BusinessCommonModel condition = new BusinessCommonModel();
        condition.setSolutionName(solutionName);
        condition.setModelId(siteQueryModel.getModelId());
        List<BusinessCommonModel> solutionTypeList = commonModelDao.queryCommonModelByCondition(condition);
        BusinessCommonModel solTypeModel = new BusinessCommonModel();
        if (CollectionUtils.isNotEmpty(solutionTypeList)) {
            solTypeModel = solutionTypeList.get(0);
        }
        String solTypeValue = solTypeModel.getStaticExtentAttr(BusinessTopoConstant.SITE_DATA_TYPE_ATTR_KEY).getStaticAttrValue();
        if (Objects.nonNull(solTypeValue)) {
            return Integer.parseInt(solTypeValue);
        } else {
            return BusinessTopoConstant.SolutionTypeLabel.CBS_TYPE;
        }
    }

    private BusinessCommonModel getSiteCommonModelByBusinessInstanceId(Integer businessId, Long timestamp) throws ServiceException {
        if (timestamp == null) {
            timestamp = 0L;
        }
        BusinessInstanceModelDB topModel = modelDao.queryUpLevelInstance(businessId, timestamp);
        BusinessCommonModel topCommonModel = commonModelDao.queryCommonModelByModelId(topModel.getModelId());
        BusinessCommonModel condition = new BusinessCommonModel();
        condition.setModelType(BusinessTopoConstant.SITE_TYPE_ID);
        condition.setSolutionName(topCommonModel.getSolutionName());
        List<BusinessCommonModel> siteCommonModels = commonModelDao.queryCommonModelByCondition(condition);
        if (CollectionUtils.isEmpty(siteCommonModels)) {
            LOGGER.error("querySiteModelBySolutionName error, solutionName = {}", topModel.getSolutionName());
            throw new ServiceException("querySiteModelBySolutionName error");
        }
        return siteCommonModels.get(0);
    }

    /**
     * Solution包装类
     *
     * @since 2024-12-5 16:04:56
     */
    @Setter
    @Getter
    public static class SolutionWrapper{
        String realSolutionName;
        Solution solution;
    }

    public UpperLayerInfo queryUpperLayerIdByDn(QueryUpperLayerParam queryUpperLayerParam)
        throws ServiceException {
        List<BusinessInstanceModelDB> modelDbs = modelDao.queryInstanceByDnsTimeline(Collections.singletonList(queryUpperLayerParam.getDn()), queryUpperLayerParam.getTimestamp());
        if (CollectionUtils.isEmpty(modelDbs)) {
            LOGGER.error("queryUpperLayerIdByDn model is empty, dn = {}, timestamp = {}", queryUpperLayerParam.getDn(), queryUpperLayerParam.getTimestamp());
            return getErrorUpperLayerInfo("queryUpperLayerIdByDn model is empty", queryUpperLayerParam.getDn(), queryUpperLayerParam.getTimestamp());
        }
        if (BusinessTopoConstant.OSS_DN.equals(queryUpperLayerParam.getDn())) {
            return getDvUpperlayerInfo(modelDbs, queryUpperLayerParam.getTimestamp());
        }
        BusinessInstanceModelDB modelDB = modelDbs.get(0);
        int solutionType = commonModelDao.querySolutionTypeByModelId(modelDB.getModelId());
        switch (modelDB.getModelType()) {
            case BusinessTopoConstant.SITE_TYPE_ID:
                return getSiteUpperLayerInfo(modelDB, queryUpperLayerParam.getTimestamp(), solutionType);
            case BusinessTopoConstant.CLUSTER_INSTANCE_ID:
                return getClusterUpperLayerInfo(modelDB, queryUpperLayerParam.getTimestamp(), solutionType, modelDB.getInstanceId(), modelDB.getDn());
            case BusinessTopoConstant.POD_TYPE_ID:
                return getPodUpperLayerInfo(modelDB, queryUpperLayerParam.getTimestamp(), solutionType, modelDB.getInstanceId(), modelDB.getDn());
            case BusinessTopoConstant.DOCKER_TYPE_ID:
                return getDockerUpperLayerInfo(modelDB, queryUpperLayerParam.getTimestamp(), solutionType, modelDB.getDn());
            case BusinessTopoConstant.VM_TYPE_ID:
                return getVmUpperLayerInfo(queryUpperLayerParam.getDn(), queryUpperLayerParam.getTimestamp(), solutionType, modelDB.getInstanceId(), modelDB.getDn());
            case BusinessTopoConstant.CONTAINER_TYPE_ID:
                return getContainerUpperLayerInfo(modelDB, queryUpperLayerParam.getTimestamp(), solutionType, modelDB.getDn());
            default:
                LOGGER.error("queryUpperLayerIdByDn, wrong model type = {}", modelDB.getModelType());
                return getErrorUpperLayerInfo("queryUpperLayerIdByDn, wrong model type", queryUpperLayerParam.getDn(), queryUpperLayerParam.getTimestamp());
        }
    }

    private UpperLayerInfo getSiteUpperLayerInfo(BusinessInstanceModelDB modelDB, Long timestamp, int solutionType) {
        List<BusinessInstanceRelationModel> relationModels = relationDao.queryAllRelationByInstanceIdsTimeline(Collections.singletonList(modelDB.getInstanceId()), timestamp);
        UpperLayerInfo info = new UpperLayerInfo();
        info.setResultCode(RestConstant.SUCCESS_CODE);
        info.setSolutionType(solutionType);
        info.setDn(modelDB.getDn());
        info.setTimestamp(timestamp);
        info.setSolutionId(getTargetIdFromRelation(relationModels, BusinessTopoConstant.RELATION_TYPE_TOP_LEVEL));
        if (solutionType == BusinessTopoConstant.SOLUTION_MOBILE_MONEY) {
            BusinessInstanceModelDB stripeGroup = businessInstanceModelDao.queryUpLevelInstance(modelDB.getInstanceId(), timestamp);
            // siteId是第2层的
            info.setSiteId(stripeGroup.getInstanceId());
            info.setDnId(modelDB.getInstanceId());
        } else {
            // siteId是第3层的
            info.setSiteId(modelDB.getInstanceId());
            info.setDnId(modelDB.getInstanceId());
        }
        return info;
    }

    private UpperLayerInfo getClusterUpperLayerInfo(BusinessInstanceModelDB modelDB, Long timestamp, int solutionType, Integer dnId, String originalDn) {
        List<BusinessInstanceRelationModel> relationModels = relationDao.queryAllRelationByInstanceIdsTimeline(Collections.singletonList(modelDB.getInstanceId()), timestamp);
        UpperLayerInfo info = new UpperLayerInfo();
        info.setResultCode(RestConstant.SUCCESS_CODE);
        info.setSolutionType(solutionType);
        info.setSolutionId(getTargetIdFromRelation(relationModels, BusinessTopoConstant.RELATION_TYPE_TOP_LEVEL));
        info.setDnId(dnId);
        info.setDn(originalDn);
        info.setTimestamp(timestamp);
        // instanceId是第5层的
        BusinessInstanceModelDB instanceType = businessInstanceModelDao.queryUpLevelInstance(modelDB.getInstanceId(), timestamp);
        info.setInstanceId(instanceType.getInstanceId());
        // groupId是第4层的
        BusinessInstanceModelDB instanceGroup = businessInstanceModelDao.queryUpLevelInstance(instanceType.getInstanceId(), timestamp);
        info.setGroupId(instanceGroup.getInstanceId());
        info.setApplicationType(Integer.valueOf(modelDB.getStaticExtentAttr(BusinessTopoConstant.APPLICATION_TYPE).getStaticAttrValue()));
        Integer siteInstanceId = getTargetIdFromRelation(relationModels, BusinessTopoConstant.RELATION_TYPE_SITE);
        if (solutionType == BusinessTopoConstant.SOLUTION_MOBILE_MONEY) {
            BusinessInstanceModelDB stripeGroup = businessInstanceModelDao.queryUpLevelInstance(siteInstanceId, timestamp);
            // siteId是第2层的
            info.setSiteId(stripeGroup.getInstanceId());
            // stripeUnit是第6层的
            info.setStripeUnit(modelDB.getExtentAttr(BusinessTopoConstant.MM_HWS_UNIT).getAttrValue());
        } else {
            // siteId是第3层的
            info.setSiteId(siteInstanceId);
        }
        return info;
    }

    private UpperLayerInfo getPodUpperLayerInfo(BusinessInstanceModelDB modelDB, Long timestamp, int solutionType, Integer dnId, String originalDn) {
        List<BusinessInstanceModelDB> instances = businessInstanceModelDao.queryUpLevelInstanceByTimeLineBatch(Collections.singletonList(modelDB.getInstanceId()), timestamp);
        if (CollectionUtils.isEmpty(instances)) {
            LOGGER.error("getPodUpperLayerInfo get instance is empty, pod dn = {}", modelDB.getDn());
            return getErrorUpperLayerInfo("getPodUpperLayerInfo get instance is empty", originalDn, timestamp);
        }
        return getClusterUpperLayerInfo(instances.get(0), timestamp, solutionType, dnId, originalDn);
    }

    private UpperLayerInfo getDockerUpperLayerInfo(BusinessInstanceModelDB modelDB, Long timestamp, int solutionType, String originalDn) {
        List<BusinessInstanceModelDB> pods = businessInstanceModelDao.queryUpLevelInstanceByTimeLineBatch(Collections.singletonList(modelDB.getInstanceId()), timestamp);
        if (CollectionUtils.isEmpty(pods)) {
            LOGGER.error("getDockerUpperLayerInfo get pod is empty, docker dn = {}", modelDB.getDn());
            return getErrorUpperLayerInfo("getDockerUpperLayerInfo get pod is empty", originalDn, timestamp);
        }
        return getPodUpperLayerInfo(pods.get(0), timestamp, solutionType, pods.get(0).getInstanceId(), originalDn);
    }

    private UpperLayerInfo getVmUpperLayerInfo(String vmDn, Long timestamp, int solutionType, Integer dnId, String originalDn) throws ServiceException {
        List<BusinessInstanceModelDB> vmIns = businessInstanceModelDao.queryRealVmInstanceByDnsTimeline(Collections.singletonList(vmDn), timestamp);
        if (CollectionUtils.isEmpty(vmIns)) {
            LOGGER.error("getVmUpperLayerInfo get vm instance is empty, vmDn = {}", vmDn);
            return getErrorUpperLayerInfo("getVmUpperLayerInfo get vm instance is empty", originalDn, timestamp);
        }
        List<Integer> podInstanceIds = businessMoTypeDao.queryPodInstanceIdByVmInstanceIdAndTimeLine(vmIns.get(0).getInstanceId(), timestamp);
        if (CollectionUtils.isEmpty(podInstanceIds)) {
            LOGGER.error("getVmUpperLayerInfo get pod is empty, vmDn = {}", vmDn);
            return getErrorUpperLayerInfo("getVmUpperLayerInfo get pod is empty", originalDn, timestamp);
        }
        BusinessInstanceModelDB podModelDB = businessInstanceModelDao.queryInstanceByInstanceIdAndTimeLine(podInstanceIds.get(0), timestamp);
        return getPodUpperLayerInfo(podModelDB, timestamp, solutionType, dnId, originalDn);
    }

    private UpperLayerInfo getContainerUpperLayerInfo(BusinessInstanceModelDB modelDB, Long timestamp, int solutionType, String originalDn)
        throws ServiceException {
        List<Integer> dockerInstanceIds = businessMoTypeDao.queryPodInstanceIdByVmInstanceIdAndTimeLine(modelDB.getInstanceId(), timestamp);
        if (CollectionUtils.isEmpty(dockerInstanceIds)) {
            LOGGER.error("getContainerUpperLayerInfo get docker is empty, container dn = {}", modelDB.getDn());
            return getErrorUpperLayerInfo("getContainerUpperLayerInfo get docker is empty", originalDn, timestamp);
        }
        BusinessInstanceModelDB dockerModelDB = businessInstanceModelDao.queryInstanceByInstanceIdAndTimeLine(dockerInstanceIds.get(0), timestamp);
        return getDockerUpperLayerInfo(dockerModelDB, timestamp, solutionType, originalDn);
    }

    private Integer getTargetIdFromRelation(List<BusinessInstanceRelationModel> relationModels, Integer type) {
        Map<Integer, Integer> relationModelMap = relationModels.stream()
            .collect(Collectors.toMap(BusinessInstanceRelationModel::getRelationType, BusinessInstanceRelationModel::getTargetInstanceId, (r1, r2) -> r1));
        return relationModelMap.get(type);
    }

    private UpperLayerInfo getErrorUpperLayerInfo(String errorMessage, String dn, Long timestamp) {
        UpperLayerInfo upperLayerInfo = new UpperLayerInfo();
        upperLayerInfo.setDn(dn);
        upperLayerInfo.setTimestamp(timestamp);
        upperLayerInfo.setResultCode(RestConstant.FAILURE_CODE);
        upperLayerInfo.setResultMessage(errorMessage);
        return upperLayerInfo;
    }

    private UpperLayerInfo getDvUpperlayerInfo(List<BusinessInstanceModelDB> modelDbs, Long timestamp) {
        BusinessInstanceModelDB dvModel = modelDbs.stream()
            .filter(modelDb -> ACTIVE.equals(modelDb.getExtentAttr(BusinessTopoConstant.IS_ACTIVE_DV).getAttrValue()))
            .findFirst()
            .orElse(null);
        if (dvModel == null) {
            LOGGER.error("cannot find active dv model in topo");
            return getErrorUpperLayerInfo("cannot find active dv model in topo", BusinessTopoConstant.OSS_DN, timestamp);
        }
        List<BusinessInstanceRelationModel> relationModels = relationDao.queryAllRelationByInstanceIdsTimeline(
            Collections.singletonList(dvModel.getInstanceId()), timestamp);
        UpperLayerInfo info = new UpperLayerInfo();
        info.setResultCode(RestConstant.SUCCESS_CODE);
        info.setSolutionId(getTargetIdFromRelation(relationModels, BusinessTopoConstant.RELATION_TYPE_TOP_LEVEL));
        info.setDnId(dvModel.getInstanceId());
        info.setDn(dvModel.getDn());
        info.setTimestamp(timestamp);
        info.setApplicationType(BusinessTopoConstant.APPLICATION_TYPE_ZERO);
        BusinessInstanceModelDB instanceType = businessInstanceModelDao.queryUpLevelInstance(dvModel.getInstanceId(), timestamp);
        info.setInstanceId(instanceType.getInstanceId());
        BusinessInstanceModelDB instanceGroup = businessInstanceModelDao.queryUpLevelInstance(instanceType.getInstanceId(), timestamp);
        info.setGroupId(instanceGroup.getInstanceId());
        Integer siteInstanceId = getTargetIdFromRelation(relationModels, BusinessTopoConstant.RELATION_TYPE_SITE);
        BusinessInstanceModelDB siteModelDB = businessInstanceModelDao.queryInstanceByInstanceIdAndTimeLine(siteInstanceId, timestamp);
        int solutionType = commonModelDao.querySolutionTypeByModelId(siteModelDB.getModelId());
        info.setSolutionType(solutionType);
        if (solutionType != BusinessTopoConstant.SOLUTION_MOBILE_MONEY) {
            info.setSiteId(siteInstanceId);
        }
        return info;
    }
}