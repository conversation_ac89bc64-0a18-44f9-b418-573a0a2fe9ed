/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.openapi.relation;

import com.alibaba.fastjson2.JSONArray;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmDetail;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.SolutionDnStorage;
import com.huawei.i2000.dvtoposervice.business.database.BusinessCommonModel;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceRelationModel;
import com.huawei.i2000.dvtoposervice.business.openapi.alarm.AlarmConditionFilter;
import com.huawei.i2000.dvtoposervice.business.openapi.alarm.model.AlarmQueryParam;
import com.huawei.i2000.dvtoposervice.business.openapi.relation.model.ApplicationMoType;
import com.huawei.i2000.dvtoposervice.business.openapi.relation.model.AssociationOfMo;
import com.huawei.i2000.dvtoposervice.business.openapi.relation.model.DnRelation;
import com.huawei.i2000.dvtoposervice.business.openapi.relation.model.MoOfGroup;
import com.huawei.i2000.dvtoposervice.business.pm.AlarmRequestParamUtil;
import com.huawei.i2000.dvtoposervice.business.pm.AlarmRequestService;
import com.huawei.i2000.dvtoposervice.business.service.bean.AssociationAppLink;
import com.huawei.i2000.dvtoposervice.business.service.bean.IncludeAppTypeInfo;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.BusinessTopoAlarmStorageService;
import com.huawei.i2000.dvtoposervice.convert.SolutionConvert;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessCommonModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceRelationDao;
import com.huawei.i2000.dvtoposervice.model.HisAlarmQueryParam;
import com.huawei.i2000.dvtoposervice.model.Solution;
import com.huawei.i2000.dvtoposervice.model.SolutionExParam;
import com.huawei.i2000.dvtoposervice.model.UserParam;
import com.huawei.i2000.dvtoposervice.util.AuthUtils;
import com.huawei.i2000.dvtoposervice.util.SiteNameUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Topo关联关系查询
 *
 * <AUTHOR>
 * @since 2025/6/23
 */
@Service
public class BusinessTopoRelationServiceImpl implements BusinessTopoRelationService {

    private static OssLog LOGGER = OssLogFactory.getLogger(BusinessTopoRelationServiceImpl.class);

    @Autowired
    private BusinessInstanceModelDao businessInstanceModelDao;

    @Autowired
    private BusinessCommonModelDao businessCommonModelDao;

    @Autowired
    private BusinessInstanceRelationDao relationDao;

    @Autowired
    private AlarmRequestService alarmRequestService;

    @Autowired
    private BusinessTopoAlarmStorageService businessTopoAlarmStorageService;

    @Override
    public List<AssociationOfMo> getApplicationRelation(SolutionExParam param) throws ServiceException {
        Integer solutionId = getInstanceFromSolutionName(param);
        LOGGER.debug("[getApplicationRelation] getApplicationRelation start, solutionId is {}", solutionId);
        // 查询所有集群类型
        BusinessInstanceModelDB solutionIns = getSolutionInstance(solutionId);
        String solutionName = solutionIns.getSolutionName();
        List<BusinessCommonModel> applicationModels = businessCommonModelDao.queryCommonModelByType(BusinessTopoConstant.MOTYPE_CLUSTER_TYPE_ID);
        // 过滤解决方案
        List<BusinessCommonModel> applicationModelsOfSol = applicationModels.stream()
                .filter(model -> solutionName.equals(model.getSolutionName()))
                .collect(Collectors.toList());
        Map<String, BusinessCommonModel> modelNameToModelMap = applicationModelsOfSol.stream()
                .collect(Collectors.toMap(BusinessCommonModel::getModelId, model -> model));
        // 查出所有下层子模型
        List<BusinessCommonModel> clusterModels = businessCommonModelDao.queryCommonModelByType(BusinessTopoConstant.CLUSTER_INSTANCE_ID);
        List<BusinessCommonModel> clusterModelsOfSol = clusterModels.stream()
                .filter(model -> solutionName.equals(model.getSolutionName()))
                .collect(Collectors.toList());

        Map<String, String> applicationToMoTypeMap = getApplicationToMoTypeMap(clusterModelsOfSol, modelNameToModelMap, applicationModelsOfSol);
        return getAssociationOfMos(applicationModelsOfSol, applicationToMoTypeMap);
    }

    private static List<AssociationOfMo> getAssociationOfMos(List<BusinessCommonModel> applicationModelsOfSol, Map<String, String> applicationToMoTypeMap) {
        List<AssociationOfMo> relations = new ArrayList<>();
        // 根据映射关系，返回对应的关联关系
        for (BusinessCommonModel applicationModel : applicationModelsOfSol) {
            String relation = applicationModel.getStaticExtentAttr(BusinessTopoConstant.ASSOCIATION_APP_LINK_LIST_ATTR_KEY).getStaticAttrValue();
            if (StringUtils.isEmpty(relation)) {
                continue;
            }

            AssociationOfMo associationOfMo = new AssociationOfMo();
            associationOfMo.setMoType(applicationToMoTypeMap.get(applicationModel.getModelName()));
            List<AssociationAppLink> associationAppLinkList = JSONArray.parseArray(relation, AssociationAppLink.class);
            for (AssociationAppLink link : associationAppLinkList) {
                link.setModelName(applicationToMoTypeMap.get(link.getModelName()));
            }
            associationOfMo.setLinks(associationAppLinkList);
            relations.add(associationOfMo);
        }
        return relations;
    }

    private static Map<String, String> getApplicationToMoTypeMap(List<BusinessCommonModel> clusterModelsOfSol, Map<String, BusinessCommonModel> modelNameToModelMap, List<BusinessCommonModel> applicationModelsOfSol) {
        Map<String, String> applicationToMoTypeMap = new HashMap<>();
        // 根据关联关系
        for (BusinessCommonModel clusterModel : clusterModelsOfSol) {
            String parentModelId = clusterModel.getParentModelId();

            Optional.ofNullable(modelNameToModelMap.get(parentModelId))
                    .ifPresent(parent -> {
                        String modelName = parent.getModelName();
                        if (applicationModelsOfSol.stream()
                                .anyMatch(model -> model.getModelId().equals(parentModelId))) {
                            String moTypeMapping = clusterModel.getStaticExtentAttr(BusinessTopoConstant.MO_TYPE_MAPPING_ATTR_KEY).getStaticAttrValue();
                            applicationToMoTypeMap.put(modelName, moTypeMapping);
                        }
                    });
        }
        return applicationToMoTypeMap;
    }

    private BusinessInstanceModelDB getSolutionInstance(Integer solutionId) {
        return businessInstanceModelDao.queryInstanceByInstanceId(solutionId);
    }

    @Override
    public List<DnRelation> getDnRelationShip(SolutionExParam param) throws ServiceException {
        Integer solutionId = getInstanceFromSolutionName(param);
        // 查询所有网元
        List<Integer> subInsList = businessInstanceModelDao.queryAllInstanceByTopId(solutionId)
                .stream()
                .filter(ins -> StringUtils.isNotEmpty(ins.getDn()))
                .map(BusinessInstanceModelDB::getInstanceId)
                .collect(Collectors.toList());
        // 查询所有网元的扩展字段
        List<BusinessInstanceModelDB> subInsFullList = businessInstanceModelDao.queryInstanceListByIdList(subInsList, 0L);
        // 上面的查询方式缺少vm\容器，需要补齐
        List<Integer> podInsList = subInsFullList.stream()
                .filter(ins -> BusinessTopoConstant.POD_TYPE_ID == ins.getModelType())
                .map(BusinessInstanceModelDB::getInstanceId)
                .collect(Collectors.toList());
        Set<Integer> insListOfVm = new HashSet<>(
                relationDao.queryRelationByTypeBatchTimeLine(podInsList, BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT, 0L)
                        .values());
        List<BusinessInstanceModelDB> vmInsList = businessInstanceModelDao.queryInstanceListByIdList(
                new ArrayList<>(insListOfVm), 0L);
        subInsFullList.addAll(vmInsList);

        Map<Integer, BusinessInstanceModelDB> insToDnMap = subInsFullList.stream()
                .collect(Collectors.toMap(BusinessInstanceModelDB::getInstanceId, ins -> ins));
        List<Integer> subInsIdList = subInsFullList.stream().map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList());
        // 查询所有网元的上层网元
        List<BusinessInstanceRelationModel> parentRelationList = relationDao.queryAllRelationByInstanceIdsTimeline(subInsIdList, 0L);
        Map<Integer, List<BusinessInstanceRelationModel>> insToRelationMap = parentRelationList.stream()
                .collect(Collectors.groupingBy(BusinessInstanceRelationModel::getInstanceId));

        return getDnRelationList(subInsFullList, insToRelationMap, insToDnMap);
    }

    private List<DnRelation> getDnRelationList(List<BusinessInstanceModelDB> subInsFullList,
        Map<Integer, List<BusinessInstanceRelationModel>> insToRelationMap, Map<Integer, BusinessInstanceModelDB> insToDnMap) {
        List<DnRelation> dnRelationList = new ArrayList<>();
        for (BusinessInstanceModelDB ins : subInsFullList) {
            DnRelation dnRelation = new DnRelation();
            dnRelation.setDn(ins.getDn());
            List<BusinessInstanceRelationModel> relationModels = insToRelationMap.get(ins.getInstanceId());
            setParentAndDeployDn(insToDnMap, ins, relationModels, dnRelation);
            setRelationLvAndType(ins, dnRelation);
            dnRelationList.add(dnRelation);
        }
        setChildrenDn(dnRelationList);
        return dnRelationList;
    }

    private void setChildrenDn(List<DnRelation> dnRelationList) {
        // 对每个有网元，设置它的子网元列表
        Map<String, List<DnRelation>> dnToChildrenMap = dnRelationList.stream()
                .filter(re -> StringUtils.isNotEmpty(re.getParentDn()))
                .collect(Collectors.groupingBy(DnRelation::getParentDn));
        for (DnRelation dnRelation : dnRelationList) {
            List<DnRelation> childrenDnList = dnToChildrenMap.getOrDefault(dnRelation.getDn(), Collections.emptyList());
            dnRelation.setChildrenList(childrenDnList.stream().map(DnRelation::getDn).collect(Collectors.toList()));
        }
    }

    private void setParentAndDeployDn(Map<Integer, BusinessInstanceModelDB> insToDnMap, BusinessInstanceModelDB ins, List<BusinessInstanceRelationModel> relationModels, DnRelation dnRelation) {
        if (CollectionUtils.isEmpty(relationModels)) {
            return;
        }
        for (BusinessInstanceRelationModel relation : relationModels) {
            if (relation.getRelationType() == 0) {
                if (Objects.nonNull(insToDnMap.get(relation.getTargetInstanceId()))) {
                    dnRelation.setParentDn(insToDnMap.get(relation.getTargetInstanceId()).getDn());
                }
            } else if (relation.getRelationType() == 1) {
                if (Objects.nonNull(insToDnMap.get(relation.getTargetInstanceId()))) {
                    dnRelation.setDeployDn(insToDnMap.get(relation.getTargetInstanceId()).getDn());
                }
            } else if (relation.getRelationType() == 3) {
                if (BusinessTopoConstant.CLUSTER_INSTANCE_ID == ins.getModelType()) {
                    if (Objects.nonNull(insToDnMap.get(relation.getTargetInstanceId()))) {
                        dnRelation.setSiteId(insToDnMap.get(relation.getTargetInstanceId()).getDn());
                        dnRelation.setParentDn(insToDnMap.get(relation.getTargetInstanceId()).getDn());
                    }
                } else {
                    if (Objects.nonNull(insToDnMap.get(relation.getTargetInstanceId()))) {
                        dnRelation.setSiteId(insToDnMap.get(relation.getTargetInstanceId()).getDn());
                    }
                }
            }
        }
    }

    private void setRelationLvAndType(BusinessInstanceModelDB ins, DnRelation dnRelation) {
        dnRelation.setInstanceId(ins.getInstanceId());
        switch (ins.getModelType()) {
            case BusinessTopoConstant.SITE_TYPE_ID:
                dnRelation.setLayer(1);
                dnRelation.setMoName(SiteNameUtils.getSiteName(ins, true));
                dnRelation.setMoType(ins.getStaticExtentAttr("siteMoType").getStaticAttrValue());
                break;
            case BusinessTopoConstant.CLUSTER_INSTANCE_ID:
                dnRelation.setLayer(2);
                dnRelation.setMoName(ins.getExtentAttr("appName").getAttrValue());
                dnRelation.setApplicationType(ins.getStaticExtentAttr(BusinessTopoConstant.APPLICATION_TYPE).getStaticAttrValue());
                dnRelation.setMoType(ins.getStaticExtentAttr("moTypeMapping").getStaticAttrValue());
                break;
            case BusinessTopoConstant.POD_TYPE_ID:
                dnRelation.setLayer(3);
                dnRelation.setMoName(ins.getExtentAttr("podName").getAttrValue());
                dnRelation.setMoType(ins.getStaticExtentAttr("moTypeMapping").getStaticAttrValue());
                break;
            case BusinessTopoConstant.VM_TYPE_ID:
                dnRelation.setMoName(ins.getExtentAttr("vmName").getAttrValue());
                dnRelation.setMoType(ins.getStaticExtentAttr("moType").getStaticAttrValue());
                break;
            case BusinessTopoConstant.DOCKER_TYPE_ID:
                dnRelation.setLayer(4);
                dnRelation.setMoName("docker");
                dnRelation.setMoType(ins.getStaticExtentAttr("moTypeMapping").getStaticAttrValue());
                break;
            default:
                break;
        }
    }

    @Override
    public List<MoOfGroup> getGroupPartition(SolutionExParam param) throws ServiceException {
        Integer solutionId = getInstanceFromSolutionName(param);
        BusinessInstanceModelDB solutionIns = getSolutionInstance(solutionId);
        String solutionName = solutionIns.getSolutionName();
        List<BusinessCommonModel> applicationModels = businessCommonModelDao.queryCommonModelByType(BusinessTopoConstant.BUSINESS_GROUP_TYPE_ID);
        // 过滤解决方案
        List<BusinessCommonModel> applicationModelsOfSol = applicationModels.stream()
                .filter(model -> solutionName.equals(model.getSolutionName()))
                .collect(Collectors.toList());
        Map<String, List<String>> gropuOfMoTypeMap = getGropuOfMoTypeMap(applicationModelsOfSol);

        List<BusinessCommonModel> moTypeModels = businessCommonModelDao.queryCommonModelByType(BusinessTopoConstant.MOTYPE_CLUSTER_TYPE_ID);
        // 过滤解决方案
        List<BusinessCommonModel> moTypeModelsOfSol = moTypeModels.stream()
                .filter(model -> solutionName.equals(model.getSolutionName()))
                .collect(Collectors.toList());

        Map<String, ApplicationMoType> moTypeEncapsulationMap = getMoTypeEncapsulationMap(moTypeModelsOfSol, solutionName);

        return getMoOfGroups(gropuOfMoTypeMap, moTypeEncapsulationMap);
    }


    private static List<MoOfGroup> getMoOfGroups(Map<String, List<String>> gropuOfMoTypeMap, Map<String, ApplicationMoType> moTypeEncapsulationMap) {
        List<MoOfGroup> moOfGroupsList = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : gropuOfMoTypeMap.entrySet()) {
            List<String> subMoTypeList = entry.getValue();
            String groupName = entry.getKey();
            MoOfGroup moOfGroup = new MoOfGroup();
            moOfGroup.setGroupName(groupName);
            for (String subMoType : subMoTypeList) {
                if (Objects.nonNull(moTypeEncapsulationMap.get(subMoType))) {
                    moOfGroup.getApplicationMoTypeList().add(moTypeEncapsulationMap.get(subMoType));
                }
            }
            moOfGroupsList.add(moOfGroup);
        }
        return moOfGroupsList;
    }

    private static Map<String, List<String>> getGropuOfMoTypeMap(List<BusinessCommonModel> applicationModelsOfSol) {
        // key: 分组modelId value: 下层网元类型Id
        Map<String, List<String>> gropuOfMoTypeMap = new HashMap<>();
        for (BusinessCommonModel applicationModel : applicationModelsOfSol) {
            String relation = applicationModel.getStaticExtentAttr(BusinessTopoConstant.INCLUDE_APP_TYPE_LIST).getStaticAttrValue();
            if (StringUtils.isEmpty(relation)) {
                continue;
            }
            List<IncludeAppTypeInfo> includeAppTypeInfos = JSONArray.parseArray(relation, IncludeAppTypeInfo.class);
            if (CollectionUtils.isEmpty(includeAppTypeInfos)) {
                continue;
            }
            List<String> subMoTypeList = includeAppTypeInfos.stream().map(appTypeInfo -> {
                String modelName = appTypeInfo.getName();
                return getMoTypeModelID(modelName);
            }).collect(Collectors.toList());
            gropuOfMoTypeMap.put(applicationModel.getModelId(), subMoTypeList);
        }
        return gropuOfMoTypeMap;
    }

    private Map<String, ApplicationMoType> getMoTypeEncapsulationMap(List<BusinessCommonModel> moTypeModelsOfSol, String solutionName) {
        Map<String, BusinessCommonModel> modelIdToMoTypeMap = moTypeModelsOfSol.stream()
                .filter(model -> solutionName.equals(model.getSolutionName()))
                .collect(Collectors.toMap(
                        BusinessCommonModel::getModelId,
                        model -> model
                ));

        List<BusinessCommonModel> clusterModels = businessCommonModelDao.queryCommonModelByType(BusinessTopoConstant.CLUSTER_INSTANCE_ID);
        // 过滤解决方案
        List<BusinessCommonModel> clusterModelsOfSol = clusterModels.stream()
                .filter(model -> solutionName.equals(model.getSolutionName()))
                .collect(Collectors.toList());
        Map<String, List<BusinessCommonModel>> moTypeToClusterMap = clusterModelsOfSol.stream().
                filter(model -> Objects.nonNull(model.getParentModelId())).
                collect(Collectors.groupingBy(BusinessCommonModel::getParentModelId));

        List<ApplicationMoType> applicationMoTypeList = new ArrayList<>();
        for (Map.Entry<String, BusinessCommonModel> entry : modelIdToMoTypeMap.entrySet()) {
            String moTypeId = entry.getKey();
            BusinessCommonModel moTypeModel = entry.getValue();
            ApplicationMoType applicationMoType = new ApplicationMoType();
            applicationMoType.setModelId(moTypeId);
            applicationMoType.setModelTypeName(moTypeModel.getModelName());
            List<BusinessCommonModel> clusterModelList = moTypeToClusterMap.get(moTypeId);
            if (CollectionUtils.isNotEmpty(clusterModelList)) {
                applicationMoType.setMoType(clusterModelList.get(0).getStaticExtentAttr("moTypeMapping").getStaticAttrValue());
            }
            applicationMoTypeList.add(applicationMoType);
        }

        return applicationMoTypeList.stream().collect(Collectors.toMap(ApplicationMoType::getModelId, model -> model));
    }

    private static String getMoTypeModelID(String modelName) {
        return BusinessTopoConstant.MOTYPE_CLUSTER_TYPE_ID + "_" + modelName;
    }

    private Integer getInstanceFromSolutionName(SolutionExParam param) throws ServiceException {
        if (Objects.nonNull(param.getSolutionId())) {
            return  param.getSolutionId();
        } else {
            List<BusinessInstanceModelDB> solutionInsList = businessInstanceModelDao.queryAllInstanceOfType(BusinessTopoConstant.SOLUTION_TYPE_ID, param.getSolutionName());
            if (CollectionUtils.isNotEmpty(solutionInsList)) {
                return solutionInsList.get(0).getInstanceId();
            } else {
                throw new ServiceException("could not find sol ins.");
            }
        }
    }

    @Override
    public List<AlarmDetail> queryHistoryAlarm(HisAlarmQueryParam param) {
        String solutionName = param.getSolutionName();
        SolutionDnStorage solutionDnStorage = businessTopoAlarmStorageService.getDnListBySol(solutionName);
        if (Objects.isNull(solutionDnStorage)) {
            return Collections.emptyList();
        }
        List<String> dnOfSolList = new ArrayList<>(solutionDnStorage.getDnToInsMap().keySet());
        AlarmConditionFilter globalModelAlarmFilter = businessTopoAlarmStorageService.getGlobalModelAlarmFilter(solutionName);
        if (Objects.isNull(globalModelAlarmFilter)) {
            return Collections.emptyList();
        }

        Set<Integer> alarmLvList = globalModelAlarmFilter.getAlarmSeverities();
        AlarmQueryParam alarmQueryParam = AlarmRequestParamUtil.getAlarmQueryParamBySeverityAndDn
                (dnOfSolList, new ArrayList<>(alarmLvList), param.getStartTime(), param.getEndTime());
        return alarmRequestService.getAlarmData(alarmQueryParam, "current");
    }

    @Override
    public List<Solution> getPermissionsSolList(UserParam param) {
        String userId = param.getUserId();
        SolutionConvert solutionConvert = new SolutionConvert();
        List<BusinessInstanceModelDB> allSolutionList = businessInstanceModelDao.queryAllInstanceOfTypeHistory(
                BusinessTopoConstant.SOLUTION_TYPE_ID, 0L);
        List<BusinessInstanceModelDB> authSolutionList = obtainAuthSolutionIns(userId, 0L, allSolutionList);
        return authSolutionList.stream().map(solutionConvert::convert).collect(Collectors.toList());
    }

    private List<BusinessInstanceModelDB> obtainAuthSolutionIns(String userId, Long timestamp, List<BusinessInstanceModelDB> allSolutionList) {
        List<BusinessInstanceModelDB> authSolutionList = new ArrayList<>();
        if (StringUtils.isEmpty(userId)) {
            return Collections.emptyList();
        }

        if (!"1".equals(userId)) {
            Set<String> authDns = AuthUtils.getAuthDnsByUserId(userId);
            for (BusinessInstanceModelDB solution : allSolutionList) {
                // 查询所有下层站点实例
                List<String> subSiteInsList = relationDao.queryInsIdOfOneLvFromUp(solution.getInstanceId(),
                                BusinessTopoConstant.RELATION_TYPE_TOP_LEVEL, BusinessTopoConstant.SITE_TYPE_ID, timestamp)
                        .stream()
                        .map(BusinessInstanceModelDB::getDn)
                        .collect(Collectors.toList());
                // 存在交集则添加
                if (CollectionUtils.isNotEmpty(authDns)) {
                    if (!Collections.disjoint(authDns, subSiteInsList)) {
                        authSolutionList.add(solution);
                    }
                }
            }
        } else {
            authSolutionList = allSolutionList;
        }
        return authSolutionList;
    }

}
