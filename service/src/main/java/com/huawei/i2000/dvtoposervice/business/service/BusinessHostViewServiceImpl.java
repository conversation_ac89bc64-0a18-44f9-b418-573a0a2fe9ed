/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.convert.ClusterConvert;
import com.huawei.i2000.dvtoposervice.convert.PodConvert;
import com.huawei.i2000.dvtoposervice.convert.VmConvert;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceRelationDao;
import com.huawei.i2000.dvtoposervice.model.BusinessClusterData;
import com.huawei.i2000.dvtoposervice.model.HostViewGrid;
import com.huawei.i2000.dvtoposervice.model.PodData;
import com.huawei.i2000.dvtoposervice.model.VmData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 主机视图
 *
 * <AUTHOR>
 * @since 2025/7/24
 */
@Service
public class BusinessHostViewServiceImpl implements BusinessHostViewService {

    @Autowired
    private BusinessInstanceModelDao businessInstanceModelDao;

    @Autowired
    private BusinessInstanceRelationDao businessInstanceRelationDao;

    @Override
    public HostViewGrid queryHostViewGrid(Integer vmId, Long timeStamp) {
        long currentTime = timeStamp == null ? System.currentTimeMillis() : timeStamp;
        BusinessInstanceModelDB vmIns = businessInstanceModelDao.queryInstanceByInstanceIdAndTimeLine(vmId, currentTime);
        List<Integer> insListOfContainer = businessInstanceRelationDao.queryAntiRelationByTime(vmIns.getInstanceId(), 1, currentTime);

        List<BusinessInstanceModelDB> deploydInsList = businessInstanceModelDao.queryInstanceListByIdList(insListOfContainer, currentTime);
        // 查询出来的部署网元，可能是Pod，也可能是数据库实例，针对不同类型的网元，采用不同的转换类
        List<PodData> podDataList = new ArrayList<>();
        List<VmData> vmDataList = new ArrayList<>();
        VmConvert vmConvert = new VmConvert();
        PodConvert podConvert = new PodConvert();
        ClusterConvert clusterConvert = new ClusterConvert();

        VmData vmData = vmConvert.convert(vmIns);
        vmDataList.add(vmData);
        List<BusinessClusterData> clusterDataList = new ArrayList<>();
        for (BusinessInstanceModelDB deployIns : deploydInsList) {
            switch (deployIns.getModelType()) {
                case BusinessTopoConstant.POD_TYPE_ID:
                    podDataList.add(podConvert.convert(deployIns));
                    break;
                case BusinessTopoConstant.CLUSTER_INSTANCE_ID:
                    clusterDataList.add(clusterConvert.convert(deployIns));
                    break;
                default:
                    break;
            }
        }
        HostViewGrid hostViewGrid = new HostViewGrid();
        hostViewGrid.setVmDataList(vmDataList);
        hostViewGrid.setPodDataList(podDataList);
        hostViewGrid.setClusterList(clusterDataList);
        return hostViewGrid;
    }
}
