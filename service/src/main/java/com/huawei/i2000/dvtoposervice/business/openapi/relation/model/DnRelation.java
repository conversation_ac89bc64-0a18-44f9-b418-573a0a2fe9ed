/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.openapi.relation.model;

import lombok.Data;

import java.util.List;

/**
 * Dn 关联关系对象
 *
 * <AUTHOR>
 * @since 2025/6/23
 */
@Data
public class DnRelation {

    private String dn;

    private Integer instanceId;

    private String moName;

    private String siteId;

    private String parentDn;

    private String deployDn;

    private String moType;

    private Integer layer;

    private String applicationType;

    private List<String> childrenList;
}
