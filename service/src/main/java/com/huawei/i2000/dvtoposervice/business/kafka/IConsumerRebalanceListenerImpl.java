/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.kafka;

import com.huawei.cloudsop.mq.api.common.TopicPartition;
import com.huawei.cloudsop.mq.api.consumer.IConsumer;
import com.huawei.cloudsop.mq.api.consumer.IConsumerRebalanceListener;
import com.huawei.cloudsop.mq.api.consumer.OffsetAndMetadata;

import java.util.Collection;
import java.util.Map;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/1/18
 */
public class IConsumerRebalanceListenerImpl implements IConsumerRebalanceListener {
    private final IConsumer<String, String> consumer;
    private final Map<TopicPartition, OffsetAndMetadata> currentOffsets;

    public IConsumerRebalanceListenerImpl(IConsumer<String, String> consumer, Map<TopicPartition, OffsetAndMetadata> currentOffsets) {
        this.consumer = consumer;
        this.currentOffsets = currentOffsets;
    }

    @Override
    public void onPartitionsRevoked(Collection<TopicPartition> collection) {
        /*
         *  在订阅时注册rebalance监听器，onPartitionsRevoked是在每次发生rebalance时的回调事件
         *  在这个回调事件里面提交当前已经消费过的topic的位置信息，以免重复消费。
         */
        consumer.commitSync(currentOffsets);
    }

    @Override
    public void onPartitionsAssigned(Collection<TopicPartition> collection) {
    }
}
