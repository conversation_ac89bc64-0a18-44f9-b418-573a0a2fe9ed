/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.convert;

import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.model.BusinessClusterData;
import org.apache.commons.lang3.StringUtils;

/**
 * 应用集群转换类
 *
 * <AUTHOR>
 * @since 2025/8/1
 */
public class ClusterConvert {

    public BusinessClusterData convert(BusinessInstanceModelDB instanceModelDB) {
        BusinessClusterData businessClusterData = new BusinessClusterData();
        convertStatic(businessClusterData, instanceModelDB);
        convertDynamic(businessClusterData, instanceModelDB);
        return businessClusterData;
    }

    public void convertStatic(BusinessClusterData businessClusterData, BusinessInstanceModelDB instance) {

    }

    public void convertDynamic(BusinessClusterData businessClusterData, BusinessInstanceModelDB instance) {
        businessClusterData.setDnId(String.valueOf(instance.getInstanceId()));
        businessClusterData.setIsGray(0);
        businessClusterData.setVersion(
                instance.getExtentAttr(BusinessTopoConstant.NODE_VERSION).getAttrValue());
        businessClusterData.setBusinessName(instance.getExtentAttr("appName").getAttrValue());
        String alarmCsn = instance.getExtentAttr(BusinessTopoConstant.ATTR_KEY_ALARM_CSN).getAttrValue();
        businessClusterData.setCsnState(StringUtils.isEmpty(alarmCsn) ? 0 : 1);
        businessClusterData.setAvailableStatus(
                instance.getExtentAttr(BusinessTopoConstant.NODE_SERVICE_STATUS).getAttrValue());
    }

}
