/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent.chain;

import static org.junit.Assert.assertEquals;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.i2000.dvaiagentservice.BaseTest;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.ChainReq;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

/**
 * ChatHandlerTest
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public class ChatHandlerTest extends BaseTest {
    @InjectMocks
    private ChatHandler chatHandler;

    private HttpContext httpContext = new MockHttpContext();

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        httpContext = new MockHttpContext();
        httpContext.setResponseStatus(200);
    }

    @Test
    public void testProcessWhenRestRequestErrorCodeThenChatId() throws Exception {
        ChainReq chainReq = new ChainReq();
        chainReq.setConversationId("989de061-293d-4a07-afd8-f12341a73188");
        chainReq.setQuestion("查询错误码【118013107】");
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("recipeName", "knowledge_search");
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("query", "查询错误码【118013107】");
        options.put("params", params);
        chainReq.setOptions(options);
        mockServer.loadScenarioFiles("rest_chat.json");
        ChainReq result = chatHandler.process(chainReq);

        assertEquals("989de061-293d-4a07-afd8-f12341a73188", result.getConversationId());
        assertEquals(200, httpContext.getResponseStatus());
    }

    @Test
    public void testProcessWhenRestRequestStatusThenOk() throws Exception {
        ChainReq chainReq = new ChainReq();
        chainReq.setConversationId("989de061-293d-4a07-afd8-f12341a73188");
        chainReq.setQuestion("查询网元【OSS】的连接状态");
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("recipeName", "managedobject_connect_status");
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("ne_name", "OSS");
        options.put("params", params);
        chainReq.setOptions(options);
        mockServer.loadScenarioFiles("rest_chat.json");
        ChainReq result = chatHandler.process(chainReq);

        assertEquals(200, httpContext.getResponseStatus());
    }
}