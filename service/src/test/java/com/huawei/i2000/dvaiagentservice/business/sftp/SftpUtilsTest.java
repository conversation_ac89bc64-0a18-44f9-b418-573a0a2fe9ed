/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.sftp;

import com.huawei.bsp.exception.OSSException;
import org.apache.sshd.sftp.client.SftpClient;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.InterruptedIOException;

/**
 * SftpUtilsTest
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
public class SftpUtilsTest {
    @Test
    public void listFiles_throwEmptyOSSException() throws Exception {
        SftpClient client = Mockito.mock(SftpClient.class);
        Mockito.when(client.readDir(Mockito.anyString())).thenThrow(new InterruptedIOException());
        Mockito.when(client.isOpen()).thenReturn(true);
        ReflectionTestUtils.setField(SftpUtils.getInstance(), "sftpClient", client);
        ReflectionTestUtils.setField(SftpUtils.getInstance(), "isLogin", true);
        try {
            SftpUtils.getInstance().listFiles("dir");
        } catch (Exception e) {
            Assert.assertTrue(e instanceof OSSException);
            Assert.assertNull(e.getMessage());
        }
    }
}