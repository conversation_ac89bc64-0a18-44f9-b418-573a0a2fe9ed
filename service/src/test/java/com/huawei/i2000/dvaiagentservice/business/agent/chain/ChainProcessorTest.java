/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent.chain;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.i2000.dvaiagentservice.BaseTest;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.RecipeReq;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.answer.DiagnosisReport;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

/**
 * ConversationHandlerTest
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public class ChainProcessorTest extends BaseTest {
    @InjectMocks
    private ConversationHandler conversationHandler;

    private HttpContext httpContext = new MockHttpContext();

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        httpContext = new MockHttpContext();
        httpContext.setResponseStatus(200);
    }

    @Test
    public void testProcessWhenRestRequestSuccessfulThenConversationId() throws Exception {
        RecipeReq recipeReq = new RecipeReq();
        recipeReq.setQuestion("What is the weather today?");
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("recipeName", "knowledge_search");
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("query", "查询错误码【118013107】");
        options.put("params", params);
        recipeReq.setOptions(options);
        mockServer.loadScenarioFiles("rest_conversation.json");
        mockServer.loadScenarioFiles("rest_chat.json");
        mockServer.loadScenarioFiles("rest_chat_query.json");
        mockServer.loadScenarioFiles("rest_chat_detail.json");
        DiagnosisReport result = ChainProcessor.execute(recipeReq);

        assertNotNull(result);
        assertEquals(200, httpContext.getResponseStatus());
    }
}