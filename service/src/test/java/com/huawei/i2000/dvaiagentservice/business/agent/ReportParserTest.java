/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent;

import com.huawei.i2000.dvaiagentservice.business.agent.dto.answer.LLMReport;
import com.huawei.i2000.dvaiagentservice.util.ContextUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Locale;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

/**
 * ReportParserTest
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ContextUtils.class})
public class ReportParserTest {

    @Test
    public void testBuildLLMReportWhenLocaleIsChineseThenFieldsSetCorrectly() throws Exception {
        String report = "## 原因\n" +
                "\n" +
                "根据提供的告警信息，问题主要集中在两个方面：一是命中错误，二是命中失败。这表明在尝试处理或响应某些操作时遇到了障碍，可能是由于配置错误、资源问题或其他技术故障。\n" +
                "\n" +
                "## 处理措施\n" +
                "\n" +
                "1. **检查告警是否恢复**：首先，需要确认告警状态是否已经恢复正常。这可以通过检查告警列表中的状态来实现。如果告警仍未清除，则需要进一步排查。\n" +
                "2. **登录A模块页面确认接口状态**：接下来，应登录A模块页面，检查接口状态是否正常。这一步骤有助于确定问题是否出在接口层面，例如接口是否可用或是否存在配置问题。\n" +
                "\n" +
                "## 检查项\n" +
                "\n" +
                "- **告警是否恢复**：确认告警状态，检查告警列表中的告警是否已经清除。\n" +
                "- **登录A模块页面确认接口状态**：检查A模块页面的接口状态，确保接口正常工作。";
        Locale locale = new Locale("zh");
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        PowerMockito.when(contextUtils.getLocale()).thenReturn(locale);

        LLMReport llmReport = ReportParser.parse(report);

        assertEquals("## 原因\n" +
                "\n" +
                "根据提供的告警信息，问题主要集中在两个方面：一是命中错误，二是命中失败。这表明在尝试处理或响应某些操作时遇到了障碍，可能是由于配置错误、资源问题或其他技术故障。\n" +
                "\n", llmReport.getReason());
        assertEquals("## 处理措施\n" +
                "\n" +
                "1. **检查告警是否恢复**：首先，需要确认告警状态是否已经恢复正常。这可以通过检查告警列表中的状态来实现。如果告警仍未清除，则需要进一步排查。\n" +
                "2. **登录A模块页面确认接口状态**：接下来，应登录A模块页面，检查接口状态是否正常。这一步骤有助于确定问题是否出在接口层面，例如接口是否可用或是否存在配置问题。\n" +
                "\n", llmReport.getMeasures());
    }

    @Test
    public void testBuildLLMReportWhenLocaleIsEnglishThenFieldsSetCorrectly() throws Exception {
        String report = "## Reason\n" +
                "\n" +
                "Based on the provided alarm information, the issue primarily revolves around two aspects: one is a hit error, and the other is a hit failure. This indicates that there were obstacles encountered when attempting to process or respond to certain operations, possibly due to configuration errors, resource issues, or other technical malfunctions.\n" +
                "\n" +
                "## Measures\n" +
                "\n" +
                "1. **Check if the Alarm has been Resolved**: First, it is necessary to confirm whether the alarm status has returned to normal. This can be achieved by checking the status in the alarm list. If the alarm has not been cleared, further investigation is required.\n" +
                "2. **Log in to Module A Page to Confirm Interface Status**: Next, log in to the Module A page to check if the interface status is normal. This step helps to determine if the problem lies at the interface level, such as whether the interface is available or if there are configuration issues.\n" +
                "\n" +
                "## Check Items\n" +
                "- **Has the Alarm been Resolved**: Confirm the alarm status and check if the alarms in the alarm list have been cleared.\n" +
                "- **Log in to Module A Page to Confirm Interface Status**: Check the interface status on the Module A page to ensure the interface is functioning properly.英文样例：\n";
        Locale locale = new Locale("en");
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        PowerMockito.when(contextUtils.getLocale()).thenReturn(locale);

        LLMReport llmReport = ReportParser.parse(report);

        assertEquals("## Reason\n" +
                "\n" +
                "Based on the provided alarm information, the issue primarily revolves around two aspects: one is a hit error, and the other is a hit failure. This indicates that there were obstacles encountered when attempting to process or respond to certain operations, possibly due to configuration errors, resource issues, or other technical malfunctions.\n" +
                "\n", llmReport.getReason());
        assertEquals("## Measures\n" +
                "\n" +
                "1. **Check if the Alarm has been Resolved**: First, it is necessary to confirm whether the alarm status has returned to normal. This can be achieved by checking the status in the alarm list. If the alarm has not been cleared, further investigation is required.\n" +
                "2. **Log in to Module A Page to Confirm Interface Status**: Next, log in to the Module A page to check if the interface status is normal. This step helps to determine if the problem lies at the interface level, such as whether the interface is available or if there are configuration issues.\n" +
                "\n", llmReport.getMeasures());
    }

    @Test
    public void testBuildLLMReportWhenReportIsEmptyThenFieldsAreEmpty() throws Exception {
        String report = "";
        Locale locale = new Locale("zh");
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        PowerMockito.when(contextUtils.getLocale()).thenReturn(locale);

        LLMReport llmReport = ReportParser.parse(report);
        assertEquals("", llmReport.getMeasures());
        assertEquals("", llmReport.getReason());
    }

    @Test
    public void testBuildLLMReportWhenReportIsNullThenFieldsAreEmpty() throws Exception {
        String report = null;
        Locale locale = new Locale("zh");
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        PowerMockito.when(contextUtils.getLocale()).thenReturn(locale);

        LLMReport llmReport = ReportParser.parse(report);
        assertNull(llmReport.getMeasures());
        assertNull(llmReport.getReason());
    }

    @Test
    public void testReportWhenReportDoesNotContainMarkersThenFieldsAreEmpty() throws Exception {
        String report = "Some content without markers";
        Locale locale = new Locale("en");
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        PowerMockito.when(contextUtils.getLocale()).thenReturn(locale);

        LLMReport llmReport = ReportParser.parse(report);

        assertEquals("", llmReport.getMeasures());
        assertEquals("", llmReport.getReason());
    }

    @Test
    public void testReportWhenReportOnlyReasonMarkerThenSetCorrectly() throws Exception {
        String report = "## 原因: 原因内容";
        Locale locale = new Locale("zh");
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        PowerMockito.when(contextUtils.getLocale()).thenReturn(locale);

        LLMReport llmReport = ReportParser.parse(report);

        assertEquals("## 原因: 原因内容", llmReport.getReason());
        assertEquals("", llmReport.getMeasures());
    }

    @Test
    public void testReportWhenReportContainsOnlyMeasuresThenSetCorrectly() throws Exception {
        String report = "## 处理措施\n" +
                "\n" +
                "1. **检查告警是否恢复**：首先，需要确认告警状态是否已经恢复正常。这可以通过检查告警列表中的状态来实现。如果告警仍未清除，则需要进一步排查。\n" +
                "2. **登录A模块页面确认接口状态**：接下来，应登录A模块页面，检查接口状态是否正常。这一步骤有助于确定问题是否出在接口层面，例如接口是否可用或是否存在配置问题。\n";
        Locale locale = new Locale("zh");
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        PowerMockito.when(contextUtils.getLocale()).thenReturn(locale);

        LLMReport llmReport = ReportParser.parse(report);

        assertEquals("", llmReport.getReason());
        assertEquals("## 处理措施\n" +
                "\n" +
                "1. **检查告警是否恢复**：首先，需要确认告警状态是否已经恢复正常。这可以通过检查告警列表中的状态来实现。如果告警仍未清除，则需要进一步排查。\n" +
                "2. **登录A模块页面确认接口状态**：接下来，应登录A模块页面，检查接口状态是否正常。这一步骤有助于确定问题是否出在接口层面，例如接口是否可用或是否存在配置问题。\n", llmReport.getMeasures());
    }

    @Test
    public void testReportWhenReportContainsOnlyMeasuresEnThenSetCorrectly() throws Exception {
        String report = "## Measures: Measures content";
        Locale locale = new Locale("en");
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        PowerMockito.when(contextUtils.getLocale()).thenReturn(locale);

        LLMReport llmReport = ReportParser.parse(report);

        assertEquals("", llmReport.getReason());
        assertEquals("## Measures: Measures content", llmReport.getMeasures());
    }

    @Test
    public void testReportWhenContainsMeasuresReasonThenSetCorrectly() throws Exception {
        String report = "## Reason:Reason content ## Measures: Measures content";
        Locale locale = new Locale("en");
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        PowerMockito.when(contextUtils.getLocale()).thenReturn(locale);

        LLMReport llmReport = ReportParser.parse(report);

        assertEquals("## Reason:Reason content ", llmReport.getReason());
        assertEquals("## Measures: Measures content", llmReport.getMeasures());
    }

    @Test
    public void testReportWhenContainsMeasuresReasonCheckItemsThenSetCorrectly() throws Exception {
        String report = "## Reason:Reason content ## Measures: Measures content ## Check Items- **Has the Alarm";
        Locale locale = new Locale("en");
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        PowerMockito.when(contextUtils.getLocale()).thenReturn(locale);

        LLMReport llmReport = ReportParser.parse(report);

        assertEquals("## Reason:Reason content ", llmReport.getReason());
        assertEquals("## Measures: Measures content ", llmReport.getMeasures());
    }
}