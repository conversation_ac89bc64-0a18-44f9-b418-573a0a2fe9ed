/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent.chain;

import static org.junit.Assert.assertEquals;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.i2000.dvaiagentservice.BaseTest;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.ChainReq;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

/**
 * ChatDetailHandlerTest
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public class ChatDetailHandlerTest extends BaseTest {
    @InjectMocks
    private ChatDetailHandler chatDetailHandler;

    private HttpContext httpContext = new MockHttpContext();

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        httpContext = new MockHttpContext();
        httpContext.setResponseStatus(200);
    }

    @Test
    public void testProcessWhenRestRequestSuccessfulThenAnswers() throws Exception {
        ChainReq chainReq = new ChainReq();
        chainReq.setConversationId("989de061-293d-4a07-afd8-f12341a73188");
        chainReq.setChatId("d6e7d18d-127a-472d-be98-051ce8deef72");
        mockServer.loadScenarioFiles("rest_chat_detail.json");
        ChainReq result = chatDetailHandler.process(chainReq);

        assertEquals(14, result.getReport().getAnswers().size());
        assertEquals(200, httpContext.getResponseStatus());
    }
}