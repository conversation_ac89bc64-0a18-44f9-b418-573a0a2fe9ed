/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.agent.chain;

import static org.junit.Assert.assertEquals;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.i2000.dvaiagentservice.BaseTest;
import com.huawei.i2000.dvaiagentservice.business.agent.dto.ChainReq;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

/**
 * ConversationHandlerTest
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public class ConversationHandlerTest extends BaseTest {
    @InjectMocks
    private ConversationHandler conversationHandler;

    private HttpContext httpContext = new MockHttpContext();

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        httpContext = new MockHttpContext();
        httpContext.setResponseStatus(200);
    }

    @Test
    public void testProcessWhenRestRequestSuccessfulThenConversationId() throws Exception {
        ChainReq chainReq = new ChainReq();
        chainReq.setQuestion("What is the weather today?");
        mockServer.loadScenarioFiles("rest_conversation.json");
        ChainReq result = conversationHandler.process(chainReq);

        assertEquals("989de061-293d-4a07-afd8-f12341a73188", result.getConversationId());
        assertEquals(200, httpContext.getResponseStatus());
    }

    @Test
    public void testProcessWhenRestRequestThrowsExceptionThenConversationIdNull() throws Exception {
        ChainReq chainReq = new ChainReq();
        chainReq.setQuestion("NULL");
        mockServer.loadScenarioFiles("rest_conversation.json");
        ChainReq result = conversationHandler.process(chainReq);

        assertEquals("NULL", result.getConversationId());
        assertEquals(200, httpContext.getResponseStatus());
    }
}