/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.business.sftp;

import com.huawei.i2000.dvaiagentservice.BaseTest;
import mockit.Mock;
import mockit.MockUp;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * SftpAccountCache
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
public class SftpAccountCacheTest extends BaseTest {
    @BeforeClass
    public static void setUp() {
        new MockUp<SftpRestProxy>(SftpRestProxy.class) {
            @Mock
            public Map<String, String> getSftpPwd() {
                return new HashMap<>();
            }
        };
        new MockUp<ScheduledThreadPoolExecutor>(ScheduledThreadPoolExecutor.class) {
            @Mock
            public ScheduledFuture<?> scheduleAtFixedRate(Runnable command, long initialDelay, long period,
                TimeUnit unit) {
                return null;
            }
        };
    }

    @Test
    public void testGetSftpPwd() {
        String sftpPwd = SftpAccountCache.getSftpPwd();

        Assert.assertEquals("", sftpPwd);
    }

    @Test
    public void testGetSftpIpv4Empty() {
        String sftpIpv4 = SftpAccountCache.getSftpIpv4();

        Assert.assertEquals("", sftpIpv4);
    }

    @Test
    public void testGetSftpIpv6Empty() {
        String sftpIpv6 = SftpAccountCache.getSftpIpv6();

        Assert.assertEquals("", sftpIpv6);
    }
}
