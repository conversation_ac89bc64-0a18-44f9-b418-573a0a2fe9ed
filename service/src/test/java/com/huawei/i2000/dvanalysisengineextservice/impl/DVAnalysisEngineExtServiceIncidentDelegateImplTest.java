/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.impl;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvanalysisengineextservice.BaseTest;
import com.huawei.i2000.dvanalysisengineextservice.delegate.DVAnalysisEngineExtServiceIncidentDelegate;
import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * DVAnalysisEngineExtServiceIncidentDelegateImplTest
 *
 * <AUTHOR>
 * @since 2024/10/26
 */
public class DVAnalysisEngineExtServiceIncidentDelegateImplTest extends BaseTest {

    @Autowired
    private DVAnalysisEngineExtServiceIncidentDelegate delegate;

    @Test
    public void addAlarms() throws ServiceException {
        mockServer.loadScenarioFiles("rest_mappings-export.json");
        ResponseResult result = delegate.aggregateAlarms(new MockHttpContext());
        Assert.assertEquals(result.getResultMessage(), "successfully export data, please download it");
    }
}