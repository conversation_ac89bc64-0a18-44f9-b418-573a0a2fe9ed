/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.impl;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvanalysisengineextservice.BaseTest;
import com.huawei.i2000.dvanalysisengineextservice.delegate.DVAnalysisEngineExtServiceHealthDelegate;
import com.huawei.i2000.dvanalysisengineextservice.mock.MockUtil;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 健康检查接口测试
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public class DVAnalysisEngineExtServiceHealthDelegateImplTest extends BaseTest {
    @Autowired
    private DVAnalysisEngineExtServiceHealthDelegate delegate;

    @Test
    public void healthCheck() throws ServiceException {
        delegate.healthCheck(null);
        Assert.assertTrue(MockUtil.mockSuccess());
    }
}