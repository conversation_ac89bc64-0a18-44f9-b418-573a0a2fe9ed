/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.mock;

import com.huawei.i2000.eam.client.mim.MITManagerClient;

import mockit.Mock;
import mockit.MockUp;

import org.mockito.Mockito;

/**
 * MITManagerClient mock
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
public class MITManagerClientMockUp extends MockUp<MITManagerClient> {
    @Mock
    public static MITManagerClient newInstance() {
        return Mockito.mock(MITManagerClient.class);
    }
}
