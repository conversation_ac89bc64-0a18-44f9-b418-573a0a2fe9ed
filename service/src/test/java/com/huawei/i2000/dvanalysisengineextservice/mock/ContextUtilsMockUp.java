/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.mock;

import com.huawei.bsp.deploy.util.DefaultEnvUtil;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.i2000.dvanalysisengineextservice.util.ContextUtils;

import mockit.Mock;
import mockit.MockUp;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;

/**
 * ContextUtils mock
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public class ContextUtilsMockUp extends MockUp<ContextUtils> {

    /**
     * 日志类
     */
    private static final OssLog LOGGER = OssLogFactory.getLogger(ContextUtils.class);

    /**
     * 线程本地变量持有器，此类可以在子线程中获取到父线程的本地变量
     */
    private static final InheritableThreadLocal<ContextUtils> CONTEXT = new InheritableThreadLocal<>();

    /**
     * request中token的键
     */
    private static final String HEADER_TOKEN = "x-user-token";

    /**
     * website传过来的浏览器地址
     */
    public static final String WEBSITE_CLIENT_TERMINAL = "website-client-terminal";

    /**
     * 拥有的网元权限且指标表中存在的网元权限（大容量场景下，查询时用所有的网元权限会导致sql过长）
     */
    private Set<String> authDnsInIndicator;

    static {
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        contextUtils.setUserId("111");
        CONTEXT.set(contextUtils);
    }

    /**
     * 用户ID
     */
    private String userId;

    /**
     * token值
     */
    private String token;

    /**
     * 用户名称
     */
    private String username;

    /**
     * 当前国际化
     */
    private Locale locale;

    /**
     * headerMap,调用流程编排接口需要request
     */
    private Map<String, String> headerMap;

    public Map<String, String> getHeaderMap() {
        return headerMap;
    }

    @Mock
    public void setHeaderMap(HttpServletRequest request) {
        this.headerMap = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement();
            this.headerMap.put(name, request.getHeader(name));
        }
    }

    @Mock
    public void addHeaderMap(String key, String value) {
        if (Objects.isNull(this.headerMap)) {
            this.headerMap = new HashMap<>();
        }
        this.headerMap.put(key, value);
    }

    @Mock
    public Locale getLocale() {
        return this.locale != null ? locale : DefaultEnvUtil.getOssLocale();
    }

    @Mock
    public void setLocale(Locale locale) {
        this.locale = locale;
    }

    /**
     * 初始化权限信息
     *
     * @param req 请求
     */
    @Mock
    public static void init(HttpServletRequest req) {

    }


    /**
     * 初始化权限信息
     *
     * @param isAdmin 是否是admin用户
     * @param userId  用户id
     */
    @Mock
    public static void init(boolean isAdmin, String userId) {

    }


    /**
     * 获取线程环境变量
     *
     * @return ContextUtils
     */
    @Mock
    public static ContextUtils getContext() {
        return CONTEXT.get();
    }

    /**
     * 设置线程
     *
     * @param context context
     */
    @Mock
    public static void setContext(ContextUtils context) {
        CONTEXT.set(context);
    }

    /**
     * 删除变量
     */
    @Mock
    public static void removeContext() {
        CONTEXT.remove();
    }

    @Mock
    public static String getHeaderToken() {
        return HEADER_TOKEN;
    }

    @Mock
    public Boolean getAdmin() {
        return true;
    }

    @Mock
    public void setAdmin(Boolean admin) {
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
