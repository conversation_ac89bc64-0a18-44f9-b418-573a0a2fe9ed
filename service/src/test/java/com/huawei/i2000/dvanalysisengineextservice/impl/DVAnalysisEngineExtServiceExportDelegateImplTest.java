/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.impl;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulOptions;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.dvanalysisengineextservice.BaseTest;
import com.huawei.i2000.dvanalysisengineextservice.delegate.DVAnalysisEngineExtServiceExportDelegate;
import com.huawei.i2000.dvanalysisengineextservice.delegate.DVAnalysisEngineExtServiceQueryDelegate;
import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineextservice.util.resource.EamUtil;
import com.huawei.i2000.dvanalysisengineextservice.util.rest.RestUtil;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.ManagedObject;
import com.huawei.oms.eam.mo.UniAgent;

import mockit.Mock;
import mockit.MockUp;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;

/**
 * 文件导出单元测试
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public class DVAnalysisEngineExtServiceExportDelegateImplTest extends BaseTest {
    @Autowired
    private DVAnalysisEngineExtServiceExportDelegate delegate;

    @Autowired
    private DVAnalysisEngineExtServiceQueryDelegate queryDelegate;

    @Test
    public void exportPmKpi() throws ServiceException {
        mockEamUtil();
        mockRestUtil();
        ResponseResult result = queryDelegate.queryPmKpi(new MockHttpContext(), "export", "10.10.00", "JVMMemory", null, null, null, null);
        Assert.assertEquals(result.getData(), "/rest/dvanalysisengineextservice/v1/website/export/pm/kpi?neName=10.10.00&taskName=JVMMemory&neType=&measTypeKey=&timeRange=&measObjects=");
    }

    private void mockEamUtil() {
        new MockUp<EamUtil>() {
            @Mock
            public ManagedObject getMoByNeName(String neName) {
                ManagedObject managedObject = new UniAgent();
                managedObject.setType("inf.os.linux");
                managedObject.setDN(new DN("OS=1"));
                return managedObject;
            }
        };
    }

    private void mockRestUtil() {
        new MockUp<RestUtil>() {
            @Mock
            public RestfulResponse sendRestRequest(HttpServletRequest req, String method, String path,
                RestfulParametes parameters, RestfulOptions options) {
                RestfulResponse rsp = new RestfulResponse();
                rsp.setStatus(200);
                rsp.setResponseJson("{\"result\":{\"JVMMemory\":[{\"moType\":\"inf.os.linux\"}]}}");
                return rsp;
            }
        };
    }
}