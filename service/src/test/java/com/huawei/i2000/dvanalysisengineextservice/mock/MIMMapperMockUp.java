/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.mock;

import com.huawei.i2000.mim.json.jackson.MIMMapper;

import mockit.Mock;
import mockit.MockUp;

import org.mockito.Mockito;

/**
 * MIMMapper mock
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
public class MIMMapperMockUp extends MockUp<MIMMapper> {
    @Mock
    public static MIMMapper newInstance() {
        return Mockito.mock(MIMMapper.class);
    }
}
