/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.mock;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

/**
 * AlarmUtil
 *
 * <AUTHOR>
 * @since 2025-02-05
 */
public class AlarmUtil {
    private static final OssLog LOGGER = OssLogFactory.getLogger(AlarmUtil.class);

    // 读取json文件
    public static String readJsonFile(String fileName) throws IOException {
        String jsonString;
        File jsonFile = new File(fileName);
        FileReader fileReader = null;
        Reader inputStreamReader = null;
        StringBuilder stringBuilder = new StringBuilder();
        try {
            fileReader = new FileReader(jsonFile);
            inputStreamReader = new InputStreamReader(Files.newInputStream(jsonFile.toPath()), StandardCharsets.UTF_8);
            int ch;
            while ((ch = inputStreamReader.read()) != -1) {
                stringBuilder.append((char) ch);
            }
            jsonString = stringBuilder.toString();
            return jsonString;
        } catch (IOException e) {
            LOGGER.error("exception is {}", e);
            return "";
        } finally {
            if (fileReader != null){
                fileReader.close();
            }
            if (inputStreamReader != null){
                inputStreamReader.close();
            }
        }
    }
}
