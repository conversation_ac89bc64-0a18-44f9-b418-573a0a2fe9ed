/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice;

import com.huawei.baize.servicesimulator.base.bsp.test.junit.BspIntegrationTests;
import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.log.Logger;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.i2000.dvanalysisengineextservice.mock.MockUtil;

import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.mockito.Mockito;

/**
 * 开发者测试用例基类，主要mock一些公共的mock方法及公共功能
 *
 * <AUTHOR>
 * @since 2023-06-05
 */
public abstract class BaseTest extends BspIntegrationTests {
    private static final Logger LOGGER = OssLogFactory.getLogger(BaseTest.class);

    public static EmbeddedRestClientAndServer mockServer;
    @BeforeClass
    public static void initTest() {
        MockUtil.init();
    }

    @AfterClass
    public static void tearDown() {
        Mockito.clearAllCaches();
    }

    static {
        try {
            mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
            EmbeddedBspRestfulClientModule.install(mockServer.getPort());
        } catch (Exception e) {
            LOGGER.error("start mock failed", e);
        }
    }
}
