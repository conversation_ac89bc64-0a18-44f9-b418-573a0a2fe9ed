/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.validate.solutiontemplatevalidate;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppGroupConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppGroupInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.ConfigurationImportEntity;
import com.huawei.i2000.dvtoposervice.business.service.bean.IncludeAppTypeInfo;
import com.huawei.i2000.dvtoposervice.business.topo.solutiontemplatevalidate.checker.BusinessAppGroupConfigValidator;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessAppGroupConfigValidatorTest extends WebServiceTest {

    @Autowired
    private BusinessAppGroupConfigValidator businessAppGroupConfigValidator;

    private static final AtomicBoolean DATALOAD = new AtomicBoolean(false);

    @Before
    public void PreparedData() throws Exception {
        businessAppGroupConfigValidator = new BusinessAppGroupConfigValidator();
        // 保证这个只运行一次
        if (!DATALOAD.compareAndSet(false, true)) {
            return;
        }
        setSqlScriptEncoding("UTF-8");
    }

    @Test
    public void checkTest() {
        ConfigurationImportEntity configurationImportEntity = new ConfigurationImportEntity();
        BusinessAppGroupConfig businessAppGroupConfig = new BusinessAppGroupConfig();
        configurationImportEntity.setBusinessAppGroupConfig(businessAppGroupConfig);
        businessAppGroupConfig.setModelType(4);
        businessAppGroupConfig.setAutoLayoutsFlag(true);
        List<BusinessAppGroupInfo> businessAppGroupList = new ArrayList<>();
        BusinessAppGroupInfo businessAppGroupInfo = new BusinessAppGroupInfo();
        businessAppGroupInfo.setGroupName("group name");
        businessAppGroupInfo.setModelName("#model name");
        List<IncludeAppTypeInfo> includeAppTypeList = new ArrayList<>();
        IncludeAppTypeInfo includeAppTypeInfo = new IncludeAppTypeInfo();
        includeAppTypeInfo.setName("name");
        includeAppTypeInfo.setType(1);
        includeAppTypeList.add(includeAppTypeInfo);
        businessAppGroupInfo.setIncludeAppTypeList(includeAppTypeList);
        businessAppGroupList.add(businessAppGroupInfo);
        businessAppGroupConfig.setBusinessAppGroupList(businessAppGroupList);
        Exception e = null;
        try {
            businessAppGroupConfigValidator.check(configurationImportEntity);
        } catch (ServiceException exception) {
            e = exception;
        }
        Assert.assertNull(e);
    }

    @Test
    public void checkModelNameTest() {
        Exception e = null;
        try {
            businessAppGroupConfigValidator.checkModelName("model_name 123");
        } catch (ServiceException exception) {
            e = exception;
        }
        Assert.assertNull(e);

        try {
            businessAppGroupConfigValidator.checkModelName("model_name 123 %%^^ ");
        } catch (ServiceException exception) {
            e = exception;
        }
        Assert.assertEquals("exception.id: framwork.remote.SystemError; BusinessAppGroupConfig businessAppGroupList modelName validate error.", e.getMessage());
    }

    @Test
    public void checkGroupNameTest() {
        Exception e = null;
        try {
            businessAppGroupConfigValidator.checkGroupName("#Group Name 123");
        } catch (ServiceException exception) {
            e = exception;
        }
        Assert.assertNull(e);

        try {
            businessAppGroupConfigValidator.checkGroupName("model_name 123 %%^^ ");
        } catch (ServiceException exception) {
            e = exception;
        }
        Assert.assertEquals("exception.id: framwork.remote.SystemError; BusinessAppGroupConfig businessAppGroupList groupName validate error.", e.getMessage());
    }

    @Test
    public void checkIncludeAppTypeListTest() {
        List<IncludeAppTypeInfo> includeAppTypeList = new ArrayList<>();
        IncludeAppTypeInfo includeAppTypeInfo1 = new IncludeAppTypeInfo();
        includeAppTypeInfo1.setName("test");
        includeAppTypeInfo1.setType(1);
        includeAppTypeList.add(includeAppTypeInfo1);
        Exception e = null;
        try {
            businessAppGroupConfigValidator.checkIncludeAppTypeList(includeAppTypeList);
        } catch (ServiceException exception) {
            e = exception;
        }
        Assert.assertNull(e);

        IncludeAppTypeInfo includeAppTypeInfo2 = new IncludeAppTypeInfo();
        includeAppTypeInfo2.setName("test");
        includeAppTypeInfo2.setType(null);
        includeAppTypeList.add(includeAppTypeInfo2);
        try {
            businessAppGroupConfigValidator.checkIncludeAppTypeList(includeAppTypeList);
        } catch (ServiceException exception) {
            e = exception;
        }
        Assert.assertNull(e);

        includeAppTypeInfo2.setName("test %%^^");
        includeAppTypeInfo2.setType(1);
        try {
            businessAppGroupConfigValidator.checkIncludeAppTypeList(includeAppTypeList);
        } catch (ServiceException exception) {
            e = exception;
        }
        Assert.assertEquals("exception.id: framwork.remote.SystemError; BusinessAppGroupConfig businessAppGroupList includeAppTypeList name validate error.", e.getMessage());

    }


}
