/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.impl.MoTypeTopoServiceDelegateImpl;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.model.PodDeployResult;
import com.huawei.i2000.dvtoposervice.model.PodIndicatorDisplayResults;
import com.huawei.i2000.dvtoposervice.model.PodViewCommonParam;
import com.huawei.i2000.dvtoposervice.model.VmDeployResult;
import com.huawei.i2000.dvtoposervice.util.AuthUtils;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 业务拓扑通用测试类
 *
 * <AUTHOR>
 * @since 2024/04/22
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class, MITManagerClient.class, LocalDateTime.class, BusinessTopoCommonServiceImpl.class, AuthUtils.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class MoTypeTopoServiceDelegateImplTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    private static final MapOper<ConfigData> BUSINESS_CONFIG_MAP_OPER = new MapOper<>(
        RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    MoTypeTopoServiceDelegateImpl impl;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (! DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_common.sql", false);
        mockProperties();
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    private void mockProperties() {
        PowerMockito.mockStatic(PropertiesUtil.class);
        Properties properties = new Properties();
        properties.put(BusinessTopoConstant.CLUSTER_TYPE, "6");
        properties.put(BusinessTopoConstant.POD_TYPE, "7");
        PowerMockito.when(PropertiesUtil.loadProperties(Mockito.anyString())).thenReturn(properties);
    }

    @Test
    public void queryPodDeployDataNotAdminTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(false);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        Set<String> dns = new HashSet<>();
        dns.add("28f59c78-64b5-4b58-949c-891cc8ec43bf");
        PowerMockito.mockStatic(AuthUtils.class);
        PowerMockito.when(AuthUtils.getAuthDns()).thenReturn(dns);

        PodViewCommonParam queryPodDeployData = new PodViewCommonParam();
        PodDeployResult result = impl.queryPodDeployData(null, queryPodDeployData);
        Assert.assertEquals(0, result.getVmDataList().size());

        queryPodDeployData.setEndTime(1L);
        PodDeployResult resultTime = impl.queryPodDeployData(null, queryPodDeployData);
        Assert.assertEquals(0, resultTime.getVmDataList().size());
    }

    @Test
    public void queryVmDeployDataNotAdminTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(false);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        Set<String> dns = new HashSet<>();
        dns.add("28f59c78-64b5-4b58-949c-891cc8ec43bf");
        PowerMockito.mockStatic(AuthUtils.class);
        PowerMockito.when(AuthUtils.getAuthDns()).thenReturn(dns);

        PodViewCommonParam queryPodDeployData = new PodViewCommonParam();
        VmDeployResult result = impl.queryVmDeployData(null, queryPodDeployData);
        Assert.assertEquals(0, result.getPodDataList().size());

        queryPodDeployData.setEndTime(1L);
        VmDeployResult resultTime = impl.queryVmDeployData(null, queryPodDeployData);
        Assert.assertEquals(0, resultTime.getPodDataList().size());
    }

    @Test
    public void queryPodIndicatorRefreshFlagTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        PodViewCommonParam queryPodIndicatorParam = new PodViewCommonParam();
        queryPodIndicatorParam.setNewIndicatorListFlag(1);
        queryPodIndicatorParam.setInstanceId(10071);

        PodIndicatorDisplayResults results = impl.queryPodIndicator(null, queryPodIndicatorParam);

        Assert.assertEquals(1, results.getIndicatorIdList().size());
    }

    @Test
    public void queryMmPodIndicator() throws Exception {
        ConfigData configStripe = new ConfigData();
        configStripe.setConfigItemName("normalTopNConfig");
        configStripe.setValue("20");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "normalTopNConfig", configStripe);

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        PodViewCommonParam queryPodIndicatorParam = new PodViewCommonParam();
        queryPodIndicatorParam.setStripeUnit("");
        queryPodIndicatorParam.setInstanceId(10071);

        PodIndicatorDisplayResults results = impl.queryPodIndicator(null, queryPodIndicatorParam);

        Assert.assertEquals(1, results.getIndicatorIdList().size());
    }
}
