/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.entity;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025-03-29
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {})
@PowerMockIgnore( {"javax.management.*", "jdk.internal.reflect.*"})
public class MoTypeConnectEntityTest {

    @Test
    public void test() throws Exception {
        MoTypeConnectEntity moTypeConnectEntity = new MoTypeConnectEntity();
        Assert.assertNotNull(moTypeConnectEntity.hashCode());
    }
}