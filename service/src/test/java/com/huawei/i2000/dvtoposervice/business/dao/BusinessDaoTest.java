/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.dao;

import com.huawei.bsp.mybatis.session.MapperManager;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessDao;
import com.huawei.i2000.dvtoposervice.model.Business;

import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * BusinessDaoTest
 *
 * <AUTHOR>
 * @since 2024/2/29
 */
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessDaoTest extends WebServiceTest {

    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    @Autowired
    MapperManager mapperMgr;

    @Autowired
    BusinessDao businessDao;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_overview.sql", false);
    }

    @Test
    public void testQueryBusinessListBySolutionId() {
        List<Business> businessList = businessDao.queryBusinessListBySolutionId(1);
        Assert.assertTrue(CollectionUtils.isNotEmpty(businessList));
    }

}
