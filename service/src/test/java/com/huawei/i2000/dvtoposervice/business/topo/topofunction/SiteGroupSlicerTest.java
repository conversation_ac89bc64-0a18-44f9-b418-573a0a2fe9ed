/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.topo.topofunction;

import com.alibaba.fastjson2.JSON;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.mybatis.session.MapperManager;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.service.bean.ConfigurationImportEntity;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.impl.DVTopoServiceImportManageDelegateImpl;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.context.ContextConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 站点分组测试类
 *
 * <AUTHOR>
 * @since 2024-9-25 17:32:17
 */
@PrepareForTest({PropertiesUtil.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class SiteGroupSlicerTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);
    private static final OssLog LOGGER = OssLogFactory.getLogger(SiteGroupSlicerTest.class);

    @Autowired
    MapperManager mapperMgr;

    @Autowired
    private SiteGroupSlicer siteGroupSlicer;

    @Autowired
    private BusinessInstanceModelDao instanceModelDao;

    private static final String CONFIG_FILE_COLLECTION = "topotest/configuration_import_file";

    @Test
    public void testUpdateSiteGroup() throws ServiceException {
        for (DVTopoServiceImportManageDelegateImpl.FileInfoBean fileInfoBean : getConfigurationImportEntityWrapperList()) {
            ConfigurationImportEntity configurationImportEntity = fileInfoBean.getConfigurationImportEntity();
            if (!Objects.equals(BusinessTopoConstant.FULL_CONFIGURATION_TEMPLATE_TYPE, configurationImportEntity.getTemplateType())) {
                continue;
            }
            siteGroupSlicer.updateSiteGroup(configurationImportEntity);
        }
        List<BusinessInstanceModelDB> instanceModelDBList = instanceModelDao.queryInstancesByModelType(2);
        Assert.assertEquals(1, instanceModelDBList.size());
    }

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_site_group_slice.sql", false);
    }

    private List<DVTopoServiceImportManageDelegateImpl.FileInfoBean> getConfigurationImportEntityWrapperList() {
        try {
            ClassPathResource classPathResource = new ClassPathResource(CONFIG_FILE_COLLECTION);
            File file = classPathResource.getFile();
            if (Objects.isNull(file.listFiles())) {
                return Collections.emptyList();
            }
            return Arrays
                    .stream(Objects.requireNonNull(file.listFiles()))
                    .map(this::getConfigurationImportEntity)
                    .filter(Objects::nonNull)
                    .sorted((a, b) -> Integer.compare(b.getConfigurationImportEntity().getTemplateType(), a.getConfigurationImportEntity().getTemplateType()))
                    .collect(Collectors.toList());
        } catch (IOException e) {
            LOGGER.error("[READ_FILE_ERR]");
        }
        return new ArrayList<>();
    }

    private DVTopoServiceImportManageDelegateImpl.FileInfoBean getConfigurationImportEntity(File file) {
        try {
            String s = FileUtils.readFileToString(file, "UTF-8");
            return new DVTopoServiceImportManageDelegateImpl.FileInfoBean(file.getName(), JSON.parseObject(s, ConfigurationImportEntity.class));
        } catch (IOException e) {
            LOGGER.error("[FileUtil_read] read configuration file failed!");
        }
        return null;
    }
}
