/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.event;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.cache.redis.EmbeddedRedisServer;
import com.huawei.baize.servicesimulator.cache.redis.EmbeddedRedisServerUtils;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.exception.OSSException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.event.BusinessTopoUpdateHandler;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.oms.eam.mo.AvailableStatus;
import com.huawei.oms.eam.mo.MOType;
import com.huawei.oms.eam.mo.ManagedElement;
import com.huawei.oms.eam.mo.ManagedObject;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 业务拓扑重启/部署/升级适配类
 *
 * <AUTHOR>
 * @since 2024/7/24
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class, MITManagerClient.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessTopoUpdateHandlerTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    private static EmbeddedRedisServer redisServer;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    BusinessTopoUpdateHandler businessTopoUpdateHandler;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_event.sql", false);
        redisServer = EmbeddedRedisServerUtils.startGlobalRedisServer();
        Assert.assertNotNull(redisServer);
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    @Test
    public void businessTopoUpdateHandlerInitTest() throws OSSException {
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        ManagedElement mo = new ManagedElement() {
            @Override
            public ManagedObject copy() {
                return null;
            }
        };
        mo.setIPAddress("*******");
        mo.setName("appName");
        mo.setAvailableStatus(AvailableStatus.Normal);
        mo.setCreatedTime(System.currentTimeMillis());
        mo.setVersion("2000");
        mo.setType("instance");
        PowerMockito.when(mitManagerClient.getMO("podFatherDn")).thenReturn(mo);

        MOType moType = new MOType();
        moType.setDisplayType("instance");
        PowerMockito.when(mitManagerClient.getMOType("instance")).thenReturn(moType);

        Exception ex = null;
        try {
            businessTopoUpdateHandler.init();
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }

    @Test
    public void complementApplicationBasicInfoTest() throws OSSException {
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        ManagedElement mo = new ManagedElement() {
            @Override
            public ManagedObject copy() {
                return null;
            }
        };
        mo.setIPAddress("*******");
        mo.setName("appName");
        mo.setAvailableStatus(AvailableStatus.Normal);
        mo.setCreatedTime(System.currentTimeMillis());
        mo.setVersion("2000");
        mo.setType("instance");
        PowerMockito.when(mitManagerClient.getMO("podFatherDn")).thenReturn(mo);

        MOType moType = new MOType();
        moType.setDisplayType("instance");
        PowerMockito.when(mitManagerClient.getMOType("instance")).thenReturn(moType);

        Exception ex = null;
        try {
            businessTopoUpdateHandler.complementApplicationBasicInfo();
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }

    @Test
    public void initLinkIndicator() {
        Exception ex = null;
        try {
            businessTopoUpdateHandler.initLinkIndicator();
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }
}
