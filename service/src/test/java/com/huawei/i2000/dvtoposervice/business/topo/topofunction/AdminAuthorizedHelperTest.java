/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.topo.topofunction;

import com.huawei.bsp.mybatis.session.MapperManager;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.topo.AdminAuthorizedHelper;
import com.huawei.i2000.dvtoposervice.util.AuthUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * AdminAuthorizedHelperTest
 *
 * @since 2024-12-3 14:38:09
 */
@PrepareForTest({PropertiesUtil.class, MITManagerClient.class, AuthUtils.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class AdminAuthorizedHelperTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    @Autowired
    private AdminAuthorizedHelper adminAuthorizedHelper;

    @Autowired
    MapperManager mapperMgr;

    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_admin_auth.sql", false);
    }

    @Test
    public void testNonAdminAuthorize(){
        Set<String> dns = new HashSet<>();
        dns.add("a2cd5d228b3fb0cc40159-3");
        PowerMockito.mockStatic(AuthUtils.class);
        PowerMockito.when(AuthUtils.getAuthDns()).thenReturn(dns);

        long timeStamp = 1733711582038L;

        // 校验权限存在
        boolean b = adminAuthorizedHelper.nonAdminAuthorize(10349648, timeStamp);
        Assert.assertTrue(b);

        // 权限不存在
        boolean authorize = adminAuthorizedHelper.nonAdminAuthorize(1, timeStamp);
        Assert.assertFalse(authorize);
    }
}
