/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.event;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.event.MoEventHandler;
import com.huawei.i2000.dvtoposervice.util.AuthUtils;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.oms.eam.mo.ManagedObject;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.lang.reflect.Method;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 网元事件处理测试类
 *
 * <AUTHOR>
 * @since 2025/2/13
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class, AuthUtils.class, MoEventHandler.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class MoEventHandlerTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;


    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    MoEventHandler moEventHandler;

    Class<MoEventHandler> moEventHandlerClass = MoEventHandler.class;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith.sql", false);
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    @Test
    public void updateIconTest() throws  NoSuchMethodException {

        Method updateIcon = moEventHandlerClass.getDeclaredMethod("updateIcon", String.class, ManagedObject.class);
        updateIcon.setAccessible(true);
        ManagedObject mo = null;

        Exception ex = null;
        try {
            updateIcon.invoke(moEventHandler, "OS=1", mo);
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }


}
