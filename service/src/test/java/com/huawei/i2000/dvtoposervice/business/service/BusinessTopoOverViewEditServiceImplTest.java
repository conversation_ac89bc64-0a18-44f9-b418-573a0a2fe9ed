/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.biz.util.HttpUtil;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.edit.BusinessEditOperation;
import com.huawei.i2000.dvtoposervice.business.edit.BusinessIndicatorComparator;
import com.huawei.i2000.dvtoposervice.business.service.bean.ToUpdateObjectSet;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.convert.BusinessConvert;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.model.Business;
import com.huawei.i2000.dvtoposervice.model.BusinessEditInitView;
import com.huawei.i2000.dvtoposervice.model.BusinessIndicator;
import com.huawei.i2000.dvtoposervice.model.Channel;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.model.Site;
import com.huawei.i2000.dvtoposervice.model.StripeTeam;
import com.huawei.i2000.dvtoposervice.util.AuthUtils;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.LogUtil;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.dvtopowebsite.impl.BusinessTopoWebsiteDelegateImpl;
import com.huawei.i2000.dvtopowebsite.model.BusinessEditParam;
import com.huawei.i2000.dvtopowebsite.model.ResponseEntity;
import com.huawei.i2000.eam.client.mim.MITManagerClient;

import com.alibaba.fastjson2.JSONObject;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 总览视图编辑功能测试类
 *
 * <AUTHOR>
 * @since 2024/8/5
 */
@PrepareForTest( {
    ContextUtils.class, PropertiesUtil.class, MITManagerClient.class, BusinessTopoOverViewServiceImpl.class,
    System.class, HttpUtil.class, LogUtil.class, AuthUtils.class
})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessTopoOverViewEditServiceImplTest extends WebServiceTest {

    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    private static final MapOper<ConfigData> BUSINESS_CONFIG_MAP_OPER = new MapOper<>(
        RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    BusinessTopoOverViewServiceImpl serviceImpl;

    @Autowired
    BusinessTopoWebsiteDelegateImpl websiteImpl;

    @Autowired
    BusinessInstanceModelDao modelDao;

    @Autowired
    BusinessEditOperation businessEditOperation;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_overview_edit.sql", false);
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    private void mockAuth() {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
    }

    @Test
    public void queryInitEditPageTest() throws ServiceException {
        mockAuth();
        BusinessEditParam editParam = new BusinessEditParam();
        editParam.setSolutionId(10001);
        ResponseEntity result = websiteImpl.queryInitEditPage(new MockHttpContext(), editParam);
        String viewResult = JSONObject.toJSONString(result.getData());
        BusinessEditInitView view = JSONObject.parseObject(viewResult, BusinessEditInitView.class);
        Assert.assertFalse(view.getBusinessList().isEmpty());
    }

    @Test
    public void testEditOverViewPage() throws Exception {
        PowerMockito.mockStatic(LogUtil.class);
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        int solutionId = 10001;
        List<Business> toUpdateBusinessList = new ArrayList<>();
        toUpdateBusinessList.add(getToEditBusiness());
        toUpdateBusinessList.add(getToAddBusiness());
        toUpdateBusinessList.forEach(channel1 -> {
            List<BusinessIndicator> indicatorList = new ArrayList<>();
            BusinessIndicator indicator = new BusinessIndicator();
            indicator.setMoType("CBS");
            indicator.setIndicatorDisplayType(1);
            indicatorList.add(indicator);
            channel1.setIndicatorList(indicatorList);
        });
        List<Site> siteList = new ArrayList<>();
        Site site = new Site();
        site.setSiteId(10031);
        site.setSiteName("name1");
        siteList.add(site);
        serviceImpl.editOverViewPage(new MockHttpContext(), solutionId, "cbs",
                JSONObject.toJSONString(toUpdateBusinessList), JSONObject.toJSONString(siteList));
        BusinessInstanceModelDB siteIns = modelDao.queryInstanceByInstanceId(10031);
        Assert.assertEquals("name1", siteIns.getExtentAttr("siteName").getAttrValue());
    }

    @Test
    public void testEditOverViewPageNoPermission() throws Exception {
        PowerMockito.mockStatic(AuthUtils.class);
        PowerMockito.mockStatic(LogUtil.class);
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(false);
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        int solutionId = 10001;
        List<Business> toUpdateBusinessList = new ArrayList<>();
        toUpdateBusinessList.add(getToEditBusiness());
        toUpdateBusinessList.add(getToAddBusiness());
        List<Site> siteList = new ArrayList<>();
        Site site = new Site();
        site.setSiteId(10031);
        site.setSiteName("name1");
        siteList.add(site);
        Exception exception = null;
        try {
            serviceImpl.editOverViewPage(new MockHttpContext(), solutionId, "cbs",
                JSONObject.toJSONString(toUpdateBusinessList), JSONObject.toJSONString(siteList));
        } catch (ServiceException e) {
            exception = e;
        }
        Assert.assertNotNull(exception);
    }

    @Test
    public void testEditOverViewPageForMmNoPermission() throws Exception {
        PowerMockito.mockStatic(AuthUtils.class);
        PowerMockito.mockStatic(LogUtil.class);
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(false);
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        int solutionId = 50001;
        List<Business> toUpdateBusinessList = new ArrayList<>();
        Business toEditBusiness = getToEditBusiness();
        toEditBusiness.setBusinessId(50002);
        toUpdateBusinessList.add(toEditBusiness);
        toUpdateBusinessList.add(getToAddBusiness());
        List<StripeTeam> stripeTeams = new ArrayList<>();
        StripeTeam stripeTeam = new StripeTeam();
        stripeTeam.setStripeTeamId(50004);
        stripeTeam.setStripeTeamName("123");
        stripeTeams.add(stripeTeam);
        Exception exception = null;
        try {
            serviceImpl.editOverviewForMm(new MockHttpContext(), solutionId, "MM",
                    JSONObject.toJSONString(toUpdateBusinessList), JSONObject.toJSONString(new ArrayList<Channel>()), JSONObject.toJSONString(stripeTeams));
        } catch (ServiceException e) {
            exception = e;
        }
        Assert.assertNotNull(exception);
    }

    @Test
    public void testEditOverViewPageForMm_editChannel() throws Exception {
        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);
        ConfigData configGroupName = new ConfigData();
        configGroupName.setConfigItemName("mmHwsGroupName");
        configGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configGroupName);

        PowerMockito.mockStatic(AuthUtils.class);
        PowerMockito.mockStatic(LogUtil.class);
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        int solutionId = 50001;
        List<Channel> updateChannelList = new ArrayList<>();
        Channel editChannel = getEditChannel();
        updateChannelList.add(editChannel);
        Channel channel = getChannel();
        updateChannelList.add(channel);
        Business business = new Business();
        business.setBusinessId(50002);
        updateChannelList.forEach(channel1 -> {
            List<BusinessIndicator> indicatorList = new ArrayList<>();
            BusinessIndicator indicator = new BusinessIndicator();
            indicator.setMoType("CBS");
            indicator.setIndicatorDisplayType(1);
            indicatorList.add(indicator);
            channel1.setIndicatorList(indicatorList);
        });
        List<StripeTeam> stripeTeams = getStripeTeams();
        serviceImpl.editOverviewForMm(new MockHttpContext(), solutionId, "MM",
                JSONObject.toJSONString(Collections.singletonList(business)), JSONObject.toJSONString(updateChannelList), JSONObject.toJSONString(stripeTeams));
        List<BusinessInstanceModelDB> mmModel = modelDao.queryNextLevelInstance(50001);
        List<BusinessInstanceModelDB> thirdPartyList = mmModel.stream().filter(ins -> ins.getModelType() == 1).collect(Collectors.toList());
        Assert.assertEquals(1, thirdPartyList.size());
        List<BusinessInstanceModelDB> channelList = mmModel.stream().filter(ins -> ins.getModelType() == 101).collect(Collectors.toList());
        Assert.assertEquals(2, channelList.size());
        for (BusinessInstanceModelDB model : channelList) {
            if (model.getInstanceId() == 50003) {
                Assert.assertEquals("modifyChannel", model.getStaticExtentAttr("channelName").getStaticAttrValue());
                Assert.assertEquals("false", model.getStaticExtentAttr("canDelete").getStaticAttrValue());
                continue;
            }
            Assert.assertEquals("true", model.getStaticExtentAttr("canDelete").getStaticAttrValue());
            Assert.assertEquals("addChannel", model.getStaticExtentAttr("channelName").getStaticAttrValue());
        }
    }

    private static Channel getChannel() {
        Channel channel = new Channel();
        channel.setChannelName("addChannel");
        List<BusinessIndicator> indicatorList = new ArrayList<>();
        BusinessIndicator businessIndicator = new BusinessIndicator();
        businessIndicator.setMeasUnitKey("unitKey");
        businessIndicator.setMoType("moType");
        businessIndicator.setIndicatorDisplayType(1);
        indicatorList.add(businessIndicator);
        channel.setIndicatorList(indicatorList);
        return channel;
    }

    private static List<StripeTeam> getStripeTeams() {
        List<StripeTeam> stripeTeams = new ArrayList<>();
        StripeTeam stripeTeam = new StripeTeam();
        stripeTeam.setStripeTeamId(50004);
        stripeTeam.setStripeTeamName("123");
        stripeTeams.add(stripeTeam);
        return stripeTeams;
    }

    private Channel getEditChannel() {
        Channel channel = new Channel();
        channel.setChannelId(50003);
        channel.setChannelName("modifyChannel");
        channel.setIsDisplay(true);
        channel.setEditType(1);
        return channel;
    }

    @Test
    public void testRecordOpLog() throws ServiceException {
        BusinessTopoOverViewServiceImpl instance = new BusinessTopoOverViewServiceImpl();
        try {
            Method recordOpLog = instance.getClass().getDeclaredMethod("recordOpLog", boolean.class, boolean.class, StringBuilder.class, String.class, String.class);
            recordOpLog.setAccessible(true);
            StringBuilder stringBuilder = new StringBuilder();
            recordOpLog.invoke(instance, false, true, stringBuilder, "", "***********");
            recordOpLog.invoke(instance, false, true, stringBuilder, "123", "***********");
            recordOpLog.invoke(instance, true, true, stringBuilder, "123", "***********");
            recordOpLog.invoke(instance, true, false, stringBuilder, "123", "***********");
            Assert.assertTrue(stringBuilder.toString().contains("123"));
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Test
    public void distinguishingBusinessOperationTypeTest() throws Exception {
        PowerMockito.mockStatic(LogUtil.class);
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        int solutionId = 10001;
        List<Business> toUpdateBusinessList = new ArrayList<>();
        toUpdateBusinessList.add(getToEditBusiness());
        toUpdateBusinessList.add(getToAddBusiness());
        List<Site> siteList = new ArrayList<>();
        Site site = new Site();
        site.setSiteId(10031);
        site.setSiteName("test");
        siteList.add(site);
        toUpdateBusinessList.forEach(channel1 -> {
            List<BusinessIndicator> indicatorList = new ArrayList<>();
            BusinessIndicator indicator = new BusinessIndicator();
            indicator.setMoType("CBS");
            indicator.setIndicatorDisplayType(1);
            indicatorList.add(indicator);
            channel1.setIndicatorList(indicatorList);
        });
        serviceImpl.editOverViewPage(new MockHttpContext(), solutionId, "cbs",
                JSONObject.toJSONString(toUpdateBusinessList), JSONObject.toJSONString(siteList));
        List<BusinessInstanceModelDB> insModelList = modelDao.queryAllInstanceOfType(1);
        Assert.assertEquals(4, insModelList.size());
        BusinessInstanceModelDB crmIns = modelDao.queryInstanceByInstanceId(10011);
        Assert.assertEquals("CRM", crmIns.getExtentAttr("businessName").getAttrValue());
    }

    @Test
    public void addBusinessOperationTypeTest() throws ServiceException {
        int solutionId = 10001;
        List<Business> toUpdateBusinessList = getAllSaveBusiness();
        Business toAdd = getToAddBusiness();
        toAdd.setIsMain(false);
        toUpdateBusinessList.add(toAdd);
        toUpdateBusinessList.forEach(business -> {
            List<BusinessIndicator> indicatorList = new ArrayList<>();
            BusinessIndicator indicator = new BusinessIndicator();
            indicator.setMoType("CBS");
            indicator.setIndicatorDisplayType(1);
            indicatorList.add(indicator);
            business.setIndicatorList(indicatorList);
        });
        serviceImpl.editOverViewPage(new MockHttpContext(), solutionId, "",
            JSONObject.toJSONString(toUpdateBusinessList),null);
        List<BusinessInstanceModelDB> insModelList = modelDao.queryAllInstanceOfType(1);
        Assert.assertEquals(7, insModelList.size());
        BusinessInstanceModelDB crmIns = modelDao.queryInstanceByInstanceId(10011);
        Assert.assertEquals("CRM", crmIns.getExtentAttr("businessName").getAttrValue());
    }

    @Test
    public void hideBusinessOperationTypeTest() throws ServiceException {
        int solutionId = 10001;
        List<Business> toUpdateBusinessList = getAllSaveBusiness();
        toUpdateBusinessList.forEach(up -> {
            up.setIsDisplay(false);
            up.setEditType(1);
        });
        toUpdateBusinessList.forEach(business -> {
            List<BusinessIndicator> indicatorList = new ArrayList<>();
            BusinessIndicator indicator = new BusinessIndicator();
            indicator.setMoType("CBS");
            indicator.setIndicatorDisplayType(1);
            indicatorList.add(indicator);
            business.setIndicatorList(indicatorList);
        });
        serviceImpl.editOverViewPage(new MockHttpContext(), solutionId, "",
            JSONObject.toJSONString(toUpdateBusinessList),null);
        List<Business> displayBusinessList= serviceImpl.queryBusinessList(solutionId, 0L);
        Assert.assertEquals(0, displayBusinessList.size());
    }

    private List<Business> getAllSaveBusiness() {
        List<BusinessInstanceModelDB> insModelList = modelDao.queryAllInstanceOfType(1);
        BusinessConvert convert = new BusinessConvert();
        return insModelList.stream().map(convert::convert).collect(Collectors.toList());
    }

    private Business getToEditBusiness() {
        Business editBusiness = new Business();
        editBusiness.setBusinessId(10011);
        editBusiness.setBusinessName("CRM");
        editBusiness.setBusinessType("south");
        editBusiness.setIsMain(true);
        editBusiness.setEditType(1);
        editBusiness.setIndicatorList(new ArrayList<>());
        return editBusiness;
    }

    private Business getToAddBusiness() {
        Business addBusiness = new Business();
        addBusiness.setBusinessName("TO_ADD");
        addBusiness.setBusinessType("north");
        addBusiness.setIsMain(true);
        addBusiness.setIndicatorList(new ArrayList<>());
        return addBusiness;
    }

    @Test
    public void businessIndicatorComparatorTest() {
        List<BusinessIndicator> editIndicatorList = new ArrayList<>();
        editIndicatorList.add(getIndicatorEdit());
        List<BusinessIndicator> curIndicatorList = new ArrayList<>();
        curIndicatorList.add(getIndicatorCurrent());
        BusinessIndicatorComparator comparator = new BusinessIndicatorComparator();
        Assert.assertTrue(comparator.compareIndicatorList(editIndicatorList, curIndicatorList));

        editIndicatorList.add(getIndicatorEditAnother());
        curIndicatorList.add(getIndicatorAnother());
        Assert.assertFalse(comparator.compareIndicatorList(editIndicatorList, curIndicatorList));
    }

    private BusinessIndicator getIndicatorEdit() {
        BusinessIndicator editIndicator = new BusinessIndicator();
        editIndicator.setMoType("motype");
        editIndicator.setMeasUnitKey("unit");
        editIndicator.setMeasTypeKey("type");
        editIndicator.setOriginalValue(null);
        return editIndicator;
    }

    private BusinessIndicator getIndicatorEditAnother() {
        BusinessIndicator editIndicator = new BusinessIndicator();
        editIndicator.setMoType("motype");
        editIndicator.setMeasUnitKey("unit");
        editIndicator.setMeasTypeKey("type");
        editIndicator.setOriginalValue("1");
        return editIndicator;
    }

    private BusinessIndicator getIndicatorCurrent() {
        BusinessIndicator curIndicator = new BusinessIndicator();
        curIndicator.setMoType("motype");
        curIndicator.setMeasUnitKey("unit");
        curIndicator.setMeasTypeKey("type");
        curIndicator.setOriginalValue(null);
        return curIndicator;
    }

    private BusinessIndicator getIndicatorAnother() {
        BusinessIndicator curIndicator = new BusinessIndicator();
        curIndicator.setMoType("motype");
        curIndicator.setMeasUnitKey("unit");
        curIndicator.setMeasTypeKey("type");
        curIndicator.setOriginalValue("2");
        return curIndicator;
    }

    @Test
    public void addBusinessMmTest() throws ServiceException {
        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);
        ConfigData configGroupName = new ConfigData();
        configGroupName.setConfigItemName("mmHwsGroupName");
        configGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configGroupName);

        Business editBusiness = new Business();
        List<BusinessIndicator> indicatorList = new ArrayList<>();
        BusinessIndicator businessIndicator = new BusinessIndicator();
        businessIndicator.setMeasUnitKey("unitKey");
        businessIndicator.setMoType("moType");
        businessIndicator.setIndicatorDisplayType(1);
        indicatorList.add(businessIndicator);
        editBusiness.setIndicatorList(indicatorList);
        ToUpdateObjectSet toUpdateObjectSet = new ToUpdateObjectSet();
        Map<String, Channel> addChannelNameMap = new HashMap<>();

        businessEditOperation.addBusiness(editBusiness, toUpdateObjectSet, addChannelNameMap);

        Assert.assertEquals(1, toUpdateObjectSet.getCommonModelList().size());
    }

}
