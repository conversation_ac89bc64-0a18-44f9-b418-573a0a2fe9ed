/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmDetail;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.ModelAlarm;
import com.huawei.i2000.dvtoposervice.business.openapi.alarm.AlarmConditionFilter;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 业务Topo告警规则过滤
 *
 * <AUTHOR>
 * @since 2024/8/30
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessAlarmConditionFilterTest extends WebServiceTest {

    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (! DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_overview.sql", false);
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    @Test
    public void testAlarmWhiteFilter() {
        List<ModelAlarm> modelAlarms = new ArrayList<>();
        modelAlarms.add(mockWhiteModel());
        Set<AlarmDetail> alarmDetails = new HashSet<>();
        alarmDetails.add(mockAlarmDetail());
        AlarmConditionFilter alarmConditionFilter = new AlarmConditionFilter(modelAlarms);
        List<AlarmDetail> alarmDetailList = new ArrayList<>(alarmConditionFilter.getFilterResult(alarmDetails));
        Assert.assertTrue(CollectionUtils.isNotEmpty(alarmDetailList));
        AlarmDetail filterAlarm = alarmDetailList.get(0);
        Assert.assertEquals(12306, (int) filterAlarm.getAlarmId());
    }

    @Test
    public void testAlarmBlackFilter() {
        List<ModelAlarm> modelAlarms = new ArrayList<>();
        modelAlarms.add(mockBlackModel());
        Set<AlarmDetail> alarmDetails = new HashSet<>();
        alarmDetails.add(mockAlarmDetail());
        AlarmConditionFilter alarmConditionFilter = new AlarmConditionFilter(modelAlarms);
        List<AlarmDetail> alarmDetailList = new ArrayList<>(alarmConditionFilter.getFilterResult(alarmDetails));
        Assert.assertTrue(CollectionUtils.isEmpty(alarmDetailList));
    }

    @Test
    public void testAlarmLevelAndClearType() {
        List<ModelAlarm> modelAlarms = new ArrayList<>();
        modelAlarms.add(mockWhiteModel());
        Set<AlarmDetail> alarmDetails = new HashSet<>();
        alarmDetails.add(mockAlarmDetailLevel());
        AlarmConditionFilter alarmConditionFilter = new AlarmConditionFilter(modelAlarms);
        List<AlarmDetail> alarmDetailList = new ArrayList<>(alarmConditionFilter.getFilterResult(alarmDetails));
        Assert.assertTrue(CollectionUtils.isNotEmpty(alarmDetailList));
    }

    @Test
    public void testAlarmByAll() {
        List<ModelAlarm> modelAlarms = new ArrayList<>();
        modelAlarms.add(mockTotalModel());
        Set<AlarmDetail> alarmDetails = new HashSet<>();
        alarmDetails.add(mockAlarmDetailLevel());
        AlarmConditionFilter alarmConditionFilter = new AlarmConditionFilter(modelAlarms);
        List<AlarmDetail> alarmDetailList = new ArrayList<>(alarmConditionFilter.getFilterResult(alarmDetails));
        Assert.assertTrue(CollectionUtils.isNotEmpty(alarmDetailList));
    }

    private ModelAlarm mockWhiteModel() {
        ModelAlarm modelAlarm = new ModelAlarm();
        modelAlarm.setModelId("1_DV");
        modelAlarm.setAlarmId(12306);
        modelAlarm.setAlarmType(1);
        modelAlarm.setListeningType(0);
        modelAlarm.setAlarmSeverity("[1,2]");
        modelAlarm.setAlarmClearType(1);
        return modelAlarm;
    }

    private ModelAlarm mockBlackModel() {
        ModelAlarm modelAlarm = new ModelAlarm();
        modelAlarm.setModelId("1_DV");
        modelAlarm.setAlarmId(12306);
        modelAlarm.setAlarmType(1);
        modelAlarm.setListeningType(1);
        return modelAlarm;
    }

    private ModelAlarm mockTotalModel() {
        ModelAlarm modelAlarm = new ModelAlarm();
        modelAlarm.setModelId("1_DV");
        modelAlarm.setAlarmId(12306);
        modelAlarm.setAlarmType(1);
        modelAlarm.setListeningType(2);
        return modelAlarm;
    }

    private AlarmDetail mockAlarmDetail() {
        AlarmDetail alarmDetail = new AlarmDetail();
        alarmDetail.setAlarmId(12306);
        alarmDetail.setSeverity(1);
        alarmDetail.setCategory(1);
        return alarmDetail;
    }

    private AlarmDetail mockAlarmDetailLevel() {
        AlarmDetail alarmDetail = new AlarmDetail();
        alarmDetail.setAlarmId(12308);
        alarmDetail.setSeverity(1);
        alarmDetail.setClearCategory(1);
        return alarmDetail;
    }

}
