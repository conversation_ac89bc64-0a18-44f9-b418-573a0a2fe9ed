/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.alarm;

import com.alibaba.fastjson2.JSON;
import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.cloudsop.mq.api.consumer.IConsumerRecord;
import com.huawei.cloudsop.mq.api.consumer.IConsumerRecords;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmDetail;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInsExtentAttrDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.openapi.alarm.AlarmNotificationService;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.BusinessTopoAlarmStorageService;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.event.AlarmConsumer;
import com.huawei.i2000.dvtoposervice.impl.DVTopoServiceDelegateImpl;
import com.huawei.i2000.dvtoposervice.util.AlarmHandleUtil;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;

import org.apache.commons.collections4.CollectionUtils;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 告警订阅可靠性测试
 *
 * <AUTHOR>
 * @since 2024/9/10
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class, MITManagerClient.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessAlarmReliabilityTest extends WebServiceTest {

    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    DVTopoServiceDelegateImpl dvTopoServiceDelegate;

    @Autowired
    AlarmConsumer alarmConsumer;

    @Autowired
    BusinessInstanceModelDao modelDao;

    @Autowired
    AlarmNotificationService notificationService;

    @Autowired
    private BusinessTopoAlarmStorageService businessTopoAlarmStorageService;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (! DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_alarm_reliability.sql", false);
        businessTopoAlarmStorageService.refreshAllStorageCache();
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }


    @Test
    public void noticeAlarmReliabilityTest() {
        List<AlarmDetail> alarmDetailList = new ArrayList<>();
        prepareDockerAlarm(alarmDetailList);
        prepareSiteAlarm(alarmDetailList);
        Exception ex = null;
        try {
            notificationService.noticeBusinessTopo(alarmDetailList);
        } catch (ServiceException e) {
            ex = e;
        }
        BusinessInstanceModelDB model = modelDao.queryInstanceByInstanceId(143093);
        BusinessInsExtentAttrDB alarmCsn = model.getExtentAttr("AlarmCsn");
        Assert.assertNull(ex);
        Assert.assertFalse(alarmCsn.getAttrValue().contains("264729104".trim()));
    }

    private void prepareDockerAlarm(List<AlarmDetail> alarmDetails) {
        AlarmDetail dockerAlarm = new AlarmDetail();
        dockerAlarm.setCsn(264729104);
        dockerAlarm.setCategory(2);
        dockerAlarm.setOccurTime(1726040859L);
        dockerAlarm.setNativeMeDn("b1606a59d21c5c1a9e0622df745bab648e3e44b6642d292aa6d12bbaea15230e");
        dockerAlarm.setAlarmId(60210036);
        dockerAlarm.setAlarmGroupId("cbs.billing.cdfmdbcloud_docker");
        dockerAlarm.setInstanceId(143345);
        alarmDetails.add(dockerAlarm);
    }

    private void prepareSiteAlarm(List<AlarmDetail> alarmDetails) {
        AlarmDetail siteAlarm = new AlarmDetail();
        siteAlarm.setCsn(264729277);
        siteAlarm.setCategory(2);
        siteAlarm.setOccurTime(1726040859L);
        siteAlarm.setNativeMeDn("a2cd5d228b3fb0cc40159-1");
        siteAlarm.setAlarmId(60210036);
        siteAlarm.setAlarmGroupId("cbs.billing.cbs");
        siteAlarm.setInstanceId(143093);
        alarmDetails.add(siteAlarm);
    }

    private void prepareOccurAlarm(List<AlarmDetail> alarmDetails) {
        AlarmDetail occurAlarm = new AlarmDetail();
        occurAlarm.setCsn(26472888);
        occurAlarm.setCategory(1);
        occurAlarm.setOccurTime(1726040859L);
        occurAlarm.setNativeMeDn("a2cd5d228b3fb0cc40159-1");
        occurAlarm.setAlarmId(60210036);
        alarmDetails.add(occurAlarm);
    }

    private void prepareAnalysisAlarm(List<AlarmDetail> alarmDetails) {
        AlarmDetail analysisAlarm = new AlarmDetail();
        analysisAlarm.setCsn(24888888);
        analysisAlarm.setCategory(1);
        analysisAlarm.setAnalysisStatus("over");
        analysisAlarm.setOccurTime(1726040859L);
        analysisAlarm.setNativeMeDn("a2cd5d228b3fb0cc40159-1");
        analysisAlarm.setAlarmId(60210036);
        alarmDetails.add(analysisAlarm);
    }

    @Test
    public void analysisAlarmSortedTest() {
        List<AlarmDetail> alarmDetailList = new ArrayList<>();
        prepareDockerAlarm(alarmDetailList);
        prepareSiteAlarm(alarmDetailList);
        prepareOccurAlarm(alarmDetailList);
        prepareAnalysisAlarm(alarmDetailList);
        AlarmHandleUtil.sortedTimeLineAlarmByRule(alarmDetailList);
        AlarmDetail alarm = alarmDetailList.get(0);
        Assert.assertEquals(24888888, (int) alarm.getCsn());
    }

    @Test
    @SuppressWarnings("unchecked")
    public void convertAlarmFromKafkaTest() {
        IConsumerRecords<String, String> records = PowerMockito.mock(IConsumerRecords.class);
        IConsumerRecord<String, String> record = PowerMockito.mock(IConsumerRecord.class);
        PowerMockito.when(records.records()).thenReturn(Collections.singletonList(record));
        PowerMockito.when(record.key()).thenReturn("key");
        PowerMockito.when(record.topic()).thenReturn("topic");
        AlarmDetail alarmDetail = new AlarmDetail();
        PowerMockito.when(record.value()).thenReturn(JSON.toJSONString(Collections.singletonList(alarmDetail)));
        PowerMockito.when(record.offset()).thenReturn(1L);
        PowerMockito.when(record.partition()).thenReturn(1);

        List<AlarmDetail> alarmDetails = alarmConsumer.convertAlarmFromKafka(records);
        Assert.assertTrue(CollectionUtils.isNotEmpty(alarmDetails));
    }
}
