/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.topo.topofunction;

import com.huawei.bsp.mybatis.session.MapperManager;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.model.HomePageConfigurationParam;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import mockit.Mock;
import mockit.MockUp;
import org.junit.Before;
import org.junit.Test;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 页面配置信息测试类
 *
 * @since 2024-11-30 18:33:24
 */
@PrepareForTest({PropertiesUtil.class, MITManagerClient.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class HomePageConfigurationServiceTest extends WebServiceTest {

    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    @Autowired
    MapperManager mapperMgr;

    @Autowired
    private HomePageConfigurationService homePageConfigurationService;

    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_home_page_config.sql", false);
    }


    private void mockContext(){
        new MockUp<ContextUtils>(ContextUtils.class) {
            @Mock
            public ContextUtils getContext() {
                ContextUtils contextUtils = new ContextUtils();
                contextUtils.setUserId("user_01");
                contextUtils.setUserName("userName");
                contextUtils.setTerminal("*******");
                return contextUtils;
            }
        };
    }

    @Test
    public void testInsertHomePageConfiguration() throws ServiceException {
        mockContext();
        // 插入分支
        HomePageConfigurationParam param = new HomePageConfigurationParam();
        param.setHomeType("type_1");
        param.setSolutionId(10349529);
        homePageConfigurationService.saveHomePageConfigurationData(param);
        // 更新分支
        homePageConfigurationService.saveHomePageConfigurationData(param);
    }
}
