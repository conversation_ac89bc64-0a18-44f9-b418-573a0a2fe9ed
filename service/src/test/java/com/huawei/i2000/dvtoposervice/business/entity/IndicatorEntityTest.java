/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.entity;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025-03-29
 */
public class IndicatorEntityTest {
    private IndicatorEntity indicatorEntity;

    @BeforeEach
    public void setUp() throws Exception {
        indicatorEntity = new IndicatorEntity();
    }

    @AfterEach
    public void tearDown() throws Exception {
    }

    @Test
    public void test_equals_should_return_ture_when_test_data_combination() {
        // setup
        Object o = new Object();

        // run the test
        boolean result = indicatorEntity.equals(o);

        // verify the results
        assertFalse(result);
    }

    @Test
    public void test_can_equal_should_return_ture_when_objects_is_null() {

        // run the test
        boolean result = indicatorEntity.canEqual(null);

        // verify the results
        assertFalse(result);
    }

    @Test
    public void test_can_equal_should_return_ture_when_test_data_combination() {
        // setup
        Object other = new Object();

        // run the test
        boolean result = indicatorEntity.canEqual(other);

        // verify the results
        assertFalse(result);
    }

    @Test
    public void test_hash_code() {

        // run the test
        int result = indicatorEntity.hashCode();

        // verify the results
        assertNotNull(result);
    }
}