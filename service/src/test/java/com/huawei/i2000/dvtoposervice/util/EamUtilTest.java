/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.util;

import com.huawei.bsp.exception.OSSException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.i2000.gapi.account.mo.AccountMO;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.ManagedObject;

import org.junit.Assert;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.test.context.ContextConfiguration;

import java.util.List;
import java.util.Vector;

/**
 * 资源工具类测试
 *
 * <AUTHOR>
 * @since 2024-04-26
 */
@PrepareForTest({PropertiesUtil.class, MITManagerClient.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class EamUtilTest extends WebServiceTest {
    @Test
    public void testBacktrackingTimestamp1() throws Exception {
        List<ManagedObject> mos = EamUtil.getMoByDns(new Vector<>());
        Assert.assertEquals(0, mos.size());
    }

    @Test
    public void isContainerEnvironmentTest() throws OSSException {
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        ManagedObject managedObject = new AccountMO();
        managedObject.setParent(new DN("OS=1"));
        managedObject.putClientProperty("K8SType", "true");
        PowerMockito.when(mitManagerClient.getMO("OS=1")).thenReturn(managedObject);
        ManagedObject managedObjectDF = new AccountMO();
        managedObjectDF.setParent(new DN("DF=1"));
        managedObjectDF.putClientProperty("chatType", "VM");
        PowerMockito.when(mitManagerClient.getMO("DF=1")).thenReturn(managedObjectDF);

        Integer type = EamUtil.isContainerEnvironment("OS=1");
        Assert.assertEquals(0, (int) type);

        Integer typeDf = EamUtil.isContainerEnvironment("DF=1");
        Assert.assertEquals(0, (int) typeDf);
    }

    @Test
    public void queryMosByMoTypeWhenRefreshTest() {
        try {
            EamUtil.queryMosByMoTypeWhenRefresh("fintech.Fintech.FCS");
        } catch (OSSException e) {
            Assert.assertTrue(true);
            return;
        }
        Assert.fail();
    }
}