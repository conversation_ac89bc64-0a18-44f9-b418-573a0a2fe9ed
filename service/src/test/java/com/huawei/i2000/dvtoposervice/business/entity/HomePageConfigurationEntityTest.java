/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.entity;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025-03-29
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {})
@PowerMockIgnore( {"javax.management.*", "jdk.internal.reflect.*"})
public class HomePageConfigurationEntityTest {

    @Test
    public void test_hash_code() {

        HomePageConfigurationEntity homePageConfigurationEntity = new HomePageConfigurationEntity();
        homePageConfigurationEntity.setHomePageId(1L);
        homePageConfigurationEntity.setHomeOrder(2L);
        homePageConfigurationEntity.setHomeUser("test");
        homePageConfigurationEntity.setHomeSortObj("name");
        homePageConfigurationEntity.setHomeUserOrganization("default");
        homePageConfigurationEntity.setHomeType("type");

        // run the test
        int result = homePageConfigurationEntity.hashCode();

        // verify the results
        assertNotNull(result);
    }
}