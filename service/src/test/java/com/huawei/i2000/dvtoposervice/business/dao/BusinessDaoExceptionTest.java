/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.dao;

import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessAlarmDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessCommonModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessCommonModelExtentAttrDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessConfigDataDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessIndicatorDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceRelationDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessMoTypeDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessSolutionDao;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;

import org.junit.Assert;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

/**
 * BusinessDaoExceptionTest
 *
 * <AUTHOR>
 * @since 2024/9/23
 */
@PrepareForTest({ContextUtils.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessDaoExceptionTest extends WebServiceTest {
    @Autowired
    BusinessAlarmDao businessAlarmDao;

    @Autowired
    BusinessCommonModelDao businessCommonModelDao;

    @Autowired
    BusinessCommonModelExtentAttrDao businessCommonModelExtentAttrDao;

    @Autowired
    BusinessConfigDataDao businessConfigDataDao;

    @Autowired
    BusinessDao businessDao;

    @Autowired
    BusinessIndicatorDao businessIndicatorDao;

    @Autowired
    BusinessInstanceModelDao businessInstanceModelDao;

    @Autowired
    BusinessInstanceRelationDao businessInstanceRelationDao;

    @Autowired
    BusinessMoTypeDao businessMoTypeDao;

    @Autowired
    BusinessSolutionDao businessSolutionDao;

    @Test
    public void businessDaoExceptionTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        executeSqlScript("classpath:database/dvtoposervice_tables_clear_all_table.sql", true);
        int count = BusinessDaoExceptionUtil.executeExceptCount(this);
        Assert.assertTrue(count > 20);
    }
}
