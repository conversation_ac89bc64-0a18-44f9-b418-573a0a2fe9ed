/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.timedtri;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.redis.oper.SetOper;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.timedtri.AnalysisIndQueryTimer;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;

import org.apache.commons.collections4.CollectionUtils;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 智能运维指标定期获取
 *
 * <AUTHOR>
 * @since 2024/5/10
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class, MITManagerClient.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class AnalysisIndQueryTimerTest extends WebServiceTest  {

    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    AnalysisIndQueryTimer analysisIndQueryTimer;

    private void mockProperties() {
        MapOper<ConfigData> businessConfigMapOper = new MapOper<>(
            RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("indicatorrange");
        configData.setValue("691200000");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "indicatorrange", configData);
        ConfigData configData1 = new ConfigData();
        configData1.setConfigItemName("distributeindicatorrange");
        configData1.setValue("3600000");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "distributeindicatorrange", configData1);
        ConfigData configData2 = new ConfigData();
        configData2.setConfigItemName("timelinerange");
        configData2.setValue("24");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "timelinerange", configData2);
        ConfigData configData3 = new ConfigData();
        configData3.setConfigItemName("vmindicatorindexname");
        configData3.setValue("\\u5185\\u5B58\\u603B\\u91CF,\\u5185\\u5B58\\u4F7F\\u7528\\u7387,CPU\\u5360\\u7528\\u7387");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "vmindicatorindexname", configData3);
        ConfigData configData4 = new ConfigData();
        configData4.setConfigItemName("vmindicatormeasunitkey");
        configData4.setValue("Memory,Memory,CPU");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "vmindicatormeasunitkey", configData4);
        ConfigData configData5 = new ConfigData();
        configData5.setConfigItemName("vmindicatormeastypekey");
        configData5.setValue("MemTotal,MemUsage,CpuUsage");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "vmindicatormeastypekey", configData5);
    }

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (! DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_analysis.sql", false);
        mockProperties();
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    private void mockAnalysisPrepare() {
        mockServer.loadScenarioFiles("module.analysis/rest_mappings_getanalysisresult.json");
    }

    @Test
    public void testAnalysisIndicatorResult() {
        mockAnalysisPrepare();
        analysisIndQueryTimer.queryAndCacheIndList();
        SetOper<String> businessIndicatorAlarmListOper = new SetOper<>(RedisConstant.BUSINESS_ANALYSIS_INDICATOR_ALARM,
            RedisConstant.REDIS_NAME);
        Set<String> alarmIds = businessIndicatorAlarmListOper.get("analysisAlarm", String.class);
        Assert.assertTrue(CollectionUtils.isNotEmpty(alarmIds));
    }
}
