/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.topo.topofunction;

import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.mybatis.session.MapperManager;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.service.bean.ConfigurationImportEntity;
import com.huawei.i2000.dvtoposervice.util.EamUtil;
import com.huawei.i2000.gapi.account.mo.AccountMO;
import com.huawei.oms.eam.mo.ManagedObject;

import com.alibaba.fastjson2.JSON;

import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * ResourceInstanceTreeServiceTest
 *
 * <AUTHOR>
 * @since 2024-4-19 21:02:56
 */
@ContextConfiguration(locations = "classpath:spring/service.xml")
@PrepareForTest({EamUtil.class})
public class ResourceInstanceTreeServiceTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    @Autowired
    MapperManager mapperMgr;

    @Autowired
    ResourceInstanceTreeService resourceInstanceTreeService;

    private static final String CONFIGURATION_IMPORT_JSON = "topotest/configuration_import_file/configuration_import_en.json";

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_overview.sql", false);
    }

    public ConfigurationImportEntity getConfigurationImportEntity(String filePath) {
        try {
            // read file stream
            File file = new File(filePath);
            String s = FileUtils.readFileToString(file, "UTF-8");
            return JSON.parseObject(s, ConfigurationImportEntity.class);

        } catch (IOException e) {
            e.printStackTrace();
        }
        return new ConfigurationImportEntity();
    }

    @Test
    public void test_resource_ins_query() throws OSSException {
        PowerMockito.mockStatic(EamUtil.class);
        List<ManagedObject> moList = new ArrayList<>();
        ManagedObject managedObject = new AccountMO();
        managedObject.setDN("28f59c78-64b5-4b58-949c-891cc8ec43bf");
        moList.add(managedObject);
        PowerMockito.when(EamUtil.bulkGetMo(Mockito.any())).thenReturn(moList);

        String path = Objects.requireNonNull(getClass().getClassLoader().getResource(CONFIGURATION_IMPORT_JSON))
                .getPath();
        ConfigurationImportEntity configurationImportEntity = getConfigurationImportEntity(path);
        Map<String, Object> map = resourceInstanceTreeService.cacheModelInstanceInfo(configurationImportEntity, a -> false);
        Assert.assertEquals(1, map.size());
    }
}