/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmDetail;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.BusinessTopoInsTreeNode;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInsExtentAttrDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.openapi.alarm.AlarmNotificationService;
import com.huawei.i2000.dvtoposervice.business.service.bean.ConfigurationImportEntity;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.business.topo.importer.CbsBesTopoImporter;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.BusinessTopoAlarmStorageService;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.PerformanceThresholdAlarmService;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.impl.DVTopoServiceDelegateImpl;
import com.huawei.i2000.dvtoposervice.model.AlarmCountResult;
import com.huawei.i2000.dvtoposervice.model.AlarmQueryData;
import com.huawei.i2000.dvtoposervice.model.AlarmStatisticParam;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.timedtri.AnalysisIndQueryTimer;
import com.huawei.i2000.dvtoposervice.util.AuthUtils;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONReader;

import org.apache.commons.collections.CollectionUtils;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 告警相关能力测试类
 *
 * <AUTHOR>
 * @since 2024/3/9
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class, AuthUtils.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessTopoAlarmTest extends WebServiceTest {

    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    DVTopoServiceDelegateImpl dvTopoServiceDelegate;

    @Autowired
    BusinessTopoCommonServiceImpl commonService;

    @Autowired
    BusinessInstanceModelDao modelDao;

    @Autowired
    AlarmNotificationService notificationService;

    @Autowired
    PerformanceThresholdAlarmService performanceThresholdAlarmService;

    @Autowired
    AnalysisIndQueryTimer analysisIndQueryTimer;

    @Autowired
    private BusinessTopoAlarmStorageService businessTopoAlarmStorageService;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (! DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_overview.sql", false);
        mockProperties();
        businessTopoAlarmStorageService.refreshAllStorageCache();
        mockAlarmInterface();
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    private void mockProperties() {
        PowerMockito.mockStatic(PropertiesUtil.class);
        Properties properties = new Properties();
        properties.put(BusinessTopoConstant.CLUSTER_TYPE, "6");
        properties.put(BusinessTopoConstant.POD_TYPE, "7");
        PowerMockito.when(PropertiesUtil.loadProperties(Mockito.anyString())).thenReturn(properties);

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        contextUtils.setLocal("en-us");
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
    }

    private void mockVmProperties() {
        MapOper<ConfigData> businessConfigMapOper = new MapOper<>(
            RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);
        ConfigData configData3 = new ConfigData();
        configData3.setConfigItemName("vmindicatorindexname");
        configData3.setValue("\\u5185\\u5B58\\u603B\\u91CF,\\u5185\\u5B58\\u4F7F\\u7528\\u7387,CPU\\u5360\\u7528\\u7387");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "vmindicatorindexname", configData3);
        ConfigData configData4 = new ConfigData();
        configData4.setConfigItemName("vmindicatormeasunitkey");
        configData4.setValue("Memory,Memory,CPU");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "vmindicatormeasunitkey", configData4);
        ConfigData configData5 = new ConfigData();
        configData5.setConfigItemName("vmindicatormeastypekey");
        configData5.setValue("MemTotal,MemUsage,CpuUsage");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "vmindicatormeastypekey", configData5);
    }

    @Test
    public void testAlarmStatic() {
        mockProperties();
        AlarmStatisticParam param = new AlarmStatisticParam();
        param.setInstanceId(100620);
        AlarmCountResult result = commonService.queryAlarmCountStatistic(param);
        List<AlarmQueryData> alarmQueryDataList = result.getAlarmDataList();
        Assert.assertTrue(CollectionUtils.isNotEmpty(alarmQueryDataList));
        AtomicBoolean existAlarm = new AtomicBoolean(false);
        alarmQueryDataList.forEach(alarm -> {
            if (result.getAlarmDataList().size() > 0) {
                existAlarm.set(true);
            }
        });
        Assert.assertTrue(existAlarm.get());
    }

    @Test
    public void testAlarmStaticUpper() {
        mockProperties();
        AlarmStatisticParam param = new AlarmStatisticParam();
        param.setInstanceId(10041);
        AlarmCountResult result = commonService.queryAlarmCountStatistic(param);
        List<AlarmQueryData> alarmQueryDataList = result.getAlarmDataList();
        Assert.assertTrue(CollectionUtils.isNotEmpty(alarmQueryDataList));
        AtomicBoolean existAlarm = new AtomicBoolean(false);
        alarmQueryDataList.forEach(alarm -> {
            if (result.getAlarmDataList().size() > 0) {
                existAlarm.set(true);
            }
        });
        Assert.assertTrue(existAlarm.get());
    }

    @Test
    public void testAlarmStaticTop() {
        mockProperties();
        AlarmStatisticParam param = new AlarmStatisticParam();
        param.setInstanceId(10001);
        AlarmCountResult result = commonService.queryAlarmCountStatistic(param);
        List<AlarmQueryData> alarmQueryDataList = result.getAlarmDataList();
        Assert.assertTrue(CollectionUtils.isNotEmpty(alarmQueryDataList));
        AtomicBoolean existAlarm = new AtomicBoolean(false);
        alarmQueryDataList.forEach(alarm -> {
            if (result.getAlarmDataList().size() > 0) {
                existAlarm.set(true);
            }
        });
        Assert.assertTrue(existAlarm.get());
    }

    @Test
    public void testNoticeAlarm() throws Exception {
        String alarmNotice
            = "[{\"userData\":\"{\\\"agentSource\\\":\\\"Agent:RESTFUL_UNIAGENT=352c\\\",\\\"isVNF\\\":\\\"false\\\",\\\"alarmProtocolType\\\":\\\"RestFm\\\",\\\"objectInstance\\\":\\\"NTP_SMART_SERVER time synchronize exception\\\"}\",\"mergeGroupId\":188181316,\"moi\":\"主机名称=kwephis480372,主机IP=***********,NTP_SMART_SERVER time synchronize exception\",\"occurTime\":1710001963120,\"clearUtc\":1709973776560,\"firstOccurDst\":0,\"productName\":\"云虚拟机\",\"backedupOnSource\":0,\"forwardType\":0,\"clearType\":1,\"tenant\":\"DefaultOrganization\",\"mergeKey\":\"SZCtEQ0ZF6tbQ7GC0TbIxg\",\"identifier\":0,\"clearDst\":0,\"originSystemType\":\"DigitalView\",\"eventType\":2,\"version\":2,\"azoneId\":\"\",\"andResetUpdateFromMergeDel\":false,\"logicalRegionName\":\"\",\"forwardUUIDKey\":\"7fb207e3-afc8-41b5-98a2-67604248210c\",\"deviceTypeId\":\"com.huawei.IRes.vm\",\"tampered\":false,\"updateFromMergeDel\":false,\"meType\":\"DVNetworkElement\",\"subCsn\":110975,\"changedFields\":[\"cleared\",\"clearType\",\"clearArriveUtc\",\"category\",\"clearUtc\",\"modifyDbUtc\",\"clearDst\",\"clearTime\",\"clearUser\"],\"aggrStatus\":0,\"manufacturer\":\"Huawei\",\"commentUtc\":0,\"probableCause\":\"同步源不匹配\",\"reasonId\":64,\"ackDst\":0,\"alarmId\":\"60210036\",\"occurUtc\":1709973163120,\"workOrderStatus\":\"0\",\"endUtc\":0,\"specialAlarmStatus\":0,\"address\":\"***********\",\"clearCategory\":1,\"aggrCount\":0,\"acked\":0,\"reportNorth\":0,\"ackUtc\":0,\"logicalRegionId\":\"\",\"latestOccurTime\":1710001963120,\"meDn\":\"bcf4966a-599f-4dd3-82f4-a29160743ecc\",\"alarmDurationTime\":0,\"clearMatchStrategy\":0,\"serviceAffectedType\":0,\"tenantId\":\"default-organization-id\",\"specialClearMatchStrategy\":false,\"occurDst\":0,\"originSystemId\":\"400\",\"nativeMoDn\":\"7dc08f7da8c60cd26229f\",\"firstOccurTime\":1709762160243,\"clearTime\":1710002576560,\"resPoolId\":\"\",\"meCategory\":\"NetworkElement\",\"dcName\":\"\",\"additionalInformation\":\"主机名称=kwephis480372,主机IP=***********,Get time synchronize exception.\",\"originSystem\":\"400\",\"count\":392,\"alarmName\":\"Linux时钟同步异常\",\"resGroupId\":\"\",\"latestOccurUtc\":1709973163120,\"region\":\"cn-global-1\",\"invalidated\":0,\"processMode\":0,\"cleared\":1,\"azoneName\":\"\",\"domainSubnetId\":\"3\",\"originSystemName\":\"DigitalView\",\"resPoolName\":\"\",\"alarmGroupId\":\"cbs.billing.adapterapp.cluster\",\"subSyncCsn\":-1,\"dcId\":\"\",\"backupStatus\":3,\"arriveUtc\":1709973167373,\"changeFlag\":1,\"modifyDbUtc\":0,\"vdcId\":\"\",\"nativeMoName\":\"kwephis480372\",\"workOrderUtc\":0,\"severity\":2,\"ckSource\":0,\"merged\":2,\"matchKey\":\"MODN:7dc08f7da8c60cd26229f^_^OBJECTINSTANCE:主机名称=kwephis480372,主机IP=***********,NTP_SMART_SERVER time synchronize exception^_^ALARMID:60210036\",\"extParams\":{\"toggleStatus\":\"0\",\"toggleCount\":\"0\",\"clearArriveUtc\":\"1709973781444\",\"changedFields\":\"cleared,clearType,changeFlag,clearArriveUtc,category,clearUtc,modifyDbUtc,clearTime,clearUser\",\"i2000_virtualNE\":\"1\",\"i2000_WorksheetStatus\":\"-1\"},\"shardId\":145,\"latestOccurDst\":0,\"firstOccurUtc\":1709733360243,\"locParseFlag\":0,\"regionId\":\"1\",\"meName\":\"kwephis480372\",\"clearUser\":\"< 网元 >\",\"nativeMeDn\":\"7dc08f7da8c60cd26229f\",\"vdcName\":\"\",\"category\":1,\"csn\":189185070}]";
        List<AlarmDetail> alarmDetailList = JSONArray.parseArray(alarmNotice, AlarmDetail.class);
        Exception ex = null;
        try {
            notificationService.noticeBusinessTopo(alarmDetailList);
        } catch (ServiceException e) {
            ex = e;
        }

        BusinessTopoInsTreeNode treeNode = new BusinessTopoInsTreeNode();
        treeNode.setDn("dn");
        treeNode.setVersion("1");
        treeNode.setMoTypeMapping("moTypeMapping");
        treeNode.setMoName("moName");
        treeNode.setNodeServiceStatus("nodeServiceStatus");
        treeNode.setNodeDisplayType("nodeDisplay");
        treeNode.setNodeIp("NodeIp");
        Map<String, List<BusinessTopoInsTreeNode>> dissociationDbInsMap = Collections.singletonMap("gauss", Collections.singletonList(treeNode));
        ConfigurationImportEntity configurationImportEntity = new ConfigurationImportEntity();
        configurationImportEntity.getBusinessAppInstanceConfig().setModelType(5);
        Map<String, String> moTypeMappingToModelName = new HashMap<>();
        CbsBesTopoImporter cbsBesTopoImporter = new CbsBesTopoImporter();
        Whitebox.invokeMethod(cbsBesTopoImporter, "instanceDissociationDbIns",
            dissociationDbInsMap, configurationImportEntity, moTypeMappingToModelName);

        BusinessInstanceModelDB model = modelDao.queryInstanceByInstanceId(100610);
        BusinessInsExtentAttrDB alarmCount = model.getExtentAttr("AlarmCount");
        Assert.assertNull(ex);
        Assert.assertTrue(Integer.parseInt(alarmCount.getAttrValue()) > 0);
    }

    @Test
    public void testNoticeEvent() {
        String alarmNotice
            = "[{\"userData\":\"{\\\"agentSource\\\":\\\"Agent:RESTFUL_UNIAGENT=352c\\\",\\\"isVNF\\\":\\\"false\\\",\\\"alarmProtocolType\\\":\\\"RestFm\\\",\\\"objectInstance\\\":\\\"NTP_SMART_SERVER time synchronize exception\\\"}\",\"mergeGroupId\":188181316,\"moi\":\"主机名称=kwephis480372,主机IP=***********,NTP_SMART_SERVER time synchronize exception\",\"occurTime\":1710001963120,\"clearUtc\":1709973776560,\"firstOccurDst\":0,\"productName\":\"云虚拟机\",\"backedupOnSource\":0,\"forwardType\":0,\"clearType\":1,\"tenant\":\"DefaultOrganization\",\"mergeKey\":\"SZCtEQ0ZF6tbQ7GC0TbIxg\",\"identifier\":0,\"clearDst\":0,\"originSystemType\":\"DigitalView\",\"eventType\":2,\"version\":2,\"azoneId\":\"\",\"andResetUpdateFromMergeDel\":false,\"logicalRegionName\":\"\",\"forwardUUIDKey\":\"7fb207e3-afc8-41b5-98a2-67604248210c\",\"deviceTypeId\":\"com.huawei.IRes.vm\",\"tampered\":false,\"updateFromMergeDel\":false,\"meType\":\"DVNetworkElement\",\"subCsn\":110975,\"changedFields\":[\"cleared\",\"clearType\",\"clearArriveUtc\",\"category\",\"clearUtc\",\"modifyDbUtc\",\"clearDst\",\"clearTime\",\"clearUser\"],\"aggrStatus\":0,\"manufacturer\":\"Huawei\",\"commentUtc\":0,\"probableCause\":\"同步源不匹配\",\"reasonId\":64,\"ackDst\":0,\"alarmId\":\"60210036\",\"occurUtc\":1709973163120,\"workOrderStatus\":\"0\",\"endUtc\":0,\"specialAlarmStatus\":0,\"address\":\"***********\",\"clearCategory\":1,\"aggrCount\":0,\"acked\":0,\"reportNorth\":0,\"ackUtc\":0,\"logicalRegionId\":\"\",\"latestOccurTime\":1710001963120,\"meDn\":\"bcf4966a-599f-4dd3-82f4-a29160743ecc\",\"alarmDurationTime\":0,\"clearMatchStrategy\":0,\"serviceAffectedType\":0,\"tenantId\":\"default-organization-id\",\"specialClearMatchStrategy\":false,\"occurDst\":0,\"originSystemId\":\"400\",\"nativeMoDn\":\"7dc08f7da8c60cd26229f\",\"firstOccurTime\":1709762160243,\"clearTime\":1710002576560,\"resPoolId\":\"\",\"meCategory\":\"NetworkElement\",\"dcName\":\"\",\"additionalInformation\":\"主机名称=kwephis480372,主机IP=***********,Get time synchronize exception.\",\"originSystem\":\"400\",\"count\":392,\"alarmName\":\"Linux时钟同步异常\",\"resGroupId\":\"\",\"latestOccurUtc\":1709973163120,\"region\":\"cn-global-1\",\"invalidated\":0,\"processMode\":0,\"cleared\":1,\"azoneName\":\"\",\"domainSubnetId\":\"3\",\"originSystemName\":\"DigitalView\",\"resPoolName\":\"\",\"alarmGroupId\":\"cbs.billing.adapterapp.cluster\",\"subSyncCsn\":-1,\"dcId\":\"\",\"backupStatus\":3,\"arriveUtc\":1709973167373,\"changeFlag\":1,\"modifyDbUtc\":0,\"vdcId\":\"\",\"nativeMoName\":\"kwephis480372\",\"workOrderUtc\":0,\"severity\":2,\"ckSource\":0,\"merged\":2,\"matchKey\":\"MODN:7dc08f7da8c60cd26229f^_^OBJECTINSTANCE:主机名称=kwephis480372,主机IP=***********,NTP_SMART_SERVER time synchronize exception^_^ALARMID:60210036\",\"extParams\":{\"toggleStatus\":\"0\",\"toggleCount\":\"0\",\"clearArriveUtc\":\"1709973781444\",\"changedFields\":\"cleared,clearType,changeFlag,clearArriveUtc,category,clearUtc,modifyDbUtc,clearTime,clearUser\",\"i2000_virtualNE\":\"1\",\"i2000_WorksheetStatus\":\"-1\"},\"shardId\":145,\"latestOccurDst\":0,\"firstOccurUtc\":1709733360243,\"locParseFlag\":0,\"regionId\":\"1\",\"meName\":\"kwephis480372\",\"clearUser\":\"< 网元 >\",\"nativeMeDn\":\"7dc08f7da8c60cd26229f\",\"vdcName\":\"\",\"category\":3,\"csn\":189185070}]";
        List<AlarmDetail> alarmDetailList = JSONArray.parseArray(alarmNotice, AlarmDetail.class);
        alarmDetailList.forEach(alr -> alr.setInstanceId(100610));
        Exception ex = null;
        try {
            notificationService.noticeBusinessTopo(alarmDetailList);
        } catch (ServiceException e) {
            ex = e;
        }
        BusinessInstanceModelDB model = modelDao.queryInstanceByInstanceId(100610);
        BusinessInsExtentAttrDB eventCsn = model.getExtentAttr("EventCsn");
        Assert.assertNull(ex);
        Assert.assertTrue(Integer.parseInt(eventCsn.getAttrValue()) > 0);
    }

    @Test
    public void testAlarmNoticeFromRoot() {
        String alarmNotice
            = "[{\"userData\":\"{\\\"agentSource\\\":\\\"Agent:RESTFUL_UNIAGENT=352c\\\",\\\"isVNF\\\":\\\"false\\\",\\\"alarmProtocolType\\\":\\\"RestFm\\\",\\\"objectInstance\\\":\\\"NTP_SMART_SERVER time synchronize exception\\\"}\",\"mergeGroupId\":188181316,\"moi\":\"主机名称=kwephis480372,主机IP=***********,NTP_SMART_SERVER time synchronize exception\",\"occurTime\":1710001963120,\"clearUtc\":1709973776560,\"firstOccurDst\":0,\"productName\":\"云虚拟机\",\"backedupOnSource\":0,\"forwardType\":0,\"clearType\":1,\"tenant\":\"DefaultOrganization\",\"mergeKey\":\"SZCtEQ0ZF6tbQ7GC0TbIxg\",\"identifier\":0,\"clearDst\":0,\"originSystemType\":\"DigitalView\",\"eventType\":2,\"version\":2,\"azoneId\":\"\",\"andResetUpdateFromMergeDel\":false,\"logicalRegionName\":\"\",\"forwardUUIDKey\":\"7fb207e3-afc8-41b5-98a2-67604248210c\",\"deviceTypeId\":\"com.huawei.IRes.vm\",\"tampered\":false,\"updateFromMergeDel\":false,\"meType\":\"DVNetworkElement\",\"subCsn\":110975,\"changedFields\":[\"cleared\",\"clearType\",\"clearArriveUtc\",\"category\",\"clearUtc\",\"modifyDbUtc\",\"clearDst\",\"clearTime\",\"clearUser\"],\"aggrStatus\":0,\"manufacturer\":\"Huawei\",\"commentUtc\":0,\"probableCause\":\"同步源不匹配\",\"reasonId\":64,\"ackDst\":0,\"alarmId\":\"60210036\",\"occurUtc\":1709973163120,\"workOrderStatus\":\"0\",\"endUtc\":0,\"specialAlarmStatus\":0,\"address\":\"***********\",\"clearCategory\":1,\"aggrCount\":0,\"acked\":0,\"reportNorth\":0,\"ackUtc\":0,\"logicalRegionId\":\"\",\"latestOccurTime\":1710001963120,\"meDn\":\"bcf4966a-599f-4dd3-82f4-a29160743ecc\",\"alarmDurationTime\":0,\"clearMatchStrategy\":0,\"serviceAffectedType\":0,\"tenantId\":\"default-organization-id\",\"specialClearMatchStrategy\":false,\"occurDst\":0,\"originSystemId\":\"400\",\"nativeMoDn\":\"7dc08f7da8c60cd26229f\",\"firstOccurTime\":1709762160243,\"clearTime\":1710002576560,\"resPoolId\":\"\",\"meCategory\":\"NetworkElement\",\"dcName\":\"\",\"additionalInformation\":\"主机名称=kwephis480372,主机IP=***********,Get time synchronize exception.\",\"originSystem\":\"400\",\"count\":392,\"alarmName\":\"Linux时钟同步异常\",\"resGroupId\":\"\",\"latestOccurUtc\":1709973163120,\"region\":\"cn-global-1\",\"invalidated\":0,\"processMode\":0,\"cleared\":1,\"azoneName\":\"\",\"domainSubnetId\":\"3\",\"originSystemName\":\"DigitalView\",\"resPoolName\":\"\",\"alarmGroupId\":\"cbs.billing.adapterapp.cluster\",\"subSyncCsn\":-1,\"dcId\":\"\",\"backupStatus\":3,\"arriveUtc\":1709973167373,\"changeFlag\":1,\"modifyDbUtc\":0,\"vdcId\":\"\",\"nativeMoName\":\"kwephis480372\",\"workOrderUtc\":0,\"severity\":2,\"ckSource\":0,\"merged\":2,\"matchKey\":\"MODN:7dc08f7da8c60cd26229f^_^OBJECTINSTANCE:主机名称=kwephis480372,主机IP=***********,NTP_SMART_SERVER time synchronize exception^_^ALARMID:60210036\",\"extParams\":{\"toggleStatus\":\"0\",\"toggleCount\":\"0\",\"clearArriveUtc\":\"1709973781444\",\"changedFields\":\"cleared,clearType,changeFlag,clearArriveUtc,category,clearUtc,modifyDbUtc,clearTime,clearUser\",\"i2000_virtualNE\":\"1\",\"i2000_WorksheetStatus\":\"-1\"},\"shardId\":145,\"latestOccurDst\":0,\"firstOccurUtc\":1709733360243,\"locParseFlag\":0,\"regionId\":\"1\",\"meName\":\"kwephis480372\",\"clearUser\":\"< 网元 >\",\"nativeMeDn\":\"7dc08f7da8c60cd26229f\",\"vdcName\":\"\",\"category\":3,\"csn\":189185070}]";
        Exception ex = null;
        try {
            dvTopoServiceDelegate.alarmSubscription(null, alarmNotice);
        } catch (ServiceException e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }

    @Test
    public void testMulSolutionAlarm() {
        String alarmNotice
            = "[{\"userData\":\"{\\\"agentSource\\\":\\\"Agent:RESTFUL_UNIAGENT=352c\\\",\\\"isVNF\\\":\\\"false\\\",\\\"alarmProtocolType\\\":\\\"RestFm\\\",\\\"objectInstance\\\":\\\"NTP_SMART_SERVER time synchronize exception\\\"}\",\"mergeGroupId\":188181316,\"moi\":\"主机名称=kwephis480372,主机IP=***********,NTP_SMART_SERVER time synchronize exception\",\"occurTime\":1710001963120,\"clearUtc\":1709973776560,\"firstOccurDst\":0,\"productName\":\"云虚拟机\",\"backedupOnSource\":0,\"forwardType\":0,\"clearType\":1,\"tenant\":\"DefaultOrganization\",\"mergeKey\":\"SZCtEQ0ZF6tbQ7GC0TbIxg\",\"identifier\":0,\"clearDst\":0,\"originSystemType\":\"DigitalView\",\"eventType\":2,\"version\":2,\"azoneId\":\"\",\"andResetUpdateFromMergeDel\":false,\"logicalRegionName\":\"\",\"forwardUUIDKey\":\"7fb207e3-afc8-41b5-98a2-67604248210c\",\"deviceTypeId\":\"com.huawei.IRes.vm\",\"tampered\":false,\"updateFromMergeDel\":false,\"meType\":\"DVNetworkElement\",\"subCsn\":110975,\"changedFields\":[\"cleared\",\"clearType\",\"clearArriveUtc\",\"category\",\"clearUtc\",\"modifyDbUtc\",\"clearDst\",\"clearTime\",\"clearUser\"],\"aggrStatus\":0,\"manufacturer\":\"Huawei\",\"commentUtc\":0,\"probableCause\":\"同步源不匹配\",\"reasonId\":64,\"ackDst\":0,\"alarmId\":\"60210036\",\"occurUtc\":1709973163120,\"workOrderStatus\":\"0\",\"endUtc\":0,\"specialAlarmStatus\":0,\"address\":\"***********\",\"clearCategory\":1,\"aggrCount\":0,\"acked\":0,\"reportNorth\":0,\"ackUtc\":0,\"logicalRegionId\":\"\",\"latestOccurTime\":1710001963120,\"meDn\":\"bcf4966a-599f-4dd3-82f4-a29160743ecc\",\"alarmDurationTime\":0,\"clearMatchStrategy\":0,\"serviceAffectedType\":0,\"tenantId\":\"default-organization-id\",\"specialClearMatchStrategy\":false,\"occurDst\":0,\"originSystemId\":\"400\",\"nativeMoDn\":\"7dc08f7da8c60cd26229f\",\"firstOccurTime\":1709762160243,\"clearTime\":1710002576560,\"resPoolId\":\"\",\"meCategory\":\"NetworkElement\",\"dcName\":\"\",\"additionalInformation\":\"主机名称=kwephis480372,主机IP=***********,Get time synchronize exception.\",\"originSystem\":\"400\",\"count\":392,\"alarmName\":\"Linux时钟同步异常\",\"resGroupId\":\"\",\"latestOccurUtc\":1709973163120,\"region\":\"cn-global-1\",\"invalidated\":0,\"processMode\":0,\"cleared\":1,\"azoneName\":\"\",\"domainSubnetId\":\"3\",\"originSystemName\":\"DigitalView\",\"resPoolName\":\"\",\"alarmGroupId\":\"cbs.billing.adapterapp.cluster\",\"subSyncCsn\":-1,\"dcId\":\"\",\"backupStatus\":3,\"arriveUtc\":1709973167373,\"changeFlag\":1,\"modifyDbUtc\":0,\"vdcId\":\"\",\"nativeMoName\":\"kwephis480372\",\"workOrderUtc\":0,\"severity\":2,\"ckSource\":0,\"merged\":2,\"matchKey\":\"MODN:7dc08f7da8c60cd26229f^_^OBJECTINSTANCE:主机名称=kwephis480372,主机IP=***********,NTP_SMART_SERVER time synchronize exception^_^ALARMID:60210036\",\"extParams\":{\"toggleStatus\":\"0\",\"toggleCount\":\"0\",\"clearArriveUtc\":\"1709973781444\",\"changedFields\":\"cleared,clearType,changeFlag,clearArriveUtc,category,clearUtc,modifyDbUtc,clearTime,clearUser\",\"i2000_virtualNE\":\"1\",\"i2000_WorksheetStatus\":\"-1\"},\"shardId\":145,\"latestOccurDst\":0,\"firstOccurUtc\":1709733360243,\"locParseFlag\":0,\"regionId\":\"1\",\"meName\":\"kwephis480372\",\"clearUser\":\"< 网元 >\",\"nativeMeDn\":\"7dc08f7da8c60cd26229f\",\"vdcName\":\"\",\"category\":3,\"csn\":189185070}]";
        Exception ex = null;
        try {
            dvTopoServiceDelegate.alarmSubscription(null, alarmNotice);
        } catch (ServiceException e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }

    @Test
    public void testMulLevelAlarm() {
        String alarmNotice =
            "[{\"userData\":\"{\\\"agentSource\\\":\\\"Agent:RESTFUL_UNIAGENT=352c\\\",\\\"isVNF\\\":\\\"false\\\",\\\"alarmProtocolType\\\":\\\"RestFm\\\",\\\"objectInstance\\\":\\\"NTP_SMART_SERVER time synchronize exception\\\"}\",\"mergeGroupId\":188181316,\"moi\":\"主机名称=kwephis480372,主机IP=***********,NTP_SMART_SERVER time synchronize exception\",\"occurTime\":1710001963120,\"clearUtc\":1709973776560,\"firstOccurDst\":0,\"productName\":\"云虚拟机\",\"backedupOnSource\":0,\"forwardType\":0,\"clearType\":1,\"tenant\":\"DefaultOrganization\",\"mergeKey\":\"SZCtEQ0ZF6tbQ7GC0TbIxg\",\"identifier\":0,\"clearDst\":0,\"originSystemType\":\"DigitalView\",\"eventType\":2,\"version\":2,\"azoneId\":\"\",\"andResetUpdateFromMergeDel\":false,\"logicalRegionName\":\"\",\"forwardUUIDKey\":\"7fb207e3-afc8-41b5-98a2-67604248210c\",\"deviceTypeId\":\"com.huawei.IRes.vm\",\"tampered\":false,\"updateFromMergeDel\":false,\"meType\":\"DVNetworkElement\",\"subCsn\":110975,\"changedFields\":[\"cleared\",\"clearType\",\"clearArriveUtc\",\"category\",\"clearUtc\",\"modifyDbUtc\",\"clearDst\",\"clearTime\",\"clearUser\"],\"aggrStatus\":0,\"manufacturer\":\"Huawei\",\"commentUtc\":0,\"probableCause\":\"同步源不匹配\",\"reasonId\":64,\"ackDst\":0,\"alarmId\":\"60210036\",\"occurUtc\":1709973163120,\"workOrderStatus\":\"0\",\"endUtc\":0,\"specialAlarmStatus\":0,\"address\":\"***********\",\"clearCategory\":1,\"aggrCount\":0,\"acked\":0,\"reportNorth\":0,\"ackUtc\":0,\"logicalRegionId\":\"\",\"latestOccurTime\":1710001963120,\"meDn\":\"bcf4966a-599f-4dd3-82f4-a29160743ecc\",\"alarmDurationTime\":0,\"clearMatchStrategy\":0,\"serviceAffectedType\":0,\"tenantId\":\"default-organization-id\",\"specialClearMatchStrategy\":false,\"occurDst\":0,\"originSystemId\":\"400\",\"nativeMoDn\":\"7dc08f7da8c60cd26229f\",\"firstOccurTime\":1709762160243,\"clearTime\":1710002576560,\"resPoolId\":\"\",\"meCategory\":\"NetworkElement\",\"dcName\":\"\",\"additionalInformation\":\"主机名称=kwephis480372,主机IP=***********,Get time synchronize exception.\",\"originSystem\":\"400\",\"count\":392,\"alarmName\":\"Linux时钟同步异常\",\"resGroupId\":\"\",\"latestOccurUtc\":1709973163120,\"region\":\"cn-global-1\",\"invalidated\":0,\"processMode\":0,\"cleared\":1,\"azoneName\":\"\",\"domainSubnetId\":\"3\",\"originSystemName\":\"DigitalView\",\"resPoolName\":\"\",\"alarmGroupId\":\"cbs.billing.adapterapp.cluster\",\"subSyncCsn\":-1,\"dcId\":\"\",\"backupStatus\":3,\"arriveUtc\":1709973167373,\"changeFlag\":1,\"modifyDbUtc\":0,\"vdcId\":\"\",\"nativeMoName\":\"kwephis480372\",\"workOrderUtc\":0,\"severity\":2,\"ckSource\":0,\"merged\":2,\"matchKey\":\"MODN:7dc08f7da8c60cd26229f^_^OBJECTINSTANCE:主机名称=kwephis480372,主机IP=***********,NTP_SMART_SERVER time synchronize exception^_^ALARMID:60210036\",\"extParams\":{\"toggleStatus\":\"0\",\"toggleCount\":\"0\",\"clearArriveUtc\":\"1709973781444\",\"changedFields\":\"cleared,clearType,changeFlag,clearArriveUtc,category,clearUtc,modifyDbUtc,clearTime,clearUser\",\"i2000_virtualNE\":\"1\",\"i2000_WorksheetStatus\":\"-1\"},\"shardId\":145,\"latestOccurDst\":0,\"firstOccurUtc\":1709733360243,\"locParseFlag\":0,\"regionId\":\"1\",\"meName\":\"kwephis480372\",\"clearUser\":\"< 网元 >\",\"nativeMeDn\":\"7dc08f7da8c60cd26229f\",\"vdcName\":\"\",\"category\":1,\"csn\":189185070},"
                + "{\"userData\":\"{\\\"agentSource\\\":\\\"Agent:RESTFUL_UNIAGENT=352c\\\",\\\"isVNF\\\":\\\"false\\\",\\\"alarmProtocolType\\\":\\\"RestFm\\\",\\\"objectInstance\\\":\\\"NTP_SMART_SERVER time synchronize exception\\\"}\",\"mergeGroupId\":188181316,\"moi\":\"主机名称=kwephis480372,主机IP=***********,NTP_SMART_SERVER time synchronize exception\",\"occurTime\":1710001963120,\"clearUtc\":1709973776560,\"firstOccurDst\":0,\"productName\":\"云虚拟机\",\"backedupOnSource\":0,\"forwardType\":0,\"clearType\":1,\"tenant\":\"DefaultOrganization\",\"mergeKey\":\"SZCtEQ0ZF6tbQ7GC0TbIxg\",\"identifier\":0,\"clearDst\":0,\"originSystemType\":\"DigitalView\",\"eventType\":2,\"version\":2,\"azoneId\":\"\",\"andResetUpdateFromMergeDel\":false,\"logicalRegionName\":\"\",\"forwardUUIDKey\":\"7fb207e3-afc8-41b5-98a2-67604248210c\",\"deviceTypeId\":\"com.huawei.IRes.vm\",\"tampered\":false,\"updateFromMergeDel\":false,\"meType\":\"DVNetworkElement\",\"subCsn\":110975,\"changedFields\":[\"cleared\",\"clearType\",\"clearArriveUtc\",\"category\",\"clearUtc\",\"modifyDbUtc\",\"clearDst\",\"clearTime\",\"clearUser\"],\"aggrStatus\":0,\"manufacturer\":\"Huawei\",\"commentUtc\":0,\"probableCause\":\"同步源不匹配\",\"reasonId\":64,\"ackDst\":0,\"alarmId\":\"60210036\",\"occurUtc\":1709973163120,\"workOrderStatus\":\"0\",\"endUtc\":0,\"specialAlarmStatus\":0,\"address\":\"***********\",\"clearCategory\":1,\"aggrCount\":0,\"acked\":0,\"reportNorth\":0,\"ackUtc\":0,\"logicalRegionId\":\"\",\"latestOccurTime\":1710001963120,\"meDn\":\"bcf4966a-599f-4dd3-82f4-a29160743ecc\",\"alarmDurationTime\":0,\"clearMatchStrategy\":0,\"serviceAffectedType\":0,\"tenantId\":\"default-organization-id\",\"specialClearMatchStrategy\":false,\"occurDst\":0,\"originSystemId\":\"400\",\"nativeMoDn\":\"7dc08f7da8c60cd26229f\",\"firstOccurTime\":1709762160243,\"clearTime\":1710002576560,\"resPoolId\":\"\",\"meCategory\":\"NetworkElement\",\"dcName\":\"\",\"additionalInformation\":\"主机名称=kwephis480372,主机IP=***********,Get time synchronize exception.\",\"originSystem\":\"400\",\"count\":392,\"alarmName\":\"Linux时钟同步异常\",\"resGroupId\":\"\",\"latestOccurUtc\":1709973163120,\"region\":\"cn-global-1\",\"invalidated\":0,\"processMode\":0,\"cleared\":1,\"azoneName\":\"\",\"domainSubnetId\":\"3\",\"originSystemName\":\"DigitalView\",\"resPoolName\":\"\",\"alarmGroupId\":\"cbs.billing.adapterapp.cluster\",\"subSyncCsn\":-1,\"dcId\":\"\",\"backupStatus\":3,\"arriveUtc\":1709973167373,\"changeFlag\":1,\"modifyDbUtc\":0,\"vdcId\":\"\",\"nativeMoName\":\"kwephis480372\",\"workOrderUtc\":0,\"severity\":2,\"ckSource\":0,\"merged\":2,\"matchKey\":\"MODN:7dc08f7da8c60cd26229f^_^OBJECTINSTANCE:主机名称=kwephis480372,主机IP=***********,NTP_SMART_SERVER time synchronize exception^_^ALARMID:60210036\",\"extParams\":{\"toggleStatus\":\"0\",\"toggleCount\":\"0\",\"clearArriveUtc\":\"1709973781444\",\"changedFields\":\"cleared,clearType,changeFlag,clearArriveUtc,category,clearUtc,modifyDbUtc,clearTime,clearUser\",\"i2000_virtualNE\":\"1\",\"i2000_WorksheetStatus\":\"-1\"},\"shardId\":145,\"latestOccurDst\":0,\"firstOccurUtc\":1709733360243,\"locParseFlag\":0,\"regionId\":\"1\",\"meName\":\"kwephis480372\",\"clearUser\":\"< 网元 >\",\"nativeMeDn\":\"7dc08f7da8c60cd26230f\",\"vdcName\":\"\",\"category\":1,\"csn\":189185071}]";
        List<AlarmDetail> alarmDetailList = JSONArray.parseArray(alarmNotice, AlarmDetail.class, JSONReader.Feature.SupportSmartMatch);
        alarmDetailList.forEach(alr -> alr.setInstanceId(100610));
        Exception ex = null;
        try {
            notificationService.noticeBusinessTopo(alarmDetailList);
        } catch (ServiceException e) {
            ex = e;
        }
        BusinessInstanceModelDB model = modelDao.queryInstanceByInstanceId(10031);
        BusinessInsExtentAttrDB alarmCount = model.getExtentAttr("AlarmCount");
        Assert.assertNull(ex);
        Assert.assertTrue(Integer.parseInt(alarmCount.getAttrValue()) > 1);
    }

    @Test
    public void testClearInOnceNoticeAlarm() {
        List<AlarmDetail> alarmDetailList = new ArrayList<>();
        prepareOccurAlarm(alarmDetailList);
        prepareClearOnceAlarm(alarmDetailList);
        Exception ex = null;
        try {
            notificationService.noticeBusinessTopo(alarmDetailList);
        } catch (ServiceException e) {
            ex = e;
        }
        BusinessInstanceModelDB model = modelDao.queryInstanceByInstanceId(100610);
        BusinessInsExtentAttrDB alarmCount = model.getExtentAttr("AlarmCount");
        Assert.assertNull(ex);
    }

    @Test
    public void testClearExistNoticeAlarm() {
        List<AlarmDetail> alarmDetailList = new ArrayList<>();
        prepareOccurAlarm(alarmDetailList);
        prepareClearAlarm(alarmDetailList);
        Exception ex = null;
        try {
            notificationService.noticeBusinessTopo(alarmDetailList);
        } catch (ServiceException e) {
            ex = e;
        }
        BusinessInstanceModelDB model = modelDao.queryInstanceByInstanceId(10051);
        BusinessInsExtentAttrDB alarmCount = model.getExtentAttr("AlarmCount");
        Assert.assertNull(ex);
        Assert.assertEquals(1, Integer.parseInt(alarmCount.getAttrValue()));
    }

    @Test
    public void testCloudDrNoticeAlarm() {
        List<AlarmDetail> alarmDetailList = new ArrayList<>();
        prepareMoTypeAlarm(alarmDetailList);
        notificationService.noticeBusinessTopoByAlarmId(alarmDetailList);
        Assert.assertFalse(alarmDetailList.isEmpty());
    }

    @Test
    public void testAlarmLevelAndClearType() {
        List<AlarmDetail> alarmDetailList = new ArrayList<>();
        prepareMinorAndADACAlarm(alarmDetailList);
        Exception ex = null;
        try {
            notificationService.noticeBusinessTopo(alarmDetailList);
        } catch (ServiceException e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }

    @Test
    public void testThresholdsAlarm() {
        mockVmProperties();
        List<AlarmDetail> alarmDetailList = new ArrayList<>();
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getpmthresholdalarm.json");
        performanceThresholdAlarmService.thresholdAlarmCronTask();
        mockServer.loadScenarioFiles("module.analysis/rest_mappings_getanalysisresult.json");
        analysisIndQueryTimer.queryAndCacheIndList();
        prepareThresholdsAlarm(alarmDetailList);
        Exception ex = null;
        try {
            notificationService.noticeBusinessThresholdsAlarm(alarmDetailList);
        } catch (ServiceException e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }

    @Test
    public void testParseObject() {
        mockProperties();
        String manageObjectColumn = "Measurement object=";
        String[] alarmParseKeys = new String[] {
            "Alarm Type=", "Measurement unit=", "Measurement counter=", "Measurement object=",
            "UserInputInformation="
        };
        String input1
            = "Alarm Type=AIOps service anomaly detection alarm, Measurement unit=License Total TPS(Stripe),Measurement counter=Total TPS(tps), Measurement object=*************, UserInputInformation=null";
        String value1 = notificationService.extractValueByKeyOrder(input1, alarmParseKeys, manageObjectColumn);
        Assert.assertEquals("*************", value1);

        String input2
            = "Alarm Type=Fixed Threshold Alarm, Measurement unit=Container Statistics, Measurement counter=Instantaneous CPU Usage, Measurement object=173d0c6fd833,q1s2-q1s2-vst100csu01pr2g-nslb,-,-,-, IP=************";
        String value2 = notificationService.extractValueByKeyOrder(input2, alarmParseKeys, manageObjectColumn);
        Assert.assertEquals("173d0c6fd833,q1s2-q1s2-vst100csu01pr2g-nslb,-,-,-, IP=************", value2);

        String input3
            = "Alarm Type=Fixed Threshold Alarm, Measurement unit=TESTBCD, Measurement counter=BBB, Measurement object=Z, DefaultSite_DefaultGroup";
        String value3 = notificationService.extractValueByKeyOrder(input3, alarmParseKeys, manageObjectColumn);
        Assert.assertEquals("Z, DefaultSite_DefaultGroup", value3);
    }

    private void prepareMinorAndADACAlarm(List<AlarmDetail> alarmDetails) {
        AlarmDetail updateAlarm = new AlarmDetail();
        updateAlarm.setCsn(209185070);
        updateAlarm.setCategory(1);
        updateAlarm.setClearCategory(1);
        updateAlarm.setSeverity(1);
        updateAlarm.setNativeMeDn("7dc08f7da8c60cd26229f");
        updateAlarm.setAlarmId(407029000);
        alarmDetails.add(updateAlarm);
    }

    private void prepareOccurAlarm(List<AlarmDetail> alarmDetails) {
        AlarmDetail updateAlarm = new AlarmDetail();
        updateAlarm.setCsn(209185070);
        updateAlarm.setCategory(1);
        updateAlarm.setNativeMeDn("7dc08f7da8c60cd26229f");
        updateAlarm.setAlarmId(407029000);
        alarmDetails.add(updateAlarm);
    }

    private void prepareClearOnceAlarm(List<AlarmDetail> alarmDetails) {
        AlarmDetail clearedAlarm = new AlarmDetail();
        clearedAlarm.setCsn(209185070);
        clearedAlarm.setCategory(2);
        clearedAlarm.setNativeMeDn("7dc08f7da8c60cd26229f");
        clearedAlarm.setAlarmId(407029000);
        alarmDetails.add(clearedAlarm);
    }

    private void prepareClearAlarm(List<AlarmDetail> alarmDetails) {
        AlarmDetail clearedAlarm = new AlarmDetail();
        clearedAlarm.setCsn(189185070);
        clearedAlarm.setCategory(2);
        clearedAlarm.setNativeMeDn("7dc08f7da8c60cd26229f");
        clearedAlarm.setAlarmId(60210036);
        alarmDetails.add(clearedAlarm);
    }

    private void prepareMoTypeAlarm(List<AlarmDetail> alarmDetails) {
        AlarmDetail moTypeAlarm = new AlarmDetail();
        moTypeAlarm.setCategory(1);
        moTypeAlarm.setAlarmId(407029000);
        moTypeAlarm.setCsn(1561687);
        moTypeAlarm.setAlarmGroupId("com.huawei.clouddr.node");
        alarmDetails.add(moTypeAlarm);
    }

    private void prepareThresholdsAlarm(List<AlarmDetail> alarmDetails) {
        AlarmDetail thresholdsAlarm = new AlarmDetail();
        thresholdsAlarm.setCategory(1);
        thresholdsAlarm.setAlarmId(12307);
        thresholdsAlarm.setCsn(15555555);
        thresholdsAlarm.setNativeMeDn("OS=1");
        thresholdsAlarm.setMoi(
            "Alarm Type=Fixed Threshold Alarm, "
                + "Measurement unit=I2K_OS, "
                + "Measurement counter=IOWait, "
                + "Measurement object=*************");

        AlarmDetail analysisAlarm = new AlarmDetail();
        analysisAlarm.setCategory(1);
        analysisAlarm.setAlarmId(505410010);
        analysisAlarm.setCsn(16666666);
        analysisAlarm.setNativeMeDn("OS=1");
        analysisAlarm.setMoi(
            "Alarm Type=AIOps service anomaly detection alarm, "
                + "Measurement unit=License Total TPS(Stripe), "
                + "Measurement counter=Total TPS(tps), "
                + "Measurement object=*************,"
                + " UserInputInformation=null");

        alarmDetails.add(thresholdsAlarm);
        alarmDetails.add(analysisAlarm);
    }

    public void mockAlarmInterface() {
        mockServer.loadScenarioFiles("module.alarm/rest_mappings-getalarmdate.json");
    }

    public void mockAnalysisJumpingInterface() {
        mockServer.loadScenarioFiles("module.analysis/rest_mappings_getanalysisresult.json");
    }

    @Test
    public void testVanishedAlarmClear() {
        List<AlarmDetail> alarmDetailList = new ArrayList<>();
        prepareVanishedAlarmList(alarmDetailList);
        Exception ex = null;
        try {
            notificationService.noticeBusinessTopo(alarmDetailList);
        } catch (ServiceException e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }

    private void prepareVanishedAlarmList(List<AlarmDetail> vanishedAlarmDetails) {
        AlarmDetail vanishedAlarm = new AlarmDetail();
        vanishedAlarm.setCsn(189185070);
        vanishedAlarm.setCategory(2);
        vanishedAlarm.setInstanceId(100610);
        vanishedAlarm.setIsVanished(true);
        vanishedAlarmDetails.add(vanishedAlarm);
    }

    @Test
    public void testOccurAndClearAlarm() {
        List<AlarmDetail> alarmDetailList = new ArrayList<>();
        prepareOccurAlarmInShort(alarmDetailList);
        prepareClearAlarmInShort(alarmDetailList);
        Exception ex = null;
        try {
            notificationService.noticeBusinessTopo(alarmDetailList);
        } catch (ServiceException e) {
            ex = e;
        }
        BusinessInstanceModelDB model = modelDao.queryInstanceByInstanceId(100610);
        BusinessInsExtentAttrDB alarmCount = model.getExtentAttr("AlarmCsn");
        Assert.assertNull(ex);
        Assert.assertFalse(alarmCount.getAttrValue().contains("666666666"));
    }

    private void prepareOccurAlarmInShort(List<AlarmDetail> alarmDetails) {
        AlarmDetail updateAlarm = new AlarmDetail();
        updateAlarm.setCsn(666666666);
        updateAlarm.setCategory(1);
        updateAlarm.setNativeMeDn("7dc08f7da8c60cd26229f");
        updateAlarm.setAlarmId(60210036);
        alarmDetails.add(updateAlarm);
    }

    private void prepareClearAlarmInShort(List<AlarmDetail> alarmDetails) {
        AlarmDetail updateAlarm = new AlarmDetail();
        updateAlarm.setCsn(666666666);
        updateAlarm.setCategory(2);
        updateAlarm.setNativeMeDn("7dc08f7da8c60cd26229f");
        updateAlarm.setAlarmId(60210036);
        alarmDetails.add(updateAlarm);
    }

    @Test
    public void testOneLvAlarmDetail() {
        AlarmStatisticParam param = new AlarmStatisticParam();
        param.setInsLevel(6);
        param.setInstanceId(10001);
        AlarmCountResult result = commonService.queryAlarmCountByLevel(param);
        List<AlarmQueryData> alarmQueryDataList = result.getAlarmDataList();
        Assert.assertTrue(CollectionUtils.isNotEmpty(alarmQueryDataList));
        AtomicBoolean existAlarm = new AtomicBoolean(false);
        alarmQueryDataList.forEach(alarm -> {
            if (CollectionUtils.isNotEmpty(result.getAlarmDataList())) {
                existAlarm.set(true);
            }
        });
        Assert.assertTrue(existAlarm.get());

        AlarmStatisticParam timeLineParam = new AlarmStatisticParam();
        timeLineParam.setInstanceId(10001);
        timeLineParam.setInsLevel(6);
        timeLineParam.setTimestamp(1717728000000L);
        AlarmCountResult timeResult = commonService.queryAlarmCountByLevel(timeLineParam);
        List<AlarmQueryData> alarmTimeQueryDataList = timeResult.getAlarmDataList();
        Assert.assertTrue(CollectionUtils.isNotEmpty(alarmTimeQueryDataList));
        AtomicBoolean existTimeAlarm = new AtomicBoolean(false);
        alarmTimeQueryDataList.forEach(alarm -> {
            if (CollectionUtils.isNotEmpty(timeResult.getAlarmDataList())) {
                existTimeAlarm.set(true);
            }
        });
        Assert.assertTrue(existTimeAlarm.get());
    }

    @Test
    public void testTimeLineNotice() {
        List<AlarmDetail> alarmDetailList = new ArrayList<>();
        prepareMinorAndADACAlarm(alarmDetailList);
        Exception ex = null;
        try {
            notificationService.noticeTimeLineAlarm(alarmDetailList);
            LocalDateTime fixedDateTime = LocalDateTime.of(2024, 4, 21, 0, 0, 0);
            PowerMockito.mockStatic(LocalDateTime.class);
            PowerMockito.when(LocalDateTime.now()).thenReturn(fixedDateTime);
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }
}
