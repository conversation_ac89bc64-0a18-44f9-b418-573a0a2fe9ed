/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.kafka;

import com.huawei.cloudsop.mq.api.factory.ProducerFactory;
import com.huawei.cloudsop.mq.api.producer.IProducer;
import com.huawei.cloudsop.mq.api.producer.IProducerRecord;
import com.huawei.i2000.dvtoposervice.WebServiceTest;

import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.test.context.ContextConfiguration;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/7/16
 */

@PrepareForTest({ProducerFactory.class, IProducer.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class KafkaUtilsTest extends WebServiceTest {

    @Test
    @SuppressWarnings("unchecked")
    public void getAlarmProducerTest() {
        ProducerFactory factory = PowerMockito.mock(ProducerFactory.class);
        PowerMockito.mockStatic(ProducerFactory.class);
        PowerMockito.when(ProducerFactory.getInstance()).thenReturn(factory);
        IProducer<Object, Object> iProducer = PowerMockito.mock(IProducer.class);
        IProducerRecord<String, String> record = PowerMockito.mock(IProducerRecord.class);
        PowerMockito.when(factory.getProducer(Mockito.anyMap())).thenReturn(null).thenReturn(iProducer);
        KafkaProducer alarmProducer = KafkaUtils.getAlarmProducer();
        alarmProducer.send("test");
        ProducerCallback producerCallback = new ProducerCallback(record);
        producerCallback.onCompletion(null, new Exception(), record);
        Assert.assertNotNull(alarmProducer);
    }

    @Test
    @SuppressWarnings("unchecked")
    public void getMoEventProducerTest() {
        ProducerFactory factory = PowerMockito.mock(ProducerFactory.class);
        PowerMockito.mockStatic(ProducerFactory.class);
        PowerMockito.when(ProducerFactory.getInstance()).thenReturn(factory);
        IProducer<Object, Object> iProducer = PowerMockito.mock(IProducer.class);
        IProducerRecord<String, String> record = PowerMockito.mock(IProducerRecord.class);
        PowerMockito.when(factory.getProducer(Mockito.anyMap())).thenReturn(null).thenReturn(iProducer);
        KafkaProducer moEventProducer = KafkaUtils.getMoEventProducer();
        moEventProducer.send("test");
        ProducerCallback producerCallback = new ProducerCallback(record);
        producerCallback.onCompletion(null, new Exception(), record);
        Assert.assertNotNull(moEventProducer);
    }
}
