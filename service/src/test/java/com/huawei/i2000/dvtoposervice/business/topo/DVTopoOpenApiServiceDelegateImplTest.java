/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.topo;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.impl.DVTopoOpenApiServiceDelegateImpl;
import com.huawei.i2000.dvtoposervice.model.Nodes;
import com.huawei.i2000.dvtoposervice.model.NodesCondition;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * DVTopoOpenApiServiceDelegateImplTest
 *
 * <AUTHOR>
 * @since 2024-11-19 17:32:17
 */
@PrepareForTest({PropertiesUtil.class,ContextUtils.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class DVTopoOpenApiServiceDelegateImplTest extends WebServiceTest {

    private static EmbeddedRestClientAndServer mockServer;

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }
    @Autowired
    private DVTopoOpenApiServiceDelegateImpl dvTopoOpenApiServiceDelegate;

    @Test
    public void getNodesByParentDnTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        Set<String> set = new HashSet<>();
        set.add("NE=3");
        contextUtils.setAdmin(false);
        contextUtils.setAuthDns(set);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        NodesCondition condition = new NodesCondition();
        condition.setTopoId(new ArrayList<>(Arrays.asList("NE=1", "NE=2")));
        Nodes nodes = dvTopoOpenApiServiceDelegate.getNodesByParentDn(new MockHttpContext(), condition);
        Assert.assertEquals(0,nodes.getResultData().size());
    }
}
