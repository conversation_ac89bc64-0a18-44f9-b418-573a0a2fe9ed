/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmDetail;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.openapi.alarm.BusinessAlarmInitService;
import com.huawei.i2000.dvtoposervice.business.openapi.relation.BusinessTopoRelationServiceImpl;
import com.huawei.i2000.dvtoposervice.business.openapi.relation.model.AssociationOfMo;
import com.huawei.i2000.dvtoposervice.business.openapi.relation.model.DnRelation;
import com.huawei.i2000.dvtoposervice.business.openapi.relation.model.MoOfGroup;
import com.huawei.i2000.dvtoposervice.business.pm.AlarmRequestService;
import com.huawei.i2000.dvtoposervice.business.service.bean.JumpingPath;
import com.huawei.i2000.dvtoposervice.business.service.bean.MoInsPath;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.TopoImportSolutionAdapter;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessIndicatorDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.impl.BusinessCommonServiceDelegateImpl;
import com.huawei.i2000.dvtoposervice.model.AlarmCountResult;
import com.huawei.i2000.dvtoposervice.model.AlarmStatisticParam;
import com.huawei.i2000.dvtoposervice.model.AlarmTimeCountData;
import com.huawei.i2000.dvtoposervice.model.BusinessIndicator;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.model.MonitorServiceStatusModel;
import com.huawei.i2000.dvtoposervice.model.SolutionExParam;
import com.huawei.i2000.dvtoposervice.model.TimelineQueryParam;
import com.huawei.i2000.dvtoposervice.util.AuthUtils;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.EamUtil;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.dvtoposervice.util.SystemLogUtil;
import com.huawei.i2000.dvtopowebsite.model.InsOfOneMomentParam;
import com.huawei.i2000.dvtopowebsite.model.JumpingParam;
import com.huawei.i2000.dvtopowebsite.model.SearchMoParam;
import com.huawei.i2000.dvtopowebsite.model.TimelineAlarmQueryParam;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.i2000.gapi.account.mo.AccountMO;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.ManagedObject;

import org.apache.commons.collections4.CollectionUtils;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

/**
 * 业务拓扑通用测试类
 *
 * <AUTHOR>
 * @since 2024/04/22
 */
@PrepareForTest( {TopoImportSolutionAdapter.class, ContextUtils.class, PropertiesUtil.class, MITManagerClient.class, LocalDateTime.class, BusinessTopoCommonServiceImpl.class, EamUtil.class, AuthUtils.class, SystemLogUtil.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessTopoCommonServiceImplTest extends WebServiceTest {

    private static EmbeddedRestClientAndServer mockServer;

    private static final MapOper<ConfigData> BUSINESS_CONFIG_MAP_OPER = new MapOper<>(
        RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    BusinessCommonServiceDelegateImpl impl;

    @Autowired
    BusinessInstanceModelDao modelDao;

    @Autowired
    @InjectMocks
    BusinessTopoCommonServiceImpl businessTopoCommonService;

    @Autowired
    BusinessAlarmInitService businessAlarmInitService;

    @Mock
    AlarmRequestService alarmRequestService;

    @Autowired
    private BusinessIndicatorDao businessIndicatorDao;

    @Autowired
    private BusinessTopoRelationServiceImpl relationService;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_common.sql", false);
        mockProperties();
        mockConfigData();
        // 初始化告警
        businessAlarmInitService.initBusinessTopoAlarm();
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    public void mockAnalysisJumpingInterface() {
        mockServer.loadScenarioFiles("module.analysis/rest_mappings_getanalysisresult.json");
    }

    private void mockProperties() {
        PowerMockito.mockStatic(PropertiesUtil.class);
        Properties properties = new Properties();
        properties.put(BusinessTopoConstant.CLUSTER_TYPE, "6");
        properties.put(BusinessTopoConstant.POD_TYPE, "7");
        PowerMockito.when(PropertiesUtil.loadProperties(Mockito.anyString())).thenReturn(properties);
    }

    private void mockConfigData() {
        MapOper<ConfigData> businessConfigMapOper = new MapOper<>(
            RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("timelinerange");
        configData.setValue("24");

        ConfigData configAlarmData = new ConfigData();
        configAlarmData.setConfigItemName("alarmSubscribeLevel");
        configAlarmData.setValue("4");

        Map<String, ConfigData> configDataList = new HashMap<>();
        configDataList.put("timelinerange", configData);
        configDataList.put("alarmSubscribeLevel", configAlarmData);
        businessConfigMapOper.putAll(RedisConstant.BUSINESS_TOPO_CONFIG, configDataList);
    }

    @Test
    public void backUpTimeLineTest() {
        // preset
        LocalDateTime fixedDateTime = LocalDateTime.of(2024, 4, 21, 0, 0, 0);
        PowerMockito.mockStatic(LocalDateTime.class);
        PowerMockito.when(LocalDateTime.now()).thenReturn(fixedDateTime);

        // test
        impl.backUpTimeLine(null);

        // assert
        List<String> tableNames = modelDao.queryBackUpTimeLineTables();
        Assert.assertNotNull(tableNames);
    }

    @Test
    public void refreshBusinessTopoTest() throws Exception {
        PowerMockito.mockStatic(SystemLogUtil.class);
        PowerMockito.doNothing().when(SystemLogUtil.class, "recordSystemLog", Mockito.anyString(), Mockito.anyString(), Mockito.any());

        PowerMockito.mockStatic(EamUtil.class);
        List<ManagedObject> mos = new ArrayList<>();
        ManagedObject managedObjectSite = new AccountMO();
        managedObjectSite.setLogicSite("1");
        managedObjectSite.putClientProperty("siteId", "1");
        mos.add(managedObjectSite);
        PowerMockito.when(EamUtil.queryMosByMoType("cbs.billing.cbs")).thenReturn(mos);
        PowerMockito.when(EamUtil.queryMosByMoType("com.huawei.IRes.vm")).thenReturn(mos);

        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        ManagedObject managedObject = new AccountMO();
        managedObject.setDN(new DN("siteDn1"));
        managedObject.setParent(new DN("siteDn"));
        List<ManagedObject> moList = new ArrayList<>();
        moList.add(managedObject);
        PowerMockito.when(mitManagerClient.getMoByType(Mockito.anyString())).thenReturn(moList);

        // test
        impl.refreshBusinessTopo(null);

        // assert
        List<BusinessInstanceModelDB> instances = modelDao.queryAllInstance();
        Assert.assertTrue(instances.size() > 1);
    }

    @Test
    public void queryAlarmCountStatisticNotAdminTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        Set<String> set = new HashSet<>();
        set.add("28f59c78-64b5-4b58-949c-891cc8ec43bf");
        contextUtils.setAdmin(false);
        contextUtils.setAuthDns(set);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        AlarmStatisticParam param = new AlarmStatisticParam();
        param.setTimestamp(1L);
        AlarmCountResult result = impl.queryAlarmCountStatistic(null, param);
        Assert.assertEquals(0, result.getAlarmDataList().size());
    }

    @Test
    public void queryTimeLineTest() throws ServiceException {
        mockConfigData();
        LocalDateTime fixedDateTime = LocalDateTime.of(2024, 4, 21, 0, 0, 0);
        PowerMockito.mockStatic(LocalDateTime.class);
        PowerMockito.when(LocalDateTime.now()).thenReturn(fixedDateTime);

        TimelineQueryParam timelineQueryParam = new TimelineQueryParam();
        timelineQueryParam.setInstanceId(0);
        AlarmCountResult result = impl.queryTimeLine(null, timelineQueryParam);
        List<AlarmTimeCountData> timeCountData = result.getAlarmTimeCountList();
        Assert.assertFalse(timeCountData.isEmpty());

        TimelineQueryParam timelineSiteQueryParam = new TimelineQueryParam();
        timelineSiteQueryParam.setInstanceId(1);
        AlarmCountResult siteResult = impl.queryTimeLine(null, timelineSiteQueryParam);
        List<AlarmTimeCountData> siteTimeCountData = siteResult.getAlarmTimeCountList();
        Assert.assertFalse(siteTimeCountData.isEmpty());

        TimelineQueryParam timelineMoQueryParam = new TimelineQueryParam();
        timelineMoQueryParam.setInstanceId(10051);
        AlarmCountResult moResult = impl.queryTimeLine(null, timelineMoQueryParam);
        List<AlarmTimeCountData> moTimeCountData = moResult.getAlarmTimeCountList();
        Assert.assertFalse(moTimeCountData.isEmpty());

        timelineMoQueryParam.setStripeUnit("pr1");
        AlarmCountResult moResult2 = impl.queryTimeLine(null, timelineMoQueryParam);
        List<AlarmTimeCountData> moTimeCountData2 = moResult2.getAlarmTimeCountList();
        Assert.assertTrue(moTimeCountData2.isEmpty());


        TimelineQueryParam timelineStripeQueryParam = new TimelineQueryParam();
        timelineStripeQueryParam.setInstanceId(10021);
        AlarmCountResult stripeResult = impl.queryTimeLine(null, timelineStripeQueryParam);
        List<AlarmTimeCountData> stripeTimeCountData = stripeResult.getAlarmTimeCountList();
        Assert.assertFalse(stripeTimeCountData.isEmpty());
    }

    @Test
    public void testQueryTimeLineAlarmDetail() {
        mockAnalysisJumpingInterface();
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(false);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        Set<String> dns = new HashSet<>();
        dns.add("7dc08f73c7fb1a24c30b8");
        PowerMockito.mockStatic(AuthUtils.class);
        PowerMockito.when(AuthUtils.getAuthDns()).thenReturn(dns);
        TimelineAlarmQueryParam timeLineAlarmDetail = new TimelineAlarmQueryParam();
        timeLineAlarmDetail.setInstanceId(0);
        LocalDateTime fixedDateTime = LocalDateTime.of(2024, 4, 21, 0, 0, 0);
        Long startTime = Timestamp.valueOf(fixedDateTime).getTime();
        timeLineAlarmDetail.setTimestamp(startTime);
        List<AlarmDetail> timeLineDetails = businessTopoCommonService.queryTimeLineAlarmDetail(timeLineAlarmDetail);
        Assert.assertTrue(CollectionUtils.isNotEmpty(timeLineDetails));
    }

    @Test
    public void updateAlarmStatusTimed() throws Exception {
        List<AlarmDetail> historyAlarm = new ArrayList<>();
        AlarmDetail alarmDetail = new AlarmDetail();
        alarmDetail.setCsn(188139088);
        alarmDetail.setCleared(1);
        historyAlarm.add(alarmDetail);
        Mockito.when(alarmRequestService.scrollQueryHistoryAlarmCsnResult(Mockito.anyList())).thenReturn(historyAlarm);

        List<AlarmDetail> currentAlarm = new ArrayList<>();
        AlarmDetail alarmCurrentDetail = new AlarmDetail();
        alarmCurrentDetail.setCsn(188139089);
        alarmCurrentDetail.setCleared(1);
        currentAlarm.add(alarmCurrentDetail);
        Mockito.when(alarmRequestService.scrollQueryAlarmCsnResult(Mockito.anyList())).thenReturn(currentAlarm);
        Exception exp = null;
        try {
            businessTopoCommonService.updateAlarmStatusTimed();
        } catch (Exception e) {
            exp = e;
        }
        Assert.assertNull(exp);
    }

    @Test
    public void queryInsOfOneMomentTest() {
        InsOfOneMomentParam insOfOneMomentParam = new InsOfOneMomentParam();
        List<Integer> instanceIdList = new ArrayList<>();
        instanceIdList.add(0);
        instanceIdList.add(10051);
        instanceIdList.add(10061);
        insOfOneMomentParam.setInstanceIdList(instanceIdList);
        insOfOneMomentParam.setTimestamp(1721641800000L);
        insOfOneMomentParam.setTargetTimestamp(1727400416000L);
        Exception exp = null;
        Map<Integer, Integer> resultMap = new HashMap<>();
        try {
            resultMap = businessTopoCommonService.queryInsOfOneMoment(insOfOneMomentParam);
        } catch (ServiceException e) {
            exp = e;
        }
        Assert.assertNull(exp);
        Assert.assertEquals(90001, (int) resultMap.get(0));
        Assert.assertEquals(90051, (int) resultMap.get(10051));
        Assert.assertEquals(90061, (int) resultMap.get(10061));
    }

    @Test
    public void queryInsOfCurrenTest() {
        InsOfOneMomentParam insOfOneMomentParam = new InsOfOneMomentParam();
        List<Integer> instanceIdList = new ArrayList<>();
        instanceIdList.add(90001);
        instanceIdList.add(90051);
        instanceIdList.add(90061);
        insOfOneMomentParam.setInstanceIdList(instanceIdList);
        insOfOneMomentParam.setTimestamp(1727400600000L);
        insOfOneMomentParam.setTargetTimestamp(0L);
        Exception exp = null;
        Map<Integer, Integer> resultMap = new HashMap<>();
        try {
            resultMap = businessTopoCommonService.queryInsOfOneMoment(insOfOneMomentParam);
        } catch (ServiceException e) {
            exp = e;
        }
        Assert.assertNull(exp);
        Assert.assertEquals(0, (int) resultMap.get(90001));

        InsOfOneMomentParam insOfOneMomentParam2 = new InsOfOneMomentParam();
        List<Integer> instanceIdList2 = new ArrayList<>();
        instanceIdList2.add(1230196);
        instanceIdList2.add(1230339);
        instanceIdList2.add(1230433);
        insOfOneMomentParam2.setInstanceIdList(instanceIdList2);
        insOfOneMomentParam2.setTimestamp(1727400600000L);
        insOfOneMomentParam2.setTargetTimestamp(0L);
        insOfOneMomentParam2.setSolutionType(3L);
        Map<Integer, Integer> resultMap2 = new HashMap<>();
        Exception exp2 = null;
        try {
            resultMap2 = businessTopoCommonService.queryInsOfOneMoment(insOfOneMomentParam2);
        } catch (ServiceException e) {
            exp2 = e;
        }
        Assert.assertNull(exp2);

        InsOfOneMomentParam insOfOneMomentParam3 = new InsOfOneMomentParam();
        List<Integer> instanceIdList3 = new ArrayList<>();
        instanceIdList3.add(10082);
        insOfOneMomentParam2.setInstanceIdList(instanceIdList3);
        insOfOneMomentParam2.setTimestamp(1727400600000L);
        insOfOneMomentParam2.setTargetTimestamp(0L);
        insOfOneMomentParam2.setSolutionType(0L);
        Map<Integer, Integer> resultMap3 = new HashMap<>();
        Exception exp3 = null;
        try {
            resultMap3 = businessTopoCommonService.queryInsOfOneMoment(insOfOneMomentParam3);
        } catch (ServiceException e) {
            exp3 = e;
        }
        Assert.assertNull(exp3);

        Assert.assertEquals(2230196, (int) resultMap2.get(1230196));
        Assert.assertEquals(2230339, (int) resultMap2.get(1230339));
        Assert.assertEquals(2230433, (int) resultMap2.get(1230433));
    }

    @Test
    public void fillDisplayValueTest() {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformanceindicator.json");
        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);
        ConfigData configGroupName = new ConfigData();
        configGroupName.setConfigItemName("mmHwsGroupName");
        configGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configGroupName);
        impl.fillDisplayValue(null);
        List<BusinessIndicator> indicators = businessIndicatorDao.queryIndicatorByInstanceId(792896, 0L);
        Assert.assertEquals("Payment AGG", indicators.get(0).getDisplayValue());
    }

    @Test
    public void fillDnValueTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformanceindicator.json");

        PowerMockito.mockStatic(TopoImportSolutionAdapter.class);
        Map<String, String> map = new HashMap<>();
        map.put("cu_250215150312_033B", "OS=1");
        PowerMockito.when(TopoImportSolutionAdapter.class, "getMmMeasUnitDnMap", Mockito.any()).thenReturn(map);

        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);
        ConfigData configGroupName = new ConfigData();
        configGroupName.setConfigItemName("mmHwsGroupName");
        configGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configGroupName);

        PowerMockito.mockStatic(EamUtil.class);
        List<ManagedObject> mos = new ArrayList<>();
        ManagedObject managedObject = new AccountMO();
        managedObject.setLogicSite("1");
        managedObject.setDN("opensftpDn");
        managedObject.setName("opensftp1");
        managedObject.setHwsSiteName("site");
        managedObject.setHwsGroupName("group");
        managedObject.setHwsSwimLane("b");
        managedObject.setHwsStripe("gsu");
        managedObject.setHwsAppSite("pr1");
        mos.add(managedObject);
        PowerMockito.when(EamUtil.getMoByMoType("com.huawei.venus.VGSftpService")).thenReturn(mos);

        impl.fillDisplayValue(null);
        List<BusinessIndicator> indicators = businessIndicatorDao.queryIndicatorByInstanceId(2230339, 0L);
        Assert.assertEquals("OS=1", indicators.get(0).getDn());
    }

    @Test
    public void monitorServiceStatusTest() {
        ConfigData canJump = new ConfigData();
        canJump.setConfigItemName(BusinessTopoConstant.JUMP_TO_PERFORMANCE_PAGE);
        canJump.setValue("false");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, BusinessTopoConstant.JUMP_TO_PERFORMANCE_PAGE, canJump);

        MonitorServiceStatusModel monitorServiceStatusModel = new MonitorServiceStatusModel();
        monitorServiceStatusModel.setBasePath("src/test/resources/file");
        Map<String, String> fileMap = new HashMap<>();
        fileMap.put("dvpmservice-pub-info.json", "db1bc8cd6e1a8c6aacc4187fde57daa0d801e8e54071adc087da02fb0e696c17");
        monitorServiceStatusModel.setFiles(fileMap);

        impl.monitorServiceStatus(null, monitorServiceStatusModel);

        canJump = BUSINESS_CONFIG_MAP_OPER.get(RedisConstant.BUSINESS_TOPO_CONFIG,
            ConfigData.class, BusinessTopoConstant.JUMP_TO_PERFORMANCE_PAGE).get(0);

        Assert.assertEquals("true", canJump.getValue());

        Map<String, String> emptyMap = new HashMap<>();
        emptyMap.put("dvpmservice-pub-info.json", null);
        monitorServiceStatusModel.setFiles(emptyMap);
        impl.monitorServiceStatus(null, monitorServiceStatusModel);
        Assert.assertEquals("true", canJump.getValue());
    }

    @Test
    public void searchDnPathTest() throws Exception {
        mockAuthDns();
        SearchMoParam searchMoParam = new SearchMoParam();
        searchMoParam.setSearchType(0);
        searchMoParam.setSolutionId(0);
        searchMoParam.setSearchKey("app");
        List<MoInsPath> moInsPaths = businessTopoCommonService.searchDnPath(searchMoParam);
        Assert.assertTrue(CollectionUtils.isNotEmpty(moInsPaths));
    }

    @Test
    public void queryJumpingPathTest() {
        JumpingParam jumpingParam = new JumpingParam();
        jumpingParam.setInstanceId(10071);
        jumpingParam.setClusterId(10061);
        JumpingPath jumpingPath = businessTopoCommonService.queryJumpingPath(jumpingParam);
        Assert.assertEquals(0, (int) jumpingPath.getSolutionId());
    }

    private void mockAuthDns() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        contextUtils.setAdmin(true);
        Mockito.when(contextUtils.getAuthDns()).thenReturn(new HashSet<>(Collections.singletonList("OS=1")));
        Mockito.when(contextUtils.getUserId()).thenReturn("user_01");
    }

    @Test
    public void getApplicationRelationTest() throws ServiceException {
        SolutionExParam exParam = new SolutionExParam();
        exParam.setSolutionId(0);
        List<AssociationOfMo> associationList = relationService.getApplicationRelation(exParam);
        Assert.assertTrue(CollectionUtils.isNotEmpty(associationList));
    }

    @Test
    public void getDnRelationShipTest() throws ServiceException {
        SolutionExParam exParam = new SolutionExParam();
        exParam.setSolutionId(0);
        List<DnRelation> dnRelations = relationService.getDnRelationShip(exParam);
        Assert.assertTrue(CollectionUtils.isNotEmpty(dnRelations));
    }

    @Test
    public void getGroupPartitionTest() throws ServiceException {
        SolutionExParam exParam = new SolutionExParam();
        exParam.setSolutionId(0);
        List<MoOfGroup> moOfGroupList = relationService.getGroupPartition(exParam);
        Assert.assertTrue(CollectionUtils.isNotEmpty(moOfGroupList));
    }
}
