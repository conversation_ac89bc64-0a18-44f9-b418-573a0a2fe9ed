/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.builder.SiteViewConcreteBuilder;
import com.huawei.i2000.dvtoposervice.builder.strategy.TableQueryContext;
import com.huawei.i2000.dvtoposervice.builder.strategy.TableQueryStrategy;
import com.huawei.i2000.dvtoposervice.impl.BusinessTopoSiteViewServiceDelegateImpl;
import com.huawei.i2000.dvtoposervice.model.SiteAlarmLinkInfoParam;
import com.huawei.i2000.dvtoposervice.model.SiteAlarmLinkInfoResp;
import com.huawei.i2000.dvtoposervice.model.SiteDetailViewParam;
import com.huawei.i2000.dvtoposervice.model.SiteViewGrid;
import com.huawei.i2000.dvtoposervice.util.AuthUtils;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 业务拓扑站点视图测试类
 *
 * <AUTHOR>
 * @since 2024/04/17
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class, MITManagerClient.class, AuthUtils.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessTopoSiteViewServiceImplTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    @Autowired
    BusinessTopoSiteViewServiceImpl serviceImpl;

    @Autowired
    BusinessTopoSiteViewServiceDelegateImpl businessTopoSiteViewServiceDelegate;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    SiteViewConcreteBuilder siteViewConcreteBuilder;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_siteview.sql", false);
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    @Test
    public void getSiteAlarmLinkInfoTest() {
        TableQueryStrategy tableQueryStrategy = TableQueryContext.getInstance().queryStrategy(0L);
        siteViewConcreteBuilder.getThreadLocal().set(tableQueryStrategy);
        mockServer.loadScenarioFiles("module.alarm/rest_mappings-getalarmdate.json");
        SiteAlarmLinkInfoParam queryParam = new SiteAlarmLinkInfoParam();
        queryParam.setInstanceId(1);
        queryParam.setTimeStamp(0L);
        SiteAlarmLinkInfoResp result = serviceImpl.getSiteAlarmLinkInfo(queryParam);
        Assert.assertEquals(1, result.getToLinks().size());
    }

    @Test
    public void querySiteDetailViewInfoNotAdminTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(false);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        Set<String> dns = new HashSet<>();
        dns.add("OS=1");
        PowerMockito.mockStatic(AuthUtils.class);
        PowerMockito.when(AuthUtils.getAuthDns()).thenReturn(dns);

        SiteDetailViewParam siteParam = new SiteDetailViewParam();
        siteParam.setTimestamp(0L);
        SiteViewGrid siteViewGrid = businessTopoSiteViewServiceDelegate.querySiteDetailViewInfo(null, siteParam);
        Assert.assertEquals(0, siteViewGrid.getMoSiteLinks().size());

        siteParam.setTimestamp(1L);
        SiteViewGrid siteViewGridTime = businessTopoSiteViewServiceDelegate.querySiteDetailViewInfo(null, siteParam);
        Assert.assertEquals(0, siteViewGridTime.getMoSiteLinks().size());
    }

    @Test
    public void querySiteDetailViewInfoMmAdminTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        Set<String> dns = new HashSet<>();
        dns.add("OS=1");
        PowerMockito.mockStatic(AuthUtils.class);
        PowerMockito.when(AuthUtils.getAuthDns()).thenReturn(dns);

        SiteDetailViewParam siteParam = new SiteDetailViewParam();
        siteParam.setTimestamp(0L);
        siteParam.setUnitId("");
        SiteViewGrid siteViewGrid = businessTopoSiteViewServiceDelegate.querySiteDetailViewInfo(null, siteParam);
        Assert.assertEquals(0, siteViewGrid.getMoSiteLinks().size());

        siteParam.setTimestamp(1L);
        SiteViewGrid siteViewGridTime = businessTopoSiteViewServiceDelegate.querySiteDetailViewInfo(null, siteParam);
        Assert.assertEquals(0, siteViewGridTime.getMoSiteLinks().size());
    }

}
