/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.entity;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.Test;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025-03-29
 */
public class AlarmDetailEntityTest {

    @Test
    public void test_hash_code() {

        AlarmDetailEntity alarmDetailEntity = new AlarmDetailEntity();

        // run the test
        int result = alarmDetailEntity.hashCode();

        // verify the results
        assertNotNull(result);
    }
}