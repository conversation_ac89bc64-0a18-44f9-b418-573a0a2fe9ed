/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.configuration;

import com.alibaba.fastjson2.JSONObject;
import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpRequest;
import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpResponse;
import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmRuleRecord;
import com.huawei.i2000.dvtoposervice.business.openapi.alarm.AlarmConditionFilter;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.BusinessTopoAlarmStorageService;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessAlarmRuleDao;
import com.huawei.i2000.dvtoposervice.impl.BusinessExternalServiceDelegateImpl;
import com.huawei.i2000.dvtoposervice.model.HisAlarmQueryParam;
import com.huawei.i2000.dvtoposervice.model.ResponseResult;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.LogUtil;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.dvtopowebsite.impl.BusinessConfigurationWebsiteDelegateImpl;
import com.huawei.i2000.dvtopowebsite.model.AlarmConditionItem;
import com.huawei.i2000.dvtopowebsite.model.AlarmRuleQueryParam;
import com.huawei.i2000.dvtopowebsite.model.AlarmRuleStartParam;
import com.huawei.i2000.dvtopowebsite.model.DefinitionQueryParam;
import com.huawei.i2000.dvtopowebsite.model.DeleteAlarmRuleParam;
import com.huawei.i2000.dvtopowebsite.model.ResponseEntity;
import com.huawei.i2000.dvtopowebsite.model.RuleAddOrModifyParam;
import com.huawei.i2000.dvtopowebsite.model.SolutionCommonParam;
import com.huawei.i2000.dvtopowebsite.util.RestConstant;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.jupiter.api.Order;
import org.junit.rules.ExpectedException;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 告警自定义配置测试类
 *
 * <AUTHOR>
 * @since 2025/1/18
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class, MITManagerClient.class, LogUtil.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessTopoAlarmConfigurationServiceTest extends WebServiceTest {

    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    private BusinessTopoAlarmStorageService businessTopoAlarmStorageService;

    @Autowired
    private BusinessConfigurationWebsiteDelegateImpl configurationWebsiteDelegate;

    @Autowired
    private BusinessAlarmRuleDao businessAlarmRuleDao;

    @Autowired
    private BusinessExternalServiceDelegateImpl externalServiceDelegate;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (! DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_alarm.sql", false);
        businessTopoAlarmStorageService.refreshAllStorageCache();
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
        mockServer.loadScenarioFiles("module.alarm/rest_mappings-getalarmdate.json");
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    public void mockAnalysisJumpingInterface() {
        mockServer.loadScenarioFiles("module.analysis/rest_mappings_getanalysisresult.json");
    }

    @Test
    public void queryAlarmRulesTest() throws ServiceException {
        SolutionCommonParam solutionCommonParam = new SolutionCommonParam();
        solutionCommonParam.setSolutionId(70000);
        mockContextUtil();
        ResponseEntity result = configurationWebsiteDelegate.queryAlarmRules(null, solutionCommonParam);
        Assert.assertTrue(Objects.nonNull(result.getData()));
    }

    @Test
    @Order(1)
    public void queryAlarmRuleDetailTest() {
        AlarmRuleQueryParam ruleQueryParam = new AlarmRuleQueryParam();
        ruleQueryParam.setSolutionId(70000);
        ruleQueryParam.setModelId("0_cbs");
        ruleQueryParam.setListeningScope(1);
        ruleQueryParam.setAlarmRuleId("UUID");
        ResponseEntity result = configurationWebsiteDelegate.queryAlarmRuleDetail(null, ruleQueryParam);
        AlarmConditionFilter alarmRule = (AlarmConditionFilter) result.getData();
        Assert.assertTrue(CollectionUtils.isNotEmpty(alarmRule.getAlarmWhiteIds()));
    }

    @Test
    public void addAndModifyAlarmRuleErrorTest() throws Exception {
        RuleAddOrModifyParam addParam = getRuleAddOrModifyParam();
        HttpContext context = getCommonContext();
        ResponseEntity addResult = new ResponseEntity();

        addParam.getAlarmConditionWhiteItems().get(0).setConditionValue("111111111111111");
        mockContextUtil();

        addResult = configurationWebsiteDelegate.addAlarmRule(context, addParam);
        Assert.assertEquals(RestConstant.FAILURE_CODE, (int) addResult.getResultCode());

        addParam.getAlarmConditionWhiteItems().get(0).setConditionValue("111111");
        addParam.getAlarmConditionBlackItems().get(0).setConditionValue("111111");

        addResult = configurationWebsiteDelegate.addAlarmRule(context, addParam);
        Assert.assertEquals(RestConstant.FAILURE_CODE, (int) addResult.getResultCode());
    }

    @Test
    @Order(2)
    public void addAndModifyAlarmRuleTest() throws Exception {
        RuleAddOrModifyParam addParam = getRuleAddOrModifyParam();
        HttpContext context = getCommonContext();
        Exception ex = null;
        ResponseEntity addResult = new ResponseEntity();
        mockContextUtil();
        try {
            addResult = configurationWebsiteDelegate.addAlarmRule(context, addParam);
        } catch (Exception e) {
            ex = e;
        }

        ResponseEntity modifyResult = new ResponseEntity();
        try {
            addParam.setAlarmRuleId("UUID");
            addParam.setAlarmRuleName("Suiyi");
            addParam.setListeningScope(1);
            modifyResult = configurationWebsiteDelegate.modifyAlarmRule(context, addParam);
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertNull(ex);
        Assert.assertEquals(RestConstant.SUCCESS_CODE, (int) addResult.getResultCode());
        Assert.assertEquals(RestConstant.SUCCESS_CODE, (int) modifyResult.getResultCode());
    }

    private void mockContextUtil() {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        contextUtils.setUserId("1");
        contextUtils.setUserName("admin");
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
    }

    private RuleAddOrModifyParam getRuleAddOrModifyParam() {
        RuleAddOrModifyParam addParam = new RuleAddOrModifyParam();
        addParam.setSolutionId(70000);
        addParam.setAlarmRuleName("TimeLineInsert");
        addParam.setAlarmSeverities(Arrays.asList(1, 2));
        addParam.setListeningScope(0);
        addParam.setAlarmFilterType(0);
        addParam.setMoType("cbs.billing.cbs");
        AlarmConditionItem conditionItem = new AlarmConditionItem();
        conditionItem.setConditionRule("IS IN");
        conditionItem.setConditionColumn("alarmId");
        conditionItem.setConditionValue("60210036,60210205");
        addParam.setAlarmConditionWhiteItems(Collections.singletonList(conditionItem));

        AlarmConditionItem conditionBlackItem = JSONObject.parseObject(JSONObject.toJSONString(conditionItem), AlarmConditionItem.class);
        conditionBlackItem.setConditionValue("60210037,60210206");
        addParam.setAlarmConditionBlackItems(Collections.singletonList(conditionBlackItem));
        addParam.setThresholdsAlarmOpen(true);
        return addParam;
    }

    @Test
    public void queryMoTypeListBySolTest() throws Exception {
        SolutionCommonParam solutionCommonParam = new SolutionCommonParam();
        solutionCommonParam.setSolutionId(70000);
        HttpContext context = getCommonContext();
        ResponseEntity result = new ResponseEntity();
        try {
            result = configurationWebsiteDelegate.queryAlarmMoType(context, solutionCommonParam);
        } catch (Exception e) {
            Assert.fail();
        }
        Assert.assertEquals(0, (int) result.getResultCode());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void queryAlarmStaticInfoTest() throws Exception {
        DefinitionQueryParam definitionQueryParam = new DefinitionQueryParam();
        definitionQueryParam.setLimit(400);
        definitionQueryParam.setCategory(1);
        List<String> alarmGroupIds = Arrays.asList("com.huawei.i2000.IRes.pm", "com.huawei.IRes.vm",
            "com.huawei.URes.docker");
        definitionQueryParam.setAlarmGroupIds(alarmGroupIds);
        HttpContext context = getCommonContext();

        ResponseEntity result = new ResponseEntity();
        try {
            result = configurationWebsiteDelegate.queryAlarmStaticInfo(context, definitionQueryParam);
        } catch (Exception e) {
            Assert.fail();
        }
        Assert.assertEquals(0, (int) result.getResultCode());
        Assert.assertNotNull(result.getData());
    }

    @Test
    @Order(10)
    public void deleteAlarmRuleTest() throws Exception {
        DeleteAlarmRuleParam deleteAlarmRuleParam = new DeleteAlarmRuleParam();
        deleteAlarmRuleParam.setSolutionId(70003);
        deleteAlarmRuleParam.setListeningScope(1);
        deleteAlarmRuleParam.setModelId("0_cbs");
        deleteAlarmRuleParam.setAlarmRuleId("UUID");
        HttpContext context = getCommonContext();

        ResponseEntity result = new ResponseEntity();
        try {
            result = configurationWebsiteDelegate.deleteAlarmRule(context, deleteAlarmRuleParam);
        } catch (Exception e) {
            Assert.fail();
        }
        Assert.assertTrue(true);
        Assert.assertEquals(RestConstant.SUCCESS_CODE, (int) result.getResultCode());
    }

    private HttpContext getCommonContext() throws Exception {
        MockHttpRequest request = new MockHttpRequest();
        MockHttpResponse response = new MockHttpResponse();
        HttpContext context = new MockHttpContext(request, response);
        PowerMockito.mockStatic(LogUtil.class);
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        return context;
    }

    @Test
    public void startAlarmRuleTest() throws Exception {
        AlarmRuleStartParam startParam = new AlarmRuleStartParam();
        startParam.setAlarmRuleId("UUID");
        startParam.setListeningScope(1);
        startParam.setSolutionId(70000);
        startParam.setIsValidate(true);
        HttpContext context = getCommonContext();
        configurationWebsiteDelegate.startAlarmRule(context, startParam);
        AlarmRuleRecord record = businessAlarmRuleDao.queryAlarmRuleById("UUID", 1, "0_cbs");
        Assert.assertTrue(record.getIsValidate());
    }

    @Test
    public void getHistoryAlarmByConditionTest() throws ServiceException {
        HisAlarmQueryParam hisAlarmQueryParam = new HisAlarmQueryParam();
        hisAlarmQueryParam.setSolutionName("CBS");
        ResponseResult responseResult = externalServiceDelegate.getHistoryAlarmByCondition(null, hisAlarmQueryParam);
        Assert.assertTrue(Objects.nonNull(responseResult.getData()));
    }
}
