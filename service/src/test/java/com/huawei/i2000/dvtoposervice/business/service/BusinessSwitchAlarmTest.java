/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmDetail;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.openapi.alarm.AlarmNotificationService;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.event.BusinessTopoRefreshHandler;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.Collections;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessSwitchAlarmTest extends WebServiceTest {

    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    private static final String ALARM_CSN = "AlarmCsn";

    @Autowired
    AlarmNotificationService notificationService;

    @Autowired
    BusinessInstanceModelDao businessInstanceModelDao;

    @Autowired
    BusinessTopoRefreshHandler businessTopoRefreshHandler;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_alarm.sql", false);
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    @Test
    public void updateMemberAlarmStatusWhenExtinctionTest() {
        Assert.assertEquals("555,666,777", businessInstanceModelDao.queryAttrValue(70005, 0L, ALARM_CSN));
        notificationService.updateMemberAlarmStatusWhenExtinction(70007);
        Assert.assertNull(businessInstanceModelDao.queryAttrValue(70007, 0L, ALARM_CSN));
        Assert.assertEquals("666", businessInstanceModelDao.queryAttrValue(70006, 0L, ALARM_CSN));
        Assert.assertEquals("555,666", businessInstanceModelDao.queryAttrValue(70005, 0L, ALARM_CSN));
        Assert.assertEquals("444,555,666", businessInstanceModelDao.queryAttrValue(70004, 0L, ALARM_CSN));
        Assert.assertEquals("333,444,555,666", businessInstanceModelDao.queryAttrValue(70003, 0L, ALARM_CSN));
    }

    @Test
    public void testSwitchAsAlarm() {
        AlarmDetail asAlarm = mockAsSwitchAlarm();
        Exception ex = null;
        try {
            notificationService.noticeSwitchAlarm(Collections.singletonList(asAlarm));
        } catch (ServiceException e) {
            ex = e;
        }
        validateAsResult(ex);
    }

    private AlarmDetail mockAsSwitchAlarm() {
        AlarmDetail asSwitchAlarm = mockArSwitchAlarm();
        asSwitchAlarm.setAlarmId(412030116);
        asSwitchAlarm.setNativeMeDn("cd0e61696fee");
        return asSwitchAlarm;
    }

    @Test
    public void testArSwitchAlarmNotice() {
        AlarmDetail arAlarm = mockArSwitchAlarm();
        Exception ex = null;
        try {
            notificationService.noticeSwitchAlarm(Collections.singletonList(arAlarm));
        } catch (ServiceException e) {
            ex = e;
        }
        validateArResult(ex);
    }

    @Test
    public void testArBackSwitchAlarmNoticeInModelASRR() {
        AlarmDetail arAlarm = mockArSwitchAlarm();
        AlarmDetail arBackAlarm = mockArBackSwitchAlarm();
        Exception ex = null;
        try {
            notificationService.noticeSwitchAlarm(Collections.singletonList(arAlarm));
            businessInstanceModelDao.updateAttrValue(70010, "hwCurrentNodeRole", "3");
            businessInstanceModelDao.updateAttrValue(70011, "hwCurrentNodeRole", "3");
            notificationService.noticeSwitchAlarm(Collections.singletonList(arBackAlarm));
        } catch (ServiceException e) {
            ex = e;
        }

        BusinessInstanceModelDB masterNode = businessInstanceModelDao.queryInstanceByInstanceId(70010);
        BusinessInstanceModelDB standByNode = businessInstanceModelDao.queryInstanceByInstanceId(70011);
        BusinessInstanceModelDB recoveryNode = businessInstanceModelDao.queryInstanceByInstanceId(70012);
        BusinessInstanceModelDB recoveryStandNode = businessInstanceModelDao.queryInstanceByInstanceId(70013);
        BusinessInstanceModelDB siteNode = businessInstanceModelDao.queryInstanceByInstanceId(70003);
        BusinessInstanceModelDB anotherSiteNode = businessInstanceModelDao.queryInstanceByInstanceId(70033);
        Assert.assertEquals(masterNode.getExtentAttr("hwCurrentNodeRole").getAttrValue(), "1");
        Assert.assertEquals(standByNode.getExtentAttr("hwCurrentNodeRole").getAttrValue(), "2");
        Assert.assertEquals(recoveryNode.getExtentAttr("hwCurrentNodeRole").getAttrValue(), "0");
        Assert.assertEquals(recoveryStandNode.getExtentAttr("hwCurrentNodeRole").getAttrValue(), "0");
        Assert.assertEquals(Integer.parseInt(siteNode.getExtentAttr("memoryDatabasearSwitchCount").getAttrValue()), 0);
        Assert.assertEquals(Integer.parseInt(anotherSiteNode.getExtentAttr("memoryDatabasearSwitchCount").getAttrValue()), 0);
        Assert.assertNull(ex);
    }

    private AlarmDetail mockArSwitchAlarm() {
        AlarmDetail arSwitchAlarm = new AlarmDetail();
        arSwitchAlarm.setAlarmId(412030115);
        arSwitchAlarm.setNativeMeDn("f3aa4093139d");
        arSwitchAlarm.setCategory(1);
        arSwitchAlarm.setCsn(12858);
        arSwitchAlarm.setSeverity(1);
        arSwitchAlarm.setMoi("VNode=140, NodeId=200001, MachineId=100.102.0.206");
        arSwitchAlarm.setAdditionalInformation("node 200001 is upgraded to master: switch over scene");
        arSwitchAlarm.setAlarmName("发生主副本切换");
        arSwitchAlarm.setOccurUtc(1710001963120L);
        arSwitchAlarm.setOccurTime(1710001963120L);
        return arSwitchAlarm;
    }

    private AlarmDetail mockArBackSwitchAlarm() {
        AlarmDetail asBackSwitchAlarm = mockArSwitchAlarm();
        asBackSwitchAlarm.setNativeMeDn("bcf0371db6fa");
        asBackSwitchAlarm.setCsn(128581);
        asBackSwitchAlarm.setOccurUtc(1710001964120L);
        asBackSwitchAlarm.setOccurTime(1710001964120L);
        asBackSwitchAlarm.setMoi("VNode=140, NodeId=100000, MachineId=100.102.0.206");
        asBackSwitchAlarm.setAdditionalInformation("node 100000 is upgraded to master: initiative fail over scene");
        return asBackSwitchAlarm;
    }

    @Test
    public void testIndicatorUpdate() throws ServiceException {
        businessTopoRefreshHandler.updateDatabaseSwitchCount();
        BusinessInstanceModelDB siteNode = businessInstanceModelDao.queryInstanceByInstanceId(70003);
        BusinessInstanceModelDB anotherSiteNode = businessInstanceModelDao.queryInstanceByInstanceId(70033);
        Assert.assertEquals(Integer.parseInt(siteNode.getExtentAttr("memoryDatabasearSwitchCount").getAttrValue()), 0);
        Assert.assertEquals(Integer.parseInt(siteNode.getExtentAttr("memoryDatabaseasSwitchCount").getAttrValue()), 0);
        Assert.assertEquals(Integer.parseInt(anotherSiteNode.getExtentAttr("memoryDatabasearSwitchCount").getAttrValue()), 0);
        Assert.assertEquals(Integer.parseInt(anotherSiteNode.getExtentAttr("memoryDatabaseasSwitchCount").getAttrValue()), 0);
    }

    @Test
    public void testIndicatorArUpdate() throws ServiceException {
        businessInstanceModelDao.updateAttrValue(70010, "hwCurrentNodeRole", "3");
        businessInstanceModelDao.updateAttrValue(70011, "hwCurrentNodeRole", "4");
        businessInstanceModelDao.updateAttrValue(70012, "hwCurrentNodeRole", "1");
        businessInstanceModelDao.updateAttrValue(70013, "hwCurrentNodeRole", "2");
        businessTopoRefreshHandler.updateDatabaseSwitchCount();
        BusinessInstanceModelDB siteNode = businessInstanceModelDao.queryInstanceByInstanceId(70003);
        BusinessInstanceModelDB anotherSiteNode = businessInstanceModelDao.queryInstanceByInstanceId(70033);
        Assert.assertEquals(Integer.parseInt(siteNode.getExtentAttr("memoryDatabasearSwitchCount").getAttrValue()), 1);
        Assert.assertEquals(Integer.parseInt(siteNode.getExtentAttr("memoryDatabaseasSwitchCount").getAttrValue()), 0);
        Assert.assertEquals(Integer.parseInt(anotherSiteNode.getExtentAttr("memoryDatabasearSwitchCount").getAttrValue()), 1);
        Assert.assertEquals(Integer.parseInt(anotherSiteNode.getExtentAttr("memoryDatabaseasSwitchCount").getAttrValue()), 0);
    }

    @Test
    public void testIndicatorAsUpdate() throws ServiceException {
        businessInstanceModelDao.updateAttrValue(70010, "hwCurrentNodeRole", "2");
        businessInstanceModelDao.updateAttrValue(70011, "hwCurrentNodeRole", "1");
        businessTopoRefreshHandler.updateDatabaseSwitchCount();
        BusinessInstanceModelDB siteNode = businessInstanceModelDao.queryInstanceByInstanceId(70003);
        Assert.assertEquals(Integer.parseInt(siteNode.getExtentAttr("memoryDatabaseasSwitchCount").getAttrValue()), 1);
    }

    @Test
    public void testIndicatorArAndAsUpdate() throws ServiceException {
        businessInstanceModelDao.updateAttrValue(70010, "hwCurrentNodeRole", "4");
        businessInstanceModelDao.updateAttrValue(70011, "hwCurrentNodeRole", "3");
        businessInstanceModelDao.updateAttrValue(70012, "hwCurrentNodeRole", "1");
        businessInstanceModelDao.updateAttrValue(70013, "hwCurrentNodeRole", "2");
        businessTopoRefreshHandler.updateDatabaseSwitchCount();
        BusinessInstanceModelDB siteNode = businessInstanceModelDao.queryInstanceByInstanceId(70003);
        BusinessInstanceModelDB anotherSiteNode = businessInstanceModelDao.queryInstanceByInstanceId(70033);
        Assert.assertEquals(Integer.parseInt(siteNode.getExtentAttr("memoryDatabasearSwitchCount").getAttrValue()), 1);
        Assert.assertEquals(Integer.parseInt(siteNode.getExtentAttr("memoryDatabaseasSwitchCount").getAttrValue()), 1);
        Assert.assertEquals(Integer.parseInt(anotherSiteNode.getExtentAttr("memoryDatabasearSwitchCount").getAttrValue()), 1);
        Assert.assertEquals(Integer.parseInt(anotherSiteNode.getExtentAttr("memoryDatabaseasSwitchCount").getAttrValue()), 0);
    }

    @Test
    public void testPdbAsUpdate() {
        AlarmDetail asAlarm = mockPdbAsSwitchAlarm();
        Exception ex = null;
        try {
            notificationService.noticeSwitchAlarm(Collections.singletonList(asAlarm));
        } catch (ServiceException e) {
            ex = e;
        }
        validateAsResult(ex);
    }

    private AlarmDetail mockPdbAsSwitchAlarm() {
        AlarmDetail asPdbSwitchAlarm = mockArSwitchAlarm();
        asPdbSwitchAlarm.setAlarmId(412030205);
        asPdbSwitchAlarm.setNativeMeDn("cd0e61696fee");
        asPdbSwitchAlarm.setCsn(156588);
        asPdbSwitchAlarm.setOccurUtc(1710001964120L);
        asPdbSwitchAlarm.setOccurTime(1710001964120L);
        asPdbSwitchAlarm.setMoi("node-id=100000,vnode-id=140,machine-id=100.102.0.205");
        asPdbSwitchAlarm.setAdditionalInformation("vnode('140')'s master failover to node '100000'");
        return asPdbSwitchAlarm;
    }

    @Test
    public void testPdbArUpdate() {
        AlarmDetail asAlarm = mockPdbArSwitchAlarm();
        Exception ex = null;
        try {
            notificationService.noticeSwitchAlarm(Collections.singletonList(asAlarm));
        } catch (ServiceException e) {
            ex = e;
        }
        validateArResult(ex);
    }

    private AlarmDetail mockPdbArSwitchAlarm() {
        AlarmDetail arPdbSwitchAlarm = mockArSwitchAlarm();
        arPdbSwitchAlarm.setAlarmId(412030205);
        arPdbSwitchAlarm.setNativeMeDn("f3aa4093139d");
        arPdbSwitchAlarm.setCsn(178963);
        arPdbSwitchAlarm.setOccurUtc(1710001964120L);
        arPdbSwitchAlarm.setOccurTime(1710001964120L);
        arPdbSwitchAlarm.setMoi("node-id=100000, vnode-id=140, machine-id=100.102.0.205");
        arPdbSwitchAlarm.setAdditionalInformation("vnode('140')'s master failover to node '100000'");
        return arPdbSwitchAlarm;
    }

    @Test
    public void testPdbAsEventUpdate() {
        AlarmDetail asAlarm = mockPdbAsSwitchEvent();
        Exception ex = null;
        try {
            notificationService.noticeSwitchAlarm(Collections.singletonList(asAlarm));
        } catch (ServiceException e) {
            ex = e;
        }
        validateAsResult(ex);
    }

    private AlarmDetail mockPdbAsSwitchEvent() {
        AlarmDetail asPdbSwitchEvent = mockArSwitchAlarm();
        asPdbSwitchEvent.setAlarmId(412030206);
        asPdbSwitchEvent.setNativeMeDn("cd0e61696fee");
        asPdbSwitchEvent.setCsn(589613);
        asPdbSwitchEvent.setOccurUtc(1710001964120L);
        asPdbSwitchEvent.setOccurTime(1710001964120L);
        asPdbSwitchEvent.setMoi("node-id=100001,vnode-id=140,machine-id=100.102.0.201");
        asPdbSwitchEvent.setAdditionalInformation("vnode('140')'s master switch over from '100000' to '100001'");
        return asPdbSwitchEvent;
    }

    @Test
    public void testPdbArEventUpdate() {
        AlarmDetail arEvent = mockPdbArSwitchEvent();
        Exception ex = null;
        try {
            notificationService.noticeSwitchAlarm(Collections.singletonList(arEvent));
        } catch (ServiceException e) {
            ex = e;
        }
        validateArResult(ex);
    }

    private AlarmDetail mockPdbArSwitchEvent() {
        AlarmDetail arPdbSwitchEvent = mockPdbAsSwitchEvent();
        arPdbSwitchEvent.setNativeMeDn("f3aa4093139d");
        return arPdbSwitchEvent;
    }

    @Test
    public void testRaRsSwitchUpdate() {
        AlarmDetail raRsSwitchAlarm = mockAsSwitchAlarm();
        raRsSwitchAlarm.setNativeMeDn("b3665f7d98d5");
        Exception ex = null;
        try {
            notificationService.noticeSwitchAlarm(Collections.singletonList(raRsSwitchAlarm));
        } catch (ServiceException e) {
            ex = e;
        }
        validateRaRsResult(ex);
    }

    private void validateAsResult(Exception ex) {
        BusinessInstanceModelDB masterNode = businessInstanceModelDao.queryInstanceByInstanceId(70010);
        BusinessInstanceModelDB standByNode = businessInstanceModelDao.queryInstanceByInstanceId(70011);
        BusinessInstanceModelDB siteNode = businessInstanceModelDao.queryInstanceByInstanceId(70003);
        Assert.assertEquals(masterNode.getExtentAttr("hwCurrentNodeRole").getAttrValue(), "2");
        Assert.assertEquals(standByNode.getExtentAttr("hwCurrentNodeRole").getAttrValue(), "1");
        Assert.assertEquals(Integer.parseInt(siteNode.getExtentAttr("memoryDatabaseasSwitchCount").getAttrValue()), 1);
        Assert.assertNull(ex);
    }

    private void validateArResult(Exception ex) {
        BusinessInstanceModelDB masterNode = businessInstanceModelDao.queryInstanceByInstanceId(70010);
        BusinessInstanceModelDB standByNode = businessInstanceModelDao.queryInstanceByInstanceId(70011);
        BusinessInstanceModelDB recoveryNode = businessInstanceModelDao.queryInstanceByInstanceId(70012);
        BusinessInstanceModelDB recoveryStandNode = businessInstanceModelDao.queryInstanceByInstanceId(70013);
        BusinessInstanceModelDB siteNode = businessInstanceModelDao.queryInstanceByInstanceId(70003);
        BusinessInstanceModelDB anotherSiteNode = businessInstanceModelDao.queryInstanceByInstanceId(70033);
        Assert.assertEquals(masterNode.getExtentAttr("hwCurrentNodeRole").getAttrValue(), "0");
        Assert.assertEquals(standByNode.getExtentAttr("hwCurrentNodeRole").getAttrValue(), "0");
        Assert.assertEquals(recoveryNode.getExtentAttr("hwCurrentNodeRole").getAttrValue(), "1");
        Assert.assertEquals(recoveryStandNode.getExtentAttr("hwCurrentNodeRole").getAttrValue(), "2");
        Assert.assertEquals(Integer.parseInt(siteNode.getExtentAttr("memoryDatabasearSwitchCount").getAttrValue()), 1);
        Assert.assertEquals(Integer.parseInt(anotherSiteNode.getExtentAttr("memoryDatabasearSwitchCount").getAttrValue()), 1);
        Assert.assertNull(ex);
    }

    private void validateRaRsResult(Exception ex) {
        BusinessInstanceModelDB recoveryNode = businessInstanceModelDao.queryInstanceByInstanceId(70012);
        BusinessInstanceModelDB recoveryStandNode = businessInstanceModelDao.queryInstanceByInstanceId(70013);
        BusinessInstanceModelDB anotherSiteNode = businessInstanceModelDao.queryInstanceByInstanceId(70033);
        Assert.assertEquals(recoveryNode.getExtentAttr("hwCurrentNodeRole").getAttrValue(), "4");
        Assert.assertEquals(recoveryStandNode.getExtentAttr("hwCurrentNodeRole").getAttrValue(), "3");
        Assert.assertEquals(Integer.parseInt(anotherSiteNode.getExtentAttr("memoryDatabaseasSwitchCount").getAttrValue()), 1);
        Assert.assertNull(ex);
    }
}
