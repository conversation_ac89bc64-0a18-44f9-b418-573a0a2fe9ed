/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.topo;

import static com.huawei.i2000.dvtoposervice.constant.DVTopoServiceConstant.TopoConstant.LABEL;
import static com.huawei.i2000.dvtoposervice.constant.DVTopoServiceConstant.TopoConstant.LEFT_FDN;
import static com.huawei.i2000.dvtoposervice.constant.DVTopoServiceConstant.TopoConstant.NATIVE_ID;
import static com.huawei.i2000.dvtoposervice.constant.DVTopoServiceConstant.TopoConstant.RIGHT_FDN;
import static com.huawei.i2000.dvtoposervice.constant.DVTopoServiceConstant.TopoConstant.TSNODES_URL;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.cbb.security.osinject.securityexec.ProcessInfo;
import com.huawei.i2000.cbb.security.osinject.securityexec.ProcessResult;
import com.huawei.i2000.cbb.security.osinject.securityexec.SecurityExecutor;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.constant.RestConstant;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.model.LinkCondition;
import com.huawei.i2000.dvtoposervice.model.LinkTipsDetails;
import com.huawei.i2000.dvtoposervice.model.MenusDetails;
import com.huawei.i2000.dvtoposervice.model.MoTipsDetail;
import com.huawei.i2000.dvtoposervice.model.NodeCondition;
import com.huawei.i2000.dvtoposervice.util.ConfigurationUtil;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.LogUtil;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.dvtoposervice.util.ReportResourceUtil;
import com.huawei.i2000.dvtoposervice.util.RestUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.i2000.gapi.account.mo.AccountMO;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.ManagedObject;

import com.alibaba.fastjson2.JSONObject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * TopoServiceImplTest
 *
 * <AUTHOR>
 * @since 2024-11-19 17:32:17
 */
@PrepareForTest({
    PropertiesUtil.class, ContextUtils.class, ConfigurationUtil.class, MITManagerClient.class, AlarmClient.class,
    RestUtil.class, JSONObject.class, ReportResourceUtil.class, SecurityExecutor.class, ProcessInfo.class, LogUtil.class
})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class TopoServiceImplTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);
    private static EmbeddedRestClientAndServer mockServer;

    public static final String ICONFIG_PROCESS = "dvtoposervice_iconfig";

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @Autowired
    private TopoServiceImpl topoService;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (! DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith.sql", false);
    }

    @Test
    public void createLinkTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        Set<String> set = new HashSet<>();
        set.add("NE=3");
        contextUtils.setAdmin(false);
        contextUtils.setAuthDns(set);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        LinkCondition linkInfo = new LinkCondition();
        linkInfo.setLeftId("NE=1");
        linkInfo.setRightId("NE=2");
        boolean result = topoService.createLink(linkInfo);
        Assert.assertEquals(false, result);
    }

    @Test
    public void createNodeTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        Set<String> set = new HashSet<>();
        set.add("NE=3");
        contextUtils.setAdmin(false);
        contextUtils.setAuthDns(set);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        NodeCondition nodeInfo = new NodeCondition();
        nodeInfo.setParentId("NE=2");
        boolean result = topoService.createNode(nodeInfo);
        Assert.assertEquals(false, result);
    }

    @Test
    public void deleteVirtualNodeTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        Set<String> set = new HashSet<>();
        set.add("NE=3");
        contextUtils.setAdmin(false);
        contextUtils.setAuthDns(set);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        boolean result = topoService.deleteVirtualNode("NE=2");
        Assert.assertEquals(false, result);
    }

    @Test
    @SuppressWarnings("unchecked")
    public void queryMaskedMenusTest() throws ServiceException, IOException {
        PowerMockito.mockStatic(ConfigurationUtil.class);
        ConfigData configData = new ConfigData();
        configData.setValue("null");
        PowerMockito.when(ConfigurationUtil.getConfigDataByName(BusinessTopoConstant.MAINTENANCE_LINK)).thenReturn(configData);

        PowerMockito.mockStatic(SecurityExecutor.class);
        ProcessInfo<ProcessResult, ProcessResult> info = PowerMockito.mock(ProcessInfo.class);

        PowerMockito.when(info.exitValue()).thenReturn(0);
        ProcessResult processResult = new ProcessResult();
        PowerMockito.when(info.getStdOut()).thenReturn(processResult);
        PowerMockito.when(SecurityExecutor.execCommand(ICONFIG_PROCESS)).thenReturn(info);

        MenusDetails menusDetails = topoService.queryMaskedMenus();
        Assert.assertEquals(Arrays.asList("iConfigPage", "mmlPage", "maintenanceTerminalPage"), menusDetails.getMaskMenus());
    }

    @Test
    public void queryNfvConfigTest() throws ServiceException {
        Assert.assertFalse(topoService.queryNfvConfig());
    }

    @Test
    public void displayLinkTipsTest() throws ServiceException {
        PowerMockito.mockStatic(RestUtil.class);
        RestfulResponse restfulResponse = new RestfulResponse();
        restfulResponse.setStatus(200);
        restfulResponse.setResponseJson("data");
        PowerMockito.when(
            RestUtil.addUrlParam(anyString(), anyString(), anyString())).thenReturn(TSNODES_URL);
        PowerMockito.when(
            RestUtil.sendRestRequest(eq(RestConstant.RESTFUL_METHOD_GET), anyString(), eq(null), eq(null))).thenReturn(restfulResponse);

        PowerMockito.mockStatic(JSONObject.class);
        JSONObject result = new JSONObject();
        List<JSONObject> datas = new ArrayList<>();
        JSONObject data1 = new JSONObject();
        data1.put(NATIVE_ID, "18c9f5bd-72f2-4bbc-9415-67e862862394");
        data1.put(LABEL, "测试网元1");
        data1.put(LEFT_FDN, "18c9f5bd-72f2-4bbc-9415-67e862862394");
        data1.put(RIGHT_FDN, "2f3af0c9-fc60-48d7-8b0e-897ca9f4773b");
        datas.add(data1);
        JSONObject data2 = new JSONObject();
        data2.put(NATIVE_ID, "2f3af0c9-fc60-48d7-8b0e-897ca9f4773b");
        data2.put(LABEL, "测试网元2");
        datas.add(data2);
        result.put("dataList", datas);
        result.put("hasNext", "false");
        PowerMockito.when(JSONObject.toJSONString(anyList())).thenReturn("jsonString");
        PowerMockito.when(JSONObject.parseObject("data")).thenReturn(result);

        String resId = "84bcf95d-5684-4ab9-b0d2-10b8a619ed43";
        LinkTipsDetails linkTipsDetails = topoService.displayLinkTips(resId);
        Assert.assertEquals("测试网元1", linkTipsDetails.getLeftMoName());
        Assert.assertEquals("测试网元2", linkTipsDetails.getRightMoName());
    }

    @Test
    public void createLinkSuccessTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        Set<String> set = new HashSet<>();
        set.add("NE=3");
        contextUtils.setAdmin(true);
        contextUtils.setAuthDns(set);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        LinkCondition linkInfo = new LinkCondition();
        linkInfo.setLabel("123");
        linkInfo.setLeftId("NE=1");
        linkInfo.setRightId("NE=2");
        linkInfo.setLeftObjId("1001");
        linkInfo.setLeftObjId("1002");

        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        PowerMockito.when(mitManagerClient.getMO(new DN("NE=1"))).thenReturn(new AccountMO());
        PowerMockito.when(mitManagerClient.getMO(new DN("NE=2"))).thenReturn(new AccountMO());

        PowerMockito.mockStatic(LogUtil.class);
        boolean result = topoService.createLink(linkInfo);
        Assert.assertTrue(result);
    }

    @Test
    public void createLinkCustomTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        Set<String> set = new HashSet<>();
        set.add("NE=3");
        contextUtils.setAdmin(true);
        contextUtils.setAuthDns(set);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        LinkCondition linkInfo = new LinkCondition();
        linkInfo.setLabel("123");
        linkInfo.setLeftId("NE=1");
        linkInfo.setRightId("NE=2");
        linkInfo.setLeftObjId("1001");
        linkInfo.setLeftObjId("1002");
        linkInfo.setTopoType("custom");

        PowerMockito.mockStatic(RestUtil.class);
        RestfulResponse restfulResponse = new RestfulResponse();
        restfulResponse.setStatus(200);
        restfulResponse.setResponseJson("{\"dataList\":[], \"hasNext\": false}");
        PowerMockito.when(
            RestUtil.addUrlParam(anyString(), anyString(), anyString())).thenReturn(TSNODES_URL);
        PowerMockito.when(
            RestUtil.sendRestRequest(eq(RestConstant.RESTFUL_METHOD_GET), anyString(), eq(null), eq(null))).thenReturn(restfulResponse);

        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        PowerMockito.when(mitManagerClient.getMO(new DN("NE=1"))).thenReturn(new AccountMO());
        PowerMockito.when(mitManagerClient.getMO(new DN("NE=2"))).thenReturn(new AccountMO());

        PowerMockito.mockStatic(LogUtil.class);
        boolean result = topoService.createLink(linkInfo);
        Assert.assertTrue(result);
    }

    @Test
    public void displayMoTipsTest() throws ServiceException, OSSException {
        String dn = "OS=1";
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        ManagedObject managedObject = new AccountMO();
        managedObject.setName("OSS");
        managedObject.setType("com.huawei.as.res.virtual");
        PowerMockito.when(mitManagerClient.getMO(dn)).thenReturn(managedObject);
        PowerMockito.when(mitManagerClient.getMO(new DN(dn))).thenReturn(managedObject);

        PowerMockito.mockStatic(AlarmClient.class);
        AlarmClient alarmClient = PowerMockito.mock(AlarmClient.class);
        PowerMockito.when(AlarmClient.getInstance()).thenReturn(alarmClient);
        Map<String, String> hit1 = new HashMap<>();
        hit1.put("severity", "1");
        Map<String, String> hit2 = new HashMap<>();
        hit2.put("severity", "2");
        Map<String, String> hit3 = new HashMap<>();
        hit3.put("severity", "3");
        List<Map<String, String>> hits = new ArrayList<>();
        hits.add(hit1);
        hits.add(hit2);
        hits.add(hit3);
        PowerMockito.when(alarmClient.getReportAlarmData(Mockito.any(), Mockito.anyString())).thenReturn(hits);

        PowerMockito.mockStatic(ConfigurationUtil.class);
        ConfigData configData = new ConfigData();
        configData.setValue("true");
        PowerMockito.when(ConfigurationUtil.getConfigDataByName(Mockito.anyString())).thenReturn(configData);

        MoTipsDetail moTipsDetail = topoService.displayMoTips(dn);

        Assert.assertEquals("OSS", moTipsDetail.getDnName());
        Assert.assertTrue(moTipsDetail.getVirtualMo());
        Integer eventLeverCount = moTipsDetail.getAlarmLever()
            .stream()
            .filter(v -> "event".equals(v.getAlarmLeverName()))
            .findFirst()
            .get()
            .getAlarmLeverCount();

        Assert.assertEquals(new Integer(3), eventLeverCount);

        Integer criticalLeverCount = moTipsDetail.getAlarmLever()
            .stream()
            .filter(v -> "critical".equals(v.getAlarmLeverName()))
            .findFirst()
            .get()
            .getAlarmLeverCount();

        Assert.assertEquals(new Integer(1), criticalLeverCount);
    }

}
