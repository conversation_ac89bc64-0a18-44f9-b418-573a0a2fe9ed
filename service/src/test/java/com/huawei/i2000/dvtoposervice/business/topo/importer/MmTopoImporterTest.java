/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.topo.importer;

import com.huawei.baize.base.cast.CastUtils;
import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.BusinessTopoInsTreeNode;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInsExtentAttrDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppGroupInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppInstanceConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppInstanceInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.ConfigurationImportEntity;
import com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.SiteGroupSlicerTest;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessIndicatorDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.model.BusinessIndicator;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.util.EamUtil;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.i2000.gapi.account.mo.AccountMO;
import com.huawei.oms.eam.mo.ManagedObject;

import com.alibaba.fastjson2.JSON;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * cbs导入类测试
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@PrepareForTest({PropertiesUtil.class, EamUtil.class, MITManagerClient.class, CastUtils.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class MmTopoImporterTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static final OssLog LOGGER = OssLogFactory.getLogger(SiteGroupSlicerTest.class);

    private static final MapOper<ConfigData> BUSINESS_CONFIG_MAP_OPER = new MapOper<>(
        RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);


    @Autowired
    BusinessInstanceModelDao businessInstanceModelDao;

    @Autowired
    BusinessIndicatorDao businessIndicatorDao;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_overview.sql", false);
    }

    @Test
    public void handleAndInsertDbInstanceTest() throws OSSException {
        prepareDbInstance();
        Map<Integer, Map<String, List<BusinessTopoInsTreeNode>>> siteInsIdToMoMappingResInsList = new HashMap<>();
        ConfigurationImportEntity configurationImportEntity = new ConfigurationImportEntity();
        Map<String, String> moTypeMappingToModelName = new HashMap<>();
        List<String> dbAppInsMoTypes = new ArrayList<>();
        dbAppInsMoTypes.add("com.huawei.gaussdb");
        BusinessAppInstanceConfig businessAppInstanceConfig = new BusinessAppInstanceConfig();
        configurationImportEntity.setBusinessAppInstanceConfig(businessAppInstanceConfig);
        businessAppInstanceConfig.setModelType(6);
        Map<String, BusinessInstanceModelDB > siteIdMap = new HashMap<>();
        BusinessInstanceModelDB siteModelDB = new BusinessInstanceModelDB();
        siteModelDB.setInstanceId(32852);
        siteIdMap.put("sitegroupgsu", siteModelDB);
        Map<String, List<BusinessTopoInsTreeNode>> resMap = new HashMap<>();
        siteInsIdToMoMappingResInsList.put(32852, resMap);

        MmTopoImporter importer = new MmTopoImporter();
        importer.handleAndInsertDbInstance(siteInsIdToMoMappingResInsList, configurationImportEntity,
            moTypeMappingToModelName, dbAppInsMoTypes, businessAppInstanceConfig, siteIdMap);

        Assert.assertEquals(1, siteInsIdToMoMappingResInsList.size());
    }

    private static void prepareDbInstance() throws OSSException {
        PowerMockito.mockStatic(EamUtil.class);
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);

        List<ManagedObject> mos = new ArrayList<>();
        ManagedObject managedObject = new AccountMO();
        managedObject.setLogicSite("1");
        managedObject.setDN("moDn");
        managedObject.setHwsSiteName("site");
        managedObject.setHwsGroupName("group");
        managedObject.setHwsSwimLane("b");
        managedObject.setHwsStripe("gsu");
        mos.add(managedObject);
        PowerMockito.when(EamUtil.queryMosByMoType("com.huawei.gaussdb")).thenReturn(mos);
    }

    @Test
    public void buildPlatformInstancesTest() throws OSSException {
        preparePlatformInstance();
        Map<Integer, Map<String, List<BusinessTopoInsTreeNode>>> siteInsIdToMoMappingResInsList = new HashMap<>();
        Map<String, List<String>> moTypeDnSet = new HashMap<>();
        Set<String> needHandleMoTypes = new HashSet<>();
        needHandleMoTypes.add("com.huawei.gaussdb");
        Map<String, BusinessInstanceModelDB> siteDnInstanceMap = new HashMap<>();
        BusinessInstanceModelDB instanceModelDB = new BusinessInstanceModelDB();
        instanceModelDB.setInstanceId(26542);
        siteDnInstanceMap.put("site", instanceModelDB);

        MmTopoImporter importer = new MmTopoImporter();
        importer.buildPlatformInstances(siteInsIdToMoMappingResInsList, moTypeDnSet, needHandleMoTypes, siteDnInstanceMap);

        Assert.assertEquals(0, siteInsIdToMoMappingResInsList.size());
    }

    @Test
    public void buildHsmInstancesTest() throws OSSException {
        preparePlatformInstance();
        Map<Integer, Map<String, List<BusinessTopoInsTreeNode>>> siteInsIdToMoMappingResInsList = new HashMap<>();
        Map<String, List<String>> moTypeDnSet = new HashMap<>();
        Set<String> needHandleMoTypes = new HashSet<>();
        needHandleMoTypes.add("com.huawei.gaussdb");
        Map<String, BusinessInstanceModelDB> siteDnInstanceMap = new HashMap<>();
        BusinessInstanceModelDB instanceModelDB = new BusinessInstanceModelDB();
        instanceModelDB.setInstanceId(26542);
        siteDnInstanceMap.put("site", instanceModelDB);

        MmTopoImporter importer = new MmTopoImporter();
        importer.buildHsmInstances(siteInsIdToMoMappingResInsList, moTypeDnSet, needHandleMoTypes, siteDnInstanceMap);

        Assert.assertEquals(0, siteInsIdToMoMappingResInsList.size());
    }

    private void preparePlatformInstance() {
        PowerMockito.mockStatic(EamUtil.class);
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);

        List<ManagedObject> mos = new ArrayList<>();
        ManagedObject managedObject = new AccountMO();
        managedObject.setDN("moDn");
        managedObject.putClientProperty("siteName", "site");
        managedObject.setHwsGroupName("group");
        managedObject.setHwsSwimLane("b");
        managedObject.setHwsStripe("gsu");
        mos.add(managedObject);
        ManagedObject managedObject2 = new AccountMO();
        managedObject2.setLogicSite("2");
        managedObject2.setDN("moDn");
        managedObject2.putClientProperty("solutionId", "site2");
        mos.add(managedObject2);
        PowerMockito.when(EamUtil.getMoByMoType("com.huawei.gaussdb")).thenReturn(mos);
    }

    @Test
    public void buildDbAppInstancesTest() {
        MmTopoImporter importer = new MmTopoImporter();
        Map<Integer, Map<String, List<BusinessTopoInsTreeNode>>> siteInsIdToMoMappingResInsList = new HashMap<>();
        siteInsIdToMoMappingResInsList.put(1, new HashMap<>());

        String json = "[{\"attrDBList\":[{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsGroupName\",\"attrValue\":\"test02\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsSiteName\",\"attrValue\":\"q3\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsStripe\",\"attrValue\":\"rsu01\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"siteId\",\"attrValue\":\"q3test02rsu01\"}],\"dn\":\"MMSwimLane-**********-rsu01-b\",\"instanceId\":140629,\"modelId\":\"3_site_MM\",\"modelType\":3,\"staticAttrDBList\":[{\"staticAttrName\":\"linkIndicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_Total_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"TPS=TPS\\\"}]\"},{\"staticAttrName\":\"stripeUnitIndicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"indicatorDisplayType\\\":3,\\\"measTypeKey\\\":\\\"TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_By_CommandId_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\",\\\"originalValue\\\":\\\"*\\\"},{\\\"indicatorDisplayType\\\":1,\\\"measTypeKey\\\":\\\"TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"FTPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"SuccessRate\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.SuperGatewaySVR\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"TimeDelay\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.SuperGatewaySVR\\\"}]\"},{\"staticAttrName\":\"indicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"indicatorDisplayType\\\":3,\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_By_CommandId_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"*\\\"},{\\\"indicatorDisplayType\\\":1,\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_Total_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"TPS=TPS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"FTPS\\\",\\\"measUnitKey\\\":\\\"License_FTPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"SuccessRate\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"TimeDelay\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"}]\"},{\"staticAttrName\":\"siteIdColumn\",\"staticAttrType\":\"java.lang.String\",\"staticAttrValue\":\"solutionId\"},{\"staticAttrName\":\"siteMoType\",\"staticAttrType\":\"java.lang.String\",\"staticAttrValue\":\"fintech.Fintech.FCS\"},{\"staticAttrName\":\"siteDataType\",\"staticAttrType\":\"java.lang.Integer\",\"staticAttrValue\":\"3\"}],\"version\":\"XX.2024\"},{\"attrDBList\":[{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsGroupName\",\"attrValue\":\"test02\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsSiteName\",\"attrValue\":\"q3\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsStripe\",\"attrValue\":\"rsu02\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"siteId\",\"attrValue\":\"q3test02rsu02\"}],\"dn\":\"MMSwimLane-**********-rsu02-b\",\"instanceId\":140626,\"modelId\":\"3_site_MM\",\"modelType\":3,\"staticAttrDBList\":[{\"staticAttrName\":\"linkIndicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_Total_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"TPS=TPS\\\"}]\"},{\"staticAttrName\":\"stripeUnitIndicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"indicatorDisplayType\\\":3,\\\"measTypeKey\\\":\\\"TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_By_CommandId_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\",\\\"originalValue\\\":\\\"*\\\"},{\\\"indicatorDisplayType\\\":1,\\\"measTypeKey\\\":\\\"TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"FTPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"SuccessRate\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.SuperGatewaySVR\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"TimeDelay\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.SuperGatewaySVR\\\"}]\"},{\"staticAttrName\":\"indicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"indicatorDisplayType\\\":3,\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_By_CommandId_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"*\\\"},{\\\"indicatorDisplayType\\\":1,\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_Total_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"TPS=TPS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"FTPS\\\",\\\"measUnitKey\\\":\\\"License_FTPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"SuccessRate\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"TimeDelay\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"}]\"},{\"staticAttrName\":\"siteIdColumn\",\"staticAttrType\":\"java.lang.String\",\"staticAttrValue\":\"solutionId\"},{\"staticAttrName\":\"siteMoType\",\"staticAttrType\":\"java.lang.String\",\"staticAttrValue\":\"fintech.Fintech.FCS\"},{\"staticAttrName\":\"siteDataType\",\"staticAttrType\":\"java.lang.Integer\",\"staticAttrValue\":\"3\"}],\"version\":\"XX.2024\"},{\"attrDBList\":[{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsGroupName\",\"attrValue\":\"test02\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsSiteName\",\"attrValue\":\"q3\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsStripe\",\"attrValue\":\"csu02\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"siteId\",\"attrValue\":\"q3test02csu02\"}],\"dn\":\"MMSwimLane-**********-csu02-b\",\"instanceId\":140624,\"modelId\":\"3_site_MM\",\"modelType\":3,\"staticAttrDBList\":[{\"staticAttrName\":\"linkIndicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_Total_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"TPS=TPS\\\"}]\"},{\"staticAttrName\":\"stripeUnitIndicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"indicatorDisplayType\\\":3,\\\"measTypeKey\\\":\\\"TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_By_CommandId_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\",\\\"originalValue\\\":\\\"*\\\"},{\\\"indicatorDisplayType\\\":1,\\\"measTypeKey\\\":\\\"TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"FTPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"SuccessRate\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.SuperGatewaySVR\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"TimeDelay\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.SuperGatewaySVR\\\"}]\"},{\"staticAttrName\":\"indicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"indicatorDisplayType\\\":3,\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_By_CommandId_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"*\\\"},{\\\"indicatorDisplayType\\\":1,\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_Total_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"TPS=TPS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"FTPS\\\",\\\"measUnitKey\\\":\\\"License_FTPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"SuccessRate\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"TimeDelay\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"}]\"},{\"staticAttrName\":\"siteIdColumn\",\"staticAttrType\":\"java.lang.String\",\"staticAttrValue\":\"solutionId\"},{\"staticAttrName\":\"siteMoType\",\"staticAttrType\":\"java.lang.String\",\"staticAttrValue\":\"fintech.Fintech.FCS\"},{\"staticAttrName\":\"siteDataType\",\"staticAttrType\":\"java.lang.Integer\",\"staticAttrValue\":\"3\"}],\"version\":\"XX.2024\"},{\"attrDBList\":[{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsGroupName\",\"attrValue\":\"test02\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsSiteName\",\"attrValue\":\"q3\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsStripe\",\"attrValue\":\"csu01\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"siteId\",\"attrValue\":\"q3test02csu01\"}],\"dn\":\"MMSwimLane-**********-csu01-b\",\"instanceId\":140625,\"modelId\":\"3_site_MM\",\"modelType\":3,\"staticAttrDBList\":[{\"staticAttrName\":\"linkIndicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_Total_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"TPS=TPS\\\"}]\"},{\"staticAttrName\":\"stripeUnitIndicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"indicatorDisplayType\\\":3,\\\"measTypeKey\\\":\\\"TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_By_CommandId_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\",\\\"originalValue\\\":\\\"*\\\"},{\\\"indicatorDisplayType\\\":1,\\\"measTypeKey\\\":\\\"TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"FTPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"SuccessRate\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.SuperGatewaySVR\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"TimeDelay\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.SuperGatewaySVR\\\"}]\"},{\"staticAttrName\":\"indicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"indicatorDisplayType\\\":3,\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_By_CommandId_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"*\\\"},{\\\"indicatorDisplayType\\\":1,\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_Total_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"TPS=TPS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"FTPS\\\",\\\"measUnitKey\\\":\\\"License_FTPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"SuccessRate\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"TimeDelay\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"}]\"},{\"staticAttrName\":\"siteIdColumn\",\"staticAttrType\":\"java.lang.String\",\"staticAttrValue\":\"solutionId\"},{\"staticAttrName\":\"siteMoType\",\"staticAttrType\":\"java.lang.String\",\"staticAttrValue\":\"fintech.Fintech.FCS\"},{\"staticAttrName\":\"siteDataType\",\"staticAttrType\":\"java.lang.Integer\",\"staticAttrValue\":\"3\"}],\"version\":\"XX.2024\"},{\"attrDBList\":[{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsGroupName\",\"attrValue\":\"test02\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsSiteName\",\"attrValue\":\"q3\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsStripe\",\"attrValue\":\"csu00\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"siteId\",\"attrValue\":\"q3test02csu00\"}],\"dn\":\"MMSwimLane-**********-csu00-b\",\"instanceId\":140630,\"modelId\":\"3_site_MM\",\"modelType\":3,\"staticAttrDBList\":[{\"staticAttrName\":\"linkIndicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_Total_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"TPS=TPS\\\"}]\"},{\"staticAttrName\":\"stripeUnitIndicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"indicatorDisplayType\\\":3,\\\"measTypeKey\\\":\\\"TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_By_CommandId_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\",\\\"originalValue\\\":\\\"*\\\"},{\\\"indicatorDisplayType\\\":1,\\\"measTypeKey\\\":\\\"TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"FTPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"SuccessRate\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.SuperGatewaySVR\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"TimeDelay\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.SuperGatewaySVR\\\"}]\"},{\"staticAttrName\":\"indicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"indicatorDisplayType\\\":3,\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_By_CommandId_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"*\\\"},{\\\"indicatorDisplayType\\\":1,\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_Total_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"TPS=TPS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"FTPS\\\",\\\"measUnitKey\\\":\\\"License_FTPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"SuccessRate\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"TimeDelay\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"}]\"},{\"staticAttrName\":\"siteIdColumn\",\"staticAttrType\":\"java.lang.String\",\"staticAttrValue\":\"solutionId\"},{\"staticAttrName\":\"siteMoType\",\"staticAttrType\":\"java.lang.String\",\"staticAttrValue\":\"fintech.Fintech.FCS\"},{\"staticAttrName\":\"siteDataType\",\"staticAttrType\":\"java.lang.Integer\",\"staticAttrValue\":\"3\"}],\"version\":\"XX.2024\"},{\"attrDBList\":[{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsGroupName\",\"attrValue\":\"test02\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsSiteName\",\"attrValue\":\"q3\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsStripe\",\"attrValue\":\"2\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"siteId\",\"attrValue\":\"q3test022\"}],\"dn\":\"MMSwimLane-**********-2-b\",\"instanceId\":140627,\"modelId\":\"3_site_MM\",\"modelType\":3,\"staticAttrDBList\":[{\"staticAttrName\":\"linkIndicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_Total_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"TPS=TPS\\\"}]\"},{\"staticAttrName\":\"stripeUnitIndicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"indicatorDisplayType\\\":3,\\\"measTypeKey\\\":\\\"TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_By_CommandId_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\",\\\"originalValue\\\":\\\"*\\\"},{\\\"indicatorDisplayType\\\":1,\\\"measTypeKey\\\":\\\"TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"FTPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"SuccessRate\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.SuperGatewaySVR\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"TimeDelay\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.SuperGatewaySVR\\\"}]\"},{\"staticAttrName\":\"indicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"indicatorDisplayType\\\":3,\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_By_CommandId_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"*\\\"},{\\\"indicatorDisplayType\\\":1,\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_Total_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"TPS=TPS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"FTPS\\\",\\\"measUnitKey\\\":\\\"License_FTPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"SuccessRate\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"TimeDelay\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"}]\"},{\"staticAttrName\":\"siteIdColumn\",\"staticAttrType\":\"java.lang.String\",\"staticAttrValue\":\"solutionId\"},{\"staticAttrName\":\"siteMoType\",\"staticAttrType\":\"java.lang.String\",\"staticAttrValue\":\"fintech.Fintech.FCS\"},{\"staticAttrName\":\"siteDataType\",\"staticAttrType\":\"java.lang.Integer\",\"staticAttrValue\":\"3\"}],\"version\":\"XX.2024\"},{\"attrDBList\":[{\"attrClass\":\"java.lang.Integer\",\"attrName\":\"AlarmCount\",\"attrValue\":\"28\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"AlarmCsn\",\"attrValue\":\"357994409,359069022,358053832,361640258,361640257,360728552,357994410,361640260,357934094,357995102,357934095,357934092,360728554,357934093,358936840,358053834,358053835,358049159,358053837,358053838,357576106,360877900,360877901,361165359,358709767,360878134,357576103,357576104\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"EventCsn\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"OwnCsn\",\"attrValue\":\"357994409,359069022,358053832,361640258,361640257,360728552,357994410,361640260,357934094,357995102,357934095,357934092,360728554,357934093,358936840,358053834,358053835,358049159,358053837,358053838,357576106,360877900,360877901,361165359,358709767,360878134,357576103,357576104\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsGroupName\",\"attrValue\":\"test02\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsSiteName\",\"attrValue\":\"q3\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"hwsStripe\",\"attrValue\":\"gsu\"},{\"attrClass\":\"java.lang.String\",\"attrName\":\"siteId\",\"attrValue\":\"q3test02gsu\"}],\"dn\":\"MMSwimLane-**********-gsu-b\",\"instanceId\":140628,\"modelId\":\"3_site_MM\",\"modelType\":3,\"staticAttrDBList\":[{\"staticAttrName\":\"linkIndicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_Total_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"TPS=TPS\\\"}]\"},{\"staticAttrName\":\"stripeUnitIndicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"indicatorDisplayType\\\":3,\\\"measTypeKey\\\":\\\"TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_By_CommandId_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\",\\\"originalValue\\\":\\\"*\\\"},{\\\"indicatorDisplayType\\\":1,\\\"measTypeKey\\\":\\\"TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"FTPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.LicenseCenter\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"SuccessRate\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.SuperGatewaySVR\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"TimeDelay\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Pr\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS.SuperGatewaySVR\\\"}]\"},{\"staticAttrName\":\"indicatorList\",\"staticAttrType\":\"java.util.List|com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo\",\"staticAttrValue\":\"[{\\\"indicatorDisplayType\\\":3,\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_TPS_By_CommandId_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"*\\\"},{\\\"indicatorDisplayType\\\":1,\\\"measTypeKey\\\":\\\"Total_TPS\\\",\\\"measUnitKey\\\":\\\"License_Total_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\",\\\"originalValue\\\":\\\"TPS=TPS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"FTPS\\\",\\\"measUnitKey\\\":\\\"License_FTPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"SuccessRate\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"},{\\\"indicatorDisplayType\\\":0,\\\"measTypeKey\\\":\\\"TimeDelay\\\",\\\"measUnitKey\\\":\\\"AGSVR_TPS_Stripe\\\",\\\"moType\\\":\\\"fintech.Fintech.FCS\\\"}]\"},{\"staticAttrName\":\"siteIdColumn\",\"staticAttrType\":\"java.lang.String\",\"staticAttrValue\":\"solutionId\"},{\"staticAttrName\":\"siteMoType\",\"staticAttrType\":\"java.lang.String\",\"staticAttrValue\":\"fintech.Fintech.FCS\"},{\"staticAttrName\":\"siteDataType\",\"staticAttrType\":\"java.lang.Integer\",\"staticAttrValue\":\"3\"}],\"version\":\"XX.2024\"}]";
        List<BusinessInstanceModelDB> solutionInsDbs = JSON.parseArray(json, BusinessInstanceModelDB.class);

        ConfigurationImportEntity configurationImportEntity = new ConfigurationImportEntity();
        BusinessAppInstanceConfig businessAppInstanceConfig = new BusinessAppInstanceConfig();
        businessAppInstanceConfig.setModelType(6);
        List<BusinessAppInstanceInfo> appInstanceInfos = new ArrayList<>();
        BusinessAppInstanceInfo appInstanceInfo = new BusinessAppInstanceInfo();
        appInstanceInfo.setApplicationType(0);
        appInstanceInfos.add(appInstanceInfo);
        businessAppInstanceConfig.setBusinessAppInstanceList(appInstanceInfos);
        configurationImportEntity.setBusinessAppInstanceConfig(businessAppInstanceConfig);
        Map<String, String> moTypeMappingToModelName = new HashMap<>();

        List<BusinessInstanceModelDB> result = importer.buildDbAppInstances(siteInsIdToMoMappingResInsList, solutionInsDbs, configurationImportEntity, moTypeMappingToModelName);

        Assert.assertEquals(0, result.size());
    }

    @Test
    public void isRequiredGroupTest() {
        BusinessAppGroupInfo businessAppGroupInfo = new BusinessAppGroupInfo();
        BusinessInstanceModelDB siteIns = new BusinessInstanceModelDB();
        MmTopoImporter importer = new MmTopoImporter();

        boolean emptyStripe = importer.isRequiredGroup(businessAppGroupInfo, siteIns);
        Assert.assertTrue(emptyStripe);

        businessAppGroupInfo.setBelongStripe("gsu");
        boolean emptySiteStripe = importer.isRequiredGroup(businessAppGroupInfo, siteIns);
        Assert.assertFalse(emptySiteStripe);

        BusinessInsExtentAttrDB attrDB = new BusinessInsExtentAttrDB();
        attrDB.setAttrName("hwsStripe");
        attrDB.setAttrValue("gsu");
        List<BusinessInsExtentAttrDB> attrDBList = Collections.singletonList(attrDB);
        siteIns.setAttrDBList(attrDBList);

        boolean gsuStripe = importer.isRequiredGroup(businessAppGroupInfo, siteIns);
        Assert.assertTrue(gsuStripe);

        attrDB.setAttrValue("csu00");
        boolean csuStripe = importer.isRequiredGroup(businessAppGroupInfo, siteIns);
        Assert.assertTrue(csuStripe);

        attrDB.setAttrValue("csu01");
        businessAppGroupInfo.setBelongStripe("rsu");
        boolean csuRsuStripe = importer.isRequiredGroup(businessAppGroupInfo, siteIns);
        Assert.assertTrue(csuRsuStripe);
    }

    @Test
    public void insertIndicatorsForAggregateMmTest() {
        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);
        ConfigData configGroupName = new ConfigData();
        configGroupName.setConfigItemName("mmHwsGroupName");
        configGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configGroupName);

        MmTopoImporter importer = new MmTopoImporter();
        importer.insertIndicatorsForAggregateMm(new ArrayList<>(), 1, "gsu");

        List<IndicatorInfo> indicatorList = new ArrayList<>();
        IndicatorInfo indicatorInfo = new IndicatorInfo();
        indicatorInfo.setOriginalValue(null);
        indicatorList.add(indicatorInfo);
        IndicatorInfo indicatorInfoStar = new IndicatorInfo();
        indicatorInfoStar.setOriginalValue("cu_727823hd=");
        indicatorList.add(indicatorInfoStar);
        indicatorList.add(indicatorInfo);
        importer.insertIndicatorsForAggregateMm(indicatorList, 1, "gsu");

        List<BusinessIndicator> indicators = businessIndicatorDao.queryIndicatorByInstanceId(1, 0L);

        Assert.assertEquals(2, indicators.size());
    }

    @Test
    public void buildSiteGroupTest() throws ServiceException {
        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);
        ConfigData configGroupName = new ConfigData();
        configGroupName.setConfigItemName("mmHwsGroupName");
        configGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configGroupName);

        MmTopoImporter importer = new MmTopoImporter();
        ConfigurationImportEntity configurationImportEntity = new ConfigurationImportEntity();
        configurationImportEntity.getSiteGroupConfig().setIndicatorList(Collections.singletonList(new IndicatorInfo()));
        BusinessInstanceModelDB dvSolutionInstanceDb = new BusinessInstanceModelDB();
        dvSolutionInstanceDb.setInstanceId(1);

        PowerMockito.mockStatic(CastUtils.class);
        BusinessInstanceModelDB modelDB = new BusinessInstanceModelDB();
        modelDB.setInstanceId(2);
        List<BusinessInsExtentAttrDB> attrDBS = new ArrayList<>();
        BusinessInsExtentAttrDB attrDB = new BusinessInsExtentAttrDB(2, "hwsStripe", null, "gsu");
        attrDBS.add(attrDB);
        BusinessInsExtentAttrDB attrDB2 = new BusinessInsExtentAttrDB(2, "hwsSiteName", null, "site");
        attrDBS.add(attrDB2);
        BusinessInsExtentAttrDB attrDB3 = new BusinessInsExtentAttrDB(2, "hwsGroupName", null, "group");
        attrDBS.add(attrDB3);
        modelDB.setAttrDBList(attrDBS);
        PowerMockito.when(CastUtils.castList(Mockito.any())).thenReturn(Collections.singletonList(modelDB));

        List<BusinessInstanceModelDB> groups = importer.buildSiteGroup(configurationImportEntity, dvSolutionInstanceDb);

        Assert.assertEquals(1, groups.size());
    }
}
