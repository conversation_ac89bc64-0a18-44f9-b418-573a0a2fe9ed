/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.event;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.cache.redis.EmbeddedRedisServer;
import com.huawei.baize.servicesimulator.cache.redis.EmbeddedRedisServerUtils;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.database.BusinessCacheInstance;
import com.huawei.i2000.dvtoposervice.business.database.BusinessCommonModel;
import com.huawei.i2000.dvtoposervice.business.database.BusinessCommonModelExtentAttr;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInsExtentAttrDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppInstanceConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppInstanceInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessMoEvent;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessMoKafkaEvent;
import com.huawei.i2000.dvtoposervice.business.service.bean.ConfigurationImportEntity;
import com.huawei.i2000.dvtoposervice.business.service.bean.RelationEvent;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.business.topo.redis.RedisExOper;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessCommonModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessCommonModelExtentAttrDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceRelationDao;
import com.huawei.i2000.dvtoposervice.event.BusinessTopoHandler;
import com.huawei.i2000.dvtoposervice.event.MoEventHandler;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.i2000.gapi.account.mo.AccountMO;
import com.huawei.oms.eam.mim.MORelation;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.MOType;
import com.huawei.oms.eam.mo.ManagedObject;

import com.alibaba.fastjson2.JSON;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * 业务拓扑变更事件测试类
 *
 * <AUTHOR>
 * @since 2024/04/13
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class, MITManagerClient.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessTopoHandlerTest extends WebServiceTest {

    private static EmbeddedRestClientAndServer mockServer;

    private static EmbeddedRedisServer redisServer;

    private static final MapOper<ConfigData> BUSINESS_CONFIG_MAP_OPER = new MapOper<>(
        RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);

    private static final RedisExOper REDIS_EX_OPER = new RedisExOper(RedisConstant.MO_STORE_LIST, RedisConstant.REDIS_NAME);

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    BusinessTopoHandler businessTopoHandler;

    @Autowired
    BusinessInstanceModelDao businessInstanceModelDao;

    @Autowired
    BusinessCommonModelDao businessCommonModelDao;

    @Autowired
    BusinessCommonModelExtentAttrDao businessCommonModelExtentAttrDao;

    @Autowired
    MoEventHandler moEventHandler;

    @Autowired
    private BusinessInstanceRelationDao businessInstanceRelationDao;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_event.sql", false);
        mockProperties();
        redisServer = EmbeddedRedisServerUtils.startGlobalRedisServer();
        Assert.assertNotNull(redisServer);
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    private void mockProperties() {
        PowerMockito.mockStatic(PropertiesUtil.class);
        Properties properties = new Properties();
        properties.put(BusinessTopoConstant.MOTYPE_CLUSTER_TYPE, "5");
        properties.put(BusinessTopoConstant.POD_TYPE, "7");
        PowerMockito.when(PropertiesUtil.loadProperties(Mockito.anyString())).thenReturn(properties);
        MapOper<BusinessCommonModel> businessMoTypeMapOper = new MapOper<>(
            RedisConstant.BUSINESS_TOPO_MO_COMMON_MODEL, RedisConstant.REDIS_NAME);
        BusinessCommonModel commonModel = new BusinessCommonModel();
        commonModel.setModelId("6_instance");
        commonModel.setModelType(6);
        List<BusinessCommonModelExtentAttr> attrs = getCommonModelExtentAttrs();
        commonModel.setExtentAttrs(attrs);
        businessMoTypeMapOper.put(RedisConstant.BUSINESS_TOPO_MO_COMMON_MODEL, "instance", commonModel);
        commonModel.setModelId("7_pod");
        commonModel.setModelType(7);
        businessMoTypeMapOper.put(RedisConstant.BUSINESS_TOPO_MO_COMMON_MODEL, "podType", commonModel);
        commonModel.setModelId("8_vm");
        commonModel.setModelType(8);
        businessMoTypeMapOper.put(RedisConstant.BUSINESS_TOPO_MO_COMMON_MODEL, "vmType", commonModel);
    }

    private static List<BusinessCommonModelExtentAttr> getCommonModelExtentAttrs() {
        List<BusinessCommonModelExtentAttr> attrs = new ArrayList<>();
        BusinessCommonModelExtentAttr attr = new BusinessCommonModelExtentAttr();
        attr.setStaticAttrName("parentMoTypeName");
        attr.setStaticAttrValue("appType");
        attrs.add(attr);
        BusinessCommonModelExtentAttr attr2 = new BusinessCommonModelExtentAttr();
        attr2.setStaticAttrName("businessSiteConfig");
        attr2.setStaticAttrValue("{\"siteIdColumn\":\"siteId\",\"type\":1}");
        attrs.add(attr2);
        return attrs;
    }

    /**
     * 新增应用实例
     *
     * @throws Exception Exception
     */
    @Test
    public void addMoEventInstanceTest() throws Exception {
        // prepare
        MapOper<BusinessCacheInstance> businessTreeMapOper = new MapOper<>(
            RedisConstant.BUSINESS_TOPO_RELATION_TREE, RedisConstant.REDIS_NAME);
        businessTreeMapOper.put(RedisConstant.BUSINESS_TOPO_RELATION_TREE, "podDn", new BusinessCacheInstance());
        ManagedObject mo = presetMo();
        String parentDn = "appType";

        Exception e = null;
        try {
            businessTopoHandler.addMoEvent(mo, parentDn);
        } catch (Exception ex) {
            e = ex;
        }
        Assert.assertNull(e);
    }

    private ManagedObject presetMo() throws OSSException {
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        ManagedObject managedObject = new AccountMO();
        managedObject.setParent(new DN("addMoDn"));
        PowerMockito.when(mitManagerClient.getMO(Mockito.anyString())).thenReturn(managedObject);
        List<MORelation> relations = new ArrayList<>();
        MORelation relation = new MORelation(new DN("podNewDn"), new DN("8_vm"), "deploy");
        relations.add(relation);
        PowerMockito.when(mitManagerClient.getAllRelationsByDns(Mockito.anyList(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(relations);

        MOType moType = new MOType();
        moType.setDisplayType("instance");
        PowerMockito.when(mitManagerClient.getMOType("instance")).thenReturn(moType);
        ManagedObject mo = JSON.parseObject("{\"alarmStatus\":\"Cleared\",\"children\":[],\"clientProperties\":"
            + "{\"siteId\":\"abc-1\"},\"createdTime\":0,\"dN\":{\"empty\":false,\"parent\":{\"empty\":false,\"parent\":"
            + "{\"$ref\":\"@\"},\"root\":true,\"value\":\"/\"},\"root\":false,\"value\":\"addMoDn\"},"
            + "\"description\":\"\",\"displayName\":\"\",\"fdn\":\"\",\"leaf\":true,\"level\":0,\"managed\":false,"
            + "\"medNodeID\":\"\",\"msId\":\"\",\"nE\":false,\"name\":\"\",\"owner\":\"\",\"sequenceNo\":0,"
            + "\"type\":\"instance\",\"typeID\":0,\"vendor\":\"\",\"version\":\"\"}", AccountMO.class);
        mo.setType("instance");
        mo.setDN("addMoDn");
        mo.getClientProperties().put("siteId", "1");
        return mo;
    }

    /**
     * 新增Pod
     *
     * @throws Exception Exception
     */
    @Test
    public void addMoEventPodTest() throws Exception {
        // prepare
        ManagedObject managedObject = new AccountMO();
        managedObject.setParent(new DN("addMoDn"));
        managedObject.setType("podType");
        managedObject.setDN("podNewDn");
        String parentDn = "podFatherDn";

        Exception e = null;
        try {
            businessTopoHandler.addMoEvent(managedObject, parentDn);
        } catch (Exception ex) {
            e = ex;
        }
        Assert.assertNull(e);
    }

    /**
     * 新增事件
     *
     * @throws Exception Exception
     */
    @Test
    public void handleMoEventTest() throws Exception {
        ManagedObject mo = presetMo();
        List<BusinessMoEvent> moEvents = presetBusinessMoEvents(mo);
        presetDeleteAndChangeEvents(moEvents);
        BusinessMoEvent businessMoEvent = new BusinessMoEvent(mo, "parent", new BusinessCommonModel(), "type");
        BusinessMoKafkaEvent event = new BusinessMoKafkaEvent(businessMoEvent);

        List<String> dns = new ArrayList<>();
        dns.add("addMoDn");
        dns.add("podNewDn");
        dns.add("dockerNewDn");
        dns.add("vmNewDn");
        businessTopoHandler.handleMoEvents(moEvents);

        List<BusinessInstanceModelDB> queryInstanceByDns = businessInstanceModelDao.queryInstanceByDns(dns);
        Assert.assertEquals(4, queryInstanceByDns.size());

        BusinessInstanceModelDB deleteModel = businessInstanceModelDao.queryInstanceByDn("deleteInstanceDn");
        Assert.assertNull(deleteModel);

        // assert
        BusinessInstanceModelDB changeModel = businessInstanceModelDao.queryInstanceByDn("podFatherDn-1");
        Assert.assertEquals("R24C10", changeModel.getAttrDBList().get(2).getAttrValue());
    }

    /**
     * 新增BES实例事件
     *
     * @throws Exception Exception
     */
    @Test
    public void handleBesMoEventTest() throws Exception {
        ManagedObject mo = presetMo();
        mo.removeClientProperty("siteId");
        mo.putClientProperty("solutionId", "siteDn");
        List<BusinessMoEvent> moEvents = presetBusinessMoEvents(mo);
        presetDeleteAndChangeEvents(moEvents);

        List<String> dns = new ArrayList<>();
        dns.add("addMoDn");
        dns.add("vmNewDn");
        businessTopoHandler.handleMoEvents(moEvents);

        List<BusinessInstanceModelDB> queryInstanceByDns = businessInstanceModelDao.queryInstanceByDns(dns);
        Assert.assertEquals(1, queryInstanceByDns.size());
    }

    /**
     * 导入中进行变更事件
     *
     * @throws Exception Exception
     */
    @Test
    public void handleMoEventImportingTest() throws Exception {
        REDIS_EX_OPER.setEx(BusinessTopoConstant.CONFIGURATION_IMPORT_TASK, 1800, "RUNNING");
        ManagedObject mo = presetMo();
        List<BusinessMoEvent> moEvents = presetBusinessMoEvents(mo);

        List<String> dns = new ArrayList<>();
        dns.add("addMoDn");
        dns.add("podNewDn");
        dns.add("dockerNewDn");
        dns.add("vmNewDn");
        businessTopoHandler.handleMoEvents(moEvents);
        REDIS_EX_OPER.delete(BusinessTopoConstant.CONFIGURATION_IMPORT_TASK);

        List<BusinessInstanceModelDB> queryInstanceByDns = businessInstanceModelDao.queryInstanceByDns(dns);
        Assert.assertEquals(0, queryInstanceByDns.size());
    }

    private List<BusinessMoEvent> presetBusinessMoEvents(ManagedObject mo) {
        String parentDn = "appType";
        BusinessCommonModel commonModel = businessCommonModelDao.queryCommonModelByModelId("6_instance");
        commonModel.setExtentAttrs(businessCommonModelExtentAttrDao.queryCommonModelAttrByModelId("6_instance"));
        List<BusinessMoEvent> moEvents = new ArrayList<>();
        BusinessMoEvent businessMoEvent = new BusinessMoEvent(mo, parentDn, commonModel, BusinessTopoConstant.MO_EVENT_ADD);
        moEvents.add(businessMoEvent);
        ManagedObject podMo = new AccountMO();
        podMo.setParent(new DN("addMoDn"));
        podMo.setType("podType");
        podMo.setDN("podNewDn");
        String podParentDn = "podFatherDn-1";
        BusinessCommonModel podCommonModel = businessCommonModelDao.queryCommonModelByModelId("7_pod");
        podCommonModel.setExtentAttrs(businessCommonModelExtentAttrDao.queryCommonModelAttrByModelId("7_pod"));
        BusinessMoEvent addPodEvent = new BusinessMoEvent(podMo, podParentDn, podCommonModel, BusinessTopoConstant.MO_EVENT_ADD);
        moEvents.add(addPodEvent);
        ManagedObject dockerMo = new AccountMO();
        dockerMo.setParent(new DN("fatherDockerDn"));
        dockerMo.setType("dockerType");
        dockerMo.setDN("dockerNewDn");
        String dockerParentDn = "fatherDockerDn";
        BusinessCommonModel dockerCommonModel = businessCommonModelDao.queryCommonModelByModelId("9_docker");
        dockerCommonModel.setExtentAttrs(businessCommonModelExtentAttrDao.queryCommonModelAttrByModelId("9_docker"));
        BusinessMoEvent addDockerEvent = new BusinessMoEvent(dockerMo, dockerParentDn, dockerCommonModel, BusinessTopoConstant.MO_EVENT_ADD);
        moEvents.add(addDockerEvent);
        ManagedObject vmMo = new AccountMO();
        vmMo.setParent(new DN("fatherVmDn"));
        vmMo.setType("vmType");
        vmMo.setDN("vmNewDn");
        String vmMoDn = "fatherVmDn";
        BusinessCommonModel vmCommonModel = businessCommonModelDao.queryCommonModelByModelId("8_vm");
        vmCommonModel.setExtentAttrs(businessCommonModelExtentAttrDao.queryCommonModelAttrByModelId("8_vm"));
        BusinessMoEvent addVmEvent = new BusinessMoEvent(vmMo, vmMoDn, vmCommonModel, BusinessTopoConstant.MO_EVENT_ADD);
        moEvents.add(addVmEvent);
        ManagedObject siteMo = new AccountMO();
        siteMo.setType("siteNewType");
        siteMo.setDN("siteNewDn");
        BusinessCommonModel siteCommonModel = businessCommonModelDao.queryCommonModelByModelId("3_site");
        siteCommonModel.setExtentAttrs(businessCommonModelExtentAttrDao.queryCommonModelAttrByModelId("3_site"));
        BusinessMoEvent addSiteEvent = new BusinessMoEvent(siteMo, vmMoDn, siteCommonModel, BusinessTopoConstant.MO_EVENT_ADD);
        moEvents.add(addSiteEvent);
        return moEvents;
    }

    private void presetDeleteAndChangeEvents(List<BusinessMoEvent> moEvents) {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("alarmSubscribeLevel");
        configData.setValue("0");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, configData.getConfigItemName(), configData);
        BusinessCommonModel commonModel = businessCommonModelDao.queryCommonModelByModelId("6_instance");
        commonModel.setExtentAttrs(businessCommonModelExtentAttrDao.queryCommonModelAttrByModelId("6_instance"));
        ManagedObject insMo = new AccountMO();
        insMo.setType("instance");
        insMo.setDN("deleteInstanceDn");
        BusinessMoEvent deleteInsEvent = new BusinessMoEvent(insMo, null, null, BusinessTopoConstant.MO_EVENT_DELETE);
        moEvents.add(deleteInsEvent);
        ManagedObject podMo = new AccountMO();
        podMo.setType("instance");
        podMo.setDN("deleteSiteDn");
        BusinessMoEvent deletePodEvent = new BusinessMoEvent(podMo, null, null, BusinessTopoConstant.MO_EVENT_DELETE);
        moEvents.add(deletePodEvent);
        MapOper<BusinessCacheInstance> businessTreeMapOper = new MapOper<>(
            RedisConstant.BUSINESS_TOPO_RELATION_TREE, RedisConstant.REDIS_NAME);
        BusinessCacheInstance businessCacheInstance = new BusinessCacheInstance();
        List<BusinessInsExtentAttrDB> attrDBList = new ArrayList<>();
        BusinessInsExtentAttrDB attrDB = new BusinessInsExtentAttrDB();
        attrDB.setAttrName("1");
        attrDB.setAttrValue("1");
        attrDBList.add(attrDB);
        businessCacheInstance.setAttrDBList(attrDBList);
        businessTreeMapOper.put(RedisConstant.BUSINESS_TOPO_RELATION_TREE, "5", businessCacheInstance);
        ManagedObject mo = JSON.parseObject("{\"alarmStatus\":\"Cleared\",\"children\":[],\"clientProperties\":"
            + "{\"siteId\":\"1\"},\"createdTime\":0,\"dN\":{\"empty\":false,\"parent\":{\"empty\":false,\"parent\":"
            + "{\"$ref\":\"@\"},\"root\":true,\"value\":\"/\"},\"root\":false,\"value\":\"addMoDn\"},"
            + "\"description\":\"\",\"displayName\":\"\",\"fdn\":\"\",\"leaf\":true,\"level\":0,\"managed\":false,"
            + "\"medNodeID\":\"\",\"msId\":\"\",\"nE\":false,\"name\":\"\",\"owner\":\"\",\"sequenceNo\":0,"
            + "\"type\":\"instance\",\"typeID\":0,\"vendor\":\"\",\"version\":\"\"}", AccountMO.class);
        mo.setType("instance");
        mo.setDN(new DN("podFatherDn-1"));
        mo.setName("moName");
        mo.setVersion("R24C10");
        mo.getClientProperties().put("siteId", "1");
        BusinessMoEvent changePodEvent = new BusinessMoEvent(mo, null, commonModel, BusinessTopoConstant.MO_EVENT_CHANGE);
        moEvents.add(changePodEvent);
    }

    /**
     * 删除应用实例
     *
     * @throws Exception Exception
     */
    @Test
    public void deleteMoEventInstanceTest() throws Exception {
        // prepare
        BusinessInstanceModelDB model = businessInstanceModelDao.queryInstanceByDn("deleteInstanceDn");
        Assert.assertNotNull(model);
        ManagedObject mo = new AccountMO();
        mo.setDN("deleteInstanceDn");
        mo.setType("instance");

        Exception e = null;
        try {
            businessTopoHandler.deleteMoEvent(mo.getDN().getValue(), mo.getType());
        } catch (Exception ex) {
            e = ex;
        }

        Assert.assertNull(e);
    }

    /**
     * 变更应用实例
     *
     * @throws Exception Exception
     */
    @Test
    public void changeMoEventInstanceTest() throws Exception {
        // prepare
        MapOper<BusinessCacheInstance> businessTreeMapOper = new MapOper<>(
            RedisConstant.BUSINESS_TOPO_RELATION_TREE, RedisConstant.REDIS_NAME);
        BusinessCacheInstance businessCacheInstance = new BusinessCacheInstance();
        List<BusinessInsExtentAttrDB> attrDBList = new ArrayList<>();
        businessCacheInstance.setAttrDBList(attrDBList);
        businessTreeMapOper.put(RedisConstant.BUSINESS_TOPO_RELATION_TREE, "5", businessCacheInstance);
        ManagedObject mo = JSON.parseObject("{\"alarmStatus\":\"Cleared\",\"children\":[],\"clientProperties\":"
            + "{\"siteId\":\"1\"},\"createdTime\":0,\"dN\":{\"empty\":false,\"parent\":{\"empty\":false,\"parent\":"
            + "{\"$ref\":\"@\"},\"root\":true,\"value\":\"/\"},\"root\":false,\"value\":\"addMoDn\"},"
            + "\"description\":\"\",\"displayName\":\"\",\"fdn\":\"\",\"leaf\":true,\"level\":0,\"managed\":false,"
            + "\"medNodeID\":\"\",\"msId\":\"\",\"nE\":false,\"name\":\"\",\"owner\":\"\",\"sequenceNo\":0,"
            + "\"type\":\"instance\",\"typeID\":0,\"vendor\":\"\",\"version\":\"\"}", AccountMO.class);
        mo.setType("instance");
        mo.setDN("podFatherDn-1");
        mo.setName("moName");
        mo.setVersion("R24C10");
        mo.getClientProperties().put("siteId", "1");

        BusinessInstanceModelDB model = businessInstanceModelDao.queryInstanceByDn("podFatherDn-1");
        Assert.assertEquals("oldVersion", model.getAttrDBList().get(0).getAttrValue());

        Exception e = null;
        try {
            businessTopoHandler.changeMoEvent(mo);
        } catch (Exception ex) {
            e = ex;
        }

        Assert.assertNull(e);
    }

    /**
     * 新增Vm
     *
     * @throws Exception Exception
     */
    @Test
    public void addMoEventVmTest() throws Exception {
        // prepare
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        List<MORelation> relations = new ArrayList<>();
        MORelation relation = new MORelation(new DN("deletePodDn"), new DN("8_vm"), "deploy");
        relations.add(relation);
        PowerMockito.when(mitManagerClient.getAllRelationsByDns(Mockito.anyList(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(relations);
        ManagedObject managedObject = new AccountMO();
        managedObject.setParent(new DN("vmDn"));
        managedObject.setType("vmType");
        managedObject.setDN("vmNewDn");
        BusinessCommonModel commonModel = new BusinessCommonModel();
        commonModel.setModelId("8_vm");
        commonModel.setModelType(8);
        BusinessMoEvent businessMoEvent = new BusinessMoEvent(managedObject, null, commonModel, "add");
        List<BusinessMoEvent> moEvents = new ArrayList<>();
        moEvents.add(businessMoEvent);

        businessTopoHandler.handleMoEvents(moEvents);

        List<BusinessInstanceModelDB> vmDbs = businessInstanceModelDao.queryAllInstanceOfType(8);
        Assert.assertEquals(2, vmDbs.size());
    }

    @Test
    public void createAppIndicatorTest() {
        Map<String, Integer> dnInstanceMap = new HashMap<>();
        dnInstanceMap.put("dn", 6);
        AccountMO mo = new AccountMO();
        mo.setLogicSite("1");
        Map<String, BusinessMoEvent> map = Collections.singletonMap("dn", new BusinessMoEvent(mo, "parent", new BusinessCommonModel(), "1"));
        BusinessInstanceModelDB instanceModelDB = new BusinessInstanceModelDB();
        instanceModelDB.setDn("dn");
        BusinessCommonModelExtentAttr extentAttr = new BusinessCommonModelExtentAttr();
        extentAttr.setStaticAttrName("applicationType");
        extentAttr.setStaticAttrValue("3");
        instanceModelDB.getStaticAttrDBList().add(extentAttr);
        List<BusinessInstanceModelDB> modelDBS = Collections.singletonList(instanceModelDB);
        Exception e = null;
        try {
            Whitebox.invokeMethod(businessTopoHandler, "buildDbAppInsRelations", modelDBS, map);
            businessTopoHandler.createAppIndicator("6_instance", dnInstanceMap, "instance", false);
        } catch (Exception ex) {
            e = ex;
        }
        Assert.assertNull(e);
    }

    @Test
    public void handleExistDbInstanceTest() {
        List<BusinessAppInstanceInfo> appInstanceInfos = new ArrayList<>();
        BusinessAppInstanceInfo appInstanceInfo = new BusinessAppInstanceInfo();
        appInstanceInfo.setApplicationType(3);
        appInstanceInfos.add(appInstanceInfo);
        ConfigurationImportEntity importEntity = new ConfigurationImportEntity();
        BusinessAppInstanceConfig appInstanceConfig = new BusinessAppInstanceConfig();
        appInstanceConfig.setBusinessAppInstanceList(appInstanceInfos);
        importEntity.setBusinessAppInstanceConfig(appInstanceConfig);

        Exception e = null;
        try {
            businessTopoHandler.handleExistDbInstance(importEntity);
        } catch (Exception ex) {
            e = ex;
        }
        Assert.assertNull(e);
    }

    @Test
    public void handleExistPlatformServiceTest() {
        ConfigurationImportEntity importEntity = new ConfigurationImportEntity();
        List<BusinessAppInstanceInfo> appInstanceInfos = new ArrayList<>();
        BusinessAppInstanceInfo appInstanceInfo = new BusinessAppInstanceInfo();
        BusinessAppInstanceConfig appInstanceConfig = new BusinessAppInstanceConfig();
        appInstanceInfo.setApplicationType(3);
        appInstanceInfos.add(appInstanceInfo);
        BusinessAppInstanceInfo appInstanceInfo1 = new BusinessAppInstanceInfo();
        appInstanceInfo1.setApplicationType(0);
        appInstanceInfos.add(appInstanceInfo1);
        appInstanceConfig.setBusinessAppInstanceList(appInstanceInfos);
        importEntity.setBusinessAppInstanceConfig(appInstanceConfig);
        List<ManagedObject> mos = new ArrayList<>();

        Exception e = null;
        try {
            businessTopoHandler.handleExistPlatformService(importEntity, mos);
        } catch (Exception ex) {
            e = ex;
        }
        Assert.assertNull(e);
    }

    @Test
    public void handleSiteChangeEventsTest() {
        BusinessCommonModel commonModel = businessCommonModelDao.queryCommonModelByModelId("3_site");
        commonModel.setExtentAttrs(businessCommonModelExtentAttrDao.queryCommonModelAttrByModelId("3_site"));
        List<BusinessMoEvent> moEvents = new ArrayList<>();
        ManagedObject mo = new AccountMO();
        mo.setDN("cbs1");
        mo.addChild(new DN("app1"));
        BusinessMoEvent businessMoEvent = new BusinessMoEvent(mo, null, commonModel, BusinessTopoConstant.MO_EVENT_CHANGE);
        moEvents.add(businessMoEvent);
        ManagedObject mo1 = new AccountMO();
        mo1.setDN("cbsChangeSite");
        mo1.addChild(new DN("app1"));
        mo1.putClientProperty("siteId", "cbsChangeSite-99");
        BusinessMoEvent businessMoEvent1 = new BusinessMoEvent(mo1, null, commonModel, BusinessTopoConstant.MO_EVENT_CHANGE);
        moEvents.add(businessMoEvent1);
        ManagedObject mo2 = new AccountMO();
        mo2.setDN("siteDn");
        mo2.addChild(new DN("app1"));
        BusinessMoEvent businessMoEvent2 = new BusinessMoEvent(mo2, null, commonModel, BusinessTopoConstant.MO_EVENT_CHANGE);
        moEvents.add(businessMoEvent2);

        businessTopoHandler.handleMoEvents(moEvents);

        List<BusinessInstanceModelDB> siteModels = businessInstanceModelDao.queryInstancesByModelType(BusinessTopoConstant.SITE_TYPE_ID);
        Assert.assertEquals(4, siteModels.size());
    }

    @Test
    public void handleMmSiteChangeEventsTest() {
        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);
        ConfigData configGroupName = new ConfigData();
        configGroupName.setConfigItemName("mmHwsGroupName");
        configGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configGroupName);

        BusinessCommonModel commonModel = businessCommonModelDao.queryCommonModelByModelId("3_site");
        commonModel.setExtentAttrs(businessCommonModelExtentAttrDao.queryCommonModelAttrByModelId("3_site"));
        List<BusinessMoEvent> moEvents = new ArrayList<>();
        ManagedObject mo1 = new AccountMO();
        mo1.setDN("cbsChangeSite");
        mo1.addChild(new DN("app1"));
        mo1.putClientProperty("siteId", "cbsChangeSite-99");
        mo1.setHwsSiteName("site");
        mo1.setHwsGroupName("group");
        mo1.setHwsStripe("gsu");
        mo1.setHwsSwimLane("b");
        BusinessMoEvent businessMoEvent1 = new BusinessMoEvent(mo1, null, commonModel, BusinessTopoConstant.MO_EVENT_CHANGE);
        moEvents.add(businessMoEvent1);

        businessTopoHandler.handleMoEvents(moEvents);

        List<BusinessInstanceModelDB> siteModels = businessInstanceModelDao.queryInstancesByModelType(BusinessTopoConstant.SITE_TYPE_ID);
        Assert.assertEquals(4, siteModels.size());

        mo1.setHwsSwimLane("g");
        businessTopoHandler.handleMoEvents(moEvents);
        List<BusinessInstanceModelDB> siteModels2 = businessInstanceModelDao.queryInstancesByModelType(BusinessTopoConstant.SITE_TYPE_ID);
        Assert.assertEquals(4, siteModels2.size());
    }

    @Test
    public void handleMoEventHandlerTest() {
        Exception e = null;
        try {
            moEventHandler.handle("new event");
        } catch (Exception ex) {
            e = ex;
        }
        Assert.assertNull(e);
    }

    @Test
    public void handleAddRelationEventsTest() throws ServiceException {
        List<BusinessMoEvent> addRelationEvents = new ArrayList<>();
        RelationEvent relationEvent = new RelationEvent();
        relationEvent.setSrcNode("podDn");
        relationEvent.setDestNode("8_vm");
        relationEvent.setType("Deployment");
        BusinessMoEvent businessMoEvent = new BusinessMoEvent(null,null, null, null, relationEvent);
        addRelationEvents.add(businessMoEvent);
        businessTopoHandler.handleAddRelationEvents(addRelationEvents);
        List<Integer> targetInstance = businessInstanceRelationDao.queryAntiRelationByType(15,1);
        Assert.assertEquals(1, targetInstance.size());
    }

    /**
     * 新增MM数据库实例事件
     *
     * @throws Exception Exception
     */
    @Test
    public void handleMmDbMoEventTest() throws Exception {
        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmDbChangeEventType");
        configSiteName.setValue("[\"bcm.node.hsm.hsmutimaco\",\"com.huawei.venus.VGSftpService.Sftp\",\"com.huawei.gaussdb\"]");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmDbChangeEventType", configSiteName);

        ManagedObject mo = new AccountMO();
        List<BusinessMoEvent> moEvents = presetBusinessMoEvents(mo);
        mo.setDN("gaussDb");
        mo.setType("com.huawei.gaussdb");
        mo.setHwsSiteName("site");
        mo.setHwsGroupName("group");
        mo.setHwsStripe("gsu");
        presetDeleteAndChangeEvents(moEvents);
        BusinessCommonModel commonModel = new BusinessCommonModel();
        commonModel.setModelType(6);
        commonModel.setModelName("gauss");
        BusinessMoEvent changePodEvent = new BusinessMoEvent(mo, null, commonModel, BusinessTopoConstant.MO_EVENT_CHANGE);
        moEvents.add(changePodEvent);

        List<String> dns = new ArrayList<>();
        dns.add("gaussDb");
        businessTopoHandler.handleMoEvents(moEvents);

        List<BusinessInstanceModelDB> queryInstanceByDns = businessInstanceModelDao.queryInstanceByDns(dns);
        Assert.assertEquals(0, queryInstanceByDns.size());
    }

    /**
     * 新增MM数据库实例事件 - 无配置项
     *
     * @throws Exception Exception
     */
    @Test
    public void handleMmDbMoEventNoConfigTest() throws Exception {
        ManagedObject mo = new AccountMO();
        List<BusinessMoEvent> moEvents = presetBusinessMoEvents(mo);
        mo.setDN("gaussDb");
        mo.setType("com.huawei.gaussdb");
        mo.setHwsSiteName("site");
        mo.setHwsGroupName("group");
        mo.setHwsStripe("gsu");
        presetDeleteAndChangeEvents(moEvents);
        BusinessCommonModel commonModel = new BusinessCommonModel();
        commonModel.setModelType(6);
        commonModel.setModelName("gauss");
        BusinessMoEvent changePodEvent = new BusinessMoEvent(mo, null, commonModel, BusinessTopoConstant.MO_EVENT_CHANGE);
        moEvents.add(changePodEvent);

        List<String> dns = new ArrayList<>();
        dns.add("gaussDb");
        businessTopoHandler.handleMoEvents(moEvents);

        List<BusinessInstanceModelDB> queryInstanceByDns = businessInstanceModelDao.queryInstanceByDns(dns);
        Assert.assertEquals(0, queryInstanceByDns.size());
    }

    /**
     * 新增MM管理类实例事件
     *
     * @throws Exception Exception
     */
    @Test
    public void handleMmManagementMoEventTest() throws Exception {
        ManagedObject mo = new AccountMO();
        mo.setDN("omPortal");
        mo.setType("fintech.Fintech.FCS.omportal");
        mo.setHwsSiteName("site");
        mo.setHwsGroupName("group");
        BusinessCommonModel commonModel = new BusinessCommonModel();
        commonModel.setModelType(601);
        commonModel.setModelName("OM Portal");
        commonModel.setModelId("601_OM Portal_MM");
        BusinessMoEvent addEvent = new BusinessMoEvent(mo, null, commonModel, BusinessTopoConstant.MO_EVENT_ADD);
        List<BusinessMoEvent> moEvents = new ArrayList<>();
        moEvents.add(addEvent);

        List<String> dns = new ArrayList<>();
        dns.add("omPortal");
        businessTopoHandler.handleMoEvents(moEvents);

        List<BusinessInstanceModelDB> queryInstanceByDns = businessInstanceModelDao.queryInstanceByDns(dns);
        Assert.assertEquals(1, queryInstanceByDns.size());
    }

    @Test
    public void getRealCommonModelWhenTogetherManageTest() {
        ManagedObject mo = new AccountMO();
        mo.setType("com.huawei.gaussdb");
        mo.putClientProperty("solutionId", "siteDn");
        List<BusinessCommonModel> siteCommonModels = new ArrayList<>();
        BusinessCommonModelExtentAttr extentAttr = new BusinessCommonModelExtentAttr();
        extentAttr.setStaticAttrName("siteDataType");
        extentAttr.setStaticAttrValue("1");
        BusinessCommonModel site1 = new BusinessCommonModel();
        site1.setExtentAttrs(Collections.singletonList(extentAttr));
        site1.setSolutionName("cbs");
        BusinessCommonModel site2 = new BusinessCommonModel();
        site2.setExtentAttrs(Collections.singletonList(extentAttr));
        site2.setSolutionName("mm");
        siteCommonModels.add(site1);
        siteCommonModels.add(site2);
        BusinessCommonModel commonModel = new BusinessCommonModel();

        BusinessCommonModel result = businessTopoHandler.getRealCommonModelWhenTogetherManage(mo, siteCommonModels, commonModel);

        Assert.assertEquals("cbs", result.getSolutionName());
    }
}
