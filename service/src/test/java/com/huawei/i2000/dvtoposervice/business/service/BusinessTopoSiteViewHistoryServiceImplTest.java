/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.builder.SiteViewConcreteBuilder;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.model.MoTypeInfo;
import com.huawei.i2000.dvtoposervice.model.SiteDetailViewDRGroupInfo;
import com.huawei.i2000.dvtoposervice.model.SiteDetailViewParam;
import com.huawei.i2000.dvtoposervice.model.SiteViewGrid;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 业务拓扑站点视图测试类
 *
 * <AUTHOR>
 * @since 2024/04/17
 */
@PrepareForTest({ContextUtils.class, PropertiesUtil.class, MITManagerClient.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessTopoSiteViewHistoryServiceImplTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    @Autowired
    BusinessTopoSiteViewServiceImpl serviceImpl;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    SiteViewConcreteBuilder siteViewConcreteBuilder;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_siteview_real_time.sql", false);
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_siteview_history.sql", false);
        MapOper<ConfigData> businessConfigMapOper = new MapOper<>(
            RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);
        ConfigData configData = new ConfigData();
        configData.setValue("20");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "dvHealthScoreThreshold", configData);
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    private void mockAdmin() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
    }


    @Test
    public void getSiteOverView() throws Exception {
        mockAdmin();
        SiteDetailViewParam siteDetailViewParam = new SiteDetailViewParam();
        siteDetailViewParam.setSiteId(81);
        siteDetailViewParam.setSolutionId(148);
        siteDetailViewParam.setTimestamp(0L);
        SiteViewGrid siteViewGrid = serviceImpl.queryOverviewGrid(siteDetailViewParam);
        Assert.assertEquals("site1", siteViewGrid.getSiteName());
    }

    @Test
    public void getSiteOverViewBySelectedStripeUnit() throws Exception {
        mockAdmin();
        SiteDetailViewParam siteDetailViewParam = new SiteDetailViewParam();
        siteDetailViewParam.setSiteId(186485);
        siteDetailViewParam.setUnitId("pr1");
        siteDetailViewParam.setSolutionId(186469);
        siteDetailViewParam.setTimestamp(0L);
        SiteViewGrid siteViewGrid = serviceImpl.queryOverviewGrid(siteDetailViewParam);
        Assert.assertEquals("rsu01", siteViewGrid.getSiteName());
    }

    @Test
    public void getSiteOverViewByFirstStripeUnit() throws Exception {
        mockAdmin();
        SiteDetailViewParam siteDetailViewParam = new SiteDetailViewParam();
        siteDetailViewParam.setSiteId(186485);
        siteDetailViewParam.setUnitId("");
        siteDetailViewParam.setSolutionId(186469);
        siteDetailViewParam.setTimestamp(0L);
        SiteViewGrid siteViewGrid = serviceImpl.queryOverviewGrid(siteDetailViewParam);
        Assert.assertEquals("rsu01", siteViewGrid.getSiteName());
    }

    @Test
    public void getHistorySiteOverView() throws Exception {
        mockAdmin();
        SiteDetailViewParam siteDetailViewParam = new SiteDetailViewParam();
        siteDetailViewParam.setSiteId(81);
        siteDetailViewParam.setSolutionId(148);
        siteDetailViewParam.setTimestamp(1714087140000L);
        SiteViewGrid siteViewGrid = serviceImpl.queryOverviewGrid(siteDetailViewParam);
        Assert.assertEquals("site1-his", siteViewGrid.getSiteName());
    }

    @Test
    public void mergeSwimLaneTest() {
        SiteViewGrid siteViewGrid = new SiteViewGrid();
        SiteDetailViewDRGroupInfo siteDetailViewDRGroupInfo = new SiteDetailViewDRGroupInfo();
        List<MoTypeInfo> moTypeInfoList = new ArrayList<>();
        MoTypeInfo moTypeInfo = new MoTypeInfo();
        moTypeInfo.setPodErrorCount(1);
        moTypeInfo.setPodCount(1);
        moTypeInfo.setAlarmCount(1);
        moTypeInfo.setMoTypeInstanceCount(1);
        moTypeInfo.setMoTypeName("name");
        moTypeInfoList.add(moTypeInfo);
        moTypeInfoList.add(moTypeInfo);
        siteDetailViewDRGroupInfo.setMoTypeInfoList(moTypeInfoList);
        siteViewGrid.setDrGroupList(Collections.singletonList(siteDetailViewDRGroupInfo));

        siteViewConcreteBuilder.mergeSwimLane(siteViewGrid);

        Assert.assertEquals(2, (int) siteViewGrid.getDrGroupList().get(0).getMoTypeInfoList().get(0).getPodCount());
    }
}
