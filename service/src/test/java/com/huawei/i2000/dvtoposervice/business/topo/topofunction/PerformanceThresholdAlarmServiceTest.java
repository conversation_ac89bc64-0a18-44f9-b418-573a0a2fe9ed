/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.topo.topofunction;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.cache.redis.EmbeddedRedisServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.redis.oper.ListOper;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.util.dto.ThresholdAlarmData;
import com.huawei.i2000.dvtoposervice.util.dto.ThresholdStaticInfo;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * PerformanceThresholdAlarmServiceTest
 *
 * <AUTHOR>
 * @since 2024/5/14
 */
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class PerformanceThresholdAlarmServiceTest extends WebServiceTest {
    private static EmbeddedRedisServer redisServer;

    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    private final ListOper<ThresholdAlarmData> businessThresholdAlarmMapOper = new ListOper<>(
        RedisConstant.BUSINESS_TOPO_THRESHOLD_ALARM, RedisConstant.REDIS_NAME);

    private void mockProperties() {
        MapOper<ConfigData> businessConfigMapOper = new MapOper<>(
            RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);
        ConfigData configData3 = new ConfigData();
        configData3.setConfigItemName("vmindicatorindexname");
        configData3.setValue("\\u5185\\u5B58\\u603B\\u91CF,\\u5185\\u5B58\\u4F7F\\u7528\\u7387,CPU\\u5360\\u7528\\u7387");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "vmindicatorindexname", configData3);
        ConfigData configData4 = new ConfigData();
        configData4.setConfigItemName("vmindicatormeasunitkey");
        configData4.setValue("Memory,Memory,CPU");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "vmindicatormeasunitkey", configData4);
        ConfigData configData5 = new ConfigData();
        configData5.setConfigItemName("vmindicatormeastypekey");
        configData5.setValue("MemTotal,MemUsage,CpuUsage");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "vmindicatormeastypekey", configData5);
    }

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_overview.sql", false);
        mockProperties();
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @Autowired
    PerformanceThresholdAlarmService performanceThresholdAlarmService;

    @Test
    public void thresholdAlarmCronTask() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getpmthresholdalarm.json");
        performanceThresholdAlarmService.thresholdAlarmCronTask();
        List<ThresholdAlarmData> thresholdAlarmData = performanceThresholdAlarmService.getBusinessTopoThresholdAlarm();
        ThresholdStaticInfo thresholdStaticInfo = performanceThresholdAlarmService.getIndicatorInfoByAlarmId(12307);
        Assert.assertEquals(4, thresholdAlarmData.size());
        Assert.assertEquals(12307, thresholdStaticInfo.getAlarmId());
    }
}
