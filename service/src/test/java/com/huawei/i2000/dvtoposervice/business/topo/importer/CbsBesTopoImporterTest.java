/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.topo.importer;

import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.BusinessTopoInsTreeNode;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppInstanceConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.ConfigurationImportEntity;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.SiteGroupSlicerTest;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.util.EamUtil;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.i2000.gapi.account.mo.AccountMO;
import com.huawei.oms.eam.mo.ManagedObject;

import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * cbs导入类测试
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@PrepareForTest({PropertiesUtil.class, EamUtil.class, MITManagerClient.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class CbsBesTopoImporterTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static final OssLog LOGGER = OssLogFactory.getLogger(SiteGroupSlicerTest.class);

    @Autowired
    BusinessInstanceModelDao businessInstanceModelDao;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_overview.sql", false);
    }

    @Test
    public void handleAndInsertDbInstanceTest() throws OSSException {
        prepareDbInstance();
        Map<Integer, Map<String, List<BusinessTopoInsTreeNode>>> siteInsIdToMoMappingResInsList = new HashMap<>();
        ConfigurationImportEntity configurationImportEntity = new ConfigurationImportEntity();
        Map<String, String> moTypeMappingToModelName = new HashMap<>();
        List<String> dbAppInsMoTypes = new ArrayList<>();
        dbAppInsMoTypes.add("com.huawei.gaussdb");
        BusinessAppInstanceConfig businessAppInstanceConfig = new BusinessAppInstanceConfig();
        configurationImportEntity.setBusinessAppInstanceConfig(businessAppInstanceConfig);
        businessAppInstanceConfig.setModelType(6);
        Map<String, BusinessInstanceModelDB > siteIdMap = new HashMap<>();
        BusinessInstanceModelDB siteModelDB = new BusinessInstanceModelDB();
        siteModelDB.setInstanceId(32852);
        siteIdMap.put("1", siteModelDB);
        Map<String, List<BusinessTopoInsTreeNode>> resMap = new HashMap<>();
        siteInsIdToMoMappingResInsList.put(32852, resMap);

        CbsBesTopoImporter importer = new CbsBesTopoImporter();
        importer.handleAndInsertDbInstance(siteInsIdToMoMappingResInsList, configurationImportEntity,
            moTypeMappingToModelName, dbAppInsMoTypes, businessAppInstanceConfig, siteIdMap);

        List<String> dns = Collections.singletonList("moDn");
        List<BusinessInstanceModelDB> models = businessInstanceModelDao.queryInstanceByDnsWithoutExtent(dns);

        Assert.assertTrue(CollectionUtils.isNotEmpty(models));
    }

    private static void prepareDbInstance() throws OSSException {
        PowerMockito.mockStatic(EamUtil.class);
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);

        List<ManagedObject> mos = new ArrayList<>();
        ManagedObject managedObject = new AccountMO();
        managedObject.setLogicSite("1");
        mos.add(managedObject);
        ManagedObject managedObject2 = new AccountMO();
        managedObject2.setLogicSite("2");
        managedObject2.setDN("moDn");
        mos.add(managedObject2);
        PowerMockito.when(mitManagerClient.getMoByType("com.huawei.gaussdb")).thenReturn(mos);
    }

    @Test
    public void buildPlatformInstancesTest() throws OSSException {
        preparePlatformInstance();
        Map<Integer, Map<String, List<BusinessTopoInsTreeNode>>> siteInsIdToMoMappingResInsList = new HashMap<>();
        Map<String, List<String>> moTypeDnSet = new HashMap<>();
        Set<String> needHandleMoTypes = new HashSet<>();
        needHandleMoTypes.add("com.huawei.gaussdb");
        Map<String, BusinessInstanceModelDB> siteDnInstanceMap = new HashMap<>();
        BusinessInstanceModelDB instanceModelDB = new BusinessInstanceModelDB();
        instanceModelDB.setInstanceId(26542);
        siteDnInstanceMap.put("site", instanceModelDB);

        CbsBesTopoImporter importer = new CbsBesTopoImporter();
        importer.buildPlatformInstances(siteInsIdToMoMappingResInsList, moTypeDnSet, needHandleMoTypes, siteDnInstanceMap);

        Assert.assertEquals(1, moTypeDnSet.size());
    }

    private void preparePlatformInstance() {
        PowerMockito.mockStatic(EamUtil.class);
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);

        List<ManagedObject> mos = new ArrayList<>();
        ManagedObject managedObject = new AccountMO();
        managedObject.setLogicSite("1");
        managedObject.setDN("moDn");
        managedObject.putClientProperty("solutionId", "site");
        mos.add(managedObject);
        ManagedObject managedObject2 = new AccountMO();
        managedObject2.setLogicSite("2");
        managedObject2.setDN("moDn");
        managedObject2.putClientProperty("solutionId", "site2");
        mos.add(managedObject2);
        PowerMockito.when(EamUtil.getMoByMoType("com.huawei.gaussdb")).thenReturn(mos);
    }
}
