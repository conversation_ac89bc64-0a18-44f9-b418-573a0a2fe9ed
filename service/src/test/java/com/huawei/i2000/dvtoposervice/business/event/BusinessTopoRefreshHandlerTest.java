/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.event;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.cache.redis.EmbeddedRedisServer;
import com.huawei.baize.servicesimulator.cache.redis.EmbeddedRedisServerUtils;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.database.BusinessCacheInstance;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceRelationModel;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppInstanceConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppInstanceInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.ConfigurationImportEntity;
import com.huawei.i2000.dvtoposervice.business.topo.BusinessTopoDvSelfService;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.event.BusinessTopoRefreshHandler;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.i2000.gapi.account.mo.AccountMO;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.Host;
import com.huawei.oms.eam.mo.ManagedObject;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 业务拓扑变更事件测试类
 *
 * <AUTHOR>
 * @since 2024/04/13
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class, MITManagerClient.class, System.class, BusinessTopoRefreshHandler.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessTopoRefreshHandlerTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    private static EmbeddedRedisServer redisServer;

    private static final MapOper<ConfigData> BUSINESS_CONFIG_MAP_OPER = new MapOper<>(
        RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    BusinessTopoRefreshHandler businessTopoRefreshHandler;

    @Autowired
    BusinessTopoDvSelfService businessTopoDvSelfService;

    @Autowired
    BusinessInstanceModelDao businessInstanceModelDao;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_event.sql", false);
        redisServer = EmbeddedRedisServerUtils.startGlobalRedisServer();
        Assert.assertNotNull(redisServer);
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    @Test
    public void handleOrphanPodAndDockerTest() throws ServiceException, OSSException {
        MapOper<BusinessCacheInstance> businessTreeMapOper = new MapOper<>(
            RedisConstant.BUSINESS_TOPO_RELATION_TREE, RedisConstant.REDIS_NAME);
        businessTreeMapOper.put(RedisConstant.BUSINESS_TOPO_RELATION_TREE, "4", new BusinessCacheInstance());

        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        ManagedObject mo = new AccountMO();
        mo.setName("podName");
        mo.setType("docker");
        mo.setParent(new DN("8_vm"));
        mo.setDN("podDn");
        ManagedObject mo2 = new AccountMO();
        mo2.setName("podName2");
        mo2.setType("docker2");
        mo2.setParent(new DN("8_vm2"));
        mo2.setDN("podDn2");
        List<ManagedObject> destNodes = new ArrayList<>();
        destNodes.add(mo);
        destNodes.add(mo2);
        PowerMockito.when(mitManagerClient.getMOList(Mockito.anyList())).thenReturn(destNodes);

        businessTopoRefreshHandler.handleOrphanPodAndDocker();
        BusinessInstanceModelDB modelDB = businessInstanceModelDao.queryInstanceByInstanceId(16);
        Assert.assertNull(modelDB);
    }

    @Test
    public void refreshPodGrayStatusTest() throws ServiceException {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");
        PowerMockito.mockStatic(System.class);
        PowerMockito.when(System.currentTimeMillis()).thenReturn(1717727780000L);
        businessTopoRefreshHandler.refreshPodGrayStatus();
        BusinessInstanceModelDB modelDB = businessInstanceModelDao.queryInstanceByInstanceId(10);
        Assert.assertEquals("48.91", modelDB.getAttrDBList().get(0).getAttrValue());
    }

    @Test
    public void refreshDvHealthAndDrTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformanceindicator.json");
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("dvIndicators");
        configData.setValue("[{\"moType\": \"OMS\",\"measUnitKey\": \"I2K_OS\",\"measTypeKey\": \"UsedCPURate\"},{\"moType\": \"OMS\",\"measUnitKey\": \"I2K_OS\",\"measTypeKey\": \"UsedMemoryRate\"}]");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, configData.getConfigItemName(), configData);
        configData.setConfigItemName("dvAlarmSeverity");
        configData.setValue("[1]");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, configData.getConfigItemName(), configData);
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        ManagedObject mo = new Host();
        mo.putClientProperty("active_dv_site", "{\"BES\":\"2\",\"noneSolutionName\":\"1\"}");
        mo.putClientProperty("dv_site_info_list", "1:************;2:************|BES:1:************;2:************");
        PowerMockito.when(mitManagerClient.getMO(Mockito.anyString())).thenReturn(mo);
        businessTopoRefreshHandler.refreshDvHealthAndDr();

        List<BusinessInstanceModelDB> modelDBs = businessInstanceModelDao.queryInstanceByDns(Collections.singletonList("OS=1"));
        Assert.assertEquals(0, modelDBs.size());
    }

    @Test
    public void rebuildSiteGroupTest() throws ServiceException {
        businessTopoRefreshHandler.rebuildSiteGroup();
        List<BusinessInstanceModelDB> modelDBs = businessInstanceModelDao.queryAllInstanceOfType(BusinessTopoConstant.SITE_GROUP_TYPE_ID);
        Assert.assertEquals(1, modelDBs.size());
    }

    @Test
    public void handleRefreshInstanceTest() {
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        ManagedObject mo = new AccountMO();
        mo.setName("podName");
        mo.setType("docker");
        mo.setParent(new DN("8_vm"));
        mo.setDN("podDn");
        List<ManagedObject> destNodes = new ArrayList<>();
        destNodes.add(mo);
        PowerMockito.when(mitManagerClient.getMoByParentDn(Mockito.anyList())).thenReturn(destNodes);
        ManagedObject siteMo = new AccountMO();
        siteMo.setDN("siteDn");
        BusinessInstanceModelDB modelDB = new BusinessInstanceModelDB();
        modelDB.setInstanceId(2);

        Exception e = null;
        try {
            businessTopoRefreshHandler.handleRefreshInstance(siteMo, modelDB, "CBS", false, new ConfigurationImportEntity());
        } catch (Exception ex) {
            e = ex;
        }
        Assert.assertNull(e);
    }

    @Test
    public void handleRefreshInstanceMmTest() {
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        ManagedObject mo = new AccountMO();
        mo.setName("podName");
        mo.setType("docker");
        mo.setParent(new DN("8_vm"));
        mo.setDN("podDn");
        List<ManagedObject> destNodes = new ArrayList<>();
        destNodes.add(mo);
        PowerMockito.when(mitManagerClient.getMoByParentDn(Mockito.anyList())).thenReturn(destNodes);
        ManagedObject siteMo = new AccountMO();
        siteMo.setDN("siteDn");
        BusinessInstanceModelDB modelDB = new BusinessInstanceModelDB();
        modelDB.setInstanceId(2);

        ConfigurationImportEntity importEntity = new ConfigurationImportEntity();
        BusinessAppInstanceConfig businessAppInstanceConfig = new BusinessAppInstanceConfig();
        List<BusinessAppInstanceInfo> infos = new ArrayList<>();
        BusinessAppInstanceInfo info = new BusinessAppInstanceInfo();
        info.setMoTypeMapping("vm");
        infos.add(info);
        businessAppInstanceConfig.setBusinessAppInstanceList(infos);
        importEntity.setBusinessAppInstanceConfig(businessAppInstanceConfig);

        Exception e = null;
        try {
            businessTopoRefreshHandler.handleRefreshInstance(siteMo, modelDB, "MM", true, importEntity);
        } catch (Exception ex) {
            e = ex;
        }
        Assert.assertNull(e);
    }

    @Test
    public void getCurrentDvSiteTest() {
        String activeDvSite = "1";
        String solutionName = "CBS";
        String result = businessTopoDvSelfService.getCurrentDvSite(activeDvSite, solutionName);
        Assert.assertEquals("default", result);
    }

    @Test
    public void isAddingNewSiteTest() throws ServiceException {
        List<BusinessInstanceRelationModel> relationModels = new ArrayList<>();
        BusinessInstanceRelationModel model = new BusinessInstanceRelationModel();
        model.setInstanceId(1);
        model.setTargetInstanceId(null);
        model.setRelationType(BusinessTopoConstant.RELATION_TYPE_SITE);
        relationModels.add(model);
        List<BusinessInstanceModelDB> dvIns = new ArrayList<>();
        boolean result = businessTopoDvSelfService.isAddingNewSite(relationModels, dvIns);
        Assert.assertTrue(result);
    }
}
