/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.topo.topofunction;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.service.bean.ConfigurationImportEntity;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * TopoInstanceRelationCreatorTest
 *
 * <AUTHOR>
 * @since 2024-11-21
 */
@PrepareForTest({PropertiesUtil.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class TopoInstanceRelationCreatorTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    @Autowired
    TopoInstanceRelationCreator topoInstanceRelationCreator;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_import.sql", false);
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    @Test
    public void buildTopRelationsTest() throws ServiceException {
        ConfigurationImportEntity configurationImportEntity = new ConfigurationImportEntity();
        configurationImportEntity.setSolutionName("solutionName");
        configurationImportEntity.setModelName("modelName");
        configurationImportEntity.setModelType(3);
        topoInstanceRelationCreator.buildTopRelations(configurationImportEntity);
        Assert.assertEquals(9, configurationImportEntity.getModelName().length());
    }
}
