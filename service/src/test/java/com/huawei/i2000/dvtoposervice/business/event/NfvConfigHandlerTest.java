/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.event;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.exception.OSSException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.event.NfvConfigHandler;
import com.huawei.i2000.dvtoposervice.util.AuthUtils;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.oms.eam.mo.AgentNode;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.ManagedObject;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * nfv事件处理测试类
 *
 * <AUTHOR>
 * @since 2025/2/13
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class, AuthUtils.class, NfvConfigHandler.class, MITManagerClient.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class NfvConfigHandlerTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;


    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    NfvConfigHandler nfvConfigHandler;

    Class<NfvConfigHandler> nfvConfigHandlerClass = NfvConfigHandler.class;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith.sql", false);
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    @Test
    public void initTest() throws OSSException {
        Exception ex = null;
        try {
            nfvConfigHandler.init();
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }

    @Test
    public void initTopoDnUpdateTaskTest() throws OSSException, NoSuchMethodException {
        String dn = "OS=1";
        PowerMockito.mockStatic(AuthUtils.class);

        List<String> authDns = new ArrayList<>();
        authDns.add(dn);
        PowerMockito.when(AuthUtils.getAuthDns(Mockito.anyList())).thenReturn(authDns);

        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        DemoAgent managedObject = new DemoAgent();
        managedObject.setName("OSS");
        managedObject.setType("com.huawei.as.res.virtual");
        Map<String, Object> clientProperties = new HashMap<>();
        clientProperties.put("isVNF", "true");
        PowerMockito.when(mitManagerClient.getMO(dn)).thenReturn(managedObject);
        PowerMockito.when(mitManagerClient.getMO(new DN(dn))).thenReturn(managedObject);

        Method initTopoDnUpdateTask = nfvConfigHandlerClass.getDeclaredMethod("initTopoDnUpdateTask");
        initTopoDnUpdateTask.setAccessible(true);

        Exception ex = null;
        try {
            initTopoDnUpdateTask.invoke(nfvConfigHandler);
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }

    @Test
    public void initTopoDnUpdateTaskExceptionTest() throws NoSuchMethodException {
        String dn = "OS=1";
        PowerMockito.mockStatic(AuthUtils.class);

        List<String> authDns = new ArrayList<>();
        authDns.add(dn);
        PowerMockito.when(AuthUtils.getAuthDns(Mockito.anyList())).thenReturn(authDns);

        Method initTopoDnUpdateTask = nfvConfigHandlerClass.getDeclaredMethod("initTopoDnUpdateTask");
        initTopoDnUpdateTask.setAccessible(true);

        Exception ex = null;
        try {
            initTopoDnUpdateTask.invoke(nfvConfigHandler);
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }


    static class DemoAgent extends AgentNode {

        @Override
        public ManagedObject copy() {
            return null;
        }

        @Override
        public List<String> getIPAddrList() {
            return null;
        }
    }

}
