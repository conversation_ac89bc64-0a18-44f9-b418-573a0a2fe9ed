/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.topo.topofunction;

import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.mybatis.session.MapperManager;
import com.huawei.bsp.redis.oper.ListOper;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.cs.uom.mo.base.Application;
import com.huawei.cs.uom.mo.base.Product;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.BusinessTopoInsTreeNode;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.ModelAlarm;
import com.huawei.i2000.dvtoposervice.business.database.BusinessCommonModel;
import com.huawei.i2000.dvtoposervice.business.database.BusinessCommonModelWithInstance;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.openapi.alarm.BusinessAlarmInitService;
import com.huawei.i2000.dvtoposervice.business.service.bean.AlarmInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.ConfigurationImportEntity;
import com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.PodConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.PodType;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.business.topo.importer.AbstractTopoImporter;
import com.huawei.i2000.dvtoposervice.business.topo.importer.CbsBesTopoImporter;
import com.huawei.i2000.dvtoposervice.business.topo.importer.MmTopoImporter;
import com.huawei.i2000.dvtoposervice.business.topo.redis.RedisExOper;
import com.huawei.i2000.dvtoposervice.business.topo.solutiontemplatevalidate.SolutionTemplateValidator;
import com.huawei.i2000.dvtoposervice.business.topo.upgrade.ConfigurationImportUpgrade;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessCommonModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.impl.DVTopoServiceImportManageDelegateImpl;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.util.EamUtil;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.i2000.gapi.account.mo.AccountMO;
import com.huawei.oms.eam.mim.MORelation;
import com.huawei.oms.eam.mim.RelationType;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.ManagedObject;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONReader;

import mockit.Mock;
import mockit.MockUp;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.context.ContextConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * BusinessDaoTest
 *
 * <AUTHOR>
 * @since 2024/2/29
 */
@PrepareForTest({PropertiesUtil.class, MITManagerClient.class, EamUtil.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class ConfigurationFileImportTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);
    private static final OssLog LOGGER = OssLogFactory.getLogger(ConfigurationFileImportTest.class);

    @Autowired
    MapperManager mapperMgr;

    @Autowired
    private ConfigurationImportUpgrade configurationImportUpgrade;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    private static final String CONFIG_FILE_COLLECTION = "topotest/configuration_import_file";

    private static final String RESOURCE_TREE_INFO = "topotest/instance_from_resource/resource_treeInfo.json";


    private static final Map<String, List<ManagedObject>> MO_TYPE_TO_INS_LIST = new HashMap<>();

    private static final Map<String, ManagedObject> DN_TO_INS_MAP = new HashMap<>();

    private static final Map<String, List<String>> PA_DN_TO_CHILD_DN_LIST = new HashMap<>();

    private ListOper<String> listOper = new ListOper<>(RedisConstant.MO_STORE_LIST, RedisConstant.REDIS_NAME);

    private static final MapOper<ConfigData> BUSINESS_CONFIG_MAP_OPER = new MapOper<>(
        RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);

    @Autowired
    private BusinessInstanceModelDao instanceModelDao;

    @Autowired
    private BusinessCommonModelDao commonModelDao;

    @Autowired
    private SolutionTemplateValidator solutionTemplateValidator;

    @Autowired
    private SiteGroupSlicer siteGroupSlicer;

    @Autowired
    private DVTopoServiceImportManageDelegateImpl importManageDelegate;

    private void mockProperties() {
        MapOper<ConfigData> businessConfigMapOper = new MapOper<>(
                RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("SOLUTION_TYPE");
        configData.setValue("0");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "SOLUTION_TYPE", configData);
        ConfigData configData1 = new ConfigData();
        configData1.setConfigItemName("BUSINESS_TYPE");
        configData1.setValue("1");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "BUSINESS_TYPE", configData1);
        ConfigData configData2 = new ConfigData();
        configData2.setConfigItemName("SITETEAM_TYPE");
        configData2.setValue("2");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "SITETEAM_TYPE", configData2);
        ConfigData configData3 = new ConfigData();
        configData3.setConfigItemName("SITE_TYPE");
        configData3.setValue("3");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "SITE_TYPE", configData3);
        ConfigData configData4 = new ConfigData();
        configData4.setConfigItemName("GROUP_TYPE");
        configData4.setValue("4");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "GROUP_TYPE", configData4);
        ConfigData configData5 = new ConfigData();
        configData5.setConfigItemName("MOTYPE_CLUSTER_TYPE");
        configData5.setValue("5");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "MOTYPE_CLUSTER_TYPE", configData5);
        ConfigData configData6 = new ConfigData();
        configData6.setConfigItemName("CLUSTER_TYPE");
        configData6.setValue("6");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "CLUSTER_TYPE", configData6);
        ConfigData configData7 = new ConfigData();
        configData7.setConfigItemName("POD_TYPE");
        configData7.setValue("7");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "POD_TYPE", configData7);
        ConfigData configData8 = new ConfigData();
        configData8.setConfigItemName("VM_TYPE");
        configData8.setValue("8");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "VM_TYPE", configData8);
    }

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        executeSqlScript("classpath:database/dvtoposervice_tables_configuration_revover.sql", false);
        mockProperties();
    }

    private void mockIndicatorPrepare() {
        PowerMockito.mockStatic(PropertiesUtil.class);
        Properties properties = new Properties();
        properties.put(BusinessTopoConstant.SITETEAM_TYPE, 2);
        PowerMockito.when(PropertiesUtil.loadProperties(Mockito.anyString())).thenReturn(properties);
    }

    private DVTopoServiceImportManageDelegateImpl.FileInfoBean getConfigurationImportEntity(File file) {
        try {
            String s = FileUtils.readFileToString(file, "UTF-8");
            return new DVTopoServiceImportManageDelegateImpl.FileInfoBean(file.getName(), JSON.parseObject(s, ConfigurationImportEntity.class,
                JSONReader.Feature.SupportSmartMatch));
        } catch (IOException e) {
            LOGGER.error("[FileUtil_read] read configuration file failed!");
        }
        return null;
    }


    private void prepareData() {
        BusinessTopoInsTreeNode root = readData(Objects.requireNonNull(getClass().getClassLoader().getResource(RESOURCE_TREE_INFO)).getPath());
        MO_TYPE_TO_INS_LIST.put("cbs.billing.cbs", root.getNext().stream().map(businessTopoInsTreeNode -> {
            Product managedObject = new Product();
            managedObject.setClientProperties(businessTopoInsTreeNode.getClientProperties());
            managedObject.setType(businessTopoInsTreeNode.getMoTypeMapping());
            managedObject.setDN(businessTopoInsTreeNode.getDn());
            return managedObject;
        }).collect(Collectors.toList()));
        MO_TYPE_TO_INS_LIST.put("cbs.billing.edrdbhiscloud.cluster", root.getNext().stream().map(businessTopoInsTreeNode -> {
            Product managedObject = new Product();
            managedObject.setClientProperties(businessTopoInsTreeNode.getClientProperties());
            managedObject.setType(businessTopoInsTreeNode.getMoTypeMapping());
            managedObject.setDN(businessTopoInsTreeNode.getDn());
            return managedObject;
        }).collect(Collectors.toList()));
        buildDnToInsList(root);
    }

    private void buildDnToInsList(BusinessTopoInsTreeNode root) {
        if (!Objects.equals(root.getLevel(), BusinessTopoConstant.ROOT_INS_LEVEL)) {
            for (BusinessTopoInsTreeNode treeNode : root.getNext()) {
                Application application = new Application();
                application.setDN(treeNode.getDn());
                application.setClientProperties(treeNode.getClientProperties());
                application.setType(treeNode.getMoTypeMapping());
                DN_TO_INS_MAP.put(treeNode.getDn(), application);
                PA_DN_TO_CHILD_DN_LIST.computeIfAbsent(root.getDn(), a -> new ArrayList<>()).add(treeNode.getDn());
            }
        }
        root.getNext().forEach(this::buildDnToInsList);
    }

    public BusinessTopoInsTreeNode readData(String filePath) {
        try {
            File file = new File(filePath);
            String s = FileUtils.readFileToString(file, "UTF-8");
            return JSON.parseObject(s, BusinessTopoInsTreeNode.class);
        } catch (IOException e) {
            e.printStackTrace();
            return new BusinessTopoInsTreeNode();
        }
    }

    public static void mock() {
        new MockUp<MITManagerClient>(MITManagerClient.class) {
            @Mock
            public List<ManagedObject> getMoByType(String type) throws OSSException {
                if (!MO_TYPE_TO_INS_LIST.containsKey(type)) {
                    return new ArrayList<>();
                }
                return MO_TYPE_TO_INS_LIST.get(type);
            }


            @Mock
            public List<ManagedObject> getMoByDns(List<DN> dns) {
                List<ManagedObject> res = new ArrayList<>();
                for (DN dn : dns) {
                    if (DN_TO_INS_MAP.containsKey(dn.getValue())) {
                        res.add(DN_TO_INS_MAP.get(dn.getValue()));
                    }
                }
                return res;
            }
        };

        new MockUp<ListOper<T>>(ListOper.class) {
            @Mock
            public List<String> get(final String key, final Class<T> type, final int start, final int end) {
                return PA_DN_TO_CHILD_DN_LIST.getOrDefault(key, new ArrayList<>());
            }
        };

        new MockUp<ConfigureCommonModeProcessor>(ConfigureCommonModeProcessor.class) {
            @Mock
            private Map<String, Set<String>> getDockerMoTypeList(PodConfig podConfig) {
                return new HashMap<>();
            }
        };
        new MockUp<BusinessAlarmInitService>(BusinessAlarmInitService.class) {
            @Mock
            public void init() {

            }
        };

        new MockUp<RedisExOper>(RedisExOper.class) {
            @Mock
            public String setEx(String key, long seconds, String value) {
                return "";
            }

            @Mock
            public String get(String key) {
                return null;
            }
        };


    }

    private List<DVTopoServiceImportManageDelegateImpl.FileInfoBean> getConfigurationImportEntityWrapperList() {
        try {
            ClassPathResource classPathResource = new ClassPathResource(CONFIG_FILE_COLLECTION);
            File file = classPathResource.getFile();
            if (Objects.isNull(file.listFiles())) {
                return Collections.emptyList();
            }
            return Arrays
                    .stream(Objects.requireNonNull(file.listFiles()))
                    .map(this::getConfigurationImportEntity)
                    .filter(Objects::nonNull)
                    .sorted((a, b) -> Integer.compare(b.getConfigurationImportEntity().getTemplateType(), a.getConfigurationImportEntity().getTemplateType()))
                    .collect(Collectors.toList());
        } catch (IOException e) {
            LOGGER.error("[READ_FILE_ERR]");
        }
        return new ArrayList<>();
    }

    private List<ConfigurationImportEntity> getConfigurationImportEntityList() {
        return getConfigurationImportEntityWrapperList().stream().map(DVTopoServiceImportManageDelegateImpl.FileInfoBean::getConfigurationImportEntity).collect(Collectors.toList());
    }

    @Test
    public void testDvModelInstantiate() throws ServiceException, OSSException {
        beforeImport();
        PowerMockito.mockStatic(EamUtil.class);
        List<ManagedObject> moList = new ArrayList<>();
        ManagedObject managedObject = new AccountMO();
        managedObject.setDN("28f59c78-64b5-4b58-949c-891cc8ec43bf");
        moList.add(managedObject);
        PowerMockito.when(EamUtil.queryMosByMoTypeWhenRefresh("cbs.billing.cbs")).thenReturn(MO_TYPE_TO_INS_LIST.get("cbs.billing.cbs"));

        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);
        ConfigData configGroupName = new ConfigData();
        configGroupName.setConfigItemName("mmHwsGroupName");
        configGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configGroupName);

        List<BusinessInstanceModelDB> businessInstanceModelDBList = new ArrayList<>();

        for (ConfigurationImportEntity configurationImportEntity : getConfigurationImportEntityList()) {
            configurationImportUpgrade.instanceUpgrade(configurationImportEntity);
            businessInstanceModelDBList.addAll(instanceModelDao.queryAllInstance()
                    .stream().filter(businessInstanceModelDB -> StringUtils.isNotEmpty(businessInstanceModelDB.getDn()))
                    .collect(Collectors.toList()));
        }
        Assert.assertTrue(CollectionUtils.isNotEmpty(businessInstanceModelDBList));
    }

    @Test
    public void testCommonModelUpgrade() {
        beforeImport();
        List<BusinessCommonModel> commonModelList = new ArrayList<>();
        for (ConfigurationImportEntity configurationImportEntity : getConfigurationImportEntityList()) {
            configurationImportUpgrade.commonModelUpgrade(configurationImportEntity);
            BusinessCommonModel condition = new BusinessCommonModel();
            condition.setSolutionName(configurationImportEntity.getSolutionName());
            condition.setTemplateType(configurationImportEntity.getTemplateType());
            condition.setProductName(configurationImportEntity.getProductName());
            commonModelList.addAll(commonModelDao.queryCommonModelByCondition(condition));
        }
        Assert.assertTrue(CollectionUtils.isNotEmpty(commonModelList));
    }


    @Test
    public void testConfigurationFileValidator() {
        beforeImport();
        List<ServiceException> errList = new ArrayList<>();
        for (ConfigurationImportEntity configurationImportEntity : getConfigurationImportEntityList()) {
            if (configurationImportEntity.getSolutionName().equals("MM")) {
                continue;
            }
            try {
                solutionTemplateValidator.check(configurationImportEntity);
            } catch (ServiceException e) {
                LOGGER.error("[testConfigurationFileValidator] err: {}", e.getMessage());
                errList.add(e);
            }
        }
        Assert.assertTrue(CollectionUtils.isEmpty(errList));
    }

    @Test
    public void testMmConfigurationFileValidator() {
        beforeImport();
        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);
        ConfigData configGroupName = new ConfigData();
        configGroupName.setConfigItemName("mmHwsGroupName");
        configGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configGroupName);

        PowerMockito.mockStatic(EamUtil.class);
        List<ManagedObject> mos = new ArrayList<>();
        ManagedObject managedObject = new AccountMO();
        managedObject.setLogicSite("1");
        managedObject.setDN("moDn");
        managedObject.setHwsSiteName("site");
        managedObject.setHwsGroupName("group");
        managedObject.setHwsSwimLane("b");
        managedObject.setHwsStripe("gsu");
        mos.add(managedObject);
        PowerMockito.when(EamUtil.queryMosByMoType("fintech.Fintech.FCS")).thenReturn(mos);

        List<ServiceException> errList = new ArrayList<>();
        for (ConfigurationImportEntity configurationImportEntity : getConfigurationImportEntityList()) {
            if (!configurationImportEntity.getSolutionName().equals("MM")) {
                continue;
            }
            try {
                solutionTemplateValidator.check(configurationImportEntity);
            } catch (ServiceException e) {
                LOGGER.error("[testConfigurationFileValidator] err: {}", e.getMessage());
                errList.add(e);
            }
        }
        Assert.assertTrue(CollectionUtils.isEmpty(errList));
    }

    @Test
    public void testMmConfigurationFileValidatorWithoutSiteGroup() {
        beforeImport();
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", new ConfigData());
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", new ConfigData());
        PowerMockito.mockStatic(EamUtil.class);
        List<ManagedObject> mos = new ArrayList<>();
        ManagedObject managedObject = new AccountMO();
        managedObject.setLogicSite("1");
        managedObject.setDN("moDn");
        managedObject.setHwsSiteName("site");
        managedObject.setHwsGroupName("group");
        managedObject.setHwsSwimLane("b");
        managedObject.setHwsStripe("gsu");
        mos.add(managedObject);
        ManagedObject mo2 = new AccountMO();
        mo2.setDN("moDn");
        mo2.setHwsSiteName("site1");
        mo2.setHwsGroupName("group1");
        mo2.setHwsSwimLane("b");
        mo2.setHwsStripe("gsu");
        mos.add(mo2);

        PowerMockito.when(EamUtil.queryMosByMoType("fintech.Fintech.FCS")).thenReturn(mos);

        List<ServiceException> errList = new ArrayList<>();
        for (ConfigurationImportEntity configurationImportEntity : getConfigurationImportEntityList()) {
            if (!configurationImportEntity.getSolutionName().equals("MM")) {
                continue;
            }
            try {
                solutionTemplateValidator.check(configurationImportEntity);
            } catch (ServiceException e) {
                LOGGER.error("[testConfigurationFileValidator] err: {}", e.getMessage());
                errList.add(e);
            }
        }
        Assert.assertTrue(CollectionUtils.isNotEmpty(errList));
    }

    @Test
    public void testSiteGroupSlicer() throws ServiceException {
        beforeImport();
        for (ConfigurationImportEntity configurationImportEntity : getConfigurationImportEntityList()) {
            if (("cbs.billing.cbs").equals(configurationImportEntity.getClassType())) {
                continue;
            }
            if (BusinessTopoConstant.FULL_CONFIGURATION_TEMPLATE_TYPE.equals(configurationImportEntity.getTemplateType())) {
                siteGroupSlicer.updateSiteGroup(configurationImportEntity);
                List<BusinessInstanceModelDB> siteGroupInsList = instanceModelDao.queryAllInstance()
                        .stream().filter(businessInstanceModelDB ->
                                Objects.equals(2, businessInstanceModelDB.getModelType())).collect(Collectors.toList());
                Assert.assertEquals(1, siteGroupInsList.size());
                break;
            }
        }
    }

    @Test
    public void testDockerAlarmBuild() {
        PodType podType = new PodType();
        podType.setAlarmSeverity("1");
        podType.setAlarmClearType(1);
        Set<ModelAlarm> ans = new HashSet<>();
        List<BusinessCommonModelWithInstance> dockerInsList = new ArrayList<>();
        BusinessCommonModelWithInstance businessCommonModelWithInstance = new BusinessCommonModelWithInstance();
        businessCommonModelWithInstance.setModelType(7);
        businessCommonModelWithInstance.setModelId("7_adapterapp");
        businessCommonModelWithInstance.setModelName("adapterapp");
        dockerInsList.add(businessCommonModelWithInstance);
        AlarmInfo alarmInfo = new AlarmInfo();
        importManageDelegate.buildDockerAlarmInfoList(podType, 1, ans, dockerInsList, alarmInfo);
        Assert.assertTrue(CollectionUtils.isNotEmpty(ans));
    }

    @Test
    public void testConfigurationGo() throws ServiceException {
        beforeImport();
        Map<String, List<DVTopoServiceImportManageDelegateImpl.FileInfoBean>> map = new HashMap<>();
        for (DVTopoServiceImportManageDelegateImpl.FileInfoBean fileInfoBean : getConfigurationImportEntityWrapperList()) {
            String solutionName = fileInfoBean.getConfigurationImportEntity().getSolutionName();
            map.computeIfAbsent(solutionName, a -> new ArrayList<>()).add(fileInfoBean);
        }
        List<BusinessCommonModel> commonModelList = new ArrayList<>();
        for (Map.Entry<String, List<DVTopoServiceImportManageDelegateImpl.FileInfoBean>> entry : map.entrySet()) {
            BusinessCommonModel condition = new BusinessCommonModel();
            condition.setSolutionName(entry.getKey());
            importManageDelegate.offerTask(entry);
            commonModelList.addAll(commonModelDao.queryCommonModelByCondition(condition));
        }
        Assert.assertTrue(CollectionUtils.isNotEmpty(commonModelList));
    }

    private void beforeImport() {
        prepareData();
        mock();
        mockIndicatorPrepare();
    }

    @Test
    public void getSrcDnToDeploymentDestMapTest() throws ServiceException, OSSException {
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        ManagedObject managedObject = new AccountMO();
        managedObject.setType("com.huawei.IRes.vm");
        managedObject.setDN("8_vm");
        managedObject.setParent(new DN("8_vm"));
        PowerMockito.when(mitManagerClient.getMOList(Mockito.anyList())).thenReturn(Collections.singletonList(managedObject));
        List<MORelation> relations = new ArrayList<>();
        MORelation relation = new MORelation(new DN("podDn"), new DN("8_vm"), "deploy");
        relations.add(relation);
        PowerMockito.when(mitManagerClient.getAllRelationsByDns(Mockito.anyList(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(relations);

        List<DN> podDnList = new ArrayList<>();
        podDnList.add(new DN("podDn"));
        CbsBesTopoImporter cbsBesTopoImporter = new CbsBesTopoImporter();
        Map<String, List<DN>> result = cbsBesTopoImporter.getSrcDnToDeploymentDestMap(podDnList);

        Assert.assertEquals(1, result.size());
    }

    @Test
    public void testMmImport() throws ServiceException, OSSException {
        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);
        ConfigData configGroupName = new ConfigData();
        configGroupName.setConfigItemName("mmHwsGroupName");
        configGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configGroupName);

        listOper.add("/", "site");
        listOper.add("site", "ins");
        listOper.add("ins", "pod");
        listOper.add("pod", "docker");

        // 预置网元数据
        List<ManagedObject> siteMos = prepareSiteMo();
        ManagedObject moIns = prepareInsMo();
        ManagedObject moPod = preparePodMo();
        ManagedObject moDocker = prepareDockerMo();
        ManagedObject moVm = new AccountMO();
        moVm.setDN("vm");
        moVm.setType("com.huawei.IRes.vm");
        ManagedObject moVm2 = new AccountMO();
        moVm.setDN("vm2");
        moVm.setType("com.huawei.IRes.vm");
        ManagedObject moContainer = new AccountMO();
        moContainer.setDN("container");

        // mock资源接口
        prepareEamService(siteMos, moVm, moIns, moPod, moDocker, moContainer, moVm2);

        List<ConfigurationImportEntity> importEntities = getConfigurationImportEntityList();
        for (ConfigurationImportEntity configurationImportEntity : importEntities) {
            if (!("fintech.Fintech.FCS").equals(configurationImportEntity.getClassType())) {
                continue;
            }
            configurationImportEntity.getSiteDataSourceConfig().setSiteDataType(3);
            configurationImportEntity.setSolutionName("MM");
            configurationImportEntity.setTemplateType(0);
            configurationImportUpgrade.instanceUpgrade(configurationImportEntity);
        }

        // 有实例说明导入成功
        List<BusinessInstanceModelDB> instances = instanceModelDao.queryAllInstance();
        Assert.assertTrue(CollectionUtils.isNotEmpty(instances));
    }

    private void prepareEamService(List<ManagedObject> siteMos, ManagedObject moVm, ManagedObject moIns,
        ManagedObject moPod, ManagedObject moDocker, ManagedObject moContainer, ManagedObject moVm2)
        throws OSSException {
        PowerMockito.mockStatic(EamUtil.class);
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);

        PowerMockito.when(EamUtil.queryMosByMoTypeWhenRefresh("fintech.Fintech.FCS")).thenReturn(siteMos);
        PowerMockito.when(EamUtil.queryMosByMoType("OMS")).thenReturn(Collections.singletonList(moIns));
        PowerMockito.when(EamUtil.getMoByDns(Collections.singletonList(
            new DN("vm")))).thenReturn(Collections.singletonList(moVm));
        PowerMockito.when(mitManagerClient.getMoByDns(Collections.singletonList(
            new DN("ins")))).thenReturn(Collections.singletonList(moIns));
        PowerMockito.when(mitManagerClient.getMoByDns(Collections.singletonList(
            new DN("pod")))).thenReturn(Collections.singletonList(moPod));
        PowerMockito.when(mitManagerClient.getMoByDns(Collections.singletonList(
            new DN("docker")))).thenReturn(Collections.singletonList(moDocker));
        PowerMockito.when(mitManagerClient.getMoByDns(Collections.singletonList(
            new DN("vm")))).thenReturn(Collections.singletonList(moVm));
        PowerMockito.when(mitManagerClient.getMoByDns(Collections.singletonList(
            new DN("container")))).thenReturn(Collections.singletonList(moContainer));

        MORelation moRelation = new MORelation(new DN("pod"), new DN("vm"), "deployment");
        List<MORelation> moRelations = Collections.singletonList(moRelation);
        MORelation moRelationDocker = new MORelation(new DN("docker"), new DN("container"), "deployment");
        List<MORelation> moRelationsDocker = Collections.singletonList(moRelationDocker);
        PowerMockito.when(mitManagerClient.getAllRelationsByDns(
            Collections.singletonList(new DN("pod")), RelationType.Deployment.name(), false)).thenReturn(moRelations);
        PowerMockito.when(mitManagerClient.getAllRelationsByDns(
            Collections.singletonList(new DN("docker")), RelationType.Deployment.name(), false)).thenReturn(moRelationsDocker);
        PowerMockito.when(mitManagerClient.getMOList(
            Collections.singletonList(new DN("vm")))).thenReturn(Collections.singletonList(moVm));
        PowerMockito.when(mitManagerClient.getMOList(
            Collections.singletonList(new DN("container")))).thenReturn(Collections.singletonList(moContainer));
        PowerMockito.when(mitManagerClient.getMoByType("com.huawei.IRes.vm")).thenReturn(Collections.singletonList(moVm2));
    }

    private ManagedObject prepareDockerMo() {
        ManagedObject moDocker = new AccountMO();
        moDocker.setDN("docker");
        moDocker.setHwsGroupName("LSHT");
        moDocker.setHwsStripe("GSU");
        moDocker.setHwsProduct("wallet");
        moDocker.setHwsSwimLane("b");
        moDocker.setHwsAppSite("pr1");
        return moDocker;
    }

    private ManagedObject preparePodMo() {
        ManagedObject moPod = new AccountMO();
        moPod.children().add(new DN("docker"));
        moPod.setDN("pod");
        moPod.setHwsGroupName("LSHT");
        moPod.setHwsStripe("GSU");
        moPod.setHwsProduct("wallet");
        moPod.setHwsSwimLane("b");
        moPod.setHwsAppSite("pr1");
        moPod.setType("fintech.Fintech.FCS.SuperGatewaySVRCluster");
        return moPod;
    }

    private ManagedObject prepareInsMo() {
        ManagedObject moIns = new AccountMO();
        moIns.setDN("ins");
        moIns.children().add(new DN("pod"));
        moIns.setHwsGroupName("LSHT");
        moIns.setHwsStripe("GSU");
        moIns.setHwsProduct("wallet");
        moIns.setHwsSwimLane("b");
        moIns.setHwsAppSite("pr1");
        moIns.setType("fintech.Fintech.FCS.SuperGatewaySVR");
        return moIns;
    }

    private List<ManagedObject> prepareSiteMo() {
        List<ManagedObject> siteMos = new ArrayList<>();
        ManagedObject moSite = new AccountMO();
        moSite.putClientProperty("siteName", "LSHT");
        moSite.putClientProperty("groupName", "LSHT");
        moSite.setHwsGroupName("group");
        moSite.setHwsSiteName("site");
        moSite.setHwsStripe("GSU");
        moSite.setHwsProduct("wallet");
        moSite.setHwsSwimLane("b");
        moSite.setDN("site");
        moSite.children().add(new DN("ins"));
        siteMos.add(moSite);
        return siteMos;
    }

    @Test
    public void buildDockerInsTest() {
        ConfigurationImportEntity configurationImportEntity = new ConfigurationImportEntity();
        BusinessInstanceModelDB podInsDb = new BusinessInstanceModelDB();
        Set<String> dockerInsDnSet = new HashSet<>();
        dockerInsDnSet.add("dockerDn");
        dockerInsDnSet.add("dockerDn1");
        Set<String> dockerDnSet = new HashSet<>();
        Map<String, BusinessTopoInsTreeNode> dockerDnToMoTypeMap = new HashMap<>();
        BusinessTopoInsTreeNode treeNode = new BusinessTopoInsTreeNode();
        treeNode.setDn("dockerDn");
        treeNode.setHwsStripe("stripe");
        treeNode.setHwsSwimLane("b");
        treeNode.setHwsGroupName("group");
        treeNode.setHwsSiteName("site");
        treeNode.setHwsAppSite("pr1");
        dockerDnToMoTypeMap.put("dockerDn", treeNode);
        dockerDnToMoTypeMap.put("dockerDn1", new BusinessTopoInsTreeNode());
        List<BusinessInstanceModelDB> dockerIns = new ArrayList<>();
        Map<Integer, List<BusinessInstanceModelDB>> podIdToDockerInsMap = new HashMap<>();

        AbstractTopoImporter importer = new CbsBesTopoImporter();
        importer.buildDockerIns(configurationImportEntity, podInsDb, dockerInsDnSet, dockerDnSet, dockerDnToMoTypeMap, dockerIns, podIdToDockerInsMap);

        Assert.assertEquals(1, podIdToDockerInsMap.size());
    }

    @Test
    public void getMmSiteDnsTest() {
        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);
        ConfigData configGroupName = new ConfigData();
        configGroupName.setConfigItemName("mmHwsGroupName");
        configGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configGroupName);

        PowerMockito.mockStatic(EamUtil.class);
        List<ManagedObject> mos = new ArrayList<>();
        ManagedObject managedObject = new AccountMO();
        managedObject.setLogicSite("1");
        managedObject.setDN("moDn");
        managedObject.setHwsSiteName("site");
        managedObject.setHwsGroupName("group");
        managedObject.setHwsSwimLane("b");
        managedObject.setHwsStripe("gsu");
        mos.add(managedObject);
        PowerMockito.when(EamUtil.getMoByMoType("fintech.Fintech.FCS")).thenReturn(mos);

        String moType = "fintech.Fintech.FCS";
        Set<String> siteDns = TopoImportSolutionAdapter.getMmSiteDns(moType, true);

        Assert.assertEquals(1, siteDns.size());
    }

    @Test
    public void handleVmIndicatorTest() {
        AbstractTopoImporter abstractTopoImporter = new MmTopoImporter();
        List<IndicatorInfo> indicatorList = new ArrayList<>();
        IndicatorInfo indicatorInfo = new IndicatorInfo();
        indicatorInfo.setMoType("OMS");
        indicatorInfo.setMeasUnitKey("unit");
        indicatorInfo.setMeasTypeKey("type");
        indicatorList.add(indicatorInfo);

        Map<String, BusinessInstanceModelDB> dnInstanceMap = new HashMap<>();
        dnInstanceMap.put("dn", new BusinessInstanceModelDB());

        Exception e = null;
        try {
            abstractTopoImporter.handleVmIndicator(indicatorList, dnInstanceMap);
        } catch (Exception ex) {
            e = ex;
        }
        Assert.assertNull(e);
    }
}
