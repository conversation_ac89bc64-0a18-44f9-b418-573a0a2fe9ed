/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.bean.pm.HistoryIndexValue;
import com.huawei.i2000.dvtoposervice.bean.pm.HistoryQueryData;
import com.huawei.i2000.dvtoposervice.bean.pm.HistoryQueryValue;
import com.huawei.i2000.dvtoposervice.business.database.BusinessStepModelDB;
import com.huawei.i2000.dvtoposervice.business.pm.entity.MoObject;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessMoTypeDao;
import com.huawei.i2000.dvtoposervice.model.ClusterDisplayResult;
import com.huawei.i2000.dvtoposervice.model.ClusterInsCommonParam;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.model.GrayscaleParam;
import com.huawei.i2000.dvtoposervice.model.GrayscaleResult;
import com.huawei.i2000.dvtoposervice.model.HostViewGrid;
import com.huawei.i2000.dvtoposervice.model.Indicator;
import com.huawei.i2000.dvtoposervice.model.MoTypeViewCommonParam;
import com.huawei.i2000.dvtoposervice.model.MoTypeViewGrid;
import com.huawei.i2000.dvtoposervice.model.PipelineDisplayResult;
import com.huawei.i2000.dvtoposervice.model.PodDeployResult;
import com.huawei.i2000.dvtoposervice.model.PodIndicatorDisplayResults;
import com.huawei.i2000.dvtoposervice.model.PodViewCommonParam;
import com.huawei.i2000.dvtoposervice.model.VmDeployResult;
import com.huawei.i2000.dvtoposervice.model.VmIndicatorDisplayResults;
import com.huawei.i2000.dvtoposervice.util.ApplicationContextHelper;
import com.huawei.i2000.dvtoposervice.util.ConfigurationUtil;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.EamUtil;
import com.huawei.i2000.dvtoposervice.util.PerformanceClient;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.i2000.gapi.account.mo.AccountMO;
import com.huawei.oms.eam.mim.MORelation;
import com.huawei.oms.eam.mim.RelationType;
import com.huawei.oms.eam.mo.AvailableStatus;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.MOType;
import com.huawei.oms.eam.mo.ManagedElement;
import com.huawei.oms.eam.mo.ManagedObject;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.Vector;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * BusinessTopoMoTypeViewServiceImplTest class
 *
 * <AUTHOR>
 * @since 2024/6/20
 */
@PrepareForTest( {
    ContextUtils.class, PropertiesUtil.class, MITManagerClient.class, System.class, PerformanceClient.class,
    ApplicationContextHelper.class, EamUtil.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessTopoMoTypeViewServiceImplTest extends WebServiceTest {

    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static final MapOper<ConfigData> BUSINESS_CONFIG_MAP_OPER = new MapOper<>(
        RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);

    private void mockAuthentication() {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
    }

    private static EmbeddedRestClientAndServer mockServer;

    @Autowired
    BusinessTopoMoTypeViewServiceImpl serviceImpl;

    @Autowired
    BusinessHostViewServiceImpl hostViewService;

    @Autowired
    BusinessMoTypeDao businessMoTypeDao;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_siteview_real_time.sql", false);
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_analysis.sql", false);
        mockProperties();
    }

    private void mockProperties() {
        PowerMockito.mockStatic(PropertiesUtil.class);
        Properties properties = new Properties();
        properties.put(BusinessTopoConstant.CLUSTER_TYPE, "6");
        properties.put(BusinessTopoConstant.POD_TYPE, "7");

        ConfigData configData = new ConfigData();
        MapOper<ConfigData> businessConfigMapOper = new MapOper<>(
            RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);
        configData.setConfigItemName("indicatorrange");
        configData.setValue("691200000");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "indicatorrange", configData);
        ConfigData configData1 = new ConfigData();
        configData1.setConfigItemName("distributeindicatorrange");
        configData1.setValue("3600000");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "distributeindicatorrange", configData1);
        ConfigData configData2 = new ConfigData();
        configData2.setConfigItemName("timelinerange");
        configData2.setValue("24");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "timelinerange", configData2);

        PowerMockito.when(PropertiesUtil.loadProperties(Mockito.anyString())).thenReturn(properties);
        configData.setConfigItemName("grayPercentageMinusOne");
        configData.setValue("0");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, configData.getConfigItemName(), configData);
        configData.setConfigItemName("grayPercentageMinusTwo");
        configData.setValue("1");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, configData.getConfigItemName(), configData);
        configData.setConfigItemName("grayPercentageMinusThree");
        configData.setValue("10");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, configData.getConfigItemName(), configData);
        configData.setConfigItemName("grayPercentageMinus");
        configData.setValue("{-1:0,-2:1,-3:10}");
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    @Test
    public void queryMoTypeViewGridTest() {
        mockAuthentication();
        MoTypeViewCommonParam param = new MoTypeViewCommonParam();
        param.setInstanceId(103);
        param.setTimestamp(0L);
        MoTypeViewGrid moTypeViewGrid = serviceImpl.queryMoTypeViewGrid(param);

        Assert.assertEquals(2, moTypeViewGrid.getBusinessClusterList().size());
    }

    @Test
    public void queryVmDeployDataTest() throws ServiceException, OSSException {
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        ManagedObject managedObject = new AccountMO();
        managedObject.setDN(new DN("podDn"));
        managedObject.setName("podDn");
        List<ManagedObject> moList = new ArrayList<>();
        moList.add(managedObject);

        Vector<DN> srcMOList = new Vector<>();
        srcMOList.add(new DN("pod"));
        PowerMockito.when(mitManagerClient.getSrcMOList(new DN("7dc08f75cab3a79402796"), RelationType.Deployment))
            .thenReturn(srcMOList);
        PowerMockito.when(mitManagerClient.getMoByDns(srcMOList)).thenReturn(moList);

        PodViewCommonParam param = new PodViewCommonParam();
        param.setInstanceId(161);
        VmDeployResult vmDeployResult = serviceImpl.queryVmDeployData(param);

        Assert.assertEquals(1, vmDeployResult.getPodDataList().size());
    }

    @Test
    public void queryPodDeployDataTest() throws ServiceException, OSSException {
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);

        ManagedElement mo = new ManagedElement() {
            @Override
            public ManagedObject copy() {
                return null;
            }
        };
        mo.setIPAddress("*******");
        mo.setName("podName");
        mo.setAvailableStatus(AvailableStatus.Normal);
        mo.setCreatedTime(System.currentTimeMillis());
        mo.setType("com.huawei.IRes.vm");
        PowerMockito.when(mitManagerClient.getMO(new DN("28f59c78-64b5-4b58-949c-891cc8ec43bf"))).thenReturn(mo);

        List<DN> dnPodList = new ArrayList<>();
        dnPodList.add(new DN("28f59c78-64b5-4b58-949c-891cc8ec43bf"));
        List<MORelation> moRelations = new ArrayList<>();
        MORelation moRelation = new MORelation(new DN("28f59c78-64b5-4b58-949c-891cc8ec43bf"), new DN("node"),
            RelationType.Deployment);
        moRelations.add(moRelation);
        PowerMockito.when(mitManagerClient.getAllRelationsByDns(dnPodList, RelationType.Deployment.name(), false))
            .thenReturn(moRelations);

        List<DN> destNodeList = new ArrayList<>();
        destNodeList.add(new DN("node"));
        List<ManagedObject> destNodes = new ArrayList<>();
        destNodes.add(mo);
        PowerMockito.when(mitManagerClient.getMOList(destNodeList)).thenReturn(destNodes);

        PodViewCommonParam param = new PodViewCommonParam();
        param.setInstanceId(145);
        PodDeployResult podDeployResult = serviceImpl.queryPodDeployData(param);

        Assert.assertEquals(1, podDeployResult.getVmDataList().size());
    }

    @Test
    public void queryPodDeployDataTimeLineTest() throws ServiceException, OSSException {
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);

        ManagedElement mo = new ManagedElement() {
            @Override
            public ManagedObject copy() {
                return null;
            }
        };
        mo.setIPAddress("*******");
        mo.setName("podName");
        mo.setAvailableStatus(AvailableStatus.Normal);
        mo.setCreatedTime(System.currentTimeMillis());
        mo.setType("com.huawei.IRes.vm");
        PowerMockito.when(mitManagerClient.getMO(new DN("28f59c78-64b5-4b58-949c-891cc8ec43bf"))).thenReturn(mo);

        List<DN> dnPodList = new ArrayList<>();
        dnPodList.add(new DN("28f59c78-64b5-4b58-949c-891cc8ec43bf"));
        List<MORelation> moRelations = new ArrayList<>();
        MORelation moRelation = new MORelation(new DN("28f59c78-64b5-4b58-949c-891cc8ec43bf"), new DN("node"),
            RelationType.Deployment);
        moRelations.add(moRelation);
        PowerMockito.when(mitManagerClient.getAllRelationsByDns(dnPodList, RelationType.Deployment.name(), false))
            .thenReturn(moRelations);

        List<DN> destNodeList = new ArrayList<>();
        destNodeList.add(new DN("node"));
        List<ManagedObject> destNodes = new ArrayList<>();
        destNodes.add(mo);
        PowerMockito.when(mitManagerClient.getMOList(destNodeList)).thenReturn(destNodes);

        PodViewCommonParam param = new PodViewCommonParam();
        param.setInstanceId(145);
        param.setEndTime(1736824461000L);
        PodDeployResult podDeployResult = serviceImpl.queryPodDeployData(param);

        Assert.assertEquals(1, podDeployResult.getVmDataList().size());
    }

    @Test
    public void queryPodDeployDataTimeLineNotDeletedTest() throws ServiceException, OSSException {
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        PowerMockito.mockStatic(EamUtil.class);
        List<MoObject> moList = new ArrayList<>();
        MoObject moObject = new MoObject();
        moObject.setDn("28f59c78-64b5-4b58-949c-891cc8ec43bf");
        moList.add(moObject);
        PowerMockito.when(EamUtil.getMoListByDnList(Mockito.anyList())).thenReturn(moList);
        ManagedElement mo = new ManagedElement() {
            @Override
            public ManagedObject copy() {
                return null;
            }
        };
        mo.setIPAddress("*******");
        mo.setName("podName");
        mo.setAvailableStatus(AvailableStatus.Normal);
        mo.setCreatedTime(System.currentTimeMillis());
        mo.setType("com.huawei.IRes.vm");
        PowerMockito.when(mitManagerClient.getMO(new DN("28f59c78-64b5-4b58-949c-891cc8ec43bf"))).thenReturn(mo);

        List<DN> dnPodList = new ArrayList<>();
        dnPodList.add(new DN("28f59c78-64b5-4b58-949c-891cc8ec43bf"));
        List<MORelation> moRelations = new ArrayList<>();
        MORelation moRelation = new MORelation(new DN("28f59c78-64b5-4b58-949c-891cc8ec43bf"), new DN("node"),
            RelationType.Deployment);
        moRelations.add(moRelation);
        PowerMockito.when(mitManagerClient.getAllRelationsByDns(dnPodList, RelationType.Deployment.name(), false))
            .thenReturn(moRelations);

        List<DN> destNodeList = new ArrayList<>();
        destNodeList.add(new DN("node"));
        List<ManagedObject> destNodes = new ArrayList<>();
        destNodes.add(mo);
        PowerMockito.when(mitManagerClient.getMOList(destNodeList)).thenReturn(destNodes);

        PodViewCommonParam param = new PodViewCommonParam();
        param.setInstanceId(145);
        param.setEndTime(1736824461000L);
        PodDeployResult podDeployResult = serviceImpl.queryPodDeployData(param);

        Assert.assertEquals(1, podDeployResult.getVmDataList().size());
    }

    @Test
    public void queryPodIndicatorTest() throws ServiceException {
        mockAuthentication();
        PodViewCommonParam param = new PodViewCommonParam();
        param.setInstanceId(145);
        PodIndicatorDisplayResults podIndicatorDisplayResults = serviceImpl.queryIndicatorListAndData(param);

        Assert.assertEquals(1, podIndicatorDisplayResults.getPodIndicatorList().size());

        PodViewCommonParam commonParam = new PodViewCommonParam();
        commonParam.setInstanceId(103);
        PodIndicatorDisplayResults indicatorDisplayResults = serviceImpl.queryIndicatorListAndData(commonParam);

        Assert.assertEquals(1, indicatorDisplayResults.getPodIndicatorList().size());
    }

    @Test
    public void queryPodIndicatorTimeLineTest() throws ServiceException {
        mockAuthentication();
        PodViewCommonParam param = new PodViewCommonParam();
        param.setInstanceId(145);
        param.setEndTime(1721641800000L);
        PodIndicatorDisplayResults podIndicatorDisplayResults = serviceImpl.queryIndicatorListAndData(param);

        Assert.assertEquals(1, podIndicatorDisplayResults.getPodIndicatorList().size());

        PodViewCommonParam commonParam = new PodViewCommonParam();
        commonParam.setInstanceId(103);
        commonParam.setEndTime(1721641800000L);
        PodIndicatorDisplayResults indicatorDisplayResults = serviceImpl.queryIndicatorListAndData(commonParam);

        Assert.assertEquals(23, indicatorDisplayResults.getPodIndicatorList().size());
    }

    @Test
    public void queryPodIndicatorNotAdminTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        Set<String> set = new HashSet<>();
        set.add("28f59c78-64b5-4b58-949c-891cc8ec43bf");
        contextUtils.setAdmin(false);
        contextUtils.setAuthDns(set);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        PodViewCommonParam param = new PodViewCommonParam();
        param.setInstanceId(145);
        PodIndicatorDisplayResults podIndicatorDisplayResults = serviceImpl.queryIndicatorListAndData(param);

        Assert.assertEquals(0, podIndicatorDisplayResults.getPodIndicatorList().size());
    }

    @Test
    public void queryClusterDeployDataTest() throws ServiceException, OSSException {
        mockAuthentication();
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);

        ManagedElement mo = new ManagedElement() {
            @Override
            public ManagedObject copy() {
                return null;
            }
        };
        mo.setIPAddress("*******");
        mo.setName("appName");
        mo.setAvailableStatus(AvailableStatus.Normal);
        mo.setCreatedTime(System.currentTimeMillis());
        mo.setVersion("2000");
        mo.setType("cbs.billing.adapterapp");
        PowerMockito.when(mitManagerClient.getMO("28f59c78-64b5-4b58-949c-891cc8ec43bf")).thenReturn(mo);

        MOType moType = new MOType();
        moType.setDisplayType("cbs.billing.adapterapp");
        PowerMockito.when(mitManagerClient.getMOType("cbs.billing.adapterapp")).thenReturn(moType);

        ClusterInsCommonParam commonParam = new ClusterInsCommonParam();
        commonParam.setInstanceId(145);
        ClusterDisplayResult clusterDisplayResult = serviceImpl.queryClusterDisplayResults(commonParam);
        Assert.assertEquals("28f59c78-64b5-4b58-949c-891cc8ec43bf", clusterDisplayResult.getDn());

        commonParam.setTimestamp(1721641800000L);
        ClusterDisplayResult clusterDisplayTimeResult = serviceImpl.queryClusterDisplayResults(commonParam);
        Assert.assertEquals("S5_CBS_BBIT_0424_1_adapterapp-1-5fb5c58864-95d4b", clusterDisplayTimeResult.getAppName());
    }

    @Test
    public void queryClusterDeployDataNotAdminTest() throws ServiceException, OSSException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        Set<String> set = new HashSet<>();
        set.add("28f59c78-64b5-4b58-949c-891cc8ec43bf");
        contextUtils.setAdmin(false);
        contextUtils.setAuthDns(set);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);

        ManagedElement mo = new ManagedElement() {
            @Override
            public ManagedObject copy() {
                return null;
            }
        };
        mo.setIPAddress("*******");
        mo.setName("appName");
        mo.setAvailableStatus(AvailableStatus.Normal);
        mo.setCreatedTime(System.currentTimeMillis());
        mo.setVersion("2000");
        mo.setType("cbs.billing.adapterapp");
        PowerMockito.when(mitManagerClient.getMO("28f59c78-64b5-4b58-949c-891cc8ec43bf")).thenReturn(mo);

        MOType moType = new MOType();
        moType.setDisplayType("cbs.billing.adapterapp");
        PowerMockito.when(mitManagerClient.getMOType("cbs.billing.adapterapp")).thenReturn(moType);

        ClusterInsCommonParam commonParam = new ClusterInsCommonParam();
        commonParam.setInstanceId(145);
        ClusterDisplayResult clusterDisplayResult = serviceImpl.queryClusterDisplayResults(commonParam);
        Assert.assertNull(clusterDisplayResult.getDn());

        commonParam.setTimestamp(1721641800000L);
        ClusterDisplayResult clusterDisplayTimeResult = serviceImpl.queryClusterDisplayResults(commonParam);
        Assert.assertNull(clusterDisplayTimeResult.getDn());
    }

    @Test
    public void queryVMIndicatorTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);

        ManagedElement mo = new ManagedElement() {
            @Override
            public ManagedObject copy() {
                return null;
            }
        };
        mo.setIPAddress("*******");
        mo.setName("podName");
        mo.setAvailableStatus(AvailableStatus.Normal);
        mo.setCreatedTime(System.currentTimeMillis());
        mo.setType("com.huawei.IRes.vm");
        List<ManagedObject> destNodes = new ArrayList<>();
        destNodes.add(mo);
        PowerMockito.when(mitManagerClient.getMOList(Mockito.anyList())).thenReturn(destNodes);

        mockIndicatorPrepare();

        PowerMockito.mockStatic(PerformanceClient.class);
        PerformanceClient performanceClient = PowerMockito.mock(PerformanceClient.class);
        PowerMockito.when(PerformanceClient.getInstance()).thenReturn(performanceClient);

        PowerMockito.when(performanceClient.getPeriod(Mockito.anyString(), Mockito.anyString())).thenReturn(5);

        List<HistoryQueryData> queryData = new ArrayList<>();
        HistoryQueryData historyQueryData = new HistoryQueryData();
        List<HistoryQueryValue> resultData = new ArrayList<>();
        HistoryQueryValue historyQueryValue = new HistoryQueryValue();
        historyQueryValue.setDn("7dc08f75cab3a79402796");
        historyQueryValue.setIndexName("test");
        historyQueryValue.setIndexUnit("KB");
        List<HistoryIndexValue> indexValues = new ArrayList<>();
        HistoryIndexValue historyIndexValue = new HistoryIndexValue();
        historyIndexValue.setTimestamp(System.currentTimeMillis());
        historyIndexValue.setIndexValue("100");
        indexValues.add(historyIndexValue);
        historyQueryValue.setIndexValues(indexValues);
        resultData.add(historyQueryValue);
        historyQueryData.setResultData(resultData);
        queryData.add(historyQueryData);

        PowerMockito.when(performanceClient.getHistoryDataFormPerformance(Mockito.any())).thenReturn(queryData);
        PowerMockito.when(performanceClient.taskVmCpuNmu(Mockito.anyString(), Mockito.anyString())).thenReturn(20);

        PodViewCommonParam param = new PodViewCommonParam();
        param.setInstanceId(161);
        VmIndicatorDisplayResults vmIndicatorDisplayResults = serviceImpl.queryVMIndicator(param);

        Assert.assertEquals(2, vmIndicatorDisplayResults.getVmIndicatorList().size());

    }

    @Test
    public void queryPipeLineTest() throws ServiceException {
        GrayscaleParam grayDisplayFlag = new GrayscaleParam();
        grayDisplayFlag.setInstanceId(1);
        PipelineDisplayResult pipelineDisplayResult = serviceImpl.queryPipeLine(grayDisplayFlag);

        Assert.assertNull(pipelineDisplayResult.getGrayDisplayFlag());

        GrayscaleParam grayDisplayFlag1 = new GrayscaleParam();
        PipelineDisplayResult pipelineDisplayResult1 = serviceImpl.queryPipeLine(grayDisplayFlag1);

        Assert.assertEquals(10, pipelineDisplayResult1.getGrayPercentage().intValue());

        BusinessStepModelDB businessStepModelDB = new BusinessStepModelDB();
        businessStepModelDB.setId(1);
        businessStepModelDB.setGrayPercentage(-1);
        businessStepModelDB.setStepId(1);
        businessStepModelDB.setGrayDisplayFlag(0);
        businessStepModelDB.setIsUpgrade(true);
        businessMoTypeDao.savePipelineStatus(businessStepModelDB, "111.11", "user");
        PipelineDisplayResult result = serviceImpl.queryPipeLine(grayDisplayFlag1);
        Assert.assertEquals(0, result.getGrayPercentage().intValue());

        businessStepModelDB.setId(1);
        businessStepModelDB.setGrayPercentage(-2);
        businessStepModelDB.setStepId(2);
        businessStepModelDB.setGrayDisplayFlag(0);
        businessStepModelDB.setIsUpgrade(true);
        businessMoTypeDao.savePipelineStatus(businessStepModelDB, "111.11", "user");
        result = serviceImpl.queryPipeLine(grayDisplayFlag1);
        Assert.assertEquals(1, result.getGrayPercentage().intValue());

        businessStepModelDB.setId(1);
        businessStepModelDB.setGrayPercentage(-3);
        businessStepModelDB.setStepId(3);
        businessStepModelDB.setGrayDisplayFlag(0);
        businessStepModelDB.setIsUpgrade(true);
        businessMoTypeDao.savePipelineStatus(businessStepModelDB, "111.11", "user");
        result = serviceImpl.queryPipeLine(grayDisplayFlag1);
        Assert.assertEquals(10, result.getGrayPercentage().intValue());
    }

    @Test
    public void queryPipeLineExceptionTest() throws ServiceException {
        ConfigData configData = ConfigurationUtil.getConfigDataByName("grayPercentageMinus");
        configData.setValue("123");
        ConfigurationUtil.updateConfigDataByName(configData);

        GrayscaleParam grayDisplayFlag = new GrayscaleParam();
        BusinessStepModelDB businessStepModelDB = new BusinessStepModelDB();
        businessStepModelDB.setId(1);
        businessStepModelDB.setGrayPercentage(-1);
        businessStepModelDB.setStepId(1);
        businessStepModelDB.setGrayDisplayFlag(0);
        businessStepModelDB.setIsUpgrade(true);
        businessMoTypeDao.savePipelineStatus(businessStepModelDB, "111.11", "user");
        PipelineDisplayResult result = serviceImpl.queryPipeLine(grayDisplayFlag);
        Assert.assertEquals(0, result.getGrayPercentage().intValue());
    }

    @Test
    public void queryGrayscaleTest() throws ServiceException {
        mockAuthentication();
        GrayscaleParam queryGrayscaleParam = new GrayscaleParam();
        queryGrayscaleParam.setInstanceId(148);
        GrayscaleResult grayscaleResult = serviceImpl.queryGrayscale(queryGrayscaleParam);

        Assert.assertEquals(1, grayscaleResult.getGrayscaleList().size());

        GrayscaleParam queryGrayscaleParam1 = new GrayscaleParam();
        queryGrayscaleParam1.setInstanceId(81);
        GrayscaleResult grayscaleResult1 = serviceImpl.queryGrayscale(queryGrayscaleParam1);

        Assert.assertEquals(1, grayscaleResult1.getGrayscaleList().size());
    }

    private void mockIndicatorPrepare() {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");
        PowerMockito.mockStatic(PropertiesUtil.class);
        String[] vmIndicatorIndexNames = "Total memory,Memory usage,CPU usage".split(",");
        String[] vmIndicatorMeasUnitKeys = "Memory,Memory,CPU".split(",");
        String[] vmIndicatorMeasTypeKeys = "MemTotal,MemUsage,CpuUsage".split(",");

        List<Indicator> indicatorList = new ArrayList<>();
        for (int i = 0; i < vmIndicatorMeasTypeKeys.length; i++) {
            Indicator indicator = new Indicator();
            indicator.setMoType("com.huawei.IRes.vm");
            indicator.setIndexName(vmIndicatorIndexNames[i]);
            indicator.setMeasUnitKey(vmIndicatorMeasUnitKeys[i]);
            indicator.setMeasTypeKey(vmIndicatorMeasTypeKeys[i]);
            indicatorList.add(indicator);
        }
        PowerMockito.when(PropertiesUtil.buildVmIndicate("com.huawei.IRes.vm")).thenReturn(indicatorList);
    }

    @Test
    public void queryHostViewGridTest() {
        Integer vmId = 153;
        Long timeStamp = 0L;
        HostViewGrid hostViewGrid = hostViewService.queryHostViewGrid(vmId, timeStamp);
        Assert.assertEquals(1, hostViewGrid.getVmDataList().size());
    }

}
