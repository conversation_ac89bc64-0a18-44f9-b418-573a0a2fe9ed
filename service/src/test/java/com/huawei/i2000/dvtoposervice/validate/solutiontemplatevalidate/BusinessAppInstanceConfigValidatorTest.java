/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.validate.solutiontemplatevalidate;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.service.bean.AlarmInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.AssociationAppLink;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppInstanceConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppInstanceInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessSiteConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.ConfigurationImportEntity;
import com.huawei.i2000.dvtoposervice.business.service.bean.EventInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo;
import com.huawei.i2000.dvtoposervice.business.topo.solutiontemplatevalidate.checker.BusinessAppInstanceConfigValidator;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public class BusinessAppInstanceConfigValidatorTest extends WebServiceTest {

    private static final AtomicBoolean DATALOAD = new AtomicBoolean(false);

    @Autowired
    private BusinessAppInstanceConfigValidator businessAppInstanceConfigValidator;

    @Before
    public void PreparedData() throws Exception {
        businessAppInstanceConfigValidator = new BusinessAppInstanceConfigValidator();
        // 保证这个只运行一次
        if (!DATALOAD.compareAndSet(false, true)) {
            return;
        }
        setSqlScriptEncoding("UTF-8");
    }

    @Test
    public void checkTest() {
        ConfigurationImportEntity configurationImportEntity = new ConfigurationImportEntity();
        BusinessAppInstanceConfig businessAppInstanceConfig = new BusinessAppInstanceConfig();
        configurationImportEntity.setBusinessAppInstanceConfig(businessAppInstanceConfig);
        businessAppInstanceConfig.setModelType(6);
        List<BusinessAppInstanceInfo> businessAppInstanceList = new ArrayList<>();
        BusinessAppInstanceInfo businessAppInstanceInfo = new BusinessAppInstanceInfo();
        businessAppInstanceInfo.setModelName("model_name 123");
        businessAppInstanceInfo.setParentModelName("#parentName123");
        businessAppInstanceInfo.setMoTypeMapping("cbs.billing123");
        businessAppInstanceInfo.setApplicationType(0);
        BusinessSiteConfig businessSiteConfig = new BusinessSiteConfig();
        businessSiteConfig.setType(1);
        businessSiteConfig.setSiteIdColumn("siteId");
        businessAppInstanceInfo.setBusinessSiteConfig(businessSiteConfig);
        List<IndicatorInfo> indicatorList = new ArrayList<>();
        IndicatorInfo indicatorInfo = new IndicatorInfo();
        indicatorInfo.setMeasUnitKey("unitKey");
        indicatorInfo.setMeasTypeKey("typeKey");
        indicatorList.add(indicatorInfo);
        businessAppInstanceInfo.setIndicatorList(indicatorList);
        businessAppInstanceInfo.setAlarmFilterType(0);
        List<AlarmInfo> alarmList = new ArrayList<>();
        AlarmInfo alarmInfo = new AlarmInfo();
        alarmInfo.setAlarmId(123);
        alarmList.add(alarmInfo);
        businessAppInstanceInfo.setAlarmList(alarmList);
        List<EventInfo> eventList = new ArrayList<>();
        EventInfo eventInfo = new EventInfo();
        eventInfo.setEventId(123);
        eventList.add(eventInfo);
        businessAppInstanceInfo.setEventList(eventList);
        List<AssociationAppLink> associationAppLinkList = new ArrayList<>();
        AssociationAppLink associationAppLink = new AssociationAppLink();
        associationAppLink.setModelName("modelName");
        associationAppLink.setProtocol("IPV4");
        associationAppLink.setLinkDirection(0);
        associationAppLink.setLinkType(0);
        associationAppLinkList.add(associationAppLink);
        businessAppInstanceInfo.setAssociationAppLinkList(associationAppLinkList);
        businessAppInstanceInfo.setPodLevel("1");
        businessAppInstanceList.add(businessAppInstanceInfo);
        businessAppInstanceConfig.setBusinessAppInstanceList(businessAppInstanceList);
        Exception e = null;
        try {
            businessAppInstanceConfigValidator.check(configurationImportEntity);
        } catch (ServiceException exception) {
            e = exception;
        }
        Assert.assertNull(e);
    }

    @Test
    public void checkModelNameTest() {
        try {
            businessAppInstanceConfigValidator.checkModelName("test * 123");
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; modelName validate error.", exception.getMessage());
        }
    }

    @Test
    public void checkParentModelNameTest() {
        try {
            businessAppInstanceConfigValidator.checkParentModelName("test * 123");
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; parentModelName validate error.", exception.getMessage());
        }
    }

    @Test
    public void checkMoTypeMappingTest() {
        try {
            businessAppInstanceConfigValidator.checkMoTypeMapping("cbs#billing.adapterapp.cluster");
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; moTypeMapping validate error.", exception.getMessage());
        }
    }

    @Test
    public void checkApplicationTypeTest() {
        try {
            businessAppInstanceConfigValidator.checkApplicationType(null);
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; applicationType validate error.", exception.getMessage());
        }

        try {
            businessAppInstanceConfigValidator.checkApplicationType(3);
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; applicationType validate error.", exception.getMessage());
        }

        try {
            businessAppInstanceConfigValidator.checkApplicationType(2);
        } catch (ServiceException exception) {
            Assert.assertNull(exception);
        }
    }

    @Test
    public void checkBusinessSiteConfigTypeTest() {
        try {
            businessAppInstanceConfigValidator.checkBusinessSiteConfigType(null);
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; businessSiteConfig type validate error.", exception.getMessage());
        }

        try {
            businessAppInstanceConfigValidator.checkBusinessSiteConfigType(3);
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; businessSiteConfig type validate error.", exception.getMessage());
        }

        try {
            businessAppInstanceConfigValidator.checkBusinessSiteConfigType(2);
        } catch (ServiceException exception) {
            Assert.assertNull(exception);
        }
    }

    @Test
    public void checkBusinessSiteConfigSiteIdColumnTest() {
        try {
            businessAppInstanceConfigValidator.checkBusinessSiteConfigSiteIdColumn("test * 123");
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; businessSiteConfig siteIdColumn validate error.", exception.getMessage());
        }
    }

    @Test
    public void checkIndicatorListMeasUnitKeyTest() {
        try {
            businessAppInstanceConfigValidator.checkIndicatorListMeasUnitKey("test * 123");
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; indicatorList measUnitKey validate error.", exception.getMessage());
        }
    }

    @Test
    public void checkIndicatorListMeasTypeKeyTest() {
        try {
            businessAppInstanceConfigValidator.checkIndicatorListMeasTypeKey("test * 123");
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; indicatorList measTypeKey validate error.", exception.getMessage());
        }
    }

    @Test
    public void checkIndicatorListOriginalValueTest() {
        try {
            businessAppInstanceConfigValidator.checkIndicatorListOriginalValue("test * 123");
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; indicatorList originalValue validate error.", exception.getMessage());
        }
    }

    @Test
    public void checkBusinessAppInstanceListAlarmFilterTypeTest() {
        try {
            businessAppInstanceConfigValidator.checkBusinessAppInstanceListAlarmFilterType(null);
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; businessAppInstanceList alarmFilterType validate error.", exception.getMessage());
        }

        try {
            businessAppInstanceConfigValidator.checkBusinessAppInstanceListAlarmFilterType(3);
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; businessAppInstanceList alarmFilterType validate error.", exception.getMessage());
        }

        try {
            businessAppInstanceConfigValidator.checkBusinessAppInstanceListAlarmFilterType(2);
        } catch (ServiceException exception) {
            Assert.assertNull(exception);
        }
    }

    @Test
    public void checkAlarmListAlarmIDTest() {
        try {
            businessAppInstanceConfigValidator.checkAlarmListAlarmID(null);
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; businessAppInstanceList alarmList alarmID validate error.", exception.getMessage());
        }
    }

    @Test
    public void checkEventListEventIDTest() {
        try {
            businessAppInstanceConfigValidator.checkEventListEventID(null);
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; businessAppInstanceList eventList eventId validate error.", exception.getMessage());
        }

        try {
            businessAppInstanceConfigValidator.checkEventListEventID(123545);
        } catch (ServiceException exception) {
            Assert.assertNull(exception);
        }
    }

    @Test
    public void checkAssociationAppLinkModelNameTest() {
        try {
            businessAppInstanceConfigValidator.checkAssociationAppLinkModelName("test * 123");
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; associationAppLinkList modelName validate error.", exception.getMessage());
        }
    }

    @Test
    public void checkAssociationAppLinkProtocolTest() {
        try {
            businessAppInstanceConfigValidator.checkAssociationAppLinkProtocol("test * 123");
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; associationAppLinkList protocol validate error.", exception.getMessage());
        }
    }

    @Test
    public void checkAssociationAppLinkLinkDirectionTest() {
        try {
            businessAppInstanceConfigValidator.checkAssociationAppLinkLinkDirection(null);
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; associationAppLinkList linkDirection validate error.", exception.getMessage());
        }
    }

    @Test
    public void checkAssociationAppLinkLinkTypeTest() {
        try {
            businessAppInstanceConfigValidator.checkAssociationAppLinkLinkType(null);
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; associationAppLinkList linkType validate error.", exception.getMessage());
        }

        try {
            businessAppInstanceConfigValidator.checkAssociationAppLinkLinkType(3);
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; associationAppLinkList linkType validate error.", exception.getMessage());
        }

        try {
            businessAppInstanceConfigValidator.checkAssociationAppLinkLinkType(1);
        } catch (ServiceException exception) {
            Assert.assertNull(exception);
        }
    }

    @Test
    public void checkPodLevelTest() {
        try {
            businessAppInstanceConfigValidator.checkPodLevel(null);
        } catch (ServiceException exception) {
            Assert.assertEquals("exception.id: framwork.remote.SystemError; podLevel validate error.", exception.getMessage());
        }
    }
}
