/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.topo.topofunction;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppGroupInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.ConfigurationImportEntity;
import com.huawei.i2000.dvtoposervice.business.service.bean.IncludeAppTypeInfo;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.i2000.gapi.account.mo.AccountMO;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.Host;
import com.huawei.oms.eam.mo.ManagedObject;

import com.alibaba.fastjson2.JSON;

import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * BusinessDaoTest
 *
 * <AUTHOR>
 * @since 2024/2/29
 */
@PrepareForTest({PropertiesUtil.class, MITManagerClient.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class AddingSiteModelHandlerTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static final MapOper<ConfigData> BUSINESS_CONFIG_MAP_OPER = new MapOper<>(
        RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);

    private static EmbeddedRestClientAndServer mockServer;

    @Autowired
    AddingSiteModelHandler addingSiteModelHandler;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_import.sql", false);
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    @Test
    public void addSolutionTest() throws ServiceException, OSSException {
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        ManagedObject mo = new Host();
        mo.putClientProperty("active_dv_site", "1");
        mo.putClientProperty("dv_site_info_list", "1:************;2:************");
        PowerMockito.when(mitManagerClient.getMO(Mockito.anyString())).thenReturn(mo);
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("dvIndicators");
        configData.setValue("[{\"moType\": \"OMS\",\"measUnitKey\": \"I2K_OS\",\"measTypeKey\": \"UsedCPURate\"},{\"moType\": \"OMS\",\"measUnitKey\": \"I2K_OS\",\"measTypeKey\": \"UsedMemoryRate\"}]");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, configData.getConfigItemName(), configData);
        configData.setConfigItemName("dvAlarmSeverity");
        configData.setValue("[1]");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, configData.getConfigItemName(), configData);
        String configJson = "{\"businessAppGroupConfig\":{\"autoLayoutsFlag\":false,\"businessAppGroupList\":[{\"groupName\":\"BDI\",\"includeAppTypeList\":[{\"name\":\"reportapp\",\"type\":1},{\"name\":\"reportdb\",\"type\":1}],\"modelName\":\"BDI\"},{\"groupName\":\"Offline Mediation\",\"includeAppTypeList\":[{\"name\":\"mdsdataproc\",\"type\":1},{\"name\":\"mdsbmapp\",\"type\":1},{\"name\":\"businesscontrol\",\"type\":1},{\"name\":\"businesscontrol\",\"type\":1},{\"name\":\"eventproc\",\"type\":1},{\"name\":\"meddb\",\"type\":1}],\"modelName\":\"Offline_Mediation\"},{\"groupName\":\"UVC\",\"includeAppTypeList\":[{\"name\":\"vmapp\",\"type\":1},{\"name\":\"vcapp\",\"type\":1},{\"name\":\"tmapp\",\"type\":1},{\"name\":\"uvcdb\",\"type\":1}],\"modelName\":\"UVC\"},{\"groupName\":\"Accessibility\",\"includeAppTypeList\":[{\"name\":\"southcgw\",\"type\":0},{\"name\":\"adapterapp\",\"type\":0},{\"name\":\"internaladapter\",\"type\":0},{\"name\":\"accessfacade\",\"type\":0},{\"name\":\"cdfapp\",\"type\":0},{\"name\":\"cdfmdb\",\"type\":0},{\"name\":\"adaptermdb\",\"type\":0},{\"name\":\"see\",\"type\":1},{\"name\":\"omgmdb\",\"type\":1}],\"modelName\":\"Accessibility\"},{\"groupName\":\"Real-time service\",\"includeAppTypeList\":[{\"name\":\"chargingcache\",\"type\":0},{\"name\":\"chargingcacheb\",\"type\":0},{\"name\":\"convergedcharging\",\"type\":0},{\"name\":\"bizmngcharging\",\"type\":0},{\"name\":\"recurringrating\",\"type\":0},{\"name\":\"onlinecharging\",\"type\":0},{\"name\":\"rerating\",\"type\":0},{\"name\":\"notification\",\"type\":0},{\"name\":\"cbpgmdb\",\"type\":0}],\"modelName\":\"Real_time_service\"},{\"groupName\":\"Management and postpaid services\",\"includeAppTypeList\":[{\"name\":\"bmapp\",\"type\":0},{\"name\":\"arapp\",\"type\":0},{\"name\":\"bbfapp\",\"type\":0},{\"name\":\"cccapp\",\"type\":0},{\"name\":\"upcapp\",\"type\":0},{\"name\":\"dcapp\",\"type\":0},{\"name\":\"glapp\",\"type\":0},{\"name\":\"cdrprocess\",\"type\":0},{\"name\":\"aggregation\",\"type\":0},{\"name\":\"billrun\",\"type\":0},{\"name\":\"hugebillgen\",\"type\":0},{\"name\":\"billmanagement\",\"type\":0},{\"name\":\"bmpdb\",\"type\":0},{\"name\":\"usrdb\",\"type\":0},{\"name\":\"billdb\",\"type\":0},{\"name\":\"edrdb\",\"type\":0},{\"name\":\"edrhisdb\",\"type\":0},{\"name\":\"invgmdb\",\"type\":0},{\"name\":\"billsharedb\",\"type\":0}],\"modelName\":\"Management_and_postpaid_services\"}],\"modelType\":4},\"businessAppInstanceConfig\":{\"businessAppInstanceList\":[{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwSMPPPlusTPS\",\"measUnitKey\":\"hwBillingSMPPPlusMsgV2WithoutDimension\"},{\"measTypeKey\":\"hwSMPPPlusSuccRate\",\"measUnitKey\":\"hwBillingSMPPPlusMsgV2WithoutDimension\"},{\"measTypeKey\":\"hwSMPPTPS\",\"measUnitKey\":\"hwBillingSMPPMsgV2WithoutDimension\"},{\"measTypeKey\":\"hwSMPPSuccRate\",\"measUnitKey\":\"hwBillingSMPPMsgV2WithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.accessfacade.cluster\",\"modelName\":\"accessfacade\",\"parentModelName\":\"accessfacade\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingRestAccessMsgNumV2WithoutDimension\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingRestAccessMsgNumV2WithoutDimension\"},{\"measTypeKey\":\"hwRestAverageTime\",\"measUnitKey\":\"hwBillingRestAccessFlowDelayWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.charginggw.cluster\",\"modelName\":\"southcgw\",\"parentModelName\":\"southcgw\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.cdfapp.cluster\",\"modelName\":\"cdfapp\",\"parentModelName\":\"cdfapp\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.notification.cluster\",\"modelName\":\"notification\",\"parentModelName\":\"notification\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.rerating.cluster\",\"modelName\":\"rerating\",\"parentModelName\":\"rerating\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.onlinecharging.cluster\",\"modelName\":\"onlinecharging\",\"parentModelName\":\"onlinecharging\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.recurringrating.cluster\",\"modelName\":\"recurringrating\",\"parentModelName\":\"recurringrating\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.bizmngcharging.cluster\",\"modelName\":\"bizmngcharging\",\"parentModelName\":\"bizmngcharging\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.convergedcharging.cluster\",\"modelName\":\"convergedcharging\",\"parentModelName\":\"convergedcharging\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.chargingcacheb.cluster\",\"modelName\":\"chargingcacheb\",\"parentModelName\":\"chargingcacheb\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.chargingcache.cluster\",\"modelName\":\"chargingcache\",\"parentModelName\":\"chargingcache\",\"podLevel\":\"3\"},{\"applicationType\":2,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.adptmdbcloud.cluster\",\"modelName\":\"adaptermdb\",\"parentModelName\":\"adaptermdb\",\"podLevel\":\"3\"},{\"applicationType\":2,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.cdfmdbcloud.cluster\",\"modelName\":\"cdfmdb\",\"parentModelName\":\"cdfmdb\",\"podLevel\":\"3\"},{\"applicationType\":2,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.cbpmdbcloud.cluster\",\"modelName\":\"cbpgmdb\",\"parentModelName\":\"cbpgmdb\",\"podLevel\":\"3\"},{\"applicationType\":2,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.invmdbcloud.cluster\",\"modelName\":\"invgmdb\",\"parentModelName\":\"invgmdb\",\"podLevel\":\"3\"},{\"applicationType\":1,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.billsharedbcloud.cluster\",\"modelName\":\"billsharedb\",\"parentModelName\":\"billsharedb\",\"podLevel\":\"3\"},{\"applicationType\":1,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.edrdbhiscloud.cluster\",\"modelName\":\"edrhisdb\",\"parentModelName\":\"edrhisdb\",\"podLevel\":\"3\"},{\"applicationType\":1,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.edrdbcloud.cluster\",\"modelName\":\"edrdb\",\"parentModelName\":\"edrdb\",\"podLevel\":\"3\"},{\"applicationType\":1,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.billdbcloud.cluster\",\"modelName\":\"billdb\",\"parentModelName\":\"billdb\",\"podLevel\":\"3\"},{\"applicationType\":1,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.usrdbcloud.cluster\",\"modelName\":\"usrdb\",\"parentModelName\":\"usrdb\",\"podLevel\":\"3\"},{\"applicationType\":1,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.bmpdbcloud.cluster\",\"modelName\":\"bmpdb\",\"parentModelName\":\"bmpdb\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.billmanagement.cluster\",\"modelName\":\"billmanagement\",\"parentModelName\":\"billmanagement\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.hugebillgen.cluster\",\"modelName\":\"hugebillgen\",\"parentModelName\":\"hugebillgen\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.billrun.cluster\",\"modelName\":\"billrun\",\"parentModelName\":\"billrun\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.aggregation.cluster\",\"modelName\":\"aggregation\",\"parentModelName\":\"aggregation\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.cdrprocess.cluster\",\"modelName\":\"cdrprocess\",\"parentModelName\":\"cdrprocess\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[],\"moTypeMapping\":\"cbs.billing.glapp.cluster\",\"modelName\":\"glapp\",\"parentModelName\":\"glapp\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwBillingBmpTaskInfoV2WithoutDimension\",\"measUnitKey\":\"hwBillingBmpTaskInfoV2WithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.dcapp.cluster\",\"modelName\":\"dcapp\",\"parentModelName\":\"dcapp\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\"},{\"measTypeKey\":\"hwResponseAvgTime\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\"},{\"measTypeKey\":\"hwRequestSuccessRate\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.upcapp.cluster\",\"modelName\":\"upcapp\",\"parentModelName\":\"upcapp\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[],\"moTypeMapping\":\"cbs.billing.cccapp.cluster\",\"modelName\":\"cccapp\",\"parentModelName\":\"cccapp\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\"},{\"measTypeKey\":\"hwResponseAvgTime\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\"},{\"measTypeKey\":\"hwRequestSuccessRate\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.bbfapp.cluster\",\"modelName\":\"bbfapp\",\"parentModelName\":\"bbfapp\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\"},{\"measTypeKey\":\"hwResponseAvgTime\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\"},{\"measTypeKey\":\"hwRequestSuccessRate\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.bmapp.cluster\",\"modelName\":\"bmapp\",\"parentModelName\":\"bmapp\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\"},{\"measTypeKey\":\"hwResponseAvgTime\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\"},{\"measTypeKey\":\"hwRequestSuccessRate\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.arapp.cluster\",\"modelName\":\"arapp\",\"parentModelName\":\"arapp\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDCCAccessMsgNumberV4WithoutDimension\"},{\"measTypeKey\":\"hwSyTPS\",\"measUnitKey\":\"hwBillingDCCAccessMsgNumberV4WithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.internaladapter.cluster\",\"modelName\":\"internaladapter\",\"parentModelName\":\"internaladapter\",\"podLevel\":\"3\"},{\"applicationType\":0,\"businessSiteConfig\":{\"siteIdColumn\":\"solutionId\",\"type\":1},\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDCCAccessMsgNumberV4WithoutDimension\"},{\"measTypeKey\":\"hwSyTPS\",\"measUnitKey\":\"hwBillingDCCAccessMsgNumberV4WithoutDimension\"}],\"moTypeMapping\":\"cbs.billing.adapterapp.cluster\",\"modelName\":\"adapterapp\",\"parentModelName\":\"adapterapp\",\"podLevel\":\"3\"}],\"modelType\":6},\"businessAppTypeConfig\":{\"businessAppTypeList\":[{\"appTypeName\":\"upcapp\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billsharedb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"}],\"modelName\":\"upcapp\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"cccapp\",\"associationAppLinkList\":[],\"modelName\":\"cccapp\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"bbfapp\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billrun\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"usrdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"bmpdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billsharedb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"}],\"modelName\":\"bbfapp\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"arapp\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"accessfacade\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"bizmngcharging\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"notification\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billrun\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billsharedb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"vcapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"}],\"modelName\":\"arapp\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"bmapp\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"accessfacade\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"bizmngcharging\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"notification\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"see\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"convergedcharging\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"recurringrating\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"onlinecharging\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cdfapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"rerating\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"reportdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"vmapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"}],\"modelName\":\"bmapp\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"adaptermdb\",\"associationAppLinkList\":[],\"modelName\":\"adaptermdb\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"cdfmdb\",\"associationAppLinkList\":[],\"modelName\":\"cdfmdb\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"cdfapp\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcache\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcacheb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cbpgmdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cdfmdb\",\"protocol\":\"IPV4\"}],\"modelName\":\"cdfapp\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"accessfacade\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcache\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcacheb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"onlinecharging\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"bmapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"arapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"dcapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"glapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"}],\"modelName\":\"accessfacade\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"internaladapter\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcache\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"see\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"onlinecharging\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cdfapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"vcapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"}],\"modelName\":\"internaladapter\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"bizmngcharging\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcache\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcacheb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cbpgmdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"arapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billrun\",\"protocol\":\"IPV4\"}],\"modelName\":\"bizmngcharging\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"convergedcharging\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcache\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcacheb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cbpgmdb\",\"protocol\":\"IPV4\"}],\"modelName\":\"convergedcharging\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"chargingcacheb\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"}],\"modelName\":\"chargingcacheb\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"chargingcache\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"}],\"modelName\":\"chargingcache\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"billsharedb\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"}],\"modelName\":\"billsharedb\"},{\"appTypeName\":\"invgmdb\",\"associationAppLinkList\":[],\"modelName\":\"invgmdb\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"edrhisdb\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"}],\"modelName\":\"edrhisdb\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"edrdb\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"}],\"modelName\":\"edrdb\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"billdb\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"}],\"modelName\":\"billdb\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"usrdb\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"}],\"modelName\":\"usrdb\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"bmpdb\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"mdsbmapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"businesscontrol\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"mdscache\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"eventproc\",\"protocol\":\"IPV4\"}],\"modelName\":\"bmpdb\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"billmanagement\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billsharedb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cbpgmdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"usrdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"bmpdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"edrdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"invgmdb\",\"protocol\":\"IPV4\"}],\"modelName\":\"billmanagement\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"hugebillgen\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billsharedb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cbpgmdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"usrdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"bmpdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"edrdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"invgmdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcache\",\"protocol\":\"IPV4\"}],\"modelName\":\"hugebillgen\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"billrun\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billsharedb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cbpgmdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"usrdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"bmpdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"edrdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"invgmdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcache\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"recurringrating\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"hugebillgen\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billmanagement\",\"protocol\":\"IPV4\"}],\"modelName\":\"billrun\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"aggregation\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billsharedb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cbpgmdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"usrdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"bmpdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"edrdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"invgmdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcache\",\"protocol\":\"IPV4\"}],\"modelName\":\"aggregation\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"cdrprocess\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billsharedb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cbpgmdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"usrdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"bmpdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"edrdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"edrhisdb\",\"protocol\":\"IPV4\"}],\"modelName\":\"cdrprocess\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"glapp\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billsharedb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"}],\"modelName\":\"glapp\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"dcapp\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"billsharedb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"accessfacade\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"notification\",\"protocol\":\"IPV4\"}],\"modelName\":\"dcapp\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"adapterapp\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcache\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"convergedcharging\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"onlinecharging\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cdfapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cdfmdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"}],\"modelName\":\"adapterapp\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"southcgw\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcache\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcacheb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"}],\"modelName\":\"southcgw\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"cbpgmdb\",\"associationAppLinkList\":[],\"modelName\":\"cbpgmdb\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"notification\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcache\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcacheb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cbpgmdb\",\"protocol\":\"IPV4\"}],\"modelName\":\"notification\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"rerating\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcache\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcacheb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cbpgmdb\",\"protocol\":\"IPV4\"}],\"modelName\":\"rerating\",\"supportGrayUpgrade\":true},{\"appTypeName\":\"onlinecharging\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcache\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcacheb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cbpgmdb\",\"protocol\":\"IPV4\"}],\"modelName\":\"onlinecharging\",\"supportGrayUpgrade\":false},{\"appTypeName\":\"recurringrating\",\"associationAppLinkList\":[{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cccapp\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcache\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"chargingcacheb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"adaptermdb\",\"protocol\":\"IPV4\"},{\"linkDirection\":0,\"linkType\":0,\"modelName\":\"cbpgmdb\",\"protocol\":\"IPV4\"}],\"modelName\":\"recurringrating\",\"supportGrayUpgrade\":true}],\"modelType\":5},\"businessConfig\":{\"businessList\":[{\"businessName\":\"Manager business\",\"businessType\":\"north\",\"indicatorList\":[{\"aggrType\":1,\"indicatorDisplayType\":1,\"measTypeKey\":\"hwManagementServiceTPS\",\"measUnitKey\":\"hwManagementService\",\"moType\":\"cbs.billing.cbs\"},{\"aggrType\":1,\"indicatorDisplayType\":0,\"measTypeKey\":\"hwManagementServiceSuccRate\",\"measUnitKey\":\"hwManagementService\",\"moType\":\"cbs.billing.cbs\"},{\"aggrType\":1,\"indicatorDisplayType\":0,\"measTypeKey\":\"hwManagementServiceAvgTime\",\"measUnitKey\":\"hwManagementService\",\"moType\":\"cbs.billing.cbs\"}],\"mainBusiness\":true,\"modelId\":\"1_Manager_business\",\"modelName\":\"Manager_business\"},{\"businessName\":\"AccessFacade SMPP Plus\",\"businessType\":\"south\",\"indicatorList\":[{\"aggrType\":1,\"indicatorDisplayType\":1,\"measTypeKey\":\"hwSMPPPlusTPS\",\"measUnitKey\":\"hwSMPPPlus\",\"moType\":\"cbs.billing.cbs\"},{\"aggrType\":1,\"indicatorDisplayType\":0,\"measTypeKey\":\"hwSMPPPlusSuccRate\",\"measUnitKey\":\"hwSMPPPlus\",\"moType\":\"cbs.billing.cbs\"},{\"aggrType\":1,\"indicatorDisplayType\":0,\"measTypeKey\":\"hwSMPPPlusAvgTime\",\"measUnitKey\":\"hwSMPPPlus\",\"moType\":\"cbs.billing.cbs\"}],\"mainBusiness\":false,\"modelId\":\"1_AccessFacade_SMPP_Plus\",\"modelName\":\"AccessFacade_SMPP_Plus\"},{\"businessName\":\"AccessFacade SMPP\",\"businessType\":\"south\",\"indicatorList\":[{\"aggrType\":1,\"indicatorDisplayType\":1,\"measTypeKey\":\"hwSMPPTPS\",\"measUnitKey\":\"hwSMPP\",\"moType\":\"cbs.billing.cbs\"},{\"aggrType\":1,\"indicatorDisplayType\":0,\"measTypeKey\":\"hwSMPPSuccRate\",\"measUnitKey\":\"hwSMPP\",\"moType\":\"cbs.billing.cbs\"},{\"aggrType\":1,\"indicatorDisplayType\":0,\"measTypeKey\":\"hwSMPPAvgTime\",\"measUnitKey\":\"hwSMPP\",\"moType\":\"cbs.billing.cbs\"}],\"mainBusiness\":false,\"modelId\":\"1_AccessFacade_SMPP\",\"modelName\":\"AccessFacade_SMPP\"},{\"businessName\":\"5G Service\",\"businessType\":\"south\",\"indicatorList\":[{\"aggrType\":1,\"indicatorDisplayType\":1,\"measTypeKey\":\"hw5GServiceTPS\",\"measUnitKey\":\"hw5GService\",\"moType\":\"cbs.billing.cbs\"},{\"aggrType\":1,\"indicatorDisplayType\":0,\"measTypeKey\":\"hw5GServiceSuccRate\",\"measUnitKey\":\"hw5GService\",\"moType\":\"cbs.billing.cbs\"},{\"aggrType\":1,\"indicatorDisplayType\":0,\"measTypeKey\":\"hw5GServiceAvgTime\",\"measUnitKey\":\"hw5GService\",\"moType\":\"cbs.billing.cbs\"}],\"mainBusiness\":false,\"modelId\":\"1_5G_Service\",\"modelName\":\"5G_Service\"},{\"businessName\":\"4G GPRS\",\"businessType\":\"south\",\"indicatorList\":[{\"aggrType\":1,\"indicatorDisplayType\":1,\"measTypeKey\":\"hwDataServiceTPS\",\"measUnitKey\":\"hw4GService\",\"moType\":\"cbs.billing.cbs\"},{\"aggrType\":1,\"indicatorDisplayType\":0,\"measTypeKey\":\"hwDataSuccRate\",\"measUnitKey\":\"hw4GService\",\"moType\":\"cbs.billing.cbs\"},{\"aggrType\":1,\"indicatorDisplayType\":0,\"measTypeKey\":\"hwDataResponseAvgTime\",\"measUnitKey\":\"hw4GService\",\"moType\":\"cbs.billing.cbs\"}],\"mainBusiness\":false,\"modelId\":\"1_4G_GPRS\",\"modelName\":\"4G_GPRS\"},{\"businessName\":\"4G Voice\",\"businessType\":\"south\",\"indicatorList\":[{\"aggrType\":1,\"indicatorDisplayType\":1,\"measTypeKey\":\"hwVoiceServiceTPS\",\"measUnitKey\":\"hw4GService\",\"moType\":\"cbs.billing.cbs\"},{\"aggrType\":1,\"indicatorDisplayType\":0,\"measTypeKey\":\"hwVoiceSuccRate\",\"measUnitKey\":\"hw4GService\",\"moType\":\"cbs.billing.cbs\"},{\"aggrType\":1,\"indicatorDisplayType\":0,\"measTypeKey\":\"hwVoiceResponseAvgTime\",\"measUnitKey\":\"hw4GService\",\"moType\":\"cbs.billing.cbs\"}],\"mainBusiness\":true,\"modelId\":\"1_4G_Voice\",\"modelName\":\"4G_Voice\"}],\"modelType\":1},\"classType\":\"cbs.billing.cbs\",\"containerConfig\":{\"modelName\":\"container_common_model\",\"modelType\":10},\"modelName\":\"cbs\",\"modelType\":0,\"podConfig\":{\"modelType\":7,\"podTypeList\":[{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwSMPPPlusTPS\",\"measUnitKey\":\"hwBillingSMPPPlusMsgV2WithoutDimension\",\"moType\":\"cbs.billing.accessfacade_docker\"},{\"measTypeKey\":\"hwSMPPPlusSuccRate\",\"measUnitKey\":\"hwBillingSMPPPlusMsgV2WithoutDimension\",\"moType\":\"cbs.billing.accessfacade_docker\"},{\"measTypeKey\":\"hwSMPPTPS\",\"measUnitKey\":\"hwBillingSMPPMsgV2WithoutDimension\",\"moType\":\"cbs.billing.accessfacade_docker\"},{\"measTypeKey\":\"hwSMPPSuccRate\",\"measUnitKey\":\"hwBillingSMPPMsgV2WithoutDimension\",\"moType\":\"cbs.billing.accessfacade_docker\"}],\"moTypeMapping\":\"cbs.billing.accessfacade\",\"modelName\":\"accessfacade\",\"parentMoTypeName\":\"accessfacade\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingRestAccessMsgNumV2WithoutDimension\",\"moType\":\"cbs.billing.charginggw_docker\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingRestAccessMsgNumV2WithoutDimension\",\"moType\":\"cbs.billing.charginggw_docker\"},{\"measTypeKey\":\"hwRestAverageTime\",\"measUnitKey\":\"hwBillingRestAccessFlowDelayWithoutDimension\",\"moType\":\"cbs.billing.charginggw_docker\"}],\"moTypeMapping\":\"cbs.billing.charginggw\",\"modelName\":\"southcgw\",\"parentMoTypeName\":\"southcgw\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.cdfapp_docker\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.cdfapp_docker\"}],\"moTypeMapping\":\"cbs.billing.cdfapp\",\"modelName\":\"cdfapp\",\"parentMoTypeName\":\"cdfapp\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.notification_docker\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.notification_docker\"}],\"moTypeMapping\":\"cbs.billing.notification\",\"modelName\":\"notification\",\"parentMoTypeName\":\"notification\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.rerating_docker\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.rerating_docker\"}],\"moTypeMapping\":\"cbs.billing.rerating\",\"modelName\":\"rerating\",\"parentMoTypeName\":\"rerating\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.onlinecharging_docker\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.onlinecharging_docker\"}],\"moTypeMapping\":\"cbs.billing.onlinecharging\",\"modelName\":\"onlinecharging\",\"parentMoTypeName\":\"onlinecharging\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.recurringrating_docker\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.recurringrating_docker\"}],\"moTypeMapping\":\"cbs.billing.recurringrating\",\"modelName\":\"recurringrating\",\"parentMoTypeName\":\"recurringrating\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.bizmngcharging_docker\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.bizmngcharging_docker\"}],\"moTypeMapping\":\"cbs.billing.bizmngcharging\",\"modelName\":\"bizmngcharging\",\"parentMoTypeName\":\"bizmngcharging\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.convergedcharging_docker\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.convergedcharging_docker\"}],\"moTypeMapping\":\"cbs.billing.convergedcharging\",\"modelName\":\"convergedcharging\",\"parentMoTypeName\":\"convergedcharging\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.chargingcacheb_docker\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.chargingcacheb_docker\"}],\"moTypeMapping\":\"cbs.billing.chargingcacheb\",\"modelName\":\"chargingcacheb\",\"parentMoTypeName\":\"chargingcacheb\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.chargingcache_docker\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.chargingcache_docker\"}],\"moTypeMapping\":\"cbs.billing.chargingcache\",\"modelName\":\"chargingcache\",\"parentMoTypeName\":\"chargingcache\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\",\"moType\":\"cbs.billing.adptmdbcloud_docker\"}],\"moTypeMapping\":\"cbs.billing.adptmdbcloud\",\"modelName\":\"adaptermdb\",\"parentMoTypeName\":\"adaptermdb\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\",\"moType\":\"cbs.billing.cdfmdbcloud_docker\"}],\"moTypeMapping\":\"cbs.billing.cdfmdbcloud\",\"modelName\":\"cdfmdb\",\"parentMoTypeName\":\"cdfmdb\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\",\"moType\":\"cbs.billing.cbpmdbcloud_docker\"}],\"moTypeMapping\":\"cbs.billing.cbpmdbcloud\",\"modelName\":\"cbpgmdb\",\"parentMoTypeName\":\"cbpgmdb\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\",\"moType\":\"cbs.billing.invmdbcloud_docker\"}],\"moTypeMapping\":\"cbs.billing.invmdbcloud\",\"modelName\":\"invgmdb\",\"parentMoTypeName\":\"invgmdb\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\",\"moType\":\"cbs.billing.billsharedbcloud_docker\"}],\"moTypeMapping\":\"cbs.billing.billsharedbcloud\",\"modelName\":\"billsharedb\",\"parentMoTypeName\":\"billsharedb\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\",\"moType\":\"cbs.billing.edrdbhiscloud_docker\"}],\"moTypeMapping\":\"cbs.billing.edrdbhiscloud\",\"modelName\":\"edrhisdb\",\"parentMoTypeName\":\"edrhisdb\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\",\"moType\":\"cbs.billing.edrdbcloud_docker\"}],\"moTypeMapping\":\"cbs.billing.edrdbcloud\",\"modelName\":\"edrdb\",\"parentMoTypeName\":\"edrdb\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\",\"moType\":\"cbs.billing.billdbcloud_docker\"}],\"moTypeMapping\":\"cbs.billing.billdbcloud\",\"modelName\":\"billdb\",\"parentMoTypeName\":\"billdb\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\",\"moType\":\"cbs.billing.usrdbcloud_docker\"}],\"moTypeMapping\":\"cbs.billing.usrdbcloud\",\"modelName\":\"usrdb\",\"parentMoTypeName\":\"usrdb\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwAvgRealTime_us\",\"measUnitKey\":\"hwBillingAgentQueueWithoutDimension\",\"moType\":\"cbs.billing.bmpdbcloud_docker\"}],\"moTypeMapping\":\"cbs.billing.bmpdbcloud\",\"modelName\":\"bmpdb\",\"parentMoTypeName\":\"bmpdb\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.billmanagement_docker\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.billmanagement_docker\"}],\"moTypeMapping\":\"cbs.billing.billmanagement\",\"modelName\":\"billmanagement\",\"parentMoTypeName\":\"billmanagement\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.hugebillgen_docker\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.hugebillgen_docker\"}],\"moTypeMapping\":\"cbs.billing.hugebillgen\",\"modelName\":\"hugebillgen\",\"parentMoTypeName\":\"hugebillgen\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.billrun_docker\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.billrun_docker\"}],\"moTypeMapping\":\"cbs.billing.billrun\",\"modelName\":\"billrun\",\"parentMoTypeName\":\"billrun\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.aggregation_docker\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.aggregation_docker\"}],\"moTypeMapping\":\"cbs.billing.aggregation\",\"modelName\":\"aggregation\",\"parentMoTypeName\":\"aggregation\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.cdrprocess_docker\"},{\"measTypeKey\":\"hwSuccRate\",\"measUnitKey\":\"hwBillingDsfProviderMsgWithoutDimension\",\"moType\":\"cbs.billing.cdrprocess_docker\"}],\"moTypeMapping\":\"cbs.billing.cdrprocess\",\"modelName\":\"cdrprocess\",\"parentMoTypeName\":\"cdrprocess\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[],\"moTypeMapping\":\"cbs.billing.glapp\",\"modelName\":\"glapp\",\"parentMoTypeName\":\"glapp\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwSuccessRate\",\"measUnitKey\":\"hwBillingBmpTaskInfoV2WithoutDimension\",\"moType\":\"cbs.billing.dcapp_docker\"}],\"moTypeMapping\":\"cbs.billing.dcapp\",\"modelName\":\"dcapp\",\"parentMoTypeName\":\"dcapp\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\",\"moType\":\"cbs.billing.upcapp_docker\"},{\"measTypeKey\":\"hwResponseAvgTime\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\",\"moType\":\"cbs.billing.upcapp_docker\"},{\"measTypeKey\":\"hwRequestSuccessRate\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\",\"moType\":\"cbs.billing.upcapp_docker\"}],\"moTypeMapping\":\"cbs.billing.upcapp\",\"modelName\":\"upcapp\",\"parentMoTypeName\":\"upcapp\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[],\"moTypeMapping\":\"cbs.billing.cccapp\",\"modelName\":\"cccapp\",\"parentMoTypeName\":\"cccapp\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\",\"moType\":\"cbs.billing.bbfapp_docker\"},{\"measTypeKey\":\"hwResponseAvgTime\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\",\"moType\":\"cbs.billing.bbfapp_docker\"},{\"measTypeKey\":\"hwRequestSuccessRate\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\",\"moType\":\"cbs.billing.bbfapp_docker\"}],\"moTypeMapping\":\"cbs.billing.bbfapp\",\"modelName\":\"bbfapp\",\"parentMoTypeName\":\"bbfapp\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\",\"moType\":\"cbs.billing.arapp_docker\"},{\"measTypeKey\":\"hwResponseAvgTime\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\",\"moType\":\"cbs.billing.arapp_docker\"},{\"measTypeKey\":\"hwRequestSuccessRate\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\",\"moType\":\"cbs.billing.arapp_docker\"}],\"moTypeMapping\":\"cbs.billing.arapp\",\"modelName\":\"arapp\",\"parentMoTypeName\":\"arapp\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\",\"moType\":\"cbs.billing.bmapp_docker\"},{\"measTypeKey\":\"hwResponseAvgTime\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\",\"moType\":\"cbs.billing.bmapp_docker\"},{\"measTypeKey\":\"hwRequestSuccessRate\",\"measUnitKey\":\"hwBillingAccessBmpReqInfoV2WithoutDimension\",\"moType\":\"cbs.billing.bmapp_docker\"}],\"moTypeMapping\":\"cbs.billing.bmapp\",\"modelName\":\"bmapp\",\"parentMoTypeName\":\"bmapp\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDCCAccessMsgNumberV4WithoutDimension\",\"moType\":\"cbs.billing.internaladapter_docker\"},{\"measTypeKey\":\"hwSyTPS\",\"measUnitKey\":\"hwBillingDCCAccessMsgNumberV4WithoutDimension\",\"moType\":\"cbs.billing.adapterapp_docker\"}],\"moTypeMapping\":\"cbs.billing.internaladapter\",\"modelName\":\"internaladapter\",\"parentMoTypeName\":\"internaladapter\"},{\"alarmFilterType\":0,\"alarmList\":[],\"indicatorList\":[{\"measTypeKey\":\"hwTPS\",\"measUnitKey\":\"hwBillingDCCAccessMsgNumberV4WithoutDimension\",\"moType\":\"cbs.billing.adapterapp_docker\"},{\"measTypeKey\":\"hwSyTPS\",\"measUnitKey\":\"hwBillingDCCAccessMsgNumberV4WithoutDimension\",\"moType\":\"cbs.billing.adapterapp_docker\"}],\"moTypeMapping\":\"cbs.billing.adapterapp\",\"modelName\":\"adapterapp\",\"parentMoTypeName\":\"adapterapp\"}]},\"siteDataSourceConfig\":{\"modelName\":\"site\",\"modelType\":3,\"siteDataType\":1,\"siteIdColumn\":\"solutionId\",\"siteMoType\":\"cbs.billing.cbs\"},\"siteGroupConfig\":{\"modelName\":\"cbs\",\"modelType\":2},\"solutionName\":\"cbs\",\"templateType\":0,\"timeLineConfig\":{\"aIOpsAlarm\":true,\"alarmFilterType\":0,\"alarmList\":[{\"alarmId\":412030116},{\"alarmId\":412030115},{\"alarmId\":412030220},{\"alarmId\":412010217},{\"alarmId\":412030111},{\"alarmId\":412030205}],\"eventList\":[{\"eventId\":412030206}]},\"version\":\"R024C10\",\"vmConfig\":{\"alarmList\":[],\"moType\":\"com.huawei.IRes.vm\",\"modelName\":\"vm\",\"modelType\":8}}";
        ConfigurationImportEntity configurationImportEntity = JSON.parseObject(configJson, ConfigurationImportEntity.class);
        BusinessAppGroupInfo info = new BusinessAppGroupInfo();
        info.setModelName("DigitalView");
        IncludeAppTypeInfo typeInfo = new IncludeAppTypeInfo();
        typeInfo.setName("DigitalView");
        List<IncludeAppTypeInfo> includeAppTypeInfos = new ArrayList<>();
        includeAppTypeInfos.add(typeInfo);
        info.setIncludeAppTypeList(includeAppTypeInfos);
        configurationImportEntity.getBusinessAppGroupConfig().getBusinessAppGroupList().add(info);
        List<ManagedObject> mos = new ArrayList<>();
        ManagedObject oss = JSON.parseObject("{\"address\":\"\",\"adminStatus\":\"Unlocked\",\"alarmStatus\":\"Cleared\",\"availableStatus\":\"Normal\",\"children\":[{\"empty\":false,\"parent\":{\"empty\":false,\"parent\":{\"$ref\":\"@\"},\"root\":true,\"value\":\"/\"},\"root\":false,\"value\":\"7dc08f7f7e2e91f23576a\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f72f72e8bed6976f\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f794a9d989c23769\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f73d1acaafbf176b\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f72183b304120768\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f70fd4829005c770\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f78039a2be7aa771\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f7a93a9c463b2772\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f7bde1055d986777\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f76be3a8b3d0d774\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f7318773f8e59773\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f72f22d892589779\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f74f5dbe7c93877a\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f7bf599d345ec77b\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f7ef7653d113b77c\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f7eecc1f8f95077e\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f764337dd39ec77f\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f7f3dc4c7c74177d\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f7148e9cd3b32782\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f77b2e245b13b783\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f750e2eb0632f789\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f78ae554d0cc4786\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f714623b87e9478b\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f74604be9a592787\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f70a24a93595d788\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f7706d53db3f878e\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f713b796b836678f\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f71156a86a350796\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f7231641ec2a7795\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f7c2193d7f4b179c\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f73250cd8b3b479f\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f77d964f182087a0\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f75d3a07822a379b\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f724e66a25dd879d\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f74262fd814357a8\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f7b5cf8b903367a2\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f70264caea5e07a3\"},{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f7c497755861b7a6\"}],\"clientProperties\":{\"oriSolutionId\":\"7dc08f7e8bfb21120271f\",\"siteName\":\"DefaultSite\",\"noConnectStatus\":\"true\",\"templateId\":\"icm.instance.7dc08f71919660bd9bd0a\",\"provinceId\":\"NA\",\"groupName\":\"DefaultGroup\",\"oriSolutionName\":\"S3_Docker0828\",\"action\":\"deploy\",\"configurationId\":\"icm.instance.7dc08f71919660bd9bd0a\",\"solutionId\":\"7dc08f7e8bfb21120271f-1\",\"solutionType\":\"true\",\"itpassDn\":\"NE=3092\"},\"connectStatus\":\"Online\",\"createdTime\":1725517891031,\"dN\":{\"empty\":false,\"parent\":{\"$ref\":\"$.children[0].parent\"},\"root\":false,\"value\":\"7dc08f7e8bfb21120271f-1\"},\"deleteTime\":0,\"deleted\":false,\"description\":\"(DF Deployed)S3_Docker0828\",\"displayName\":\"S3_Docker0828_1\",\"fdn\":\"1.59923\",\"hAStatus\":\"Single\",\"iPAddress\":\"\",\"leaf\":false,\"level\":0,\"maintainer\":\"000000000000000000001\",\"managed\":false,\"medNodeID\":\"medNode_Master\",\"nE\":true,\"name\":\"S3_Docker0828_1\",\"operationalStatus\":\"Enabled\",\"owner\":\"auto.access\",\"parent\":{\"$ref\":\"$.children[0].parent\"},\"productSerialNumber\":\"UnknownNumber\",\"productStatus\":\"Unkown\",\"sequenceNo\":59923,\"softDelete\":false,\"type\":\"cbs.billing.cbs\",\"typeID\":0,\"usageStatus\":\"Active\",\"validFor\":0,\"vendor\":\"Huawei\",\"version\":\"24.09.000ForSingleChart\"}", AccountMO.class);
        oss.setDN(new DN("cbs-1"));
        mos.add(oss);
        addingSiteModelHandler.addNewSiteInstance(configurationImportEntity, mos);
    }
}
