/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.openapi.alarm.BusinessAlarmInitService;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.timedtri.AnalysisIndQueryTimer;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 告警初始化测试类
 *
 * <AUTHOR>
 * @since 2024/9/6
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class, LocalDateTime.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessAlarmInitServiceTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    BusinessAlarmInitService businessAlarmInitService;

    @Autowired
    BusinessTopoCommonServiceImpl businessTopoCommonService;

    @Autowired
    BusinessInstanceModelDao modelDao;

    @Autowired
    AnalysisIndQueryTimer analysisIndQueryTimer;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (! DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_alarm.sql", false);
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    @Test
    public void testInitAlarmServiceTest() {
        Exception ex = null;
        try {
            businessAlarmInitService.initBusinessTopoAlarm();
        } catch (ServiceException e) {
            ex = e;
        }
        Assert.assertTrue(Objects.isNull(ex));
    }

    @Test
    public void testInitThresholdAlarmServiceTest() {
        Exception ex = null;
        try {
            businessAlarmInitService.initBusinessThresholdAlarm();
        } catch (ServiceException e) {
            ex = e;
        }
        Assert.assertTrue(Objects.isNull(ex));
    }

    @Test
    public void testInitEditThresholdAlarmTest() {
        Exception ex = null;
        try {
            businessAlarmInitService.initEditAlarmImmediately(Collections.singleton(70001));
            businessAlarmInitService.syncInitEditBusinessThresholdAlarm(Collections.singleton(70001));
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertTrue(Objects.isNull(ex));
    }

    @Test
    public void timeLineHistoryUpdateTest() {
        Exception ex = null;
        try {
            businessAlarmInitService.updateHistoryAlarm();
        } catch (ServiceException e) {
            ex = e;
        }
        Assert.assertTrue(Objects.isNull(ex));
    }

    @Test
    public void updateAnalysisAlarmLabelTest() {
        mockPresetData();
        LocalDateTime fixedDateTime = LocalDateTime.of(2024, 4, 17, 0, 0, 0);
        PowerMockito.mockStatic(LocalDateTime.class);
        PowerMockito.when(LocalDateTime.now()).thenReturn(fixedDateTime);

        Exception ex = null;
        try {
            businessAlarmInitService.updateAnalysisAlarmLabel();
        } catch (ServiceException e) {
            ex = e;
        }
        Assert.assertTrue(Objects.isNull(ex));
    }

    private void mockPresetData() {
        MapOper<ConfigData> businessConfigMapOper = new MapOper<>(
            RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("timelinerange");
        configData.setValue("24");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "timelinerange", configData);

        mockServer.loadScenarioFiles("module.analysis/rest_mappings_getanalysisresult.json");
        analysisIndQueryTimer.queryAndCacheIndList();
    }

    @Test
    public void refreshCurrentAlarmTest() {
        Exception e = null;
        try {
            businessTopoCommonService.refreshCurrentAlarm();
        } catch (Exception ex) {
            e = ex;
        }
        Assert.assertNull(e);
    }
}
