/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.topo.topofunction;

import com.huawei.bsp.mybatis.session.MapperManager;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.database.BusinessCommonModel;
import com.huawei.i2000.dvtoposervice.business.service.bean.ConfigurationImportEntity;
import com.huawei.i2000.dvtoposervice.business.topo.configrecover.ConfigurationRecoverManager;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessCommonModelDao;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * ConfigurationRecoverTest
 *
 * <AUTHOR>
 * @since 2024-7-24 11:39:45
 */
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class ConfigurationRecoverTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    MapperManager mapperMgr;

    @Autowired
    private BusinessCommonModelDao commonModelDao;

    @Autowired
    private ConfigurationRecoverManager recoverManager;

    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_configuration_revover.sql", false);
    }

    @Test
    public void testGetDataFromDatabase() {
        BusinessCommonModel condition = new BusinessCommonModel();
        condition.setSolutionName("cbs");
        condition.setTemplateType(0);
        List<BusinessCommonModel> list = commonModelDao.queryCommonModelByCondition(condition);
        ConfigurationImportEntity configurationImportEntity = recoverManager.process(list);
        Assert.assertEquals(35, configurationImportEntity.getBusinessAppInstanceConfig().getBusinessAppInstanceList().size());
    }
}
