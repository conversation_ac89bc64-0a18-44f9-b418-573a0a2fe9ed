/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.entity;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025-03-29
 */
public class MoTypeEntityTest {
    private MoTypeEntity moTypeEntity;

    @BeforeEach
    public void setUp() throws Exception {
        moTypeEntity = new MoTypeEntity();
    }

    @AfterEach
    public void tearDown() throws Exception {
    }

    @Test
    public void test_hash_code() {

        // run the test
        int result = moTypeEntity.hashCode();

        // verify the results
        assertNotNull(result);
    }
}