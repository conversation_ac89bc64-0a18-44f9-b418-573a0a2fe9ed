/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.impl.BusinessTopoServiceDelegateImpl;
import com.huawei.i2000.dvtoposervice.model.IndicatorDisplayResults;
import com.huawei.i2000.dvtoposervice.model.IndicatorDistribution;
import com.huawei.i2000.dvtoposervice.model.OverViewGrid;
import com.huawei.i2000.dvtoposervice.model.OverViewIndicatorParam;
import com.huawei.i2000.dvtoposervice.model.OverviewGridParam;
import com.huawei.i2000.dvtoposervice.model.QueryUpperLayerParam;
import com.huawei.i2000.dvtoposervice.model.QueryUpperLayerParamBatch;
import com.huawei.i2000.dvtoposervice.model.SiteDistribution;
import com.huawei.i2000.dvtoposervice.model.UpperLayerInfo;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 业务拓扑通用测试类
 *
 * <AUTHOR>
 * @since 2024/04/22
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class, MITManagerClient.class, LocalDateTime.class, BusinessTopoCommonServiceImpl.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessTopoServiceDelegateImplTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Autowired
    BusinessTopoServiceDelegateImpl impl;

    @Autowired
    BusinessTopoOverViewServiceImpl overViewService;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (! DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_common.sql", false);
        mockProperties();
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    private void mockProperties() {
        PowerMockito.mockStatic(PropertiesUtil.class);
        Properties properties = new Properties();
        properties.put(BusinessTopoConstant.CLUSTER_TYPE, "6");
        properties.put(BusinessTopoConstant.POD_TYPE, "7");
        PowerMockito.when(PropertiesUtil.loadProperties(Mockito.anyString())).thenReturn(properties);
    }

    @Test
    public void queryOverViewIndicatorDataNotAdminTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        Set<String> set = new HashSet<>();
        set.add("28f59c78-64b5-4b58-949c-891cc8ec43bf");
        contextUtils.setAdmin(false);
        contextUtils.setAuthDns(set);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        OverViewIndicatorParam param = new OverViewIndicatorParam();
        param.setCurrentTime(1L);
        IndicatorDisplayResults result = impl.queryOverViewIndicatorData(null, param);
        Assert.assertEquals(0, result.getSiteHistoryList().size());
    }

    @Test
    public void queryIndicatorDistributionNotAdminTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        Set<String> set = new HashSet<>();
        set.add("28f59c78-64b5-4b58-949c-891cc8ec43bf");
        contextUtils.setAdmin(false);
        contextUtils.setAuthDns(set);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        IndicatorDistribution param = new IndicatorDistribution();
        param.setCurrentTime(1L);
        SiteDistribution result = impl.queryIndicatorDistribution(null, param);
        Assert.assertEquals(0, result.getSiteDataList().size());
    }

    @Test
    public void queryOverviewGridTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        Set<String> set = new HashSet<>();
        set.add("28f59c78-64b5-4b58-949c-891cc8ec43bf");
        contextUtils.setAdmin(false);
        contextUtils.setAuthDns(set);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        OverviewGridParam param = new OverviewGridParam();
        param.setTimestamp(1L);
        OverViewGrid result = impl.queryOverviewGrid(null, param);
        Assert.assertEquals(0, result.getSiteTeamList().size());
    }

    @Test
    public void queryUpperLayerIdByDnTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        Set<String> set = new HashSet<>();
        set.add("28f59c78-64b5-4b58-949c-891cc8ec43bf");
        contextUtils.setAdmin(false);
        contextUtils.setAuthDns(set);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        QueryUpperLayerParam param = new QueryUpperLayerParam();
        param.setDn("siteDn");
        param.setTimestamp(0L);
        UpperLayerInfo upperLayerInfoSite = overViewService.queryUpperLayerIdByDn(param);
        Assert.assertEquals(1, (int) upperLayerInfoSite.getSiteId());

        param.setDn("app");
        UpperLayerInfo upperLayerInfoApp = overViewService.queryUpperLayerIdByDn(param);
        Assert.assertEquals(10051, (int) upperLayerInfoApp.getInstanceId());

        param.setDn("pod");
        UpperLayerInfo upperLayerInfoPod = overViewService.queryUpperLayerIdByDn(param);
        Assert.assertEquals(10051, (int) upperLayerInfoPod.getInstanceId());

        param.setDn("docker");
        UpperLayerInfo upperLayerInfoDocker = overViewService.queryUpperLayerIdByDn(param);
        Assert.assertEquals(10051, (int) upperLayerInfoDocker.getInstanceId());

        param.setDn("vm");
        UpperLayerInfo upperLayerInfoVm = overViewService.queryUpperLayerIdByDn(param);
        Assert.assertEquals(10051, (int) upperLayerInfoVm.getInstanceId());

        param.setDn("container");
        UpperLayerInfo upperLayerInfoContainer = overViewService.queryUpperLayerIdByDn(param);
        Assert.assertEquals(10051, (int) upperLayerInfoContainer.getInstanceId());

        param.setDn("null");
        UpperLayerInfo upperLayerInfoNull = overViewService.queryUpperLayerIdByDn(param);
        Assert.assertNull(upperLayerInfoNull.getInstanceId());
    }

    @Test
    public void queryDvUpperLayerIdByDnTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        QueryUpperLayerParam param = new QueryUpperLayerParam();
        param.setDn("OS=1");
        param.setTimestamp(0L);
        UpperLayerInfo upperLayerInfoSite = overViewService.queryUpperLayerIdByDn(param);
        Assert.assertEquals(1, (int) upperLayerInfoSite.getSiteId());
    }

    @Test
    public void queryDvUpperLayerIdByDnBatchTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        QueryUpperLayerParam param = new QueryUpperLayerParam();
        param.setDn("OS=1");
        param.setTimestamp(0L);
        QueryUpperLayerParamBatch paramBatch = new QueryUpperLayerParamBatch();
        paramBatch.setParamList(Collections.singletonList(param));
        List<UpperLayerInfo> infos = impl.queryUpperLayerIdByDn(null, paramBatch);
        Assert.assertEquals(1, (int) infos.get(0).getSiteId());
    }

}
