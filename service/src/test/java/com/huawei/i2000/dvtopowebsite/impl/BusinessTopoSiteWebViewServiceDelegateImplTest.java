/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtopowebsite.impl;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.dvtopowebsite.model.ResponseEntity;
import com.huawei.i2000.dvtopowebsite.model.SiteAlarmLinkInfoParam;
import com.huawei.i2000.dvtopowebsite.model.SiteDetailViewParam;
import com.huawei.i2000.dvtopowebsite.util.RestUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * BusinessTopoSiteWebViewServiceDelegateImplTest
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@ContextConfiguration(locations = "classpath:spring/service.xml")
@PrepareForTest({ContextUtils.class, PropertiesUtil.class, MITManagerClient.class, RestUtil.class})
public class BusinessTopoSiteWebViewServiceDelegateImplTest extends WebServiceTest {
    private static final AtomicBoolean DATALOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    @Autowired
    BusinessTopoSiteWebViewServiceDelegateImpl businessTopoSiteWebViewServiceDelegateImpl;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATALOAD.compareAndSet(false, true)) {
            return;
        }
        setSqlScriptEncoding("UTF-8");
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    /**
     * 测试所有ER接口
     *
     * @throws ServiceException ServiceException
     */
    @Test
    public void allRestTest() throws ServiceException {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getpmthresholdalarm.json");
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        RestfulResponse response = new RestfulResponse();
        response.setStatus(200);
        response.setResponseJson("{}");
        PowerMockito.mockStatic(RestUtil.class);
        PowerMockito.when(RestUtil.sendRestRequest(Mockito.anyString(), Mockito.anyString(), Mockito.any(),
            Mockito.any())).thenReturn(response);

        SiteDetailViewParam siteParam = new SiteDetailViewParam();
        ResponseEntity responseSiteView = businessTopoSiteWebViewServiceDelegateImpl.querySiteDetailViewInfo(new MockHttpContext(), siteParam);
        Assert.assertEquals(0, (int) responseSiteView.getResultCode());

        SiteAlarmLinkInfoParam querySiteAlarmInfoParam = new SiteAlarmLinkInfoParam();
        ResponseEntity responseSiteAlarm = businessTopoSiteWebViewServiceDelegateImpl.getSiteAlarmLinkInfo(new MockHttpContext(), querySiteAlarmInfoParam);
        Assert.assertEquals(0, (int) responseSiteAlarm.getResultCode());
    }
}
