/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtopowebsite.impl;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.pm.entity.DeletedHistoryValues;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.TopoImportSolutionAdapter;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.model.IndicatorDisplayResults;
import com.huawei.i2000.dvtoposervice.model.OverViewGridForMm;
import com.huawei.i2000.dvtoposervice.util.AuthUtils;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.EamUtil;
import com.huawei.i2000.dvtoposervice.util.LogUtil;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.dvtopowebsite.model.BusinessTopNConfig;
import com.huawei.i2000.dvtopowebsite.model.IndicatorDataForMm;
import com.huawei.i2000.dvtopowebsite.model.IndicatorDistribution;
import com.huawei.i2000.dvtopowebsite.model.IndicatorDistributionForMm;
import com.huawei.i2000.dvtopowebsite.model.OverViewIndicatorParam;
import com.huawei.i2000.dvtopowebsite.model.OverViewIndicatorParamForMm;
import com.huawei.i2000.dvtopowebsite.model.OverviewGridParam;
import com.huawei.i2000.dvtopowebsite.model.ResponseEntity;
import com.huawei.i2000.dvtopowebsite.model.TopIndicatorData;
import com.huawei.i2000.dvtopowebsite.model.TopIndicatorDistribution;
import com.huawei.i2000.dvtopowebsite.util.RestUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.i2000.gapi.account.mo.AccountMO;
import com.huawei.oms.eam.mo.ManagedObject;

import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.context.ContextConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * BusinessTopoWebsiteDelegateImplTest
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@ContextConfiguration(locations = "classpath:spring/service.xml")
@PrepareForTest({EamUtil.class, ContextUtils.class, PropertiesUtil.class, MITManagerClient.class, RestUtil.class, AuthUtils.class, LogUtil.class, TopoImportSolutionAdapter.class})
public class BusinessTopoWebsiteDelegateImplTest extends WebServiceTest {
    private static final AtomicBoolean DATALOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    private static final MapOper<ConfigData> BUSINESS_CONFIG_MAP_OPER = new MapOper<>(
        RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);

    @Autowired
    BusinessTopoWebsiteDelegateImpl businessTopoWebsiteDelegateImpl;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATALOAD.compareAndSet(false, true)) {
            return;
        }
        setSqlScriptEncoding("UTF-8");
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_overview_mm.sql", false);
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    /**
     * 测试所有ER接口
     *
     * @throws ServiceException ServiceException
     */
    @Test
    public void allRestTest() throws ServiceException {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getpmthresholdalarm.json");
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        RestfulResponse response = new RestfulResponse();
        response.setStatus(200);
        response.setResponseJson("[]");
        PowerMockito.mockStatic(RestUtil.class);
        PowerMockito.when(RestUtil.sendRestRequest(Mockito.anyString(), Mockito.anyString(), Mockito.any(),
            Mockito.any())).thenReturn(response);

        ResponseEntity responseSolution = businessTopoWebsiteDelegateImpl.querySolution(new MockHttpContext(), 0L);
        Assert.assertEquals(0, (int) responseSolution.getResultCode());

        ResponseEntity responseBusiness = businessTopoWebsiteDelegateImpl.queryBusiness(new MockHttpContext(), 1, 0L);
        Assert.assertEquals(0, (int) responseBusiness.getResultCode());

        response.setResponseJson("{}");
        OverViewIndicatorParam queryOverViewIndicatorData = new OverViewIndicatorParam();
        ResponseEntity responseOverView = businessTopoWebsiteDelegateImpl.queryOverViewIndicatorData(new MockHttpContext(), queryOverViewIndicatorData);
        Assert.assertEquals(0, (int) responseOverView.getResultCode());

        OverviewGridParam queryOverviewGrid = new OverviewGridParam();
        ResponseEntity responseGrid = businessTopoWebsiteDelegateImpl.queryOverviewGrid(new MockHttpContext(), queryOverviewGrid);
        Assert.assertEquals(0, (int) responseGrid.getResultCode());

        IndicatorDistribution queryIndicatorDistribution = new IndicatorDistribution();
        ResponseEntity responseDistribution = businessTopoWebsiteDelegateImpl.queryIndicatorDistribution(new MockHttpContext(), queryIndicatorDistribution);
        Assert.assertEquals(0, (int) responseDistribution.getResultCode());
    }

    @Test
    public void queryMmTipsBusinessTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        OverViewIndicatorParamForMm param = new OverViewIndicatorParamForMm();
        param.setBusinessId(98966);
        param.setIndicatorId("MMSwimLane-2346195-gsu-bfintech.Fintech.FCScu_250213192913_EA45cu_250213192913_EA45_t01cu_250213192913_EA45_i00=Env,cu_250213192913_EA45_i01=CBS");

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorHistoryForMm(null, param);

        IndicatorDisplayResults result = (IndicatorDisplayResults) entity.getData();
        Assert.assertEquals(1, result.getSiteHistoryList().size());
    }

    @Test
    public void queryMmTipsChannelTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        OverViewIndicatorParamForMm param = new OverViewIndicatorParamForMm();
        param.setChannelId(98961);
        param.setIndicatorId("MMSwimLane-2346195-gsu-bfintech.Fintech.FCScu_250102051008_BCA7cu_250102051008_BCA7_t01cu_250102051008_BCA7_i00=3000TPS-HCS,cu_250102051008_BCA7_i01=STK,cu_250102051008_BCA7_i02=TPS");

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorHistoryForMm(null, param);

        IndicatorDisplayResults result = (IndicatorDisplayResults) entity.getData();
        Assert.assertEquals(1, result.getSiteHistoryList().size());
    }

    @Test
    public void queryMmTipsStripeTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");

        ConfigData configGroupName = new ConfigData();
        configGroupName.setConfigItemName("mmHwsGroupName");
        configGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configGroupName);
        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);

        ConfigData mmHwsGroupName = new ConfigData();
        mmHwsGroupName.setConfigItemName("mmHwsGroupName");
        mmHwsGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configSiteName);

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        OverViewIndicatorParamForMm param = new OverViewIndicatorParamForMm();
        param.setStripeId(98972);
        List<String> values = new ArrayList<>();
        values.add("cu_250215150312_033B_i00=Send Money");
        values.add("cu_250215150312_033B_i00=TransactionService");
        param.setOriginalValues(values);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorHistoryForMm(null, param);

        IndicatorDisplayResults result = (IndicatorDisplayResults) entity.getData();
        Assert.assertEquals(1, result.getSiteHistoryList().size());
    }

    @Test
    public void queryMmHistoryBusinessTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        OverViewIndicatorParamForMm param = new OverViewIndicatorParamForMm();
        List<Integer> ins = new ArrayList<>();
        ins.add(98966);
        ins.add(98967);
        param.setMoType("fintech.Fintech.FCS");
        param.setMeasUnitKey("cu_250213192913_EA45");
        param.setMeasTypeKey("cu_250213192913_EA45_t00");
        param.setOriginalValue("cu_250213192913_EA45_i00=Env,cu_250213192913_EA45_i01=CBS");
        param.setInstanceIds(ins);
        param.setModelType(1);
        param.setIndicatorList(
            "[{\"indicatorId\":\"MMSwimLane-2462783-gsu-bfintech.Fintech.FCScu_250217162957_0786cu_250217162957_0786_t00cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=API\",\"instanceId\":1230199,\"measUnitKey\":\"cu_250217162957_0786\",\"measUnitName\":\"License TPS By Channel(Single Env)\",\"measTypeKey\":\"cu_250217162957_0786_t00\",\"threshold\":null,\"IP\":null,\"moName\":null,\"dn\":\"MMSwimLane-2462783-gsu-b\",\"dnName\":\"wallet_Q1S2_Q1S2_gsu_b\",\"indexKey\":null,\"indexName\":\"Total TPS\",\"moType\":\"fintech.Fintech.FCS\",\"displayValue\":\"Env, API\",\"originalValue\":\"cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=API\",\"unit\":\"tps\",\"indexId\":\"cu_250217162957_0786~~cu_250217162957_0786_t00\",\"hasMeasObj\":null,\"indicatorDisplayType\":1,\"aggrType\":null,\"indicatorStatus\":null,\"indicatorCsnList\":null,\"indicatorSortNumber\":0},{\"indicatorId\":\"MMSwimLane-2462783-gsu-bfintech.Fintech.FCScu_250217162957_0786cu_250217162957_0786_t00cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=WEB\",\"instanceId\":1230197,\"measUnitKey\":\"cu_250217162957_0786\",\"measUnitName\":\"License TPS By Channel(Single Env)\",\"measTypeKey\":\"cu_250217162957_0786_t00\",\"threshold\":null,\"IP\":null,\"moName\":null,\"dn\":\"MMSwimLane-2462783-gsu-b\",\"dnName\":\"wallet_Q1S2_Q1S2_gsu_b\",\"indexKey\":null,\"indexName\":\"Total TPS\",\"moType\":\"fintech.Fintech.FCS\",\"displayValue\":\"Env, WEB\",\"originalValue\":\"cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=WEB\",\"unit\":\"tps\",\"indexId\":\"cu_250217162957_0786~~cu_250217162957_0786_t00\",\"hasMeasObj\":null,\"indicatorDisplayType\":1,\"aggrType\":null,\"indicatorStatus\":null,\"indicatorCsnList\":null,\"indicatorSortNumber\":null},{\"indicatorId\":\"MMSwimLane-2462783-gsu-bfintech.Fintech.FCScu_250217162957_0786cu_250217162957_0786_t00cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=STK\",\"instanceId\":1230200,\"measUnitKey\":\"cu_250217162957_0786\",\"measUnitName\":\"License TPS By Channel(Single Env)\",\"measTypeKey\":\"cu_250217162957_0786_t00\",\"threshold\":null,\"IP\":null,\"moName\":null,\"dn\":\"MMSwimLane-2462783-gsu-b\",\"dnName\":\"wallet_Q1S2_Q1S2_gsu_b\",\"indexKey\":null,\"indexName\":\"Total TPS\",\"moType\":\"fintech.Fintech.FCS\",\"displayValue\":\"Env, STK\",\"originalValue\":\"cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=STK\",\"unit\":\"tps\",\"indexId\":\"cu_250217162957_0786~~cu_250217162957_0786_t00\",\"hasMeasObj\":null,\"indicatorDisplayType\":1,\"aggrType\":null,\"indicatorStatus\":null,\"indicatorCsnList\":null,\"indicatorSortNumber\":0}]");

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorHistoryForMm(null, param);

        IndicatorDisplayResults result = (IndicatorDisplayResults) entity.getData();
        Assert.assertEquals(0, result.getSiteHistoryList().size());
    }

    @Test
    public void queryMmHistoryChannelTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        OverViewIndicatorParamForMm param = new OverViewIndicatorParamForMm();
        List<Integer> ins = new ArrayList<>();
        ins.add(98961);
        ins.add(98960);
        param.setMoType("fintech.Fintech.FCS");
        param.setMeasUnitKey("cu_250102051008_BCA7");
        param.setMeasTypeKey("cu_250102051008_BCA7_t01");
        param.setOriginalValue(
            "cu_250102051008_BCA7_i00=3000TPS-HCS,cu_250102051008_BCA7_i01=API,cu_250102051008_BCA7_i02=TPS");
        param.setInstanceIds(ins);
        param.setModelType(101);
        param.setIndicatorList(
            "[{\"indicatorId\":\"MMSwimLane-2462783-gsu-bfintech.Fintech.FCScu_250217162957_0786cu_250217162957_0786_t00cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=API\",\"instanceId\":1230199,\"measUnitKey\":\"cu_250217162957_0786\",\"measUnitName\":\"License TPS By Channel(Single Env)\",\"measTypeKey\":\"cu_250217162957_0786_t00\",\"threshold\":null,\"IP\":null,\"moName\":null,\"dn\":\"MMSwimLane-2462783-gsu-b\",\"dnName\":\"wallet_Q1S2_Q1S2_gsu_b\",\"indexKey\":null,\"indexName\":\"Total TPS\",\"moType\":\"fintech.Fintech.FCS\",\"displayValue\":\"Env, API\",\"originalValue\":\"cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=API\",\"unit\":\"tps\",\"indexId\":\"cu_250217162957_0786~~cu_250217162957_0786_t00\",\"hasMeasObj\":null,\"indicatorDisplayType\":1,\"aggrType\":null,\"indicatorStatus\":null,\"indicatorCsnList\":null,\"indicatorSortNumber\":0},{\"indicatorId\":\"MMSwimLane-2462783-gsu-bfintech.Fintech.FCScu_250217162957_0786cu_250217162957_0786_t00cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=WEB\",\"instanceId\":1230197,\"measUnitKey\":\"cu_250217162957_0786\",\"measUnitName\":\"License TPS By Channel(Single Env)\",\"measTypeKey\":\"cu_250217162957_0786_t00\",\"threshold\":null,\"IP\":null,\"moName\":null,\"dn\":\"MMSwimLane-2462783-gsu-b\",\"dnName\":\"wallet_Q1S2_Q1S2_gsu_b\",\"indexKey\":null,\"indexName\":\"Total TPS\",\"moType\":\"fintech.Fintech.FCS\",\"displayValue\":\"Env, WEB\",\"originalValue\":\"cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=WEB\",\"unit\":\"tps\",\"indexId\":\"cu_250217162957_0786~~cu_250217162957_0786_t00\",\"hasMeasObj\":null,\"indicatorDisplayType\":1,\"aggrType\":null,\"indicatorStatus\":null,\"indicatorCsnList\":null,\"indicatorSortNumber\":null},{\"indicatorId\":\"MMSwimLane-2462783-gsu-bfintech.Fintech.FCScu_250217162957_0786cu_250217162957_0786_t00cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=STK\",\"instanceId\":1230200,\"measUnitKey\":\"cu_250217162957_0786\",\"measUnitName\":\"License TPS By Channel(Single Env)\",\"measTypeKey\":\"cu_250217162957_0786_t00\",\"threshold\":null,\"IP\":null,\"moName\":null,\"dn\":\"MMSwimLane-2462783-gsu-b\",\"dnName\":\"wallet_Q1S2_Q1S2_gsu_b\",\"indexKey\":null,\"indexName\":\"Total TPS\",\"moType\":\"fintech.Fintech.FCS\",\"displayValue\":\"Env, STK\",\"originalValue\":\"cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=STK\",\"unit\":\"tps\",\"indexId\":\"cu_250217162957_0786~~cu_250217162957_0786_t00\",\"hasMeasObj\":null,\"indicatorDisplayType\":1,\"aggrType\":null,\"indicatorStatus\":null,\"indicatorCsnList\":null,\"indicatorSortNumber\":0}]");

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorHistoryForMm(null, param);

        IndicatorDisplayResults result = (IndicatorDisplayResults) entity.getData();
        Assert.assertEquals(0, result.getSiteHistoryList().size());
    }

    @Test
    public void queryMmHistoryChannelCommonIndicatorTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        ConfigData configChannel = new ConfigData();
        configChannel.setConfigItemName("channelTopNConfig");
        configChannel.setValue("10");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "channelTopNConfig", configChannel);

        OverViewIndicatorParamForMm param = new OverViewIndicatorParamForMm();
        List<Integer> ins = new ArrayList<>();
        ins.add(98961);
        ins.add(98960);
        param.setMoType("fintech.Fintech.FCS");
        param.setMeasUnitKey("cu_250102051008_BCA7");
        param.setMeasTypeKey("cu_250102051008_BCA7_t01");
        param.setOriginalValue(
            "cu_250102051008_BCA7_i00=3000TPS-HCS,cu_250102051008_BCA7_i01=API,cu_250102051008_BCA7_i02=TPS");
        param.setInstanceIds(ins);
        param.setModelType(101);
        param.setIndicatorList(
            "[{\"indicatorId\":\"MMSwimLane-2462783-gsu-bfintech.Fintech.FCScu_250217162957_0786cu_250217162957_0786_t00cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=API\",\"instanceId\":1230199,\"measUnitKey\":\"cu_250217162957_0786\",\"measUnitName\":\"License TPS By Channel(Single Env)\",\"measTypeKey\":\"cu_250217162957_0786_t00\",\"threshold\":null,\"IP\":null,\"moName\":null,\"dn\":\"MMSwimLane-2462783-gsu-b\",\"dnName\":\"wallet_Q1S2_Q1S2_gsu_b\",\"indexKey\":null,\"indexName\":\"Total TPS\",\"moType\":\"fintech.Fintech.FCS\",\"displayValue\":\"Env, API\",\"originalValue\":\"cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=API\",\"unit\":\"tps\",\"indexId\":\"cu_250217162957_0786~~cu_250217162957_0786_t00\",\"hasMeasObj\":null,\"indicatorDisplayType\":1,\"aggrType\":null,\"indicatorStatus\":null,\"indicatorCsnList\":null,\"indicatorSortNumber\":0},{\"indicatorId\":\"MMSwimLane-2462783-gsu-bfintech.Fintech.FCScu_250217162957_0786cu_250217162957_0786_t00cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=WEB\",\"instanceId\":1230197,\"measUnitKey\":\"cu_250217162957_0786\",\"measUnitName\":\"License TPS By Channel(Single Env)\",\"measTypeKey\":\"cu_250217162957_0786_t00\",\"threshold\":null,\"IP\":null,\"moName\":null,\"dn\":\"MMSwimLane-2462783-gsu-b\",\"dnName\":\"wallet_Q1S2_Q1S2_gsu_b\",\"indexKey\":null,\"indexName\":\"Total TPS\",\"moType\":\"fintech.Fintech.FCS\",\"displayValue\":\"Env, WEB\",\"originalValue\":\"cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=WEB\",\"unit\":\"tps\",\"indexId\":\"cu_250217162957_0786~~cu_250217162957_0786_t00\",\"hasMeasObj\":null,\"indicatorDisplayType\":1,\"aggrType\":null,\"indicatorStatus\":null,\"indicatorCsnList\":null,\"indicatorSortNumber\":null},{\"indicatorId\":\"MMSwimLane-2462783-gsu-bfintech.Fintech.FCScu_250217162957_0786cu_250217162957_0786_t00cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=STK\",\"instanceId\":1230200,\"measUnitKey\":\"cu_250217162957_0786\",\"measUnitName\":\"License TPS By Channel(Single Env)\",\"measTypeKey\":\"cu_250217162957_0786_t00\",\"threshold\":null,\"IP\":null,\"moName\":null,\"dn\":\"MMSwimLane-2462783-gsu-b\",\"dnName\":\"wallet_Q1S2_Q1S2_gsu_b\",\"indexKey\":null,\"indexName\":\"Total TPS\",\"moType\":\"fintech.Fintech.FCS\",\"displayValue\":\"Env, STK\",\"originalValue\":\"cu_250217162957_0786_i00=Env,cu_250217162957_0786_i01=STK\",\"unit\":\"tps\",\"indexId\":\"cu_250217162957_0786~~cu_250217162957_0786_t00\",\"hasMeasObj\":null,\"indicatorDisplayType\":1,\"aggrType\":null,\"indicatorStatus\":null,\"indicatorCsnList\":null,\"indicatorSortNumber\":0}]");
        param.setIndicatorDisplayType(0);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorHistoryForMm(null, param);

        IndicatorDisplayResults result = (IndicatorDisplayResults) entity.getData();
        Assert.assertEquals(0, result.getSiteHistoryList().size());
    }

    @Test
    public void queryMmHistoryStripeTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");

        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        OverViewIndicatorParamForMm param = new OverViewIndicatorParamForMm();
        List<Integer> ins = new ArrayList<>();
        ins.add(98971);
        ins.add(98972);
        param.setMoType("fintech.Fintech.FCS");
        param.setMeasUnitKey("cu_250217150538_A0CB");
        param.setMeasTypeKey("cu_250217150538_A0CB_t00");
        param.setInstanceIds(ins);
        param.setModelType(2);
        List<String> values = new ArrayList<>();
        values.add("cu_250215150312_033B_i00=Send Money");
        values.add("cu_250215150312_033B_i00=TransactionService");
        param.setOriginalValues(values);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorHistoryForMm(null, param);

        IndicatorDisplayResults result = (IndicatorDisplayResults) entity.getData();
        Assert.assertEquals(1, result.getSiteHistoryList().size());
    }

    @Test
    public void queryMmHistoryStripeSingleTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");

        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        OverViewIndicatorParamForMm param = new OverViewIndicatorParamForMm();
        List<Integer> ins = new ArrayList<>();
        ins.add(98971);
        param.setMoType("fintech.Fintech.FCS");
        param.setMeasUnitKey("cu_250215150312_033B");
        param.setMeasTypeKey("cu_250215150312_033B_t00");
        param.setInstanceIds(ins);
        param.setModelType(2);
        List<String> values = new ArrayList<>();
        values.add("cu_250215150312_033B_i00=Send Money");
        values.add("cu_250215150312_033B_i00=TransactionService");
        param.setOriginalValues(values);
        param.setIndicatorDisplayType(0);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorHistoryForMm(null, param);

        IndicatorDisplayResults result = (IndicatorDisplayResults) entity.getData();
        Assert.assertEquals(1, result.getSiteHistoryList().size());
    }

    @Test
    public void queryMmHistoryStripeNotMainIndicatorTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        OverViewIndicatorParamForMm param = new OverViewIndicatorParamForMm();
        List<Integer> ins = new ArrayList<>();
        ins.add(98971);
        ins.add(98972);
        param.setMoType("fintech.Fintech.FCS");
        param.setMeasUnitKey("cu_250215150312_033B");
        param.setMeasTypeKey("cu_250215150312_033B_t00");
        param.setInstanceIds(ins);
        param.setModelType(2);
        List<String> values = new ArrayList<>();
        values.add("cu_250215150312_033B_i00=Send Money");
        values.add("cu_250215150312_033B_i00=TransactionService");
        param.setOriginalValues(values);
        param.setIndicatorDisplayType(0);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorHistoryForMm(null, param);

        IndicatorDisplayResults result = (IndicatorDisplayResults) entity.getData();
        Assert.assertEquals(0, result.getSiteHistoryList().size());
    }

    @Test
    public void queryMmHistoryStripeUnitTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        OverViewIndicatorParamForMm param = new OverViewIndicatorParamForMm();
        List<Integer> ins = new ArrayList<>();
        ins.add(98971);
        param.setMoType("fintech.Fintech.FCS");
        param.setMeasUnitKey("cu_250215150312_033B");
        param.setMeasTypeKey("cu_250215150312_033B_t00");
        param.setInstanceIds(ins);
        param.setModelType(2);
        List<String> values = new ArrayList<>();
        values.add("cu_250215150312_033B_i00=Send Money");
        values.add("cu_250215150312_033B_i00=TransactionService");
        param.setOriginalValues(values);
        param.setAppSite("pr1");

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorHistoryForMm(null, param);

        IndicatorDisplayResults result = (IndicatorDisplayResults) entity.getData();
        Assert.assertEquals(0, result.getSiteHistoryList().size());
    }

    @Test
    public void queryMmHistoryStripeUnitAggregateEmptyTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");

        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);
        ConfigData configGroupName = new ConfigData();
        configGroupName.setConfigItemName("mmHwsGroupName");
        configGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configGroupName);

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        OverViewIndicatorParamForMm param = new OverViewIndicatorParamForMm();
        List<Integer> ins = new ArrayList<>();
        ins.add(98971);
        param.setMoType("fintech.Fintech.FCS");
        param.setMeasUnitKey("cu_250215150312_033C");
        param.setMeasTypeKey("cu_250215150312_033C_t01");
        param.setInstanceIds(ins);
        param.setModelType(2);
        List<String> values = new ArrayList<>();
        values.add("cu_250215150312_033C_i00=Send Money");
        values.add("cu_250215150312_033C_i00=TransactionService");
        param.setOriginalValues(values);
        param.setAppSite("pr1");

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorHistoryForMm(null, param);

        IndicatorDisplayResults result = (IndicatorDisplayResults) entity.getData();
        Assert.assertEquals(0, result.getSiteHistoryList().size());
    }

    @Test
    public void queryMmHistoryStripeUnitAggregateTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");

        PowerMockito.mockStatic(TopoImportSolutionAdapter.class);
        Map<String, String> map = new HashMap<>();
        map.put("cu_250215150312_033C", "OS=1");
        PowerMockito.when(TopoImportSolutionAdapter.class, "getMmMeasUnitDnMap", Mockito.any()).thenReturn(map);
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        OverViewIndicatorParamForMm param = new OverViewIndicatorParamForMm();
        List<Integer> ins = new ArrayList<>();
        ins.add(98971);
        param.setMoType("fintech.Fintech.FCS");
        param.setMeasUnitKey("cu_250215150312_033C");
        param.setMeasTypeKey("cu_250215150312_033C_t01");
        param.setInstanceIds(ins);
        param.setModelType(2);
        List<String> values = new ArrayList<>();
        values.add("cu_250215150312_033C_i00=Send Money");
        values.add("cu_250215150312_033C_i00=TransactionService");
        param.setOriginalValues(values);
        param.setAppSite("pr1");

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorHistoryForMm(null, param);

        IndicatorDisplayResults result = (IndicatorDisplayResults) entity.getData();
        Assert.assertEquals(0, result.getSiteHistoryList().size());
    }

    @Test
    public void queryMmTopIndicatorBusinessTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        TopIndicatorDistribution param = new TopIndicatorDistribution();
        param.setModelType(1);
        param.setSolutionId(98000);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryTopIndicator(null, param);

        TopIndicatorData result = (TopIndicatorData) entity.getData();
        Assert.assertEquals(1, (int) result.getModelType());
    }

    @Test
    public void queryMmTopIndicatorChannelTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        TopIndicatorDistribution param = new TopIndicatorDistribution();
        param.setModelType(101);
        param.setSolutionId(98000);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryTopIndicator(null, param);

        TopIndicatorData result = (TopIndicatorData) entity.getData();
        Assert.assertEquals(101, (int) result.getModelType());
    }

    @Test
    public void queryMmTopIndicatorStripeTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        TopIndicatorDistribution param = new TopIndicatorDistribution();
        param.setModelType(2);
        param.setSolutionId(98000);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryTopIndicator(null, param);

        TopIndicatorData result = (TopIndicatorData) entity.getData();
        Assert.assertEquals(2, (int) result.getModelType());
    }

    @Test
    public void queryMmTopIndicatorStripeSelectTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        TopIndicatorDistribution param = new TopIndicatorDistribution();
        param.setModelType(2);
        param.setSolutionId(98000);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryTopIndicator(null, param);

        TopIndicatorData result = (TopIndicatorData) entity.getData();
        Assert.assertEquals(2, (int) result.getModelType());
    }

    @Test
    public void queryMmTopIndicatorStripeUnitTest() throws Exception {
        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("stripeTopNConfig");
        configSiteName.setValue("5");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "stripeTopNConfig", configSiteName);

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        PowerMockito.mockStatic(EamUtil.class);
        List<ManagedObject> mos = new ArrayList<>();
        ManagedObject managedObject = new AccountMO();
        managedObject.setLogicSite("1");
        managedObject.setDN("moDn");
        managedObject.setHwsSiteName("Q1S2");
        managedObject.setHwsGroupName("Q1S2");
        managedObject.setHwsSwimLane("b");
        managedObject.setHwsStripe("rsu02");
        managedObject.setHwsAppSite("pr1");
        mos.add(managedObject);
        PowerMockito.when(EamUtil.queryMosByMoType("fintech.Fintech.FCS")).thenReturn(mos);

        TopIndicatorDistribution param = new TopIndicatorDistribution();
        param.setModelType(2);
        param.setSolutionId(98000);
        param.setStripeUnit("pr1");
        param.setStripeId(577761);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryTopIndicator(null, param);

        TopIndicatorData result = (TopIndicatorData) entity.getData();
        Assert.assertEquals(2, (int) result.getModelType());
    }

    @Test
    public void queryMmOverViewTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        OverviewGridParam param = new OverviewGridParam();
        param.setTimestamp(0L);
        param.setSolutionId(98000);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryOverviewGridForMm(null, param);

        OverViewGridForMm result = (OverViewGridForMm) entity.getData();
        Assert.assertEquals(1, result.getStripeTeamList().size());
    }

    @Test
    public void queryMmOverViewNotAdminTest() throws Exception {

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(false);

        PowerMockito.mockStatic(AuthUtils.class);
        PowerMockito.when(AuthUtils.getAuthDns()).thenReturn(new HashSet<>(mockGetAuthDns()));

        OverviewGridParam param = new OverviewGridParam();
        param.setTimestamp(0L);
        param.setSolutionId(98000);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryOverviewGridForMm(null, param);

        OverViewGridForMm result = (OverViewGridForMm) entity.getData();
        Assert.assertEquals(1, result.getStripeTeamList().size());
    }

    @Test
    public void queryMmIndicatorDistributionTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);
        ConfigData configGroupName = new ConfigData();
        configGroupName.setConfigItemName("mmHwsGroupName");
        configGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configGroupName);

        IndicatorDistributionForMm param = new IndicatorDistributionForMm();
        param.setCurrentTime(0L);
        param.setSolutionId(98000);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorDistributionForMm(null, param);

        IndicatorDataForMm result = (IndicatorDataForMm) entity.getData();
        Assert.assertEquals(4, result.getLineChannel2StripIns().size());
    }

    @Test
    public void queryMmIndicatorDistributionSelectBusinessTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        IndicatorDistributionForMm param = new IndicatorDistributionForMm();
        param.setCurrentTime(0L);
        param.setSolutionId(98000);
        param.setInstanceId(98967);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorDistributionForMm(null, param);

        IndicatorDataForMm result = (IndicatorDataForMm) entity.getData();
        Assert.assertEquals(1, result.getLineThird2Channel().size());
    }

    @Test
    public void queryMmIndicatorDistributionSelectChannelTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        ConfigData configSiteName = new ConfigData();
        configSiteName.setConfigItemName("mmHwsSiteName");
        configSiteName.setValue("site");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsSiteName", configSiteName);
        ConfigData configGroupName = new ConfigData();
        configGroupName.setConfigItemName("mmHwsGroupName");
        configGroupName.setValue("group");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "mmHwsGroupName", configGroupName);

        IndicatorDistributionForMm param = new IndicatorDistributionForMm();
        param.setCurrentTime(0L);
        param.setSolutionId(98000);
        param.setInstanceId(98961);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorDistributionForMm(null, param);

        IndicatorDataForMm result = (IndicatorDataForMm) entity.getData();
        Assert.assertEquals(0, result.getLineChannel2StripIns().size());
    }

    @Test
    public void queryMmIndicatorDistributionSelectStripeTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);

        IndicatorDistributionForMm param = new IndicatorDistributionForMm();
        param.setCurrentTime(0L);
        param.setSolutionId(98000);
        param.setInstanceId(577761);

        ResponseEntity entity = businessTopoWebsiteDelegateImpl.queryIndicatorDistributionForMm(null, param);

        IndicatorDataForMm result = (IndicatorDataForMm) entity.getData();
        Assert.assertEquals(3, result.getLineThird2Channel().size());
    }

    private Set<String> mockGetAuthDns() {
        Set<String> authDnSet = new HashSet<>();
        try {
            ClassPathResource classPathResource = new ClassPathResource("/topotest/dbview/dbview_dn.txt");
            File file = classPathResource.getFile();
            authDnSet.addAll(Arrays.asList(FileUtils.readFileToString(file, "UTF-8").split(",")));
        } catch (IOException e) {
            return new HashSet<>();
        }
        return authDnSet;
    }

    @Test
    public void queryMmTopNConfigTest() {
        prepareTopNConfig();

        ResponseEntity responseEntity = businessTopoWebsiteDelegateImpl.queryMmTopNConfig(null);
        Assert.assertEquals(0, (int) responseEntity.getResultCode());

        BUSINESS_CONFIG_MAP_OPER.clear(RedisConstant.BUSINESS_TOPO_CONFIG);

        ResponseEntity responseError = businessTopoWebsiteDelegateImpl.queryMmTopNConfig(null);
        Assert.assertEquals(-1, (int) responseError.getResultCode());
    }

    @Test
    public void editMmTopNConfigTest() throws Exception {
        prepareTopNConfig();
        PowerMockito.mockStatic(LogUtil.class);

        BusinessTopNConfig businessTopNConfig = new BusinessTopNConfig();
        businessTopNConfig.setBusinessTopNConfig(10);
        businessTopNConfig.setChannelTopNConfig(15);
        businessTopNConfig.setStripeTopNConfig(20);

        ResponseEntity responseEntity = businessTopoWebsiteDelegateImpl.editMmTopNConfig(new MockHttpContext(), businessTopNConfig);
        Assert.assertEquals(0, (int) responseEntity.getResultCode());

        BUSINESS_CONFIG_MAP_OPER.clear(RedisConstant.BUSINESS_TOPO_CONFIG);

        ResponseEntity responseError = businessTopoWebsiteDelegateImpl.editMmTopNConfig(new MockHttpContext(), businessTopNConfig);
        Assert.assertEquals(-1, (int) responseError.getResultCode());
    }

    private static void prepareTopNConfig() {
        ConfigData configBusiness = new ConfigData();
        configBusiness.setConfigItemName("businessTopNConfig");
        configBusiness.setValue("5");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "businessTopNConfig", configBusiness);
        ConfigData configChannel = new ConfigData();
        configChannel.setConfigItemName("channelTopNConfig");
        configChannel.setValue("10");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "channelTopNConfig", configChannel);
        ConfigData configStripe = new ConfigData();
        configStripe.setConfigItemName("channelTopNConfig");
        configStripe.setValue("20");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, "stripeTopNConfig", configStripe);
    }

    @Test
    public void getValueTest() {
        Map<String, DeletedHistoryValues> dnValueMap = new HashMap<>();
        DeletedHistoryValues values = new DeletedHistoryValues();
        values.setTimestamp(1L);
        values.setDn("dn");
        values.setValues(new HashMap<>());
        dnValueMap.put("indicatorId", values);
        String result = businessTopoWebsiteDelegateImpl.getValue(dnValueMap);

        Assert.assertNull(result);
    }

}
