/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtopowebsite.impl;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpRequest;
import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpResponse;
import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.cloudsop.common.tokenhelper.exception.AuthServiceException;
import com.huawei.cloudsop.common.tokenhelper.token.CommonTokenManager;
import com.huawei.cloudsop.common.tokenhelper.token.OMToken;
import com.huawei.i2000.cbb.sm.UserRoleUtil;
import com.huawei.i2000.cbb.sm.model.UserDetail;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.dvtopowebsite.model.AlarmCsnParam;
import com.huawei.i2000.dvtopowebsite.model.AlarmStatisticParam;
import com.huawei.i2000.dvtopowebsite.model.Indicator;
import com.huawei.i2000.dvtopowebsite.model.ResponseEntity;
import com.huawei.i2000.dvtopowebsite.model.TimelineAlarmQueryParam;
import com.huawei.i2000.dvtopowebsite.model.TimelineQueryParam;
import com.huawei.i2000.dvtopowebsite.util.RestUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.oms.eam.mo.ManagedElement;
import com.huawei.oms.eam.mo.ManagedObject;

import com.alibaba.fastjson2.JSON;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * BusinessCommonWebsiteDelegateImplTest
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@ContextConfiguration(locations = "classpath:spring/service.xml")
@PrepareForTest({ContextUtils.class, PropertiesUtil.class, MITManagerClient.class, RestUtil.class, CommonTokenManager.class, UserRoleUtil.class})
public class BusinessCommonWebsiteDelegateImplTest extends WebServiceTest {
    private static final AtomicBoolean DATALOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    private static final MapOper<ConfigData> BUSINESS_CONFIG_MAP_OPER = new MapOper<>(
        RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);

    @Autowired
    BusinessCommonWebsiteDelegateImpl businessCommonWebsiteDelegateimpl;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATALOAD.compareAndSet(false, true)) {
            return;
        }
        setSqlScriptEncoding("UTF-8");
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getpmthresholdalarm.json");
    }

    /**
     * 测试所有ER接口
     *
     * @throws ServiceException ServiceException
     */
    @Test
    public void allRestTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        RestfulResponse response = new RestfulResponse();
        response.setStatus(200);
        response.setResponseJson("{}");
        PowerMockito.mockStatic(RestUtil.class);
        PowerMockito.when(RestUtil.sendRestRequest(Mockito.anyString(), Mockito.anyString(), Mockito.any(),
            Mockito.any())).thenReturn(response);

        TimelineQueryParam queryTimeLine = new TimelineQueryParam();
        ResponseEntity responseTimeLine = businessCommonWebsiteDelegateimpl.queryTimeLine(new MockHttpContext(), queryTimeLine);
        Assert.assertEquals(0, (int) responseTimeLine.getResultCode());

        AlarmStatisticParam alarmStatisticParam = new AlarmStatisticParam();
        ResponseEntity responseAlarmCount = businessCommonWebsiteDelegateimpl.queryAlarmCountStatistic(new MockHttpContext(), alarmStatisticParam);
        Assert.assertEquals(0, (int) responseAlarmCount.getResultCode());

        AlarmCsnParam queryAlarmByCsn = new AlarmCsnParam();
        queryAlarmByCsn.setCsnList("1954512356");
        ResponseEntity responseAlarmCsn = businessCommonWebsiteDelegateimpl.queryAlarmByCsn(new MockHttpContext(), queryAlarmByCsn);
        Assert.assertEquals(0, (int) responseAlarmCsn.getResultCode());

        AlarmCsnParam queryAlarmDetail = new AlarmCsnParam();
        queryAlarmDetail.setCsnList("1954512356");
        ResponseEntity responseAlarmDetail = businessCommonWebsiteDelegateimpl.queryAlarmDetail(new MockHttpContext(), queryAlarmDetail);
        Assert.assertEquals(0, (int) responseAlarmDetail.getResultCode());

        AlarmCsnParam queryAlarmDetailWithStamp = new AlarmCsnParam();
        queryAlarmDetailWithStamp.setCsnList("1954512356");
        queryAlarmDetailWithStamp.setTimestamp(1L);
        ResponseEntity responseAlarmDetailWithStamp = businessCommonWebsiteDelegateimpl.queryAlarmDetail(new MockHttpContext(), queryAlarmDetailWithStamp);
        Assert.assertEquals(0, (int) responseAlarmDetailWithStamp.getResultCode());

        TimelineAlarmQueryParam queryTimeLineAlarmDetail = new TimelineAlarmQueryParam();
        ResponseEntity responseAlarmTimeline = businessCommonWebsiteDelegateimpl.queryTimeLineAlarmDetail(new MockHttpContext(), queryTimeLineAlarmDetail);
        Assert.assertEquals(200, (int) responseAlarmTimeline.getResultCode());
    }

    @Test
    public void getMoTypeTreeTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        ResponseEntity moTypeTree = businessCommonWebsiteDelegateimpl.getMoTypeTree(new MockHttpContext(), StringUtils.EMPTY);
        Assert.assertEquals(0, (int) moTypeTree.getResultCode());

        ResponseEntity moTypeTreeWithName = businessCommonWebsiteDelegateimpl.getMoTypeTree(new MockHttpContext(), "adapterapp");
        Assert.assertEquals(0, (int) moTypeTreeWithName.getResultCode());
    }

    @Test
    public void getMeasTypeTreeTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        ResponseEntity measTypeTree = businessCommonWebsiteDelegateimpl.getMeasTypeTree(new MockHttpContext(),
            "cbs.billing.cbs", StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY,
            StringUtils.EMPTY, StringUtils.EMPTY);
        Assert.assertEquals(0, (int) measTypeTree.getResultCode());
    }

    @Test
    public void getMeasObjectTest() throws ServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        ResponseEntity measObject = businessCommonWebsiteDelegateimpl.getMeasObjectInfoByMoType(new MockHttpContext(),
            "com.huawei.IRes.vm", "NetworkIF", StringUtils.EMPTY, StringUtils.EMPTY);
        Assert.assertEquals(0, (int) measObject.getResultCode());
    }

    @Test
    public void getIndicatorInstanceTest() throws ServiceException, OSSException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        List<ManagedObject> managedObjects = new ArrayList<>();
        ManagedObject mo = new ManagedElement() {
            @Override
            public ManagedObject copy() {
                return null;
            }
        };
        mo.setType("com.huawei.IRes.vm");
        managedObjects.add(mo);
        PowerMockito.when(mitManagerClient.getMoByType("com.huawei.IRes.vm")).thenReturn(managedObjects);
        Indicator indicator = new Indicator();
        indicator.setMoType("com.huawei.IRes.vm");
        ResponseEntity measObject = businessCommonWebsiteDelegateimpl.getIndicatorIns(new MockHttpContext(), Collections.singletonList(indicator));
        Assert.assertEquals(0, (int) measObject.getResultCode());
    }

    @Test
    public void userAuthenticateTest() throws ServiceException, AuthServiceException {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(false);
        contextUtils.setAuthDns(new HashSet<>());
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        PowerMockito.mockStatic(CommonTokenManager.class);
        CommonTokenManager tokenManager = PowerMockito.mock(CommonTokenManager.class);
        PowerMockito.when(CommonTokenManager.getInstanse()).thenReturn(tokenManager);
        String tokenJson
            = "{\"domainID\":\"0\",\"domainName\":\"Default Domain\",\"ovpnTagIds\":[],\"projectId\":\"global\",\"projectName\":\"Default Domain\",\"roleIds\":[1,2],\"roleList\":[{\"id\":\"1\",\"name\":\"Administrators\"},{\"id\":\"2\",\"name\":\"安全管理员组\"}],\"rolePermissions\":{},\"sourceName\":\"admin\",\"targetName\":\"Default Domain\",\"tokenExpireTime\":\"2021-12-02T06:24:10.427000Z\",\"tokenMapObj\":{\"token\":{\"user\":{\"id\":\"1\",\"name\":\"admin\"},\"project\":{\"id\":\"global\",\"name\":\"Default Domain\"},\"odomain\":{\"id\":\"0\",\"name\":\"Default Domain\"},\"roles\":[{\"$ref\":\"$.roleList[0]\"},{\"$ref\":\"$.roleList[1]\"}],\"resources\":{\"a\":[{\"groupId\":\"2\"}]},\"permissions\":{},\"expires_at\":\"2021-12-02T06:24:10.427000Z\",\"issued_at\":\"2021-12-01T06:24:10.427000Z\"}},\"userID\":\"1\",\"userName\":\"admin\"}";
        OMToken token = JSON.parseObject(tokenJson, OMToken.class);
        PowerMockito.when(tokenManager.parserOMToken(Mockito.any())).thenReturn(token);
        PowerMockito.mockStatic(UserRoleUtil.class);
        UserDetail orgUserDetail = new UserDetail();
        PowerMockito.when(UserRoleUtil.getOrgUserDetail(Mockito.any(), Mockito.any())).thenReturn(orgUserDetail);
        MockHttpRequest request = new MockHttpRequest();
        MockHttpResponse response = new MockHttpResponse();
        HttpContext context = new MockHttpContext(request, response);

        ResponseEntity result = businessCommonWebsiteDelegateimpl.userAuthenticate(context);
        Assert.assertEquals(0, (int) result.getResultCode());
    }

    @Test
    public void queryDependentServiceStatusTest() throws ServiceException {
        ConfigData canJump = new ConfigData();
        canJump.setConfigItemName(BusinessTopoConstant.JUMP_TO_PERFORMANCE_PAGE);
        canJump.setValue("false");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, BusinessTopoConstant.JUMP_TO_PERFORMANCE_PAGE, canJump);

        ResponseEntity result = businessCommonWebsiteDelegateimpl.queryDependentServiceStatus(null);

        Assert.assertEquals(0, (int) result.getResultCode());
    }
}
