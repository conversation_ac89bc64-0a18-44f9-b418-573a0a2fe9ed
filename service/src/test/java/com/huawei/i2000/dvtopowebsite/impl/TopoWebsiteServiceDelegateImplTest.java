/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtopowebsite.impl;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.dvtopowebsite.model.ResponseEntity;
import com.huawei.i2000.dvtopowebsite.util.RestConstant;
import com.huawei.i2000.dvtopowebsite.util.RestUtil;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import org.eclipse.jetty.util.StringUtil;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * websiteImpl测试类
 *
 * <AUTHOR>
 * @since 2025/2/13
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {JSON.class, StringUtil.class, JSONObject.class, JSONArray.class, RestUtil.class})
@PowerMockIgnore({"javax.management.*", "jdk.internal.reflect.*"})
public class TopoWebsiteServiceDelegateImplTest {
    private TopoWebsiteServiceDelegateImpl topoWebsiteServiceDelegateImpl;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Before
    public void setUp() throws Exception {
        topoWebsiteServiceDelegateImpl = new TopoWebsiteServiceDelegateImpl();
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void queryMaskedMenusTest() throws Exception {
        PowerMockito.mockStatic(RestUtil.class);
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(JSONArray.class);
        PowerMockito.mockStatic(JSONObject.class);
        PowerMockito.mockStatic(StringUtil.class);
        // setup
        RestfulResponse restfulResponse = new RestfulResponse();
        restfulResponse.setStatus(200);
        PowerMockito.when(
                RestUtil.sendRestRequest(eq(RestConstant.RESTFUL_METHOD_GET), anyString(), eq(null), eq(null)))
            .thenReturn(restfulResponse);

        PowerMockito.when(JSON.isValidArray(anyString())).thenReturn(true);

        JSONArray list = new JSONArray();
        PowerMockito.when(JSONArray.parseArray(anyString())).thenReturn(list);

        JSONObject map = new JSONObject();
        PowerMockito.when(JSONObject.parseObject(anyString())).thenReturn(map);

        PowerMockito.when(StringUtil.valueOf(anyInt())).thenReturn("string");

        HttpServletRequest httpServletRequest = Mockito.mock(HttpServletRequest.class);
        HttpServletResponse httpServletResponse = Mockito.mock(HttpServletResponse.class);
        HttpContext context = new HttpContext(httpServletRequest, httpServletResponse);

        // run the test
        ResponseEntity result1 = topoWebsiteServiceDelegateImpl.queryMaskedMenus(context);

        // verify the results
        assertNotNull(result1);

        restfulResponse.setStatus(0);
        PowerMockito.when(
                RestUtil.sendRestRequest(eq(RestConstant.RESTFUL_METHOD_GET), anyString(), eq(null), eq(null)))
            .thenReturn(restfulResponse);

        // run the test
        ResponseEntity result2 = topoWebsiteServiceDelegateImpl.queryMaskedMenus(context);

        // verify the results
        assertNotNull(result2);
    }

    @Test
    public void queryNfvConfigTest() throws Exception {
        PowerMockito.mockStatic(RestUtil.class);
        PowerMockito.mockStatic(JSON.class);
        // setup
        RestfulResponse restfulResponse = new RestfulResponse();
        restfulResponse.setStatus(200);
        PowerMockito.when(
                RestUtil.sendRestRequest(eq(RestConstant.RESTFUL_METHOD_GET), anyString(), eq(null), eq(null)))
            .thenReturn(restfulResponse);

        PowerMockito.when(JSON.parseObject(anyString(), eq(Boolean.class))).thenReturn(true);

        HttpServletRequest httpServletRequest = Mockito.mock(HttpServletRequest.class);
        HttpServletResponse httpServletResponse = Mockito.mock(HttpServletResponse.class);
        HttpContext context = new HttpContext(httpServletRequest, httpServletResponse);

        // run the test
        ResponseEntity result1 = topoWebsiteServiceDelegateImpl.queryNfvConfig(context);

        // verify the results
        assertNotNull(result1);

        restfulResponse.setStatus(0);
        PowerMockito.when(
                RestUtil.sendRestRequest(eq(RestConstant.RESTFUL_METHOD_GET), anyString(), eq(null), eq(null)))
            .thenReturn(restfulResponse);

        // run the test
        ResponseEntity result2 = topoWebsiteServiceDelegateImpl.queryNfvConfig(context);

        // verify the results
        assertNotNull(result2);
    }

    @Test
    public void displayLinkTipsTest() throws Exception {
        PowerMockito.mockStatic(RestUtil.class);
        // setup
        PowerMockito.when(RestUtil.addUrlParam(anyString(), anyString(), anyString())).thenReturn("string");
        RestfulResponse restfulResponse = new RestfulResponse();
        restfulResponse.setStatus(200);
        PowerMockito.when(
                RestUtil.sendRestRequest(eq(RestConstant.RESTFUL_METHOD_GET), anyString(), eq(null), eq(null)))
            .thenReturn(restfulResponse);

        HttpServletRequest httpServletRequest = Mockito.mock(HttpServletRequest.class);
        HttpServletResponse httpServletResponse = Mockito.mock(HttpServletResponse.class);
        HttpContext context = new HttpContext(httpServletRequest, httpServletResponse);

        // run the test
        ResponseEntity result1 = topoWebsiteServiceDelegateImpl.displayLinkTips(context, "string");

        // verify the results
        assertNotNull(result1);

        restfulResponse.setStatus(0);
        PowerMockito.when(
                RestUtil.sendRestRequest(eq(RestConstant.RESTFUL_METHOD_GET), anyString(), eq(null), eq(null)))
            .thenReturn(restfulResponse);

        // run the test
        ResponseEntity result2 = topoWebsiteServiceDelegateImpl.displayLinkTips(context, "string");

        // verify the results
        assertNotNull(result2);
    }

    @Test
    public void displayMoTipsTest() throws Exception {
        PowerMockito.mockStatic(RestUtil.class);
        // setup
        PowerMockito.when(RestUtil.addUrlParam(anyString(), anyString(), anyString())).thenReturn("string");
        RestfulResponse restfulResponse = new RestfulResponse();
        restfulResponse.setStatus(200);
        PowerMockito.when(
                RestUtil.sendRestRequest(eq(RestConstant.RESTFUL_METHOD_GET), anyString(), eq(null), eq(null)))
            .thenReturn(restfulResponse);

        HttpServletRequest httpServletRequest = Mockito.mock(HttpServletRequest.class);
        HttpServletResponse httpServletResponse = Mockito.mock(HttpServletResponse.class);
        HttpContext context = new HttpContext(httpServletRequest, httpServletResponse);

        // run the test
        ResponseEntity result1 = topoWebsiteServiceDelegateImpl.displayMoTips(context, "string");

        // verify the results
        assertNotNull(result1);

        restfulResponse.setStatus(0);
        PowerMockito.when(
                RestUtil.sendRestRequest(eq(RestConstant.RESTFUL_METHOD_GET), anyString(), eq(null), eq(null)))
            .thenReturn(restfulResponse);

        // run the test
        ResponseEntity result2 = topoWebsiteServiceDelegateImpl.displayMoTips(context, "string");

        // verify the results
        assertNotNull(result2);
    }
}