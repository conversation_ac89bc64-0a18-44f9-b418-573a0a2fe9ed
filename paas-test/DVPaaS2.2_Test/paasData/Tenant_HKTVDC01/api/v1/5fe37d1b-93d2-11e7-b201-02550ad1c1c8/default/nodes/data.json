{"kind": "NodeList", "apiVersion": "v1", "metadata": {"selfLink": "/api/v1/namespaces/default/nodes", "resourceVersion": "41682121"}, "items": [{"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "fd720d18-caf4-11e7-8f32-fa163e811497", "resourceVersion": "41682103", "creationTimestamp": "2017-11-16T17:38:43Z", "labels": {"beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "EulerOS_2.0_SP2", "os.version": "3.10.0-327.36.58.4.x86_64", "path": "ebackup", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "2", "memory": "1867148Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "2", "memory": "1867148Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:22:57Z", "lastTransitionTime": "2017-11-17T07:35:03Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:22:57Z", "lastTransitionTime": "2017-11-16T17:38:42Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:22:57Z", "lastTransitionTime": "2017-11-16T17:38:42Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:22:57Z", "lastTransitionTime": "2017-11-16T17:38:42Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:22:57Z", "lastTransitionTime": "2017-11-17T07:35:13Z", "reason": "KubeletReady", "message": "kubelet is posting ready status"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "a615fa07e3136775490394f8f1a99aaa", "systemUUID": "10F96B62-629C-48D0-919E-FF338DCB07AD", "bootID": "aa5406c1-3210-43d1-9ca1-bb00dd8b17d2", "kernelVersion": "3.10.0-327.36.58.4.x86_64", "osImage": "EulerOS 2.0 (SP2)", "containerRuntimeVersion": "docker://1.11.2", "kubeletVersion": "v2.11.1-FusionStage2.1-B039-SP1-dirty", "kubeProxyVersion": "v2.11.1-FusionStage2.1-B039-SP1-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/redis-server:v8r2c30"], "sizeBytes": 477862941}, {"names": ["canal-agent:2.3.RC3.SPC3", "canal-agent:latest"], "sizeBytes": 406890280}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender:1.0.4"], "sizeBytes": 78645352}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css:1.0.4"], "sizeBytes": 1094548}, {"names": ["************:20202/op_svc_cfe/default/cfe-pause:2.11.1"], "sizeBytes": 350164}], "nodeState": {}, "hostname": "host-10-209-67-137"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "b63074a1-9463-11e7-b1a3-fa163e4a66d8", "resourceVersion": "41682088", "creationTimestamp": "2017-09-08T07:02:43Z", "labels": {"beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "elb_pr.lvs": "lvs", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "6", "memory": "32422152Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "6", "memory": "32422152Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:05Z", "lastTransitionTime": "2017-11-22T07:06:34Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:05Z", "lastTransitionTime": "2017-09-08T07:02:43Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:05Z", "lastTransitionTime": "2017-09-08T07:02:43Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:05Z", "lastTransitionTime": "2017-09-08T07:02:43Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:05Z", "lastTransitionTime": "2017-11-22T07:06:34Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "72EBA04D-5D90-4151-93AA-F64D90D1AED2", "bootID": "320a4e8d-2201-48c5-8cb7-ea1f99fa4870", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["canal-agent:2.3.RC3.SPC3", "canal-agent:latest"], "sizeBytes": 406890280}, {"names": ["canal-agent:2.3.RC3.SPC2.B010"], "sizeBytes": 391548702}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender@sha256:f1f9a7a2b2f3cf380c6de17d8f10473e348def63b37902a1f410079a9ff3a7e9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender:1.0.4"], "sizeBytes": 78645352}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css@sha256:42007e581a318ca8b51962e700bc92f2e0cac07ed9a3c55defd301d1789bef3a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css:1.0.4"], "sizeBytes": 1094548}, {"names": ["************:20202/op_svc_cfe/default/cfe-pause@sha256:16c6191b2e2e625aea965d5a0f193f6239240a50a4b65f424a3f37eb54ea3f56", "************:20202/op_svc_cfe/default/cfe-pause:2.9.6"], "sizeBytes": 350164}], "nodeState": {}, "hostname": "prapplvsa"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "98fbbe54-9464-11e7-b1a3-fa163e4a66d8", "resourceVersion": "41682099", "creationTimestamp": "2017-09-08T07:09:04Z", "labels": {"beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "elb_pr.lvs": "lvs", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "6", "memory": "32422152Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "6", "memory": "32422152Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-11-22T07:06:39Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-09-08T07:09:04Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-09-08T07:09:04Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-09-08T07:09:04Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-11-22T07:06:39Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "AEF02326-4912-4753-91B0-B535FDA27ED6", "bootID": "54160b5b-23a7-4684-abaa-f20622fa15ca", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["canal-agent:2.3.RC3.SPC3", "canal-agent:latest"], "sizeBytes": 406890280}, {"names": ["canal-agent:2.3.RC3.SPC2.B010"], "sizeBytes": 391548702}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktdr/elb_broker@sha256:dfcff21d5b0b35c576b53e1cc98ae343e25b298c398d6bf968b8da13300e5e0d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/elb_broker@sha256:dfcff21d5b0b35c576b53e1cc98ae343e25b298c398d6bf968b8da13300e5e0d", "************:20202/tenant_hktvdc01/tenant_hktdr/elb_broker:0.1.1", "************:20202/tenant_hktvdc01/tenant_hktvdc01/elb_broker:0.1.1"], "sizeBytes": 324875836}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender@sha256:f1f9a7a2b2f3cf380c6de17d8f10473e348def63b37902a1f410079a9ff3a7e9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender:1.0.4"], "sizeBytes": 78645352}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css@sha256:42007e581a318ca8b51962e700bc92f2e0cac07ed9a3c55defd301d1789bef3a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css:1.0.4"], "sizeBytes": 1094548}, {"names": ["************:20202/op_svc_cfe/default/cfe-pause@sha256:16c6191b2e2e625aea965d5a0f193f6239240a50a4b65f424a3f37eb54ea3f56", "************:20202/op_svc_cfe/default/cfe-pause:2.9.6"], "sizeBytes": 350164}], "nodeState": {}, "hostname": "prapplvsb"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "780170e8-9482-11e7-bbbd-fa163e811497", "resourceVersion": "41682110", "creationTimestamp": "2017-09-08T10:42:53Z", "labels": {"beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "elb_pr.etcd": "etcd", "elb_pr.mysql": "mysql", "elb_pr.svc": "svc", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "2", "memory": "15907616Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "2", "memory": "15907616Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:08Z", "lastTransitionTime": "2017-11-22T07:04:37Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:08Z", "lastTransitionTime": "2017-09-08T10:42:53Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:08Z", "lastTransitionTime": "2017-09-08T10:42:53Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:08Z", "lastTransitionTime": "2017-09-08T10:42:53Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:08Z", "lastTransitionTime": "2017-11-22T07:04:37Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "038B8DB5-7363-4C93-8585-8CB38C99B121", "bootID": "d0dd3ada-3ce2-4356-821f-3489bf88f209", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["canal-agent:2.3.RC3.SPC3", "canal-agent:latest"], "sizeBytes": 406890280}, {"names": ["canal-agent:2.3.RC3.SPC2.B010"], "sizeBytes": 391548702}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktdr/elb-etcd@sha256:69829dcba108d4bcfb0e4331b2054a3c72cdffe2730732e0334e04b566508c84", "************:20202/tenant_hktvdc01/tenant_hktvdc01/elb-etcd@sha256:69829dcba108d4bcfb0e4331b2054a3c72cdffe2730732e0334e04b566508c84", "************:20202/tenant_hktvdc01/tenant_hktdr/elb-etcd:2.1.8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/elb-etcd:2.1.8"], "sizeBytes": 302714652}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender@sha256:f1f9a7a2b2f3cf380c6de17d8f10473e348def63b37902a1f410079a9ff3a7e9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender:1.0.4"], "sizeBytes": 78645352}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css@sha256:42007e581a318ca8b51962e700bc92f2e0cac07ed9a3c55defd301d1789bef3a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css:1.0.4"], "sizeBytes": 1094548}, {"names": ["************:20202/op_svc_cfe/default/cfe-pause@sha256:16c6191b2e2e625aea965d5a0f193f6239240a50a4b65f424a3f37eb54ea3f56", "************:20202/op_svc_cfe/default/cfe-pause:2.9.6"], "sizeBytes": 350164}], "nodeState": {}, "hostname": "prappmgmt1"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "c585314b-9465-11e7-b1a3-fa163e4a66d8", "resourceVersion": "41682120", "creationTimestamp": "2017-09-08T07:17:28Z", "labels": {"beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "elb_pr.etcd": "etcd", "elb_pr.mysql": "mysql", "elb_pr.svc": "svc", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true", "test-broker": "broker"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "2", "memory": "15907588Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "2", "memory": "15907588Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:11Z", "lastTransitionTime": "2017-11-22T07:06:35Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:11Z", "lastTransitionTime": "2017-09-08T07:17:28Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:11Z", "lastTransitionTime": "2017-09-08T07:17:28Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:11Z", "lastTransitionTime": "2017-09-08T07:17:28Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:11Z", "lastTransitionTime": "2017-11-22T07:06:35Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "A63EB41D-4AD9-49BC-BD4D-C4D73E63FCD6", "bootID": "f91de024-7650-472d-9294-f9f2daf9db13", "kernelVersion": "4.4.74-92.35-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["canal-agent:2.3.RC3.SPC3", "canal-agent:latest"], "sizeBytes": 406890280}, {"names": ["canal-agent:2.3.RC3.SPC2.B010"], "sizeBytes": 391548702}, {"names": ["************:20202/tenant_hktdr/elb_broker@sha256:dfcff21d5b0b35c576b53e1cc98ae343e25b298c398d6bf968b8da13300e5e0d", "************:20202/tenant_hktdr/elb_broker:0.1.1"], "sizeBytes": 324875836}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktdr/elb-etcd@sha256:69829dcba108d4bcfb0e4331b2054a3c72cdffe2730732e0334e04b566508c84", "************:20202/tenant_hktvdc01/tenant_hktvdc01/elb-etcd@sha256:69829dcba108d4bcfb0e4331b2054a3c72cdffe2730732e0334e04b566508c84", "************:20202/tenant_hktvdc01/tenant_hktdr/elb-etcd:2.1.8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/elb-etcd:2.1.8"], "sizeBytes": 302714652}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender@sha256:f1f9a7a2b2f3cf380c6de17d8f10473e348def63b37902a1f410079a9ff3a7e9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender:1.0.4"], "sizeBytes": 78645352}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css@sha256:42007e581a318ca8b51962e700bc92f2e0cac07ed9a3c55defd301d1789bef3a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css:1.0.4"], "sizeBytes": 1094548}, {"names": ["************:20202/op_svc_cfe/default/cfe-pause@sha256:16c6191b2e2e625aea965d5a0f193f6239240a50a4b65f424a3f37eb54ea3f56", "************:20202/op_svc_cfe/default/cfe-pause:2.9.6"], "sizeBytes": 350164}], "nodeState": {}, "hostname": "prappmgmt2"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "ed18e583-9465-11e7-bbbd-fa163e811497", "resourceVersion": "41682100", "creationTimestamp": "2017-09-08T07:18:34Z", "labels": {"beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "elb_pr.etcd": "etcd", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "2", "memory": "15907616Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "2", "memory": "15907616Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-11-22T07:06:34Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-09-08T07:18:34Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-09-08T07:18:34Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-09-08T07:18:34Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-11-22T07:06:34Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "BE472BF5-2BF6-4FA7-B236-5D38EE6414F1", "bootID": "9023e9f6-d7c4-49b3-b5bd-394bcec84a75", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["canal-agent:2.3.RC3.SPC3", "canal-agent:latest"], "sizeBytes": 406890280}, {"names": ["canal-agent:2.3.RC3.SPC2.B010"], "sizeBytes": 391548702}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktdr/elb-etcd@sha256:69829dcba108d4bcfb0e4331b2054a3c72cdffe2730732e0334e04b566508c84", "************:20202/tenant_hktvdc01/tenant_hktvdc01/elb-etcd@sha256:69829dcba108d4bcfb0e4331b2054a3c72cdffe2730732e0334e04b566508c84", "************:20202/tenant_hktvdc01/tenant_hktdr/elb-etcd:2.1.8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/elb-etcd:2.1.8"], "sizeBytes": 302714652}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender@sha256:f1f9a7a2b2f3cf380c6de17d8f10473e348def63b37902a1f410079a9ff3a7e9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender:1.0.4"], "sizeBytes": 78645352}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css@sha256:42007e581a318ca8b51962e700bc92f2e0cac07ed9a3c55defd301d1789bef3a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css:1.0.4"], "sizeBytes": 1094548}, {"names": ["************:20202/op_svc_cfe/default/cfe-pause@sha256:16c6191b2e2e625aea965d5a0f193f6239240a50a4b65f424a3f37eb54ea3f56", "************:20202/op_svc_cfe/default/cfe-pause:2.9.6"], "sizeBytes": 350164}], "nodeState": {}, "hostname": "prappmgmt3"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "b838678a-9482-11e7-bbbd-fa163e811497", "resourceVersion": "41682113", "creationTimestamp": "2017-09-08T10:44:41Z", "labels": {"beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "elb_pr.nginx": "nginx", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "6", "memory": "32422124Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "6", "memory": "32422124Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:09Z", "lastTransitionTime": "2017-11-22T07:06:41Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:09Z", "lastTransitionTime": "2017-09-08T10:44:41Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:09Z", "lastTransitionTime": "2017-09-08T10:44:41Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:09Z", "lastTransitionTime": "2017-09-08T10:44:41Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:09Z", "lastTransitionTime": "2017-11-22T07:06:41Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "6388A29F-1B48-4B1A-87C4-CB3B270984EB", "bootID": "41b0173c-3fa7-44dc-a367-79bc63b7d021", "kernelVersion": "4.4.74-92.35-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_console_suse11sp3@sha256:acc9773a661271814713c6822f07056ba1042d27ddd47ef3c4c59118ac319497", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_console_suse11sp3:v600r001c11b025"], "sizeBytes": 1628082513}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricgovernancectz@sha256:fe301116ef26cfb7de704642d8a64b1a9c739c3f7712270f3a5cf56b0e9100b1", "************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricgovernancectz:8.2.20.LG0010_1026"], "sizeBytes": 1168174192}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricgovernancectz@sha256:6b7ca89780db0f08f8bacef2594f6f4f07b0d290704ad0f2151a6cb0fb174545", "************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricgovernancectz:8.2.20.LG0009_0927"], "sizeBytes": 1135291107}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dbscript_job@sha256:5cdb5c64a86cc7723c59dfdbe1288e055cf4bccea922ff3012a4f7dd04a58fff", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dbscript_job:latest"], "sizeBytes": 683059719}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_broker_jetmq@sha256:6181811f4da8546c21b75a8c6dc09d191dc39540bab324fff61f26c63d79e07f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_broker_jetmq:v600r001c11"], "sizeBytes": 544050012}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_broker_suse11sp3@sha256:bc7c46852a39015db10d76935c612888c82152b1905bd7d68d30d7575f3d089c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_broker_suse11sp3:v600r001c11b024"], "sizeBytes": 498814160}, {"names": ["canal-agent:2.3.RC3.SPC3", "canal-agent:latest"], "sizeBytes": 406890280}, {"names": ["canal-agent:2.3.RC3.SPC2.B010"], "sizeBytes": 391548702}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktdr/elb_broker@sha256:dfcff21d5b0b35c576b53e1cc98ae343e25b298c398d6bf968b8da13300e5e0d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/elb_broker@sha256:dfcff21d5b0b35c576b53e1cc98ae343e25b298c398d6bf968b8da13300e5e0d", "************:20202/tenant_hktvdc01/tenant_hktdr/elb_broker:0.1.1", "************:20202/tenant_hktvdc01/tenant_hktvdc01/elb_broker:0.1.1"], "sizeBytes": 324875836}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktdr/elb-etcd@sha256:69829dcba108d4bcfb0e4331b2054a3c72cdffe2730732e0334e04b566508c84", "************:20202/tenant_hktvdc01/tenant_hktdr/elb-etcd:2.1.8"], "sizeBytes": 302714652}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender@sha256:f1f9a7a2b2f3cf380c6de17d8f10473e348def63b37902a1f410079a9ff3a7e9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender:1.0.4"], "sizeBytes": 78645352}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css@sha256:42007e581a318ca8b51962e700bc92f2e0cac07ed9a3c55defd301d1789bef3a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css:1.0.4"], "sizeBytes": 1094548}, {"names": ["************:20202/op_svc_cfe/default/cfe-pause@sha256:16c6191b2e2e625aea965d5a0f193f6239240a50a4b65f424a3f37eb54ea3f56", "************:20202/op_svc_cfe/default/cfe-pause:2.9.6"], "sizeBytes": 350164}], "nodeState": {}, "hostname": "prappnginx1"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "b112c204-a2b6-11e7-a622-fa163e4a66d8", "resourceVersion": "41682098", "creationTimestamp": "2017-09-26T12:31:59Z", "labels": {"beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "elb_pr.nginx": "nginx", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "6", "memory": "32422152Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "6", "memory": "32422152Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-11-22T07:06:34Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-09-26T12:31:59Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-09-26T12:31:59Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-09-26T12:31:59Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-11-22T07:06:34Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "D84039F4-CE12-4BE8-AF99-B3B7F7E611B6", "bootID": "6a44a7ac-39ad-428f-9a50-ccce6f33559e", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricgovernancectz@sha256:6b7ca89780db0f08f8bacef2594f6f4f07b0d290704ad0f2151a6cb0fb174545", "************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricgovernancectz:8.2.20.LG0009_0927"], "sizeBytes": 1135291107}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dbscript_job@sha256:5cdb5c64a86cc7723c59dfdbe1288e055cf4bccea922ff3012a4f7dd04a58fff", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dbscript_job:latest"], "sizeBytes": 683059719}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dbscript_job@sha256:d28421523c54c2afcc6e1cae95d75247ec3ffaec3bdcf3b4316745c61fabacf7", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dbscript_job:latest_2017102511"], "sizeBytes": 679710033}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_broker_zookeeper@sha256:4340703dcdf85967079f6dc9580db97452176cc809c6994b2336accf6305be60", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_broker_zookeeper:v600r001c11"], "sizeBytes": 544780111}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/redis-server@sha256:fb5f6a36c336a7dc5f7a8dca3b40ddaefa2fd6f67576b649297ff20b5b847eda", "************:20202/tenant_hktvdc01/tenant_hktvdc01/redis-server:2.300.1"], "sizeBytes": 480841654}, {"names": ["canal-agent:2.3.RC3.SPC3", "canal-agent:latest"], "sizeBytes": 406890280}, {"names": ["canal-agent:2.3.RC3.SPC2.B010"], "sizeBytes": 391548702}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/hkt-broker@sha256:dd56209f671a07f859c48025a3a78e2c4c12399f9aad50b0a203bfbb1b7eb792", "************:20202/tenant_hktvdc01/tenant_hktvdc01/hkt-broker:3.2"], "sizeBytes": 341534192}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/elb_broker@sha256:dfcff21d5b0b35c576b53e1cc98ae343e25b298c398d6bf968b8da13300e5e0d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/elb_broker:0.1.1"], "sizeBytes": 324875836}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender@sha256:f1f9a7a2b2f3cf380c6de17d8f10473e348def63b37902a1f410079a9ff3a7e9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender:1.0.4"], "sizeBytes": 78645352}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css@sha256:42007e581a318ca8b51962e700bc92f2e0cac07ed9a3c55defd301d1789bef3a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css:1.0.4"], "sizeBytes": 1094548}, {"names": ["************:20202/op_svc_cfe/default/cfe-pause@sha256:16c6191b2e2e625aea965d5a0f193f6239240a50a4b65f424a3f37eb54ea3f56", "************:20202/op_svc_cfe/default/cfe-pause:2.9.6"], "sizeBytes": 350164}], "nodeState": {}, "hostname": "prappnginx2"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "9635ad83-c90f-11e7-83ff-fa163e4a66d8", "resourceVersion": "41682107", "creationTimestamp": "2017-11-14T07:44:03Z", "labels": {"AppDefaultResourceType": "Normal", "BESResourceType": "Normal", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:08Z", "lastTransitionTime": "2017-11-22T07:06:35Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:08Z", "lastTransitionTime": "2017-11-14T07:44:02Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:08Z", "lastTransitionTime": "2017-11-14T07:44:02Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:08Z", "lastTransitionTime": "2017-11-14T07:44:02Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:08Z", "lastTransitionTime": "2017-11-22T07:06:35Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "A5B75F3C-538F-44CD-A392-9C6191FCB3CE", "bootID": "4b963681-cbc3-4662-9872-054831bd2790", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.11.1-FusionStage2.1-B039-SP1-dirty", "kubeProxyVersion": "v2.11.1-FusionStage2.1-B039-SP1-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:8a91d49eae81f8e3127abb723682d0089f3dfb478020fbbbd1431a61657a9ba2", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 3198384626}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:662a469c98ec52dee363a76671634203a90b1ff9721a288349ab320cb036d350", "bes_customerserviceportal_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 3150200466}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:ee9a372e54765ecbaace7487c0c164509a679fd8550da6c11de7957de62ef9f4", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 2855481928}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:53dc6ce7b6a46af258fea1d3d7e508131ae843029248d6dc9bc8ddaf060995f0", "bes_osp_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 2830511269}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:6048139c60ceb3c9b0fb2e7a591c5c7f2e01147d35045c05b5cd31f61cb66922", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11172200", "bes_adminportal_hkt:8.2.20.LG0010_11172200"], "sizeBytes": 2764600281}, {"names": ["bes_ifengine_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 2149059722}, {"names": ["bes_occoreapp3_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1902228249}, {"names": ["bes_occoreapp2_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1734624308}, {"names": ["bes_occoreapp1_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1734205878}, {"names": ["dw_dds_console_suse11sp3:v600r001c11b025"], "sizeBytes": 1628082513}, {"names": ["bes_commonapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1572610477}, {"names": ["bes_cmapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1546121274}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:793a9f8f27a51e5bae92dd27990df59e88bd393dc6925fda67116c005c0168b3", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0010_11151800", "bes_searchcenterapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1435422745}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:daf26a2f8acf855070b473e66f7a011dfca112d59ad3af1cca6e1edc0c3046f8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11172250"], "sizeBytes": 1348724555}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:2ea3beb2f52898dcc04b067e9f440b7aa23f0073a368a6e15f91d6fb11ae362c", "bes_omapp_hkt:8.2.20.LG0010_11172250"], "sizeBytes": 1347384973}, {"names": ["bes_prodconfapp_hkt:8.2.20.LG0010_11162000"], "sizeBytes": 1340834771}, {"names": ["bes_prodcenterapp_hkt:8.2.20.LG0010_11162230"], "sizeBytes": 1322553429}, {"names": ["bes_invapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1319512571}, {"names": ["bes_usmapp_hkt:8.2.20.LG0010_11172200"], "sizeBytes": 1306224934}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:471d0c97928fdf25f005388dbbcd04c0b6a47b7f908ac505ad508291170e13c4", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11151800", "bes_msp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1295975169}, {"names": ["dw_vsearch_suse11sp3:v600r001c11b024"], "sizeBytes": 1295223056}, {"names": ["bes_ceapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1289171047}, {"names": ["dw_jetmq:v600r001c11spc100"], "sizeBytes": 1250712998}, {"names": ["dw_dds_suse11sp3:v600r001c11b024"], "sizeBytes": 1166712588}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricgovernancectz@sha256:fc5ca89fc2e4811458cc916a3a3f0534e70549b4b9e6ad30556b72ef19c92901", "************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricgovernancectz:8.2.20.LG0010_11151800", "apifabricgovernancectz:8.2.20.LG0010_11151800"], "sizeBytes": 1144307991}, {"names": ["dw_lss_suse11sp3:v600r001c11b024"], "sizeBytes": 1011753196}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricportalctz@sha256:2cbc2632ce7cafec14b3550e64c7d7b81ec856d120be15b310629182b52db7f5", "************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricportalctz:8.2.20.LG0010_11151800", "apifabricportalctz:8.2.20.LG0010_11151800"], "sizeBytes": 1011218320}, {"names": ["dw_zookeeper:v600r001c11spc100"], "sizeBytes": 991525885}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricaccessctz@sha256:a2531c3409e2dd83ea5dab09be33330eb505a4d0835c1d6f8866c76387bf2a29", "************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricaccessctz:8.2.20.LG0010_11171645", "apifabricaccessctz:8.2.20.LG0010_11171645"], "sizeBytes": 976415535}, {"names": ["redis-broker:commerce-2.300.1-20171031v1"], "sizeBytes": 793308488}, {"names": ["dbscript_job:latest"], "sizeBytes": 678099581}, {"names": ["dw_broker_zookeeper:v600r001c11spc100"], "sizeBytes": 547145180}, {"names": ["dw_broker_jetmq:v600r001c11spc100"], "sizeBytes": 546415083}, {"names": ["dw_vsearch_broker_suse11sp3:v600r001c12"], "sizeBytes": 534978526}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/redis-server@sha256:399e66b5c1f3b1b6c19df9d79fd338c63fc0e66130db855c97ecd58e0efa7172", "************:20202/tenant_hktvdc01/tenant_hktvdc01/redis-server:commerce-2.300.1", "redis-server:commerce-2.300.1"], "sizeBytes": 514303063}, {"names": ["dw_dds_broker_suse11sp3:v600r001c11b024"], "sizeBytes": 501249152}, {"names": ["dw_lss_broker_suse11sp3:v600r001c11b024"], "sizeBytes": 501152774}, {"names": ["apifabric-broker:v600r001c11"], "sizeBytes": 479875147}, {"names": ["canal-agent:2.3.RC3.SPC3", "canal-agent:latest"], "sizeBytes": 406890280}, {"names": ["hkt-broker:3.2"], "sizeBytes": 341534192}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender@sha256:f1f9a7a2b2f3cf380c6de17d8f10473e348def63b37902a1f410079a9ff3a7e9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender:1.0.4"], "sizeBytes": 78645352}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css@sha256:42007e581a318ca8b51962e700bc92f2e0cac07ed9a3c55defd301d1789bef3a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css:1.0.4"], "sizeBytes": 1094548}, {"names": ["************:20202/op_svc_cfe/default/cfe-pause@sha256:16c6191b2e2e625aea965d5a0f193f6239240a50a4b65f424a3f37eb54ea3f56", "************:20202/op_svc_cfe/default/cfe-pause:2.11.1"], "sizeBytes": 350164}], "nodeState": {}, "hostname": "prdocker01"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "fa4f7873-93dd-11e7-b1a3-fa163e4a66d8", "resourceVersion": "41682081", "creationTimestamp": "2017-09-07T15:05:25Z", "labels": {"AppDefaultResourceType": "Normal", "BESResourceType": "Normal", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-11-22T07:06:32Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-09-07T15:05:25Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-09-07T15:05:25Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-09-07T15:05:25Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-11-22T07:06:32Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "FE1D4F54-9AB9-41ED-BC59-3C494DD6EE4C", "bootID": "7094a90c-49ea-4821-a409-8aeb00aeafb9", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:86297349ed4267db06361f175258e748b74714b286c002f2ad69eee75cd990bb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3061639616}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:4272a71a91738b27f6d83a54017d9f14d1bb8d6c8bb993ba04c677e86c45478c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008"], "sizeBytes": 2988216417}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:a1cad0635ee5cd7d503a2c1437eb1bd3d412d939942e4e51b9511556abdf10fe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0008"], "sizeBytes": 2836528915}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:6048139c60ceb3c9b0fb2e7a591c5c7f2e01147d35045c05b5cd31f61cb66922", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11172200"], "sizeBytes": 2764600281}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:17ab37ec440a2d995be6aae94e3e7900e2374f1fb93cce520553c456e41dc30a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0009_10202100_2017102511"], "sizeBytes": 2683559702}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f62fe5644220b084eb8931292daab8ae1fe11d2b9a6cbfd6b859f61a3fcc8bdc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2494377012}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:f58f8716497ae0743cc58505b3afad38ffd36a9a9c429ddf205c563bdcb26863", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2371593185}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:0e19b71595f3762b7cbde603f7318c528c77ab47955b767be70d09d0c0c50c25", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2346599590}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5ed371dd08dd88cb9e95b422d45fe5ebc2806696db3155d8b84c7b89bf6df754", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2249861035}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:6f573a7af65f4c37a1d6ac08dd2ff80915b222905c42962a77ac5f76550ffe70", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2218876394}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:6ba98d948aeb945eb803a147a8c5e867a9ba76233c075efe94c180930f674dec"], "sizeBytes": 2212409238}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:b9feb97c3708f88340282675535b320c2b17eb6f2b4ec77f6a5a426dd7ac935b"], "sizeBytes": 2160854595}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:428b66ed8fe30ded4a123d2241cdb57d438b8be4f37aa982b39dca0b6137922d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 2149059722}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:da133564d6fff16c619ebe18aaf4e8ffc16b176173aceee452c2adde2ede5bbe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 2149050768}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:ff3a0b0e390741d4ea69908cf5092a9df8a69a65476349cdf411837c09abf525"], "sizeBytes": 2081588109}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:a1d3fb3df22bfa89db2424d3793c4a5a8b80735caab284e15b3c3c0f93846855", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1902228249}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:21d898165876999963297dc279cc6b9a87325b734c34a7300a175136deb47fe5", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11031128"], "sizeBytes": 1736797489}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:70434e954ed031014447cf8dc623d6f6a9e75629271134877e23516b63a2596b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1734205878}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:ed2d966a2f613548fd159fdf5bfefa0317614c650a4ba9c8b49b8566af6ea3ec", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0009_1019_branch_2017102511"], "sizeBytes": 1723190338}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:6c158dc37f48bb324f0817162a1ba1c8558965d53582e32a10bfe71dd002ac6e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0009_092900"], "sizeBytes": 1696583736}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:1769a83d4507578b56d69c925452bf532f1b0387b4a27d6bb3d0eb783cbe08ac"], "sizeBytes": 1683833490}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:566c0934d8e267c6b944b01156609761e6be38223aa38ca093da5282d8ae5dcf", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0008"], "sizeBytes": 1632244881}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:4a5e8eb8b11e6b8fc9191b60839240fe2c887426c3e87314c2fed503f8bb62ab", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11101514_2017111317"], "sizeBytes": 1568121394}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:8ddc0faddaf7b79285766691cde9ba511eb39df54627536a8469e85aded8ecbb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0009_09300330"], "sizeBytes": 1530378254}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:edc4f7b16fec29e335508908f6dc2417f145866a48c1d5e5794205f15e0dc57b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0009_09300530"], "sizeBytes": 1484207289}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:c449e3c5c2588ae8df4c8678fa58706ee779e42337ce7016ccb8aaf08d7a9908", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0009_09291840"], "sizeBytes": 1473453291}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:4cc87a8560fb89cf3b7ab8828993c5d53dba6fc5ddd353824452d32758c47e2d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1473037369}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:793a9f8f27a51e5bae92dd27990df59e88bd393dc6925fda67116c005c0168b3", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1435422745}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:070c72b4d88a8992391e0eee0016a0b27e02a8e75fbfc06339d978da631d8581", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11031800"], "sizeBytes": 1339302858}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:2ce6589d144079bae33dc9ddc8c924f1c9e346fa05cd558343f08a3a70d799bc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1320862914}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:e027b521ec22dc8beb2de4db42d3211c270ade2197309b66cd94ca600ff889f9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1319512571}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:ad8ca18721e8f9932a940be8b33680b53de711334d7f6a58bc70a4c4ca052f94", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1316898047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:77fdddea2964440d400104f4a81e763d297b53dc5e6cb3d33e8bc7d89bc0a473", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1307948018}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:191f93117f651ed9e2f048d18a741b9de7aab0f4b88b4f739e6a473c64ec097f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0010_11172200"], "sizeBytes": 1306224934}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:471d0c97928fdf25f005388dbbcd04c0b6a47b7f908ac505ad508291170e13c4", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1295975169}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:4c85614d3ec7df8eca0643e78dcb23d3de3fafc4bfaa24d6559a750863228825", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11b024"], "sizeBytes": 1295223056}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:d21e6b24e4769111a181baaed7040ea424e0ae1f7fffe786e4f84cc6c8a45857"], "sizeBytes": 1294994735}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:0a482ebf19a8458d4c15cebd23ae0ed0c213ae7065d97494a645619ef7b66262"], "sizeBytes": 1291567500}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:5875de68092a1912924958ee1a083adb01a40b8bd4a7e06c2bd61e5686e227d0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11"], "sizeBytes": 1291515726}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:8dd0ea23334b73e20866e9b1fdf0bfca4d7b0180f63709ce580845dc33354978", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0009_09291730"], "sizeBytes": 1274929375}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:d05be33d018496c0fc34be16be6df58bea623f684505e020330b32b8e6c65242", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0009_09291817"], "sizeBytes": 1271551462}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:b0418b196d7df4bc7f8cc1d5dadb6da3e0795ce1b91f0ee2acdea34e03bd1052", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0009_10202000_2017102511"], "sizeBytes": 1261825776}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:81ac9a319e1ba1e4e0d8ab32edea486d7ba27b2c3135a7046944d914fd49ce1c"], "sizeBytes": 1241965328}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:13df06abe624e9e5b757cc2e55f4aeae18175c140b1efdf209117ef7e54526c6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1241137758}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_jetmq@sha256:3cceb64947ab5768cc314cac0d6a97ff0bed77a2c13e4a39126e67032183e3f1", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_jetmq:v600r001c11"], "sizeBytes": 1239178194}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_jetmq@sha256:2a6afbe4280e6dfb3dc7b1f6aec6c05c19453e097f18d34a015a2e47cd2f1314"], "sizeBytes": 1239057631}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_jetmq@sha256:7f33798ca2ee9b06f94d06e026c02a4e5b9b74355b2fd094b38b7a6c0284b3d5"], "sizeBytes": 1239056407}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:07e6bc04c33cda21e05b8eec84a208a00fa8301912e959ef167cd513f8322717", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0009"], "sizeBytes": 1236171837}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:2251ec7cebc52165a794c3783844dd9fe726d074e576b4a8a17617f79dbc2f87"], "sizeBytes": 1231921755}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:3219ae8690189be14b682c1f1cf9d5a02a866a5d83cefcde61fa62633cf8c2b0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1225786700}], "nodeState": {}, "hostname": "prdocker04"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "3668deea-9433-11e7-bbbd-fa163e811497", "resourceVersion": "41682071", "creationTimestamp": "2017-09-08T01:15:33Z", "labels": {"AppDefaultResourceType": "Normal", "BESResourceType": "Normal", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-11-22T07:06:41Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-09-08T01:15:33Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-09-08T01:15:33Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-09-08T01:15:33Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-11-22T07:06:41Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "5802BC95-1846-456E-9181-E473E8A64161", "bootID": "3906f11e-f5e8-4b94-a467-7a56c36b9be2", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:968aa92f65cd933b2410261099b4319e685f9360b39f74b62fd60546574f6854", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 4222357091}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:50d05dbca580b0ba3c58c5976d98394e3ecc04d30690f1b57b5d1e7adda2a174", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008"], "sizeBytes": 3959444519}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:2ad76c611ec100d9756ca6405020e044c11fa3e7d1a96c95fcc653b486f5372f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3884157513}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:86297349ed4267db06361f175258e748b74714b286c002f2ad69eee75cd990bb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3061639616}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:4272a71a91738b27f6d83a54017d9f14d1bb8d6c8bb993ba04c677e86c45478c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008"], "sizeBytes": 2988216417}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:a1cad0635ee5cd7d503a2c1437eb1bd3d412d939942e4e51b9511556abdf10fe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0008"], "sizeBytes": 2836528915}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:9919eefcff2fbdbaa82593545df371930143822750f27850d0aafe401ee32add", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2811609391}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:2b732c57b71cc05c7bb051038c398c7ea01f87733dc8cbb93bd32056a0ab7404", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2493590740}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:73f7abe6ac8dc13458864a6d09e749d2924c1bd2efe841e20a368d0b1aa8c513"], "sizeBytes": 2371593185}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:f58f8716497ae0743cc58505b3afad38ffd36a9a9c429ddf205c563bdcb26863", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2371593185}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:0e19b71595f3762b7cbde603f7318c528c77ab47955b767be70d09d0c0c50c25", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2346599590}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:232f4fc637817e426b8ba73317fe97a6e9f82d58616fd3ba4f383baaf9dd23c9"], "sizeBytes": 2346583060}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:ed4520e87b86c5747da4470b9e7dfa0af74018dbce6effaeaccad4798da1376c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2249861035}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:601686830ac50df2a7cd2b33d1ec05f3be63254b164ccb1d808c100fdc16ecf7"], "sizeBytes": 2248052637}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:6ba98d948aeb945eb803a147a8c5e867a9ba76233c075efe94c180930f674dec"], "sizeBytes": 2212409238}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:129bbadc35be33e500c278486f5f8356c8b29d3c6c0351e20b3bd24591f79e6b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2192371137}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:428b66ed8fe30ded4a123d2241cdb57d438b8be4f37aa982b39dca0b6137922d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 2149059722}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:da133564d6fff16c619ebe18aaf4e8ffc16b176173aceee452c2adde2ede5bbe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 2149050768}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:a1d3fb3df22bfa89db2424d3793c4a5a8b80735caab284e15b3c3c0f93846855", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1902228249}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:3fd27657c7fe7a397c28b73373a8fe4795f248305e6c5199cc68f7e396a7ea1e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0009_1019_branch_2017102511"], "sizeBytes": 1816428554}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:21d898165876999963297dc279cc6b9a87325b734c34a7300a175136deb47fe5", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11031128"], "sizeBytes": 1736797489}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:6c158dc37f48bb324f0817162a1ba1c8558965d53582e32a10bfe71dd002ac6e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0009_092900"], "sizeBytes": 1696583736}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:4dd55e19ba082eaa8d26c05f1d8c0cb679bdcd1f0e025cc47d6e590b2a4d0248", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1666936762}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:566c0934d8e267c6b944b01156609761e6be38223aa38ca093da5282d8ae5dcf", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0008"], "sizeBytes": 1632244881}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:bd0d16829c3bfdf4cc249b9072959c2d3b17da7c019985210126ecd759e66669", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1572610477}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:2d711c4c7e94483847d8cf85a3cec57ee63dac3e8c7017f5593dd3c6c8ec1579", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_11031815"], "sizeBytes": 1547418834}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:8ddc0faddaf7b79285766691cde9ba511eb39df54627536a8469e85aded8ecbb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0009_09300330"], "sizeBytes": 1530378254}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:271567f4ed1926b21d0831269f6f07b0a5ed09a42ca61818ff74ce5128c85b45"], "sizeBytes": 1521806676}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:edc4f7b16fec29e335508908f6dc2417f145866a48c1d5e5794205f15e0dc57b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0009_09300530"], "sizeBytes": 1484207289}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:aad19ed59b0e80fd5d6f68c823fd7f8e26baab6aa68ad5f533e295b63d0b8b2a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0009_09300536"], "sizeBytes": 1483802821}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f7c9dc8d2905ef21c68cd3b191a07131a91db53804f762221eedf947f7acfae0"], "sizeBytes": 1475408329}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:c449e3c5c2588ae8df4c8678fa58706ee779e42337ce7016ccb8aaf08d7a9908", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0009_09291840"], "sizeBytes": 1473453291}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:4cc87a8560fb89cf3b7ab8828993c5d53dba6fc5ddd353824452d32758c47e2d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1473037369}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:bf5f9642502f46a509862b6016e050d8275ebd2f9730a3d05ff1aac3ffcac3ba", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1472786453}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:e0596079828ad21e5e0266c2069f5c06ba1e139cdfcd67197a3f4ca0f694dd43", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0010_11031640"], "sizeBytes": 1339690362}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:2ce6589d144079bae33dc9ddc8c924f1c9e346fa05cd558343f08a3a70d799bc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1320862914}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:9f832cf78cbe29a6c014985437b207cd1c847984ec75d112cda1921cad5992b0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0009_10202030_2017102511"], "sizeBytes": 1320229744}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:77fdddea2964440d400104f4a81e763d297b53dc5e6cb3d33e8bc7d89bc0a473", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1307948018}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:191f93117f651ed9e2f048d18a741b9de7aab0f4b88b4f739e6a473c64ec097f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0010_11172200"], "sizeBytes": 1306224934}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6b154038b64a13d2ceeee6b53592a52fd1041539d4019cfea29c84e41bd6001c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0009_09300310"], "sizeBytes": 1299092342}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:471d0c97928fdf25f005388dbbcd04c0b6a47b7f908ac505ad508291170e13c4", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1295975169}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:d21e6b24e4769111a181baaed7040ea424e0ae1f7fffe786e4f84cc6c8a45857", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11b024"], "sizeBytes": 1294994735}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:d28bbb0e20a4eea91e6a6ab922be54db464130d762ea4171874b89ed7966ea52", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1289171047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:81cf4d768da0be2021ce8cd6adb1fb5fe71a80e9a5de563af5d79b10d87890dc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1280803609}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:ebf129985201207bf81fd78db53510900ee07cb11d89434e09fefa7008b78b91"], "sizeBytes": 1277251039}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:8dd0ea23334b73e20866e9b1fdf0bfca4d7b0180f63709ce580845dc33354978", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0009_09291730"], "sizeBytes": 1274929375}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:d05be33d018496c0fc34be16be6df58bea623f684505e020330b32b8e6c65242", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0009_09291817"], "sizeBytes": 1271551462}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:b0418b196d7df4bc7f8cc1d5dadb6da3e0795ce1b91f0ee2acdea34e03bd1052", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0009_10202000_2017102511"], "sizeBytes": 1261825776}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:8310975166901870a3faa50da81e77bffd3b003c569715657a0553b83605094f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0009_09291735"], "sizeBytes": 1248782412}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:ad23d0a1c8a9a26994fee243051f41641ae7f3e8aedfa743f37fcc7df1b7b80e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0009_09291830"], "sizeBytes": 1241965328}], "nodeState": {}, "hostname": "prdocker05"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "148f494d-9435-11e7-8ff2-fa163e99a979", "resourceVersion": "41682072", "creationTimestamp": "2017-09-08T01:28:55Z", "labels": {"AppDefaultResourceType": "Normal", "BESResourceType": "Normal", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-11-22T07:06:41Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-09-08T01:28:55Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-09-08T01:28:55Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-09-08T01:28:55Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-11-22T07:06:41Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "3230A363-584B-4B9B-971A-2878FF0B54F3", "bootID": "7577293b-c278-4f55-af9d-45099f55240a", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:968aa92f65cd933b2410261099b4319e685f9360b39f74b62fd60546574f6854", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 4222357091}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:50d05dbca580b0ba3c58c5976d98394e3ecc04d30690f1b57b5d1e7adda2a174", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008"], "sizeBytes": 3959444519}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:86297349ed4267db06361f175258e748b74714b286c002f2ad69eee75cd990bb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3061639616}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:30ba975a6bd6cda8338a69e48434a2d038038dbc9acecb4d361f9e2635b7a642", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3056445206}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:4272a71a91738b27f6d83a54017d9f14d1bb8d6c8bb993ba04c677e86c45478c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008"], "sizeBytes": 2988216417}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:a1cad0635ee5cd7d503a2c1437eb1bd3d412d939942e4e51b9511556abdf10fe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0008"], "sizeBytes": 2836528915}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:9919eefcff2fbdbaa82593545df371930143822750f27850d0aafe401ee32add", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2811609391}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:6048139c60ceb3c9b0fb2e7a591c5c7f2e01147d35045c05b5cd31f61cb66922", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11172200"], "sizeBytes": 2764600281}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:f58f8716497ae0743cc58505b3afad38ffd36a9a9c429ddf205c563bdcb26863", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2371593185}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:0e19b71595f3762b7cbde603f7318c528c77ab47955b767be70d09d0c0c50c25", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2346599590}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5ed371dd08dd88cb9e95b422d45fe5ebc2806696db3155d8b84c7b89bf6df754", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2249861035}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:bd52b2923bd9e4f82658606b320855f039290cd2f6a717246f2871a0affe138e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11031700"], "sizeBytes": 2233091696}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:6f573a7af65f4c37a1d6ac08dd2ff80915b222905c42962a77ac5f76550ffe70", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2218876394}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:6ba98d948aeb945eb803a147a8c5e867a9ba76233c075efe94c180930f674dec"], "sizeBytes": 2212409238}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:f354780441a9eabd4aa8f004cf614e3e3ccb2993aaaf7810c795d931057eadcf", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2192387667}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:428b66ed8fe30ded4a123d2241cdb57d438b8be4f37aa982b39dca0b6137922d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 2149059722}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:a602a05af317312f4f4576fbe56769542f96cd76b9dd9bf172a1a693c56b1bba", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0009_10201700_2017102511"], "sizeBytes": 2144710404}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:70434e954ed031014447cf8dc623d6f6a9e75629271134877e23516b63a2596b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1734205878}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:6c158dc37f48bb324f0817162a1ba1c8558965d53582e32a10bfe71dd002ac6e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0009_092900"], "sizeBytes": 1696583736}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:566c0934d8e267c6b944b01156609761e6be38223aa38ca093da5282d8ae5dcf", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0008"], "sizeBytes": 1632244881}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:bd0d16829c3bfdf4cc249b9072959c2d3b17da7c019985210126ecd759e66669", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1572610477}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:4a5e8eb8b11e6b8fc9191b60839240fe2c887426c3e87314c2fed503f8bb62ab", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11101514_2017111317"], "sizeBytes": 1568121394}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:70d79ef389eb8037ac9ea94b4eb930c662f75eba6bb3333c7144c772bebb2cef", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_11031815"], "sizeBytes": 1548687126}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:142104ce6fc98dc9af2b8e9c3ddf978661c8c1a564edd1cccf1b85ca819e4507", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1546121274}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:8ddc0faddaf7b79285766691cde9ba511eb39df54627536a8469e85aded8ecbb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0009_09300330"], "sizeBytes": 1530378254}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:271567f4ed1926b21d0831269f6f07b0a5ed09a42ca61818ff74ce5128c85b45"], "sizeBytes": 1521806676}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:edc4f7b16fec29e335508908f6dc2417f145866a48c1d5e5794205f15e0dc57b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0009_09300530"], "sizeBytes": 1484207289}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:c449e3c5c2588ae8df4c8678fa58706ee779e42337ce7016ccb8aaf08d7a9908", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0009_09291840"], "sizeBytes": 1473453291}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:3ce140568c09f8887866c6bdd870c64aeea4706b81c694acc83ffec4f665175e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11031800"], "sizeBytes": 1340567032}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:74bd94a69dd1e7ea61fd4b5053bfc5124924006f3a9893847f5cddc7afca2d97", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0010_1108_2017111317"], "sizeBytes": 1339141433}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:2ce6589d144079bae33dc9ddc8c924f1c9e346fa05cd558343f08a3a70d799bc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1320862914}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:e027b521ec22dc8beb2de4db42d3211c270ade2197309b66cd94ca600ff889f9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1319512571}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:00c651abe06b00bb4b15293303d0de9225eb87accaaf0f456cd998bb0e33c35a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0009_10202000_2017102511"], "sizeBytes": 1308825221}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:191f93117f651ed9e2f048d18a741b9de7aab0f4b88b4f739e6a473c64ec097f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0010_11172200"], "sizeBytes": 1306224934}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6b154038b64a13d2ceeee6b53592a52fd1041539d4019cfea29c84e41bd6001c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0009_09300310"], "sizeBytes": 1299092342}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:471d0c97928fdf25f005388dbbcd04c0b6a47b7f908ac505ad508291170e13c4", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1295975169}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:4c85614d3ec7df8eca0643e78dcb23d3de3fafc4bfaa24d6559a750863228825", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11b024"], "sizeBytes": 1295223056}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:d21e6b24e4769111a181baaed7040ea424e0ae1f7fffe786e4f84cc6c8a45857"], "sizeBytes": 1294994735}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:0a482ebf19a8458d4c15cebd23ae0ed0c213ae7065d97494a645619ef7b66262"], "sizeBytes": 1291567500}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:5875de68092a1912924958ee1a083adb01a40b8bd4a7e06c2bd61e5686e227d0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11"], "sizeBytes": 1291515726}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:b3edc680f6c124a1cb15eb67c19b84ed1f5b2313c1498c4b9416fb0d751ffec6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11092000_2017111317"], "sizeBytes": 1287607731}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:81cf4d768da0be2021ce8cd6adb1fb5fe71a80e9a5de563af5d79b10d87890dc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1280803609}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:d05be33d018496c0fc34be16be6df58bea623f684505e020330b32b8e6c65242", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0009_09291817"], "sizeBytes": 1271551462}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:417622ea491fa7695be2a5bac50b1d0c868b8ef1b54721d2807b310ef6ce5f1c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0009_10210506_2017102511"], "sizeBytes": 1258938305}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:8310975166901870a3faa50da81e77bffd3b003c569715657a0553b83605094f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0009_09291735"], "sizeBytes": 1248782412}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:cc35d1ed11496ded1509c21cbdf59fffa1e7010da31fff14a5397e09c3eb300a"], "sizeBytes": 1244532083}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:ad23d0a1c8a9a26994fee243051f41641ae7f3e8aedfa743f37fcc7df1b7b80e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0009_09291830"], "sizeBytes": 1241965328}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:13df06abe624e9e5b757cc2e55f4aeae18175c140b1efdf209117ef7e54526c6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1241137758}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:2c890f0f3538e7e9831f663b86c84acffdd05d82950bc1391bce81cbb1279fd0"], "sizeBytes": 1237715246}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:07e6bc04c33cda21e05b8eec84a208a00fa8301912e959ef167cd513f8322717", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0009"], "sizeBytes": 1236171837}], "nodeState": {}, "hostname": "prdocker06"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "c81fd883-9436-11e7-b1a3-fa163e4a66d8", "resourceVersion": "41682078", "creationTimestamp": "2017-09-08T01:41:06Z", "labels": {"AppDefaultResourceType": "Normal", "BESResourceType": "Normal", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115568Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115568Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:03Z", "lastTransitionTime": "2017-11-22T07:06:39Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:03Z", "lastTransitionTime": "2017-09-08T01:41:06Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:03Z", "lastTransitionTime": "2017-09-08T01:41:06Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:03Z", "lastTransitionTime": "2017-09-08T01:41:06Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:03Z", "lastTransitionTime": "2017-11-22T07:06:39Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "C7D2EACE-C173-4B84-8841-D3007E825A85", "bootID": "e7a8229a-d332-4227-9aef-48a4a975eca1", "kernelVersion": "4.4.74-92.35-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:50d05dbca580b0ba3c58c5976d98394e3ecc04d30690f1b57b5d1e7adda2a174", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008"], "sizeBytes": 3959444519}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:edc2fed28420dcfa19d25b5e6c2e9101da0fdf7645c765a42d85a975a9fe6fb2", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3611808153}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:86297349ed4267db06361f175258e748b74714b286c002f2ad69eee75cd990bb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3061639616}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:30ba975a6bd6cda8338a69e48434a2d038038dbc9acecb4d361f9e2635b7a642", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3056445206}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:4272a71a91738b27f6d83a54017d9f14d1bb8d6c8bb993ba04c677e86c45478c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008"], "sizeBytes": 2988216417}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:a1cad0635ee5cd7d503a2c1437eb1bd3d412d939942e4e51b9511556abdf10fe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0008"], "sizeBytes": 2836528915}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:53dc6ce7b6a46af258fea1d3d7e508131ae843029248d6dc9bc8ddaf060995f0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 2830511269}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:9919eefcff2fbdbaa82593545df371930143822750f27850d0aafe401ee32add", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2811609391}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5c9c814d9032c7b1d3b7d1c81a6a29acbefeeb869ea42281105eb78909b26ca2", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11101600_2017111317"], "sizeBytes": 2613738651}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:2b732c57b71cc05c7bb051038c398c7ea01f87733dc8cbb93bd32056a0ab7404", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2493590740}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:0907c22556db9b912f979a03d0594ffd40089973dcdb3a9876cd99772adc4234", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2402074256}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:f58f8716497ae0743cc58505b3afad38ffd36a9a9c429ddf205c563bdcb26863", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2371593185}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:6f573a7af65f4c37a1d6ac08dd2ff80915b222905c42962a77ac5f76550ffe70", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2218876394}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:f354780441a9eabd4aa8f004cf614e3e3ccb2993aaaf7810c795d931057eadcf", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2192387667}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:428b66ed8fe30ded4a123d2241cdb57d438b8be4f37aa982b39dca0b6137922d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 2149059722}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:da133564d6fff16c619ebe18aaf4e8ffc16b176173aceee452c2adde2ede5bbe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 2149050768}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:a602a05af317312f4f4576fbe56769542f96cd76b9dd9bf172a1a693c56b1bba", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0009_10201700_2017102511"], "sizeBytes": 2144710404}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:ff3a0b0e390741d4ea69908cf5092a9df8a69a65476349cdf411837c09abf525"], "sizeBytes": 2081588109}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:6c158dc37f48bb324f0817162a1ba1c8558965d53582e32a10bfe71dd002ac6e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0009_092900"], "sizeBytes": 1696583736}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:bd0d16829c3bfdf4cc249b9072959c2d3b17da7c019985210126ecd759e66669", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1572610477}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:2d711c4c7e94483847d8cf85a3cec57ee63dac3e8c7017f5593dd3c6c8ec1579", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_11031815"], "sizeBytes": 1547418834}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:142104ce6fc98dc9af2b8e9c3ddf978661c8c1a564edd1cccf1b85ca819e4507", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1546121274}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:8ddc0faddaf7b79285766691cde9ba511eb39df54627536a8469e85aded8ecbb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0009_09300330"], "sizeBytes": 1530378254}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:edc4f7b16fec29e335508908f6dc2417f145866a48c1d5e5794205f15e0dc57b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0009_09300530"], "sizeBytes": 1484207289}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:b7f5dae492eba3106d9493823cc1995f655b725edbb45dee7428b401246c86f9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0009_09300536"], "sizeBytes": 1483758968}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:c449e3c5c2588ae8df4c8678fa58706ee779e42337ce7016ccb8aaf08d7a9908", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0009_09291840"], "sizeBytes": 1473453291}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:4cc87a8560fb89cf3b7ab8828993c5d53dba6fc5ddd353824452d32758c47e2d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1473037369}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:bf5f9642502f46a509862b6016e050d8275ebd2f9730a3d05ff1aac3ffcac3ba", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1472786453}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:82ec4acde356514caa77039503d4c49700c559b23087d351911d6d8c6b9f4777"], "sizeBytes": 1460134008}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:2ea3beb2f52898dcc04b067e9f440b7aa23f0073a368a6e15f91d6fb11ae362c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11172250"], "sizeBytes": 1347384973}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:472e959f254368315a9669a6cc2805a682e7da12608412d36752043e1d856626", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0010_11162000"], "sizeBytes": 1340834771}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:e0596079828ad21e5e0266c2069f5c06ba1e139cdfcd67197a3f4ca0f694dd43", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0010_11031640"], "sizeBytes": 1339690362}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:070c72b4d88a8992391e0eee0016a0b27e02a8e75fbfc06339d978da631d8581", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11031800"], "sizeBytes": 1339302858}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:74bd94a69dd1e7ea61fd4b5053bfc5124924006f3a9893847f5cddc7afca2d97", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0010_1108_2017111317"], "sizeBytes": 1339141433}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:08ac7622d03d6f61ada282da8c80d89c555794316bdaa1aa98c777b74c1f5e72", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0010_11162230"], "sizeBytes": 1322553429}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:ad8ca18721e8f9932a940be8b33680b53de711334d7f6a58bc70a4c4ca052f94", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1316898047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:77fdddea2964440d400104f4a81e763d297b53dc5e6cb3d33e8bc7d89bc0a473", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1307948018}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:5f51b9c372eacfbcbd377f329462fa6efdbba722aece2814749137fc817c2fd6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0010_11091834_2017111317"], "sizeBytes": 1306824875}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6b154038b64a13d2ceeee6b53592a52fd1041539d4019cfea29c84e41bd6001c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0009_09300310"], "sizeBytes": 1299092342}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:471d0c97928fdf25f005388dbbcd04c0b6a47b7f908ac505ad508291170e13c4", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1295975169}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:4c85614d3ec7df8eca0643e78dcb23d3de3fafc4bfaa24d6559a750863228825", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11b024"], "sizeBytes": 1295223056}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:aceae32196902b5d221c8c3d972e0367a0d42d7b70eaebc9b8ceb45de904960d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11022200"], "sizeBytes": 1287603633}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:8dd0ea23334b73e20866e9b1fdf0bfca4d7b0180f63709ce580845dc33354978", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0009_09291730"], "sizeBytes": 1274929375}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:d05be33d018496c0fc34be16be6df58bea623f684505e020330b32b8e6c65242", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0009_09291817"], "sizeBytes": 1271551462}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:fb260ee38ba79757fc6a93ac21707660c5da76ecf4c9d48ab655ba8ab70c1e79"], "sizeBytes": 1270679099}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:bb3e93109429e543ab51afac594b8c45b12b585fd90d9b4a271baa522f52f021"], "sizeBytes": 1258153512}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:8310975166901870a3faa50da81e77bffd3b003c569715657a0553b83605094f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0009_09291735"], "sizeBytes": 1248782412}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:cc35d1ed11496ded1509c21cbdf59fffa1e7010da31fff14a5397e09c3eb300a"], "sizeBytes": 1244532083}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:ad23d0a1c8a9a26994fee243051f41641ae7f3e8aedfa743f37fcc7df1b7b80e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0009_09291830"], "sizeBytes": 1241965328}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_jetmq@sha256:2a6afbe4280e6dfb3dc7b1f6aec6c05c19453e097f18d34a015a2e47cd2f1314", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_jetmq:v600r001c11"], "sizeBytes": 1239057631}], "nodeState": {}, "hostname": "prdocker07"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "ccbb11fd-9436-11e7-8ff2-fa163e99a979", "resourceVersion": "41682106", "creationTimestamp": "2017-09-08T01:41:14Z", "labels": {"AppDefaultResourceType": "Normal", "BESResourceType": "Normal", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:07Z", "lastTransitionTime": "2017-11-22T07:06:38Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:07Z", "lastTransitionTime": "2017-09-08T01:41:14Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:07Z", "lastTransitionTime": "2017-09-08T01:41:14Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:07Z", "lastTransitionTime": "2017-09-08T01:41:14Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:07Z", "lastTransitionTime": "2017-11-22T07:06:38Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "1446B3F5-D540-42D8-B07A-DAD788BE56B9", "bootID": "785d9dc1-0e4b-4a44-ab10-23d277538701", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:50d05dbca580b0ba3c58c5976d98394e3ecc04d30690f1b57b5d1e7adda2a174", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008"], "sizeBytes": 3959444519}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:edc2fed28420dcfa19d25b5e6c2e9101da0fdf7645c765a42d85a975a9fe6fb2", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3611808153}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:662a469c98ec52dee363a76671634203a90b1ff9721a288349ab320cb036d350", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 3150200466}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:b686cee3eeb31d4fc94c2efa6216f6fd09517e37ecc491c0b2eac61d48e21f06", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0009_10202100_2017102511"], "sizeBytes": 3072304575}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:86297349ed4267db06361f175258e748b74714b286c002f2ad69eee75cd990bb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3061639616}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:4272a71a91738b27f6d83a54017d9f14d1bb8d6c8bb993ba04c677e86c45478c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008"], "sizeBytes": 2988216417}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:a1cad0635ee5cd7d503a2c1437eb1bd3d412d939942e4e51b9511556abdf10fe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0008"], "sizeBytes": 2836528915}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:9919eefcff2fbdbaa82593545df371930143822750f27850d0aafe401ee32add", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2811609391}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:17ab37ec440a2d995be6aae94e3e7900e2374f1fb93cce520553c456e41dc30a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0009_10202100_2017102511"], "sizeBytes": 2683559702}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:afa03a0788f543345c2da30cc8399223720071de9398b0aa82a42c5f62721815", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11101600_2017111317"], "sizeBytes": 2644317263}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:f58f8716497ae0743cc58505b3afad38ffd36a9a9c429ddf205c563bdcb26863", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2371593185}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5ed371dd08dd88cb9e95b422d45fe5ebc2806696db3155d8b84c7b89bf6df754", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2249861035}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:601686830ac50df2a7cd2b33d1ec05f3be63254b164ccb1d808c100fdc16ecf7"], "sizeBytes": 2248052637}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:6f573a7af65f4c37a1d6ac08dd2ff80915b222905c42962a77ac5f76550ffe70", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2218876394}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:6ba98d948aeb945eb803a147a8c5e867a9ba76233c075efe94c180930f674dec"], "sizeBytes": 2212409238}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:b9feb97c3708f88340282675535b320c2b17eb6f2b4ec77f6a5a426dd7ac935b"], "sizeBytes": 2160854595}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:428b66ed8fe30ded4a123d2241cdb57d438b8be4f37aa982b39dca0b6137922d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 2149059722}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:da133564d6fff16c619ebe18aaf4e8ffc16b176173aceee452c2adde2ede5bbe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 2149050768}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:ff3a0b0e390741d4ea69908cf5092a9df8a69a65476349cdf411837c09abf525"], "sizeBytes": 2081588109}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:21d898165876999963297dc279cc6b9a87325b734c34a7300a175136deb47fe5", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11031128"], "sizeBytes": 1736797489}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:e2c8b9218eccb99566c455ada1e51b3e31868cd361e29390cb589f8f56e6be9f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1734624308}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:17569cec11d34a6771dd6d72a29a0c2130e1ba8a668f014f784649476f1b2a0e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_1711101700_2017111317"], "sizeBytes": 1731285845}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:6c158dc37f48bb324f0817162a1ba1c8558965d53582e32a10bfe71dd002ac6e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0009_092900"], "sizeBytes": 1696583736}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:1769a83d4507578b56d69c925452bf532f1b0387b4a27d6bb3d0eb783cbe08ac"], "sizeBytes": 1683833490}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:4dd55e19ba082eaa8d26c05f1d8c0cb679bdcd1f0e025cc47d6e590b2a4d0248", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1666936762}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_console_suse11sp3@sha256:48b050361b066d50134d1e4643d3c0ca50b89ff45e5a193c4e2bdc08ba14aec6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_console_suse11sp3:v600r001c11b024"], "sizeBytes": 1638790330}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:566c0934d8e267c6b944b01156609761e6be38223aa38ca093da5282d8ae5dcf", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0008"], "sizeBytes": 1632244881}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:bd0d16829c3bfdf4cc249b9072959c2d3b17da7c019985210126ecd759e66669", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1572610477}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:2d711c4c7e94483847d8cf85a3cec57ee63dac3e8c7017f5593dd3c6c8ec1579", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_11031815"], "sizeBytes": 1547418834}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:142104ce6fc98dc9af2b8e9c3ddf978661c8c1a564edd1cccf1b85ca819e4507", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1546121274}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:5bf0a024560ddd03192a3664cac21874b36c5c3bd92f7ed485fa73999c478c0c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1535847110}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:8ddc0faddaf7b79285766691cde9ba511eb39df54627536a8469e85aded8ecbb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0009_09300330"], "sizeBytes": 1530378254}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:271567f4ed1926b21d0831269f6f07b0a5ed09a42ca61818ff74ce5128c85b45"], "sizeBytes": 1521806676}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:edc4f7b16fec29e335508908f6dc2417f145866a48c1d5e5794205f15e0dc57b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0009_09300530"], "sizeBytes": 1484207289}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:aad19ed59b0e80fd5d6f68c823fd7f8e26baab6aa68ad5f533e295b63d0b8b2a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0009_09300536"], "sizeBytes": 1483802821}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:bf5f9642502f46a509862b6016e050d8275ebd2f9730a3d05ff1aac3ffcac3ba", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1472786453}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:82ec4acde356514caa77039503d4c49700c559b23087d351911d6d8c6b9f4777", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0009_09291840"], "sizeBytes": 1460134008}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:2ea3beb2f52898dcc04b067e9f440b7aa23f0073a368a6e15f91d6fb11ae362c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11172250"], "sizeBytes": 1347384973}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:3ce140568c09f8887866c6bdd870c64aeea4706b81c694acc83ffec4f665175e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11031800"], "sizeBytes": 1340567032}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:e0596079828ad21e5e0266c2069f5c06ba1e139cdfcd67197a3f4ca0f694dd43", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0010_11031640"], "sizeBytes": 1339690362}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:08ac7622d03d6f61ada282da8c80d89c555794316bdaa1aa98c777b74c1f5e72", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0010_11162230"], "sizeBytes": 1322553429}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:e027b521ec22dc8beb2de4db42d3211c270ade2197309b66cd94ca600ff889f9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1319512571}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:5f51b9c372eacfbcbd377f329462fa6efdbba722aece2814749137fc817c2fd6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0010_11091834_2017111317"], "sizeBytes": 1306824875}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:191f93117f651ed9e2f048d18a741b9de7aab0f4b88b4f739e6a473c64ec097f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0010_11172200"], "sizeBytes": 1306224934}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6b154038b64a13d2ceeee6b53592a52fd1041539d4019cfea29c84e41bd6001c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0009_09300310"], "sizeBytes": 1299092342}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:5875de68092a1912924958ee1a083adb01a40b8bd4a7e06c2bd61e5686e227d0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11"], "sizeBytes": 1291515726}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:d28bbb0e20a4eea91e6a6ab922be54db464130d762ea4171874b89ed7966ea52", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1289171047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:b3edc680f6c124a1cb15eb67c19b84ed1f5b2313c1498c4b9416fb0d751ffec6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11092000_2017111317"], "sizeBytes": 1287607731}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:aceae32196902b5d221c8c3d972e0367a0d42d7b70eaebc9b8ceb45de904960d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11022200"], "sizeBytes": 1287603633}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:ebf129985201207bf81fd78db53510900ee07cb11d89434e09fefa7008b78b91"], "sizeBytes": 1277251039}], "nodeState": {}, "hostname": "prdocker08"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "ce8723b3-9436-11e7-b1a3-fa163e4a66d8", "resourceVersion": "41682073", "creationTimestamp": "2017-09-08T01:41:17Z", "labels": {"AppDefaultResourceType": "Normal", "BESResourceType": "Normal", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-11-22T07:06:32Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-09-08T01:41:17Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-09-08T01:41:17Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-09-08T01:41:17Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-11-22T07:06:32Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "DCC65FB2-BC65-4E2F-8E8E-47EB55B7F646", "bootID": "f603f843-ce71-4121-8e46-18d15d6804ae", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:968aa92f65cd933b2410261099b4319e685f9360b39f74b62fd60546574f6854", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 4222357091}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:2ad76c611ec100d9756ca6405020e044c11fa3e7d1a96c95fcc653b486f5372f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3884157513}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:edc2fed28420dcfa19d25b5e6c2e9101da0fdf7645c765a42d85a975a9fe6fb2", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3611808153}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:30ba975a6bd6cda8338a69e48434a2d038038dbc9acecb4d361f9e2635b7a642", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3056445206}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:4272a71a91738b27f6d83a54017d9f14d1bb8d6c8bb993ba04c677e86c45478c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008"], "sizeBytes": 2988216417}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:53dc6ce7b6a46af258fea1d3d7e508131ae843029248d6dc9bc8ddaf060995f0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 2830511269}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:9919eefcff2fbdbaa82593545df371930143822750f27850d0aafe401ee32add", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2811609391}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f62fe5644220b084eb8931292daab8ae1fe11d2b9a6cbfd6b859f61a3fcc8bdc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2494377012}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:2b732c57b71cc05c7bb051038c398c7ea01f87733dc8cbb93bd32056a0ab7404", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2493590740}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:0e19b71595f3762b7cbde603f7318c528c77ab47955b767be70d09d0c0c50c25", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2346599590}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5ed371dd08dd88cb9e95b422d45fe5ebc2806696db3155d8b84c7b89bf6df754", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2249861035}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:6f573a7af65f4c37a1d6ac08dd2ff80915b222905c42962a77ac5f76550ffe70", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2218876394}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:6ba98d948aeb945eb803a147a8c5e867a9ba76233c075efe94c180930f674dec", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2212409238}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:f354780441a9eabd4aa8f004cf614e3e3ccb2993aaaf7810c795d931057eadcf", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2192387667}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:c4aca17d0cc72a744c2a5cdeb3a86a363beb53b2b1f667f6f7ba7281825dcbf2"], "sizeBytes": 2172691820}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:b9feb97c3708f88340282675535b320c2b17eb6f2b4ec77f6a5a426dd7ac935b"], "sizeBytes": 2160854595}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:da133564d6fff16c619ebe18aaf4e8ffc16b176173aceee452c2adde2ede5bbe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 2149050768}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:21d898165876999963297dc279cc6b9a87325b734c34a7300a175136deb47fe5", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11031128"], "sizeBytes": 1736797489}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:e2c8b9218eccb99566c455ada1e51b3e31868cd361e29390cb589f8f56e6be9f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1734624308}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:6c158dc37f48bb324f0817162a1ba1c8558965d53582e32a10bfe71dd002ac6e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0009_092900"], "sizeBytes": 1696583736}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:1769a83d4507578b56d69c925452bf532f1b0387b4a27d6bb3d0eb783cbe08ac"], "sizeBytes": 1683833490}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:4dd55e19ba082eaa8d26c05f1d8c0cb679bdcd1f0e025cc47d6e590b2a4d0248", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1666936762}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:bd0d16829c3bfdf4cc249b9072959c2d3b17da7c019985210126ecd759e66669", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1572610477}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:19cec444a278531cea656cc354fbc4356aa4679a1e7262cb854fb3cd82643a7e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1535830580}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:8ddc0faddaf7b79285766691cde9ba511eb39df54627536a8469e85aded8ecbb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0009_09300330"], "sizeBytes": 1530378254}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:271567f4ed1926b21d0831269f6f07b0a5ed09a42ca61818ff74ce5128c85b45"], "sizeBytes": 1521806676}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:5a5e4534ecd9234e3409d06447bcb583b781531013bea8bb86e2e39c103b45f8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0010_11021405"], "sizeBytes": 1496026826}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:edc4f7b16fec29e335508908f6dc2417f145866a48c1d5e5794205f15e0dc57b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0009_09300530"], "sizeBytes": 1484207289}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:aad19ed59b0e80fd5d6f68c823fd7f8e26baab6aa68ad5f533e295b63d0b8b2a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0009_09300536"], "sizeBytes": 1483802821}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f7c9dc8d2905ef21c68cd3b191a07131a91db53804f762221eedf947f7acfae0"], "sizeBytes": 1475408329}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:c449e3c5c2588ae8df4c8678fa58706ee779e42337ce7016ccb8aaf08d7a9908", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0009_09291840"], "sizeBytes": 1473453291}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:4cc87a8560fb89cf3b7ab8828993c5d53dba6fc5ddd353824452d32758c47e2d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1473037369}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:cafdbfe9c5f7299e648c73f48f9ace06cec89209a26e6e753abffb410ef28927", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0009_10202030_2017102511"], "sizeBytes": 1469327042}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:82ec4acde356514caa77039503d4c49700c559b23087d351911d6d8c6b9f4777"], "sizeBytes": 1460134008}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:793a9f8f27a51e5bae92dd27990df59e88bd393dc6925fda67116c005c0168b3", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1435422745}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:0cb382aa5a199a7d1b0029fb0421d7f749d0662dcaa637083effa83a2526bce3", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0009_10202016_2017102511"], "sizeBytes": 1346097982}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:e0596079828ad21e5e0266c2069f5c06ba1e139cdfcd67197a3f4ca0f694dd43", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0010_11031640"], "sizeBytes": 1339690362}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:070c72b4d88a8992391e0eee0016a0b27e02a8e75fbfc06339d978da631d8581", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11031800"], "sizeBytes": 1339302858}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:08ac7622d03d6f61ada282da8c80d89c555794316bdaa1aa98c777b74c1f5e72", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0010_11162230"], "sizeBytes": 1322553429}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:e027b521ec22dc8beb2de4db42d3211c270ade2197309b66cd94ca600ff889f9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1319512571}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:ad8ca18721e8f9932a940be8b33680b53de711334d7f6a58bc70a4c4ca052f94", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1316898047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:77fdddea2964440d400104f4a81e763d297b53dc5e6cb3d33e8bc7d89bc0a473", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1307948018}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6b154038b64a13d2ceeee6b53592a52fd1041539d4019cfea29c84e41bd6001c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0009_09300310"], "sizeBytes": 1299092342}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:d21e6b24e4769111a181baaed7040ea424e0ae1f7fffe786e4f84cc6c8a45857", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11b024"], "sizeBytes": 1294994735}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:0a482ebf19a8458d4c15cebd23ae0ed0c213ae7065d97494a645619ef7b66262"], "sizeBytes": 1291567500}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:5875de68092a1912924958ee1a083adb01a40b8bd4a7e06c2bd61e5686e227d0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11"], "sizeBytes": 1291515726}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:81cf4d768da0be2021ce8cd6adb1fb5fe71a80e9a5de563af5d79b10d87890dc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1280803609}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:ebf129985201207bf81fd78db53510900ee07cb11d89434e09fefa7008b78b91"], "sizeBytes": 1277251039}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:8dd0ea23334b73e20866e9b1fdf0bfca4d7b0180f63709ce580845dc33354978", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0009_09291730"], "sizeBytes": 1274929375}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:d05be33d018496c0fc34be16be6df58bea623f684505e020330b32b8e6c65242", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0009_09291817"], "sizeBytes": 1271551462}], "nodeState": {}, "hostname": "prdocker09"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "d1825a63-9436-11e7-b1a3-fa163e4a66d8", "resourceVersion": "41682082", "creationTimestamp": "2017-09-08T01:41:22Z", "labels": {"AppDefaultResourceType": "Normal", "BESResourceType": "Normal", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-11-22T07:06:41Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-09-08T01:41:22Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-09-08T01:41:22Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-09-08T01:41:22Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-11-22T07:06:41Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "D02B2F90-F13B-459C-9329-2F8A9A49FBB3", "bootID": "dc8b6cca-995b-487c-8d0b-5b7793fd0648", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:662a469c98ec52dee363a76671634203a90b1ff9721a288349ab320cb036d350", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 3150200466}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:4272a71a91738b27f6d83a54017d9f14d1bb8d6c8bb993ba04c677e86c45478c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008"], "sizeBytes": 2988216417}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:a1cad0635ee5cd7d503a2c1437eb1bd3d412d939942e4e51b9511556abdf10fe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0008"], "sizeBytes": 2836528915}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:53dc6ce7b6a46af258fea1d3d7e508131ae843029248d6dc9bc8ddaf060995f0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 2830511269}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:9919eefcff2fbdbaa82593545df371930143822750f27850d0aafe401ee32add", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2811609391}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:6048139c60ceb3c9b0fb2e7a591c5c7f2e01147d35045c05b5cd31f61cb66922", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11172200"], "sizeBytes": 2764600281}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:afa03a0788f543345c2da30cc8399223720071de9398b0aa82a42c5f62721815", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11101600_2017111317"], "sizeBytes": 2644317263}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5c9c814d9032c7b1d3b7d1c81a6a29acbefeeb869ea42281105eb78909b26ca2", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11101600_2017111317"], "sizeBytes": 2613738651}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f62fe5644220b084eb8931292daab8ae1fe11d2b9a6cbfd6b859f61a3fcc8bdc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2494377012}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:2b732c57b71cc05c7bb051038c398c7ea01f87733dc8cbb93bd32056a0ab7404", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2493590740}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:f58f8716497ae0743cc58505b3afad38ffd36a9a9c429ddf205c563bdcb26863", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2371593185}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:b454004dfe2966fd239bea04e253f29e93c78149d5df6b2f0500b11f147b0e4c"], "sizeBytes": 2370393292}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:0e19b71595f3762b7cbde603f7318c528c77ab47955b767be70d09d0c0c50c25", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2346599590}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:232f4fc637817e426b8ba73317fe97a6e9f82d58616fd3ba4f383baaf9dd23c9"], "sizeBytes": 2346583060}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5ed371dd08dd88cb9e95b422d45fe5ebc2806696db3155d8b84c7b89bf6df754", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2249861035}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:4bce750304f3ea75e10442c26b142d69c04b7d6885946c544d2bf982337997f2", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11031700"], "sizeBytes": 2233075166}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:6f573a7af65f4c37a1d6ac08dd2ff80915b222905c42962a77ac5f76550ffe70", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2218876394}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:6ba98d948aeb945eb803a147a8c5e867a9ba76233c075efe94c180930f674dec"], "sizeBytes": 2212409238}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:f354780441a9eabd4aa8f004cf614e3e3ccb2993aaaf7810c795d931057eadcf", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2192387667}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:c4aca17d0cc72a744c2a5cdeb3a86a363beb53b2b1f667f6f7ba7281825dcbf2"], "sizeBytes": 2172691820}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:b9feb97c3708f88340282675535b320c2b17eb6f2b4ec77f6a5a426dd7ac935b"], "sizeBytes": 2160854595}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:a1d3fb3df22bfa89db2424d3793c4a5a8b80735caab284e15b3c3c0f93846855", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1902228249}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:17569cec11d34a6771dd6d72a29a0c2130e1ba8a668f014f784649476f1b2a0e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_1711101700_2017111317"], "sizeBytes": 1731285845}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:70d79ef389eb8037ac9ea94b4eb930c662f75eba6bb3333c7144c772bebb2cef", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_11031815"], "sizeBytes": 1548687126}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:142104ce6fc98dc9af2b8e9c3ddf978661c8c1a564edd1cccf1b85ca819e4507", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1546121274}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:271567f4ed1926b21d0831269f6f07b0a5ed09a42ca61818ff74ce5128c85b45", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0009_09300330"], "sizeBytes": 1521806676}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:5a5e4534ecd9234e3409d06447bcb583b781531013bea8bb86e2e39c103b45f8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0010_11021405"], "sizeBytes": 1496026826}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f7c9dc8d2905ef21c68cd3b191a07131a91db53804f762221eedf947f7acfae0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0009_09300530"], "sizeBytes": 1475408329}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:4114a9b8cd05b7a50abe0e5baa402588446b77c75b828824150ec88c9e8b78d6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0009_09300536"], "sizeBytes": 1475187390}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:c449e3c5c2588ae8df4c8678fa58706ee779e42337ce7016ccb8aaf08d7a9908", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0009_09291840"], "sizeBytes": 1473453291}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:bf5f9642502f46a509862b6016e050d8275ebd2f9730a3d05ff1aac3ffcac3ba", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1472786453}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:793a9f8f27a51e5bae92dd27990df59e88bd393dc6925fda67116c005c0168b3", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1435422745}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6235cd4d031d4b43a4e7d47f12696827b971c373b0b741b4241296c9947f8329", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11102300_2017111317"], "sizeBytes": 1350229449}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:2ea3beb2f52898dcc04b067e9f440b7aa23f0073a368a6e15f91d6fb11ae362c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11172250"], "sizeBytes": 1347384973}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:e0596079828ad21e5e0266c2069f5c06ba1e139cdfcd67197a3f4ca0f694dd43", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0010_11031640"], "sizeBytes": 1339690362}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:070c72b4d88a8992391e0eee0016a0b27e02a8e75fbfc06339d978da631d8581", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11031800"], "sizeBytes": 1339302858}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:08ac7622d03d6f61ada282da8c80d89c555794316bdaa1aa98c777b74c1f5e72", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0010_11162230"], "sizeBytes": 1322553429}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:ad8ca18721e8f9932a940be8b33680b53de711334d7f6a58bc70a4c4ca052f94", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1316898047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:00c651abe06b00bb4b15293303d0de9225eb87accaaf0f456cd998bb0e33c35a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0009_10202000_2017102511"], "sizeBytes": 1308825221}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6b154038b64a13d2ceeee6b53592a52fd1041539d4019cfea29c84e41bd6001c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0009_09300310"], "sizeBytes": 1299092342}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:471d0c97928fdf25f005388dbbcd04c0b6a47b7f908ac505ad508291170e13c4", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1295975169}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:0a482ebf19a8458d4c15cebd23ae0ed0c213ae7065d97494a645619ef7b66262", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11b024"], "sizeBytes": 1291567500}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:5875de68092a1912924958ee1a083adb01a40b8bd4a7e06c2bd61e5686e227d0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11"], "sizeBytes": 1291515726}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:d28bbb0e20a4eea91e6a6ab922be54db464130d762ea4171874b89ed7966ea52", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1289171047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:81cf4d768da0be2021ce8cd6adb1fb5fe71a80e9a5de563af5d79b10d87890dc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1280803609}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:8dd0ea23334b73e20866e9b1fdf0bfca4d7b0180f63709ce580845dc33354978", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0009_09291730"], "sizeBytes": 1274929375}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:d05be33d018496c0fc34be16be6df58bea623f684505e020330b32b8e6c65242", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0009_09291817"], "sizeBytes": 1271551462}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:bb3e93109429e543ab51afac594b8c45b12b585fd90d9b4a271baa522f52f021"], "sizeBytes": 1258153512}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:02a2bea6f6d8f436e72372cfb1ec78df8d6c0a9db152cba7d0e307fc01955a0c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0009_10202000_2017102511"], "sizeBytes": 1255055106}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:8310975166901870a3faa50da81e77bffd3b003c569715657a0553b83605094f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0009_09291735"], "sizeBytes": 1248782412}], "nodeState": {}, "hostname": "prdocker10"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "093f144c-9442-11e7-b1a3-fa163e4a66d8", "resourceVersion": "41682079", "creationTimestamp": "2017-09-08T03:01:40Z", "labels": {"AppDefaultResourceType": "Normal", "BESResourceType": "Normal", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:03Z", "lastTransitionTime": "2017-11-22T07:06:39Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:03Z", "lastTransitionTime": "2017-09-08T03:01:40Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:03Z", "lastTransitionTime": "2017-09-08T03:01:40Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:03Z", "lastTransitionTime": "2017-09-08T03:01:40Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:03Z", "lastTransitionTime": "2017-11-22T07:06:39Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "C1A8ED8E-9162-4D2D-99D9-5CD4CD21CD96", "bootID": "496b7c6a-2331-46a2-9604-f4b09444d236", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:968aa92f65cd933b2410261099b4319e685f9360b39f74b62fd60546574f6854", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 4222357091}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:2ad76c611ec100d9756ca6405020e044c11fa3e7d1a96c95fcc653b486f5372f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3884157513}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:662a469c98ec52dee363a76671634203a90b1ff9721a288349ab320cb036d350", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 3150200466}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:4272a71a91738b27f6d83a54017d9f14d1bb8d6c8bb993ba04c677e86c45478c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008"], "sizeBytes": 2988216417}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:a1cad0635ee5cd7d503a2c1437eb1bd3d412d939942e4e51b9511556abdf10fe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0008"], "sizeBytes": 2836528915}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:53dc6ce7b6a46af258fea1d3d7e508131ae843029248d6dc9bc8ddaf060995f0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 2830511269}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f62fe5644220b084eb8931292daab8ae1fe11d2b9a6cbfd6b859f61a3fcc8bdc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2494377012}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:2b732c57b71cc05c7bb051038c398c7ea01f87733dc8cbb93bd32056a0ab7404", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2493590740}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:f58f8716497ae0743cc58505b3afad38ffd36a9a9c429ddf205c563bdcb26863", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2371593185}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:0e19b71595f3762b7cbde603f7318c528c77ab47955b767be70d09d0c0c50c25", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2346599590}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:232f4fc637817e426b8ba73317fe97a6e9f82d58616fd3ba4f383baaf9dd23c9"], "sizeBytes": 2346583060}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5ed371dd08dd88cb9e95b422d45fe5ebc2806696db3155d8b84c7b89bf6df754", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2249861035}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:bd52b2923bd9e4f82658606b320855f039290cd2f6a717246f2871a0affe138e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11031700"], "sizeBytes": 2233091696}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:6f573a7af65f4c37a1d6ac08dd2ff80915b222905c42962a77ac5f76550ffe70", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2218876394}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:129bbadc35be33e500c278486f5f8356c8b29d3c6c0351e20b3bd24591f79e6b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2192371137}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:a1d3fb3df22bfa89db2424d3793c4a5a8b80735caab284e15b3c3c0f93846855", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1902228249}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:21d898165876999963297dc279cc6b9a87325b734c34a7300a175136deb47fe5", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11031128"], "sizeBytes": 1736797489}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:c1f2632583c4d84d8af9ac37bd86d1417f94bd3c779b2e963bd01976215c2dc6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0010_1711092303_2017111317"], "sizeBytes": 1731703438}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:1769a83d4507578b56d69c925452bf532f1b0387b4a27d6bb3d0eb783cbe08ac", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0009_092900"], "sizeBytes": 1683833490}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:566c0934d8e267c6b944b01156609761e6be38223aa38ca093da5282d8ae5dcf", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0008"], "sizeBytes": 1632244881}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_console_suse11sp3@sha256:acc9773a661271814713c6822f07056ba1042d27ddd47ef3c4c59118ac319497", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_console_suse11sp3:v600r001c11b025"], "sizeBytes": 1628082513}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:bd0d16829c3bfdf4cc249b9072959c2d3b17da7c019985210126ecd759e66669", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1572610477}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:142104ce6fc98dc9af2b8e9c3ddf978661c8c1a564edd1cccf1b85ca819e4507", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1546121274}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:5bf0a024560ddd03192a3664cac21874b36c5c3bd92f7ed485fa73999c478c0c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1535847110}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:edc4f7b16fec29e335508908f6dc2417f145866a48c1d5e5794205f15e0dc57b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0009_09300530"], "sizeBytes": 1484207289}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:aad19ed59b0e80fd5d6f68c823fd7f8e26baab6aa68ad5f533e295b63d0b8b2a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0009_09300536"], "sizeBytes": 1483802821}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:c449e3c5c2588ae8df4c8678fa58706ee779e42337ce7016ccb8aaf08d7a9908", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0009_09291840"], "sizeBytes": 1473453291}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:bf5f9642502f46a509862b6016e050d8275ebd2f9730a3d05ff1aac3ffcac3ba", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1472786453}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:793a9f8f27a51e5bae92dd27990df59e88bd393dc6925fda67116c005c0168b3", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1435422745}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:e9edae2089b4c3d59b1a534adf3c3ef8526f0c72459096a3f09823cd706245d7", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0009_10202016_2017102511"], "sizeBytes": 1405025364}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:08ac7622d03d6f61ada282da8c80d89c555794316bdaa1aa98c777b74c1f5e72", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0010_11162230"], "sizeBytes": 1322553429}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:2ce6589d144079bae33dc9ddc8c924f1c9e346fa05cd558343f08a3a70d799bc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1320862914}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:9f832cf78cbe29a6c014985437b207cd1c847984ec75d112cda1921cad5992b0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0009_10202030_2017102511"], "sizeBytes": 1320229744}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:ad8ca18721e8f9932a940be8b33680b53de711334d7f6a58bc70a4c4ca052f94", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1316898047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6b154038b64a13d2ceeee6b53592a52fd1041539d4019cfea29c84e41bd6001c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0009_09300310"], "sizeBytes": 1299092342}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:5875de68092a1912924958ee1a083adb01a40b8bd4a7e06c2bd61e5686e227d0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11"], "sizeBytes": 1291515726}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:d28bbb0e20a4eea91e6a6ab922be54db464130d762ea4171874b89ed7966ea52", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1289171047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:b3edc680f6c124a1cb15eb67c19b84ed1f5b2313c1498c4b9416fb0d751ffec6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11092000_2017111317"], "sizeBytes": 1287607731}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:81cf4d768da0be2021ce8cd6adb1fb5fe71a80e9a5de563af5d79b10d87890dc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1280803609}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:8dd0ea23334b73e20866e9b1fdf0bfca4d7b0180f63709ce580845dc33354978", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0009_09291730"], "sizeBytes": 1274929375}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:d05be33d018496c0fc34be16be6df58bea623f684505e020330b32b8e6c65242", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0009_09291817"], "sizeBytes": 1271551462}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:dd5eabd1e64f05af317785f29add7288ee381dd168845a46896b7093fc4fae05", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0009_10202030_2017102511"], "sizeBytes": 1269051051}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_jetmq@sha256:3ed49639be7d085467f144e15240a112300592d779f37ddd681a889108cc688a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_jetmq:v600r001c11spc100"], "sizeBytes": 1250712998}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:8310975166901870a3faa50da81e77bffd3b003c569715657a0553b83605094f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0009_09291735"], "sizeBytes": 1248782412}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:ad23d0a1c8a9a26994fee243051f41641ae7f3e8aedfa743f37fcc7df1b7b80e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0009_09291830"], "sizeBytes": 1241965328}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_jetmq@sha256:7812f0a167e321a878ded518ff5ace415c7a6c5cf89619f6da451f115cc09179", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_jetmq:v600r001c11"], "sizeBytes": 1239172372}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:07e6bc04c33cda21e05b8eec84a208a00fa8301912e959ef167cd513f8322717", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0009"], "sizeBytes": 1236171837}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:0f191d1c92f5b7e5dccd057c01e8d895f8307fd42af5abd06e98210c3e56d53c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0009_091945"], "sizeBytes": 1228079598}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:3219ae8690189be14b682c1f1cf9d5a02a866a5d83cefcde61fa62633cf8c2b0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1225786700}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:639cee9f05b8272512af163282f069a0593ac1bf00c035b31f62b8a89684db85", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1223145049}], "nodeState": {}, "hostname": "prdocker11"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "deefe81f-9436-11e7-8ff2-fa163e99a979", "resourceVersion": "41682085", "creationTimestamp": "2017-09-08T01:41:44Z", "labels": {"AppDefaultResourceType": "Normal", "BESResourceType": "Normal", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-11-22T07:06:41Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-09-08T01:41:44Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-09-08T01:41:44Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-09-08T01:41:44Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-11-22T07:06:41Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "097B4B71-A013-4C43-899E-2D8A12413FB0", "bootID": "9c14f8f4-861a-47e1-abbd-48e1c930863a", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:50d05dbca580b0ba3c58c5976d98394e3ecc04d30690f1b57b5d1e7adda2a174", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008"], "sizeBytes": 3959444519}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:86297349ed4267db06361f175258e748b74714b286c002f2ad69eee75cd990bb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3061639616}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:53dc6ce7b6a46af258fea1d3d7e508131ae843029248d6dc9bc8ddaf060995f0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 2830511269}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:9919eefcff2fbdbaa82593545df371930143822750f27850d0aafe401ee32add", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2811609391}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f62fe5644220b084eb8931292daab8ae1fe11d2b9a6cbfd6b859f61a3fcc8bdc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2494377012}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:2b732c57b71cc05c7bb051038c398c7ea01f87733dc8cbb93bd32056a0ab7404", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2493590740}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:bd52b2923bd9e4f82658606b320855f039290cd2f6a717246f2871a0affe138e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11031700"], "sizeBytes": 2233091696}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:4bce750304f3ea75e10442c26b142d69c04b7d6885946c544d2bf982337997f2"], "sizeBytes": 2233075166}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:6ba98d948aeb945eb803a147a8c5e867a9ba76233c075efe94c180930f674dec", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2212409238}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:71699537fd14a1cac24bc0d7054dce88e16dea5fd361a3219ba47493550a2062", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2196050578}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:b9feb97c3708f88340282675535b320c2b17eb6f2b4ec77f6a5a426dd7ac935b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2160854595}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:da133564d6fff16c619ebe18aaf4e8ffc16b176173aceee452c2adde2ede5bbe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 2149050768}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:ff3a0b0e390741d4ea69908cf5092a9df8a69a65476349cdf411837c09abf525"], "sizeBytes": 2081588109}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:21d898165876999963297dc279cc6b9a87325b734c34a7300a175136deb47fe5", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11031128"], "sizeBytes": 1736797489}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:e2c8b9218eccb99566c455ada1e51b3e31868cd361e29390cb589f8f56e6be9f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1734624308}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:70434e954ed031014447cf8dc623d6f6a9e75629271134877e23516b63a2596b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1734205878}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:ed2d966a2f613548fd159fdf5bfefa0317614c650a4ba9c8b49b8566af6ea3ec", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0009_1019_branch_2017102511"], "sizeBytes": 1723190338}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:6c158dc37f48bb324f0817162a1ba1c8558965d53582e32a10bfe71dd002ac6e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0009_092900"], "sizeBytes": 1696583736}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:1769a83d4507578b56d69c925452bf532f1b0387b4a27d6bb3d0eb783cbe08ac"], "sizeBytes": 1683833490}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:566c0934d8e267c6b944b01156609761e6be38223aa38ca093da5282d8ae5dcf", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0008"], "sizeBytes": 1632244881}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_console_suse11sp3@sha256:0a604e2882a82838cb350a2f7c3b1cb87ac3cfda53e01209490c6ed38df0f6e8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_console_suse11sp3:v600r001c11"], "sizeBytes": 1621516224}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:2d711c4c7e94483847d8cf85a3cec57ee63dac3e8c7017f5593dd3c6c8ec1579", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_11031815"], "sizeBytes": 1547418834}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:142104ce6fc98dc9af2b8e9c3ddf978661c8c1a564edd1cccf1b85ca819e4507", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1546121274}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:5bf0a024560ddd03192a3664cac21874b36c5c3bd92f7ed485fa73999c478c0c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1535847110}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:8ddc0faddaf7b79285766691cde9ba511eb39df54627536a8469e85aded8ecbb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0009_09300330"], "sizeBytes": 1530378254}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:b7f5dae492eba3106d9493823cc1995f655b725edbb45dee7428b401246c86f9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0009_09300536"], "sizeBytes": 1483758968}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:4114a9b8cd05b7a50abe0e5baa402588446b77c75b828824150ec88c9e8b78d6"], "sizeBytes": 1475187390}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:c449e3c5c2588ae8df4c8678fa58706ee779e42337ce7016ccb8aaf08d7a9908", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0009_09291840"], "sizeBytes": 1473453291}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:4cc87a8560fb89cf3b7ab8828993c5d53dba6fc5ddd353824452d32758c47e2d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1473037369}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:472e959f254368315a9669a6cc2805a682e7da12608412d36752043e1d856626", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0010_11162000"], "sizeBytes": 1340834771}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:070c72b4d88a8992391e0eee0016a0b27e02a8e75fbfc06339d978da631d8581", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11031800"], "sizeBytes": 1339302858}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:08ac7622d03d6f61ada282da8c80d89c555794316bdaa1aa98c777b74c1f5e72", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0010_11162230"], "sizeBytes": 1322553429}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:8b6ff5af3ad385aaf5116b2a9f29682ca2e50eb0408d186d8da2bc4668fe8a68", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_1108_2017111317"], "sizeBytes": 1316912058}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:ad8ca18721e8f9932a940be8b33680b53de711334d7f6a58bc70a4c4ca052f94", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1316898047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:77fdddea2964440d400104f4a81e763d297b53dc5e6cb3d33e8bc7d89bc0a473", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1307948018}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:e0901fae60744cca8cb5e88bc13763a2b62ac767ff789b08d6f1ac48c9ff1978", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0009_09300310"], "sizeBytes": 1297980157}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:4c85614d3ec7df8eca0643e78dcb23d3de3fafc4bfaa24d6559a750863228825", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11b024"], "sizeBytes": 1295223056}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:0a482ebf19a8458d4c15cebd23ae0ed0c213ae7065d97494a645619ef7b66262"], "sizeBytes": 1291567500}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:5875de68092a1912924958ee1a083adb01a40b8bd4a7e06c2bd61e5686e227d0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11"], "sizeBytes": 1291515726}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:d28bbb0e20a4eea91e6a6ab922be54db464130d762ea4171874b89ed7966ea52", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1289171047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:aceae32196902b5d221c8c3d972e0367a0d42d7b70eaebc9b8ceb45de904960d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11022200"], "sizeBytes": 1287603633}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:8dd0ea23334b73e20866e9b1fdf0bfca4d7b0180f63709ce580845dc33354978", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0009_09291730"], "sizeBytes": 1274929375}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:d05be33d018496c0fc34be16be6df58bea623f684505e020330b32b8e6c65242", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0009_09291817"], "sizeBytes": 1271551462}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:ad23d0a1c8a9a26994fee243051f41641ae7f3e8aedfa743f37fcc7df1b7b80e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0009_09291830"], "sizeBytes": 1241965328}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_jetmq@sha256:3cceb64947ab5768cc314cac0d6a97ff0bed77a2c13e4a39126e67032183e3f1", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_jetmq:v600r001c11"], "sizeBytes": 1239178194}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:37ccd4a951c497c7a12b33e8d02d1d2b6f0cbb648003e4e5854ff8c0e39cc2cd", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0009_091945"], "sizeBytes": 1228618435}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:0f191d1c92f5b7e5dccd057c01e8d895f8307fd42af5abd06e98210c3e56d53c"], "sizeBytes": 1228079598}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:33194a0eab8d60a51aec231c25774cdc7a336b2042d83572a0567e57640eb1ca", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1220311071}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:44935bf606ec708074572a0292c75e3bd57cb9fb7b3102d0ff443a274e212152", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0008"], "sizeBytes": 1216896473}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:35d4e23dbd7d91b35068c3b9c97028b739c66fd934a536f9c2aea280515f51af", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0008"], "sizeBytes": 1203596829}], "nodeState": {}, "hostname": "prdocker12"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "dc09642b-9436-11e7-b1a3-fa163e4a66d8", "resourceVersion": "41682118", "creationTimestamp": "2017-09-08T01:41:39Z", "labels": {"AppDefaultResourceType": "Normal", "BESResourceType": "Normal", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115568Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115568Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:10Z", "lastTransitionTime": "2017-11-22T07:06:41Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:10Z", "lastTransitionTime": "2017-09-08T01:41:39Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:10Z", "lastTransitionTime": "2017-09-08T01:41:39Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:10Z", "lastTransitionTime": "2017-09-08T01:41:39Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:10Z", "lastTransitionTime": "2017-11-22T07:06:41Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "72291784-60E1-480C-9BA6-149C71F332A4", "bootID": "59b33509-db26-46d7-b011-6ecacf0105de", "kernelVersion": "4.4.74-92.35-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:50d05dbca580b0ba3c58c5976d98394e3ecc04d30690f1b57b5d1e7adda2a174", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008"], "sizeBytes": 3959444519}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:edc2fed28420dcfa19d25b5e6c2e9101da0fdf7645c765a42d85a975a9fe6fb2", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3611808153}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:30ba975a6bd6cda8338a69e48434a2d038038dbc9acecb4d361f9e2635b7a642", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3056445206}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:4272a71a91738b27f6d83a54017d9f14d1bb8d6c8bb993ba04c677e86c45478c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008"], "sizeBytes": 2988216417}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:a1cad0635ee5cd7d503a2c1437eb1bd3d412d939942e4e51b9511556abdf10fe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0008"], "sizeBytes": 2836528915}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:9919eefcff2fbdbaa82593545df371930143822750f27850d0aafe401ee32add", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2811609391}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:afa03a0788f543345c2da30cc8399223720071de9398b0aa82a42c5f62721815", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11101600_2017111317"], "sizeBytes": 2644317263}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f62fe5644220b084eb8931292daab8ae1fe11d2b9a6cbfd6b859f61a3fcc8bdc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2494377012}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:b454004dfe2966fd239bea04e253f29e93c78149d5df6b2f0500b11f147b0e4c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2370393292}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5ed371dd08dd88cb9e95b422d45fe5ebc2806696db3155d8b84c7b89bf6df754", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2249861035}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:4bce750304f3ea75e10442c26b142d69c04b7d6885946c544d2bf982337997f2", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11031700"], "sizeBytes": 2233075166}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:6ba98d948aeb945eb803a147a8c5e867a9ba76233c075efe94c180930f674dec"], "sizeBytes": 2212409238}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:b9feb97c3708f88340282675535b320c2b17eb6f2b4ec77f6a5a426dd7ac935b"], "sizeBytes": 2160854595}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:da133564d6fff16c619ebe18aaf4e8ffc16b176173aceee452c2adde2ede5bbe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 2149050768}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:ff3a0b0e390741d4ea69908cf5092a9df8a69a65476349cdf411837c09abf525", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2081588109}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:a1d3fb3df22bfa89db2424d3793c4a5a8b80735caab284e15b3c3c0f93846855", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1902228249}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:d6fef8769c05bedfcbe80e794bbee037d4e5251c8e0879fcb8014a978d27ad0e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_1711092303_2017111317"], "sizeBytes": 1899306557}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:70434e954ed031014447cf8dc623d6f6a9e75629271134877e23516b63a2596b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1734205878}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:17569cec11d34a6771dd6d72a29a0c2130e1ba8a668f014f784649476f1b2a0e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_1711101700_2017111317"], "sizeBytes": 1731285845}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:6c158dc37f48bb324f0817162a1ba1c8558965d53582e32a10bfe71dd002ac6e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0009_092900"], "sizeBytes": 1696583736}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:1769a83d4507578b56d69c925452bf532f1b0387b4a27d6bb3d0eb783cbe08ac"], "sizeBytes": 1683833490}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:4dd55e19ba082eaa8d26c05f1d8c0cb679bdcd1f0e025cc47d6e590b2a4d0248", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1666936762}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:566c0934d8e267c6b944b01156609761e6be38223aa38ca093da5282d8ae5dcf", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0008"], "sizeBytes": 1632244881}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_console_suse11sp3@sha256:acc9773a661271814713c6822f07056ba1042d27ddd47ef3c4c59118ac319497", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_console_suse11sp3:v600r001c11b025"], "sizeBytes": 1628082513}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:4a5e8eb8b11e6b8fc9191b60839240fe2c887426c3e87314c2fed503f8bb62ab", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11101514_2017111317"], "sizeBytes": 1568121394}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:5bf0a024560ddd03192a3664cac21874b36c5c3bd92f7ed485fa73999c478c0c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1535847110}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:8ddc0faddaf7b79285766691cde9ba511eb39df54627536a8469e85aded8ecbb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0009_09300330"], "sizeBytes": 1530378254}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:5a5e4534ecd9234e3409d06447bcb583b781531013bea8bb86e2e39c103b45f8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0010_11021405"], "sizeBytes": 1496026826}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:edc4f7b16fec29e335508908f6dc2417f145866a48c1d5e5794205f15e0dc57b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0009_09300530"], "sizeBytes": 1484207289}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:b7f5dae492eba3106d9493823cc1995f655b725edbb45dee7428b401246c86f9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0009_09300536"], "sizeBytes": 1483758968}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f7c9dc8d2905ef21c68cd3b191a07131a91db53804f762221eedf947f7acfae0"], "sizeBytes": 1475408329}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:4114a9b8cd05b7a50abe0e5baa402588446b77c75b828824150ec88c9e8b78d6"], "sizeBytes": 1475187390}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:c449e3c5c2588ae8df4c8678fa58706ee779e42337ce7016ccb8aaf08d7a9908", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0009_09291840"], "sizeBytes": 1473453291}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:bf5f9642502f46a509862b6016e050d8275ebd2f9730a3d05ff1aac3ffcac3ba", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1472786453}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:82ec4acde356514caa77039503d4c49700c559b23087d351911d6d8c6b9f4777"], "sizeBytes": 1460134008}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:2ea3beb2f52898dcc04b067e9f440b7aa23f0073a368a6e15f91d6fb11ae362c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11172250"], "sizeBytes": 1347384973}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:472e959f254368315a9669a6cc2805a682e7da12608412d36752043e1d856626", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0010_11162000"], "sizeBytes": 1340834771}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:e0596079828ad21e5e0266c2069f5c06ba1e139cdfcd67197a3f4ca0f694dd43", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0010_11031640"], "sizeBytes": 1339690362}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:74bd94a69dd1e7ea61fd4b5053bfc5124924006f3a9893847f5cddc7afca2d97", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0010_1108_2017111317"], "sizeBytes": 1339141433}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:e027b521ec22dc8beb2de4db42d3211c270ade2197309b66cd94ca600ff889f9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1319512571}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:77fdddea2964440d400104f4a81e763d297b53dc5e6cb3d33e8bc7d89bc0a473", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1307948018}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:5f51b9c372eacfbcbd377f329462fa6efdbba722aece2814749137fc817c2fd6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0010_11091834_2017111317"], "sizeBytes": 1306824875}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6b154038b64a13d2ceeee6b53592a52fd1041539d4019cfea29c84e41bd6001c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0009_09300310"], "sizeBytes": 1299092342}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:e0901fae60744cca8cb5e88bc13763a2b62ac767ff789b08d6f1ac48c9ff1978"], "sizeBytes": 1297980157}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:d21e6b24e4769111a181baaed7040ea424e0ae1f7fffe786e4f84cc6c8a45857", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11b024"], "sizeBytes": 1294994735}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:0a482ebf19a8458d4c15cebd23ae0ed0c213ae7065d97494a645619ef7b66262"], "sizeBytes": 1291567500}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:b3edc680f6c124a1cb15eb67c19b84ed1f5b2313c1498c4b9416fb0d751ffec6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11092000_2017111317"], "sizeBytes": 1287607731}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:81cf4d768da0be2021ce8cd6adb1fb5fe71a80e9a5de563af5d79b10d87890dc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1280803609}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:ebf129985201207bf81fd78db53510900ee07cb11d89434e09fefa7008b78b91"], "sizeBytes": 1277251039}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:8dd0ea23334b73e20866e9b1fdf0bfca4d7b0180f63709ce580845dc33354978", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0009_09291730"], "sizeBytes": 1274929375}], "nodeState": {}, "hostname": "prdocker13"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "e0da25b4-9436-11e7-bbbd-fa163e811497", "resourceVersion": "41682094", "creationTimestamp": "2017-09-08T01:41:47Z", "labels": {"AppDefaultResourceType": "Normal", "BESResourceType": "Normal", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:05Z", "lastTransitionTime": "2017-11-22T07:06:34Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:05Z", "lastTransitionTime": "2017-09-08T01:41:47Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:05Z", "lastTransitionTime": "2017-09-08T01:41:47Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:05Z", "lastTransitionTime": "2017-09-08T01:41:47Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:05Z", "lastTransitionTime": "2017-11-22T07:06:34Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "F7C0C4CA-EC6E-4125-8260-34865A825B33", "bootID": "aedad6f3-45ab-41ea-9f3f-a3ca5d1b715d", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:968aa92f65cd933b2410261099b4319e685f9360b39f74b62fd60546574f6854", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 4222357091}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:50d05dbca580b0ba3c58c5976d98394e3ecc04d30690f1b57b5d1e7adda2a174", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008"], "sizeBytes": 3959444519}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:2ad76c611ec100d9756ca6405020e044c11fa3e7d1a96c95fcc653b486f5372f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3884157513}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:edc2fed28420dcfa19d25b5e6c2e9101da0fdf7645c765a42d85a975a9fe6fb2", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3611808153}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:662a469c98ec52dee363a76671634203a90b1ff9721a288349ab320cb036d350", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 3150200466}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:b686cee3eeb31d4fc94c2efa6216f6fd09517e37ecc491c0b2eac61d48e21f06", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0009_10202100_2017102511"], "sizeBytes": 3072304575}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:c279c39297ac85cb3613faa94cc1910a1bb130593efe29e18fcb1ae44f8281bb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11101600_2017111317"], "sizeBytes": 3016459179}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:a1cad0635ee5cd7d503a2c1437eb1bd3d412d939942e4e51b9511556abdf10fe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0008"], "sizeBytes": 2836528915}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:53dc6ce7b6a46af258fea1d3d7e508131ae843029248d6dc9bc8ddaf060995f0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 2830511269}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:9919eefcff2fbdbaa82593545df371930143822750f27850d0aafe401ee32add", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2811609391}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5f88b9791d91e3ae4706340f6fa580c2a03aec342282979a54953e31c5639a73", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_10202100_2017102511"], "sizeBytes": 2624700495}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f62fe5644220b084eb8931292daab8ae1fe11d2b9a6cbfd6b859f61a3fcc8bdc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2494377012}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:2b732c57b71cc05c7bb051038c398c7ea01f87733dc8cbb93bd32056a0ab7404", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2493590740}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:f58f8716497ae0743cc58505b3afad38ffd36a9a9c429ddf205c563bdcb26863", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2371593185}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5ed371dd08dd88cb9e95b422d45fe5ebc2806696db3155d8b84c7b89bf6df754", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2249861035}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:bd52b2923bd9e4f82658606b320855f039290cd2f6a717246f2871a0affe138e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11031700"], "sizeBytes": 2233091696}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:6f573a7af65f4c37a1d6ac08dd2ff80915b222905c42962a77ac5f76550ffe70", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2218876394}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:b9feb97c3708f88340282675535b320c2b17eb6f2b4ec77f6a5a426dd7ac935b"], "sizeBytes": 2160854595}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:da133564d6fff16c619ebe18aaf4e8ffc16b176173aceee452c2adde2ede5bbe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 2149050768}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:ff3a0b0e390741d4ea69908cf5092a9df8a69a65476349cdf411837c09abf525"], "sizeBytes": 2081588109}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:a1d3fb3df22bfa89db2424d3793c4a5a8b80735caab284e15b3c3c0f93846855", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1902228249}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:21d898165876999963297dc279cc6b9a87325b734c34a7300a175136deb47fe5", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11031128"], "sizeBytes": 1736797489}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:e2c8b9218eccb99566c455ada1e51b3e31868cd361e29390cb589f8f56e6be9f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1734624308}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:70434e954ed031014447cf8dc623d6f6a9e75629271134877e23516b63a2596b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1734205878}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:1769a83d4507578b56d69c925452bf532f1b0387b4a27d6bb3d0eb783cbe08ac", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0009_092900"], "sizeBytes": 1683833490}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:bd0d16829c3bfdf4cc249b9072959c2d3b17da7c019985210126ecd759e66669", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1572610477}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:5bf0a024560ddd03192a3664cac21874b36c5c3bd92f7ed485fa73999c478c0c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1535847110}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:271567f4ed1926b21d0831269f6f07b0a5ed09a42ca61818ff74ce5128c85b45", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0009_09300330"], "sizeBytes": 1521806676}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:5a5e4534ecd9234e3409d06447bcb583b781531013bea8bb86e2e39c103b45f8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0010_11021405"], "sizeBytes": 1496026826}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:edc4f7b16fec29e335508908f6dc2417f145866a48c1d5e5794205f15e0dc57b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0009_09300530"], "sizeBytes": 1484207289}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:b7f5dae492eba3106d9493823cc1995f655b725edbb45dee7428b401246c86f9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0009_09300536"], "sizeBytes": 1483758968}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f7c9dc8d2905ef21c68cd3b191a07131a91db53804f762221eedf947f7acfae0"], "sizeBytes": 1475408329}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:4114a9b8cd05b7a50abe0e5baa402588446b77c75b828824150ec88c9e8b78d6"], "sizeBytes": 1475187390}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:c449e3c5c2588ae8df4c8678fa58706ee779e42337ce7016ccb8aaf08d7a9908", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0009_09291840"], "sizeBytes": 1473453291}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:82ec4acde356514caa77039503d4c49700c559b23087d351911d6d8c6b9f4777"], "sizeBytes": 1460134008}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:e9edae2089b4c3d59b1a534adf3c3ef8526f0c72459096a3f09823cd706245d7", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0009_10202016_2017102511"], "sizeBytes": 1405025364}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:2ea3beb2f52898dcc04b067e9f440b7aa23f0073a368a6e15f91d6fb11ae362c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11172250"], "sizeBytes": 1347384973}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:472e959f254368315a9669a6cc2805a682e7da12608412d36752043e1d856626", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0010_11162000"], "sizeBytes": 1340834771}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:e0596079828ad21e5e0266c2069f5c06ba1e139cdfcd67197a3f4ca0f694dd43", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0010_11031640"], "sizeBytes": 1339690362}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:070c72b4d88a8992391e0eee0016a0b27e02a8e75fbfc06339d978da631d8581", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11031800"], "sizeBytes": 1339302858}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:2ce6589d144079bae33dc9ddc8c924f1c9e346fa05cd558343f08a3a70d799bc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1320862914}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:ad8ca18721e8f9932a940be8b33680b53de711334d7f6a58bc70a4c4ca052f94", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1316898047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:77fdddea2964440d400104f4a81e763d297b53dc5e6cb3d33e8bc7d89bc0a473", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1307948018}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6b154038b64a13d2ceeee6b53592a52fd1041539d4019cfea29c84e41bd6001c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0009_09300310"], "sizeBytes": 1299092342}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:471d0c97928fdf25f005388dbbcd04c0b6a47b7f908ac505ad508291170e13c4", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1295975169}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:0a482ebf19a8458d4c15cebd23ae0ed0c213ae7065d97494a645619ef7b66262", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11b024"], "sizeBytes": 1291567500}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:d28bbb0e20a4eea91e6a6ab922be54db464130d762ea4171874b89ed7966ea52", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1289171047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:ebf129985201207bf81fd78db53510900ee07cb11d89434e09fefa7008b78b91"], "sizeBytes": 1277251039}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:fb260ee38ba79757fc6a93ac21707660c5da76ecf4c9d48ab655ba8ab70c1e79", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0009_09291730"], "sizeBytes": 1270679099}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:dd5eabd1e64f05af317785f29add7288ee381dd168845a46896b7093fc4fae05", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0009_10202030_2017102511"], "sizeBytes": 1269051051}], "nodeState": {}, "hostname": "prdocker14"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "e1356260-9436-11e7-bbbd-fa163e811497", "resourceVersion": "41682080", "creationTimestamp": "2017-09-08T01:41:48Z", "labels": {"AppDefaultResourceType": "Normal", "BESResourceType": "Normal", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115568Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115568Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-11-24T09:00:12Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-09-08T01:41:48Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-09-08T01:41:48Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-09-08T01:41:48Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-11-24T09:00:51Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "31ADC7AF-D80A-4AA0-B23C-22ECBA7FF57F", "bootID": "74e150d1-5b55-4942-8a7f-c78c13f7195a", "kernelVersion": "4.4.74-92.35-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:662a469c98ec52dee363a76671634203a90b1ff9721a288349ab320cb036d350", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 3150200466}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:c279c39297ac85cb3613faa94cc1910a1bb130593efe29e18fcb1ae44f8281bb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11101600_2017111317"], "sizeBytes": 3016459179}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:53dc6ce7b6a46af258fea1d3d7e508131ae843029248d6dc9bc8ddaf060995f0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 2830511269}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:6048139c60ceb3c9b0fb2e7a591c5c7f2e01147d35045c05b5cd31f61cb66922", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11172200"], "sizeBytes": 2764600281}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:2b732c57b71cc05c7bb051038c398c7ea01f87733dc8cbb93bd32056a0ab7404", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2493590740}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5ed371dd08dd88cb9e95b422d45fe5ebc2806696db3155d8b84c7b89bf6df754", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2249861035}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:bd52b2923bd9e4f82658606b320855f039290cd2f6a717246f2871a0affe138e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11031700"], "sizeBytes": 2233091696}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:428b66ed8fe30ded4a123d2241cdb57d438b8be4f37aa982b39dca0b6137922d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 2149059722}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:d6fef8769c05bedfcbe80e794bbee037d4e5251c8e0879fcb8014a978d27ad0e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_1711092303_2017111317"], "sizeBytes": 1899306557}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:21d898165876999963297dc279cc6b9a87325b734c34a7300a175136deb47fe5", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0010_11031128"], "sizeBytes": 1736797489}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:bd0d16829c3bfdf4cc249b9072959c2d3b17da7c019985210126ecd759e66669", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1572610477}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6235cd4d031d4b43a4e7d47f12696827b971c373b0b741b4241296c9947f8329", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11102300_2017111317"], "sizeBytes": 1350229449}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:2ea3beb2f52898dcc04b067e9f440b7aa23f0073a368a6e15f91d6fb11ae362c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11172250"], "sizeBytes": 1347384973}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:070c72b4d88a8992391e0eee0016a0b27e02a8e75fbfc06339d978da631d8581", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11031800"], "sizeBytes": 1339302858}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:ad8ca18721e8f9932a940be8b33680b53de711334d7f6a58bc70a4c4ca052f94", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1316898047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:77fdddea2964440d400104f4a81e763d297b53dc5e6cb3d33e8bc7d89bc0a473", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1307948018}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6b154038b64a13d2ceeee6b53592a52fd1041539d4019cfea29c84e41bd6001c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0009_09300310"], "sizeBytes": 1299092342}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:d21e6b24e4769111a181baaed7040ea424e0ae1f7fffe786e4f84cc6c8a45857", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11b024"], "sizeBytes": 1294994735}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:d28bbb0e20a4eea91e6a6ab922be54db464130d762ea4171874b89ed7966ea52", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1289171047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:aceae32196902b5d221c8c3d972e0367a0d42d7b70eaebc9b8ceb45de904960d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11022200"], "sizeBytes": 1287603633}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:81cf4d768da0be2021ce8cd6adb1fb5fe71a80e9a5de563af5d79b10d87890dc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1280803609}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:8dd0ea23334b73e20866e9b1fdf0bfca4d7b0180f63709ce580845dc33354978", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0009_09291730"], "sizeBytes": 1274929375}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:44935bf606ec708074572a0292c75e3bd57cb9fb7b3102d0ff443a274e212152", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0008"], "sizeBytes": 1216896473}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:35d4e23dbd7d91b35068c3b9c97028b739c66fd934a536f9c2aea280515f51af", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0008"], "sizeBytes": 1203596829}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:a470ebaefc2cc24277c44ada8ff63550c6112e158cec6882f3120ed2b5121c04", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0008"], "sizeBytes": 1166323045}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_suse11sp3@sha256:26ec6ace9dd750bde69bf13108d1026edef2d0bee40da579ef211e41208700d8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_suse11sp3:v600r001c11b024"], "sizeBytes": 1164276489}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:8efb455fffdd1639f127fbba6ff36c7c29ed0e208098211905ef70c9fc349994", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0008"], "sizeBytes": 1158352327}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_lss_suse11sp3@sha256:21fa7425b1d1b51e3ba03c11cc76392dc191b1ec66d78488513c54924b788dcb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_lss_suse11sp3:v600r001c11b024"], "sizeBytes": 1011753196}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricportalctz@sha256:d15fd7b019a1f6157a4de960bb011755d574100c5efd6489f007cb4ca389951c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricportalctz:8.2.20.LG0009"], "sizeBytes": 996573086}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_zookeeper@sha256:7658e82ce9a30e3f67d53b553f4cff54ed7b61cd1488a3f144f56b62d873f0a2", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_zookeeper:v600r001c11"], "sizeBytes": 975251318}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_broker_zookeeper@sha256:3ac901363ebd45204d096aa7b1298063808cf30d277051892fa04aab4fa7b6a7", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_broker_zookeeper:v600r001c11spc100"], "sizeBytes": 547145180}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_broker_suse11sp3@sha256:0ed6389f3eb32cc246bdc9a94848457e858bd876041ecba2d8b4845a3ebffa99", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_broker_suse11sp3:v600r001c11"], "sizeBytes": 532385877}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/redis-server@sha256:399e66b5c1f3b1b6c19df9d79fd338c63fc0e66130db855c97ecd58e0efa7172", "************:20202/tenant_hktvdc01/tenant_hktvdc01/redis-server:commerce-2.300.1"], "sizeBytes": 514303063}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/redis-server@sha256:cce74c4659d0a272ff360d519dfc5093b23da1e64fda7bc2bad74b01fb0458d5"], "sizeBytes": 514301582}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_lss_broker_suse11sp3@sha256:f0ece9e38f6619004bb7a8c998c002e627eb1eafd2f4f565602ec5f3f480e8f9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_lss_broker_suse11sp3:v600r001c11b024"], "sizeBytes": 501152774}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_broker_jetmq@sha256:faffbe5f1a226a38506256e7241f2ce892212c99861de83d6c4fec7a4277ccf6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_broker_jetmq:v600r001c11"], "sizeBytes": 490012608}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/redis-server@sha256:fb5f6a36c336a7dc5f7a8dca3b40ddaefa2fd6f67576b649297ff20b5b847eda", "************:20202/tenant_hktvdc01/tenant_hktvdc01/redis-server:2.300.1"], "sizeBytes": 480841654}, {"names": ["canal-agent:2.3.RC3.SPC3", "canal-agent:latest"], "sizeBytes": 406890280}, {"names": ["canal-agent:2.3.RC3.SPC2.B010"], "sizeBytes": 391548702}, {"names": ["canal-agent:2.3.RC3.B075"], "sizeBytes": 391466402}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender@sha256:f1f9a7a2b2f3cf380c6de17d8f10473e348def63b37902a1f410079a9ff3a7e9", "************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender:1.0.4"], "sizeBytes": 78645352}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css@sha256:42007e581a318ca8b51962e700bc92f2e0cac07ed9a3c55defd301d1789bef3a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css:1.0.4"], "sizeBytes": 1094548}, {"names": ["************:20202/op_svc_cfe/default/cfe-pause@sha256:16c6191b2e2e625aea965d5a0f193f6239240a50a4b65f424a3f37eb54ea3f56", "************:20202/op_svc_cfe/default/cfe-pause:2.8.9"], "sizeBytes": 350164}], "nodeState": {}, "hostname": "prdocker15"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "5a74fa06-c432-11e7-bb95-fa163e4a66d8", "resourceVersion": "41682117", "creationTimestamp": "2017-11-08T03:10:20Z", "labels": {"beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:10Z", "lastTransitionTime": "2017-11-22T07:06:41Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:10Z", "lastTransitionTime": "2017-11-08T03:10:20Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:10Z", "lastTransitionTime": "2017-11-08T03:10:20Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:10Z", "lastTransitionTime": "2017-11-08T03:10:20Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:10Z", "lastTransitionTime": "2017-11-22T07:06:41Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "92E65923-5D51-4992-AED3-EA7FB83F507E", "bootID": "c3b3a0da-34d4-423b-a0f3-fa7647dfb846", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:968aa92f65cd933b2410261099b4319e685f9360b39f74b62fd60546574f6854", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 4222357091}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:50d05dbca580b0ba3c58c5976d98394e3ecc04d30690f1b57b5d1e7adda2a174", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008"], "sizeBytes": 3959444519}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:edc2fed28420dcfa19d25b5e6c2e9101da0fdf7645c765a42d85a975a9fe6fb2", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3611808153}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:a1cad0635ee5cd7d503a2c1437eb1bd3d412d939942e4e51b9511556abdf10fe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0008"], "sizeBytes": 2836528915}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:9919eefcff2fbdbaa82593545df371930143822750f27850d0aafe401ee32add", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2811609391}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f62fe5644220b084eb8931292daab8ae1fe11d2b9a6cbfd6b859f61a3fcc8bdc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2494377012}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:2b732c57b71cc05c7bb051038c398c7ea01f87733dc8cbb93bd32056a0ab7404", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2493590740}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5ed371dd08dd88cb9e95b422d45fe5ebc2806696db3155d8b84c7b89bf6df754", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2249861035}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:4bce750304f3ea75e10442c26b142d69c04b7d6885946c544d2bf982337997f2", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11031700"], "sizeBytes": 2233075166}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:da133564d6fff16c619ebe18aaf4e8ffc16b176173aceee452c2adde2ede5bbe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 2149050768}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:3fd27657c7fe7a397c28b73373a8fe4795f248305e6c5199cc68f7e396a7ea1e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0009_1019_branch_2017102511"], "sizeBytes": 1816428554}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:5ba8064b1dac880223b8a7205572b3a250b5f31b04c621abc99c888e2f34d70d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0009_1019_branch_2017102511"], "sizeBytes": 1723580163}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:6c158dc37f48bb324f0817162a1ba1c8558965d53582e32a10bfe71dd002ac6e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0009_092900"], "sizeBytes": 1696583736}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_console_suse11sp3@sha256:acc9773a661271814713c6822f07056ba1042d27ddd47ef3c4c59118ac319497", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_console_suse11sp3:v600r001c11b025"], "sizeBytes": 1628082513}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_console_suse11sp3@sha256:0a604e2882a82838cb350a2f7c3b1cb87ac3cfda53e01209490c6ed38df0f6e8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_console_suse11sp3:v600r001c11"], "sizeBytes": 1621516224}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:8ddc0faddaf7b79285766691cde9ba511eb39df54627536a8469e85aded8ecbb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0009_09300330"], "sizeBytes": 1530378254}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:5a5e4534ecd9234e3409d06447bcb583b781531013bea8bb86e2e39c103b45f8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0010_11021405"], "sizeBytes": 1496026826}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:aad19ed59b0e80fd5d6f68c823fd7f8e26baab6aa68ad5f533e295b63d0b8b2a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0009_09300536"], "sizeBytes": 1483802821}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:c449e3c5c2588ae8df4c8678fa58706ee779e42337ce7016ccb8aaf08d7a9908", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0009_09291840"], "sizeBytes": 1473453291}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6b154038b64a13d2ceeee6b53592a52fd1041539d4019cfea29c84e41bd6001c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0009_09300310"], "sizeBytes": 1299092342}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:0a482ebf19a8458d4c15cebd23ae0ed0c213ae7065d97494a645619ef7b66262", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11b024"], "sizeBytes": 1291567500}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:5875de68092a1912924958ee1a083adb01a40b8bd4a7e06c2bd61e5686e227d0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11"], "sizeBytes": 1291515726}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:d05be33d018496c0fc34be16be6df58bea623f684505e020330b32b8e6c65242", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0009_09291817"], "sizeBytes": 1271551462}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:8310975166901870a3faa50da81e77bffd3b003c569715657a0553b83605094f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0009_09291735"], "sizeBytes": 1248782412}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:ad23d0a1c8a9a26994fee243051f41641ae7f3e8aedfa743f37fcc7df1b7b80e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0009_09291830"], "sizeBytes": 1241965328}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:13df06abe624e9e5b757cc2e55f4aeae18175c140b1efdf209117ef7e54526c6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1241137758}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:07e6bc04c33cda21e05b8eec84a208a00fa8301912e959ef167cd513f8322717", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0009"], "sizeBytes": 1236171837}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:551ede0ef9c1a2f090c38b06ac73af7d8dbdde7bb887cbf57edc21efa38ffb7c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0008_0818"], "sizeBytes": 1234572714}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:639cee9f05b8272512af163282f069a0593ac1bf00c035b31f62b8a89684db85", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1223145049}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:c195885c7594fc1f2753f30035b6c592782c150ebd70d4357c679b3b0ae17692", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0009_10202027_2017102511"], "sizeBytes": 1222845162}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:44935bf606ec708074572a0292c75e3bd57cb9fb7b3102d0ff443a274e212152", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0008"], "sizeBytes": 1216896473}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:09c46c4ebf51d9e64034f2a3db0f274bde707cdd7d83bd13086b3258b3c143cb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1205783688}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:35d4e23dbd7d91b35068c3b9c97028b739c66fd934a536f9c2aea280515f51af", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0008"], "sizeBytes": 1203596829}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:a46eb4a2c92ab39b05faac89f40b57355c0d61ac937dd713684ed219a476092c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0008"], "sizeBytes": 1203126642}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:55c27031e10112b53b1d73c6a4df2295d5a347e6ded2ebe189868c94ae676e29", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0009_09291830"], "sizeBytes": 1188209503}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:17821c24f88fe5559787c430c8a609c3debccba29a46a297c0469963e75ef250", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0008"], "sizeBytes": 1186993220}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:78cdb14ea72401a0966762cc512c0905519a5aaa73b0ab5d59fb9d90c57e72ee", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.20170818185918"], "sizeBytes": 1183406278}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:4297e23240463ea08591f39fb11424a183322502deb77931d9eff9c6d22b5b70", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1167491351}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_suse11sp3@sha256:bb2e1a80a74f12be2e814a21cd33d1423f77137cacf80c0ae068e2a960682d98", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_suse11sp3:v600r001c11"], "sizeBytes": 1164352711}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_suse11sp3@sha256:26ec6ace9dd750bde69bf13108d1026edef2d0bee40da579ef211e41208700d8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_dds_suse11sp3:v600r001c11b024"], "sizeBytes": 1164276489}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:50e0af947e250e07fe6a5db7a6ad937d6548547da71e809b5909582127353c4e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0008"], "sizeBytes": 1159550406}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:8efb455fffdd1639f127fbba6ff36c7c29ed0e208098211905ef70c9fc349994", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0008"], "sizeBytes": 1158352327}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricgovernancectz@sha256:fc5ca89fc2e4811458cc916a3a3f0534e70549b4b9e6ad30556b72ef19c92901", "************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricgovernancectz:8.2.20.LG0010_11151800"], "sizeBytes": 1144307991}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricgovernancectz@sha256:6b7ca89780db0f08f8bacef2594f6f4f07b0d290704ad0f2151a6cb0fb174545", "************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricgovernancectz:8.2.20.LG0009_0927"], "sizeBytes": 1135291107}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_lss_suse11sp3@sha256:5b71ff9db48fb6b571de0dd2c27422a1cfa4db7d16b0f13f9e5ca8d979f98cfc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_lss_suse11sp3:v600r001c11"], "sizeBytes": 1008944924}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricportalctz@sha256:d15fd7b019a1f6157a4de960bb011755d574100c5efd6489f007cb4ca389951c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/apifabricportalctz:8.2.20.LG0009"], "sizeBytes": 996573086}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/redis-broker@sha256:63778f219bb3a10f5f4b559f8f9d61be732dabe587a8f8f40c766c15197ed88e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/redis-broker:2.300.1"], "sizeBytes": 790945870}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dbscript_job@sha256:9a5ea0bd5829a1475e0e9ead95aedc13c4e2f0c1a9cabbd5f8992ab0d3bc63e9"], "sizeBytes": 679854302}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dbscript_job@sha256:d28421523c54c2afcc6e1cae95d75247ec3ffaec3bdcf3b4316745c61fabacf7", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dbscript_job:latest_2017102511"], "sizeBytes": 679710033}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dbscript_job@sha256:11dfc931d022cb366a13bc1ebf892350dcdb85974b430c76854659b8162536f8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dbscript_job:latest"], "sizeBytes": 678099581}], "nodeState": {}, "hostname": "prdocker16"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "b15cc62b-caef-11e7-8f32-fa163e811497", "resourceVersion": "40126967", "creationTimestamp": "2017-11-16T17:00:47Z", "labels": {"beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "ebackup": "true", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "EulerOS_2.0_SP2", "os.version": "3.10.0-327.36.58.4.x86_64", "supportContainer": "true"}, "annotations": {"node.kubernetes.io/finish-mark-pods-not-ready": "true", "volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "4", "memory": "7993980Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "4", "memory": "7993980Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "Unknown", "lastHeartbeatTime": "2017-11-21T15:04:36Z", "lastTransitionTime": "2017-11-21T15:05:23Z", "reason": "NodeStatusUnknown", "message": "Kubelet stopped posting node status."}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-21T15:04:36Z", "lastTransitionTime": "2017-11-16T17:00:47Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-21T15:04:36Z", "lastTransitionTime": "2017-11-16T17:00:47Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-21T15:04:36Z", "lastTransitionTime": "2017-11-16T17:00:47Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "Unknown", "lastHeartbeatTime": "2017-11-21T15:04:36Z", "lastTransitionTime": "2017-11-21T15:05:23Z", "reason": "NodeStatusUnknown", "message": "Kubelet stopped posting node status."}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "a615fa07e3136775490394f8f1a99aaa", "systemUUID": "C10B960C-CEE3-4A06-AE1F-C948390FFE66", "bootID": "a0fffa8c-f2b9-4136-90a4-ed5f55f97ca8", "kernelVersion": "3.10.0-327.36.58.4.x86_64", "osImage": "EulerOS 2.0 (SP2)", "containerRuntimeVersion": "docker://1.11.2", "kubeletVersion": "v2.11.1-FusionStage2.1-B039-SP1-dirty", "kubeProxyVersion": "v2.11.1-FusionStage2.1-B039-SP1-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/redis-server:v8r2c30"], "sizeBytes": 477862941}, {"names": ["canal-agent:2.3.RC3.SPC3", "canal-agent:latest"], "sizeBytes": 406890280}, {"names": ["************:20202/paas-test/mysql:latest"], "sizeBytes": 374045642}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender:1.0.4"], "sizeBytes": 78645352}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css:1.0.4"], "sizeBytes": 1094548}, {"names": ["************:20202/op_svc_cfe/default/cfe-pause:2.11.1"], "sizeBytes": 350164}], "nodeState": {}, "hostname": "host-10-209-67-189"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "770e2d6f-93da-11e7-8ff2-fa163e99a979", "resourceVersion": "41682083", "creationTimestamp": "2017-09-07T14:40:16Z", "labels": {"AppDefaultResourceType": "Normal", "BESResourceType": "Normal", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-11-22T07:06:33Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-09-07T14:40:16Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-09-07T14:40:16Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-09-07T14:40:16Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:04Z", "lastTransitionTime": "2017-11-22T07:06:33Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "C5F6429D-4FB9-4773-9445-AC325FEF4766", "bootID": "30d82753-2118-4cf9-8bc7-ac03093db405", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:968aa92f65cd933b2410261099b4319e685f9360b39f74b62fd60546574f6854", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 4222357091}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:50d05dbca580b0ba3c58c5976d98394e3ecc04d30690f1b57b5d1e7adda2a174", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008"], "sizeBytes": 3959444519}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:2ad76c611ec100d9756ca6405020e044c11fa3e7d1a96c95fcc653b486f5372f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3884157513}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:662a469c98ec52dee363a76671634203a90b1ff9721a288349ab320cb036d350", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11180000"], "sizeBytes": 3150200466}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:86297349ed4267db06361f175258e748b74714b286c002f2ad69eee75cd990bb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3061639616}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:30ba975a6bd6cda8338a69e48434a2d038038dbc9acecb4d361f9e2635b7a642", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3056445206}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:4272a71a91738b27f6d83a54017d9f14d1bb8d6c8bb993ba04c677e86c45478c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008"], "sizeBytes": 2988216417}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:a1cad0635ee5cd7d503a2c1437eb1bd3d412d939942e4e51b9511556abdf10fe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0008"], "sizeBytes": 2836528915}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:9919eefcff2fbdbaa82593545df371930143822750f27850d0aafe401ee32add", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2811609391}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:6048139c60ceb3c9b0fb2e7a591c5c7f2e01147d35045c05b5cd31f61cb66922", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11172200"], "sizeBytes": 2764600281}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:afa03a0788f543345c2da30cc8399223720071de9398b0aa82a42c5f62721815", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11101600_2017111317"], "sizeBytes": 2644317263}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5f88b9791d91e3ae4706340f6fa580c2a03aec342282979a54953e31c5639a73", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_10202100_2017102511"], "sizeBytes": 2624700495}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f62fe5644220b084eb8931292daab8ae1fe11d2b9a6cbfd6b859f61a3fcc8bdc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2494377012}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:2b732c57b71cc05c7bb051038c398c7ea01f87733dc8cbb93bd32056a0ab7404", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2493590740}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:0907c22556db9b912f979a03d0594ffd40089973dcdb3a9876cd99772adc4234", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2402074256}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:f58f8716497ae0743cc58505b3afad38ffd36a9a9c429ddf205c563bdcb26863", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2371593185}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5ed371dd08dd88cb9e95b422d45fe5ebc2806696db3155d8b84c7b89bf6df754", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2249861035}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:4bce750304f3ea75e10442c26b142d69c04b7d6885946c544d2bf982337997f2", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11031700"], "sizeBytes": 2233075166}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:6f573a7af65f4c37a1d6ac08dd2ff80915b222905c42962a77ac5f76550ffe70", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2218876394}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:129bbadc35be33e500c278486f5f8356c8b29d3c6c0351e20b3bd24591f79e6b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2192371137}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:b9feb97c3708f88340282675535b320c2b17eb6f2b4ec77f6a5a426dd7ac935b"], "sizeBytes": 2160854595}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:428b66ed8fe30ded4a123d2241cdb57d438b8be4f37aa982b39dca0b6137922d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 2149059722}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:d6fef8769c05bedfcbe80e794bbee037d4e5251c8e0879fcb8014a978d27ad0e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_1711092303_2017111317"], "sizeBytes": 1899306557}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:c1f2632583c4d84d8af9ac37bd86d1417f94bd3c779b2e963bd01976215c2dc6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0010_1711092303_2017111317"], "sizeBytes": 1731703438}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:6c158dc37f48bb324f0817162a1ba1c8558965d53582e32a10bfe71dd002ac6e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0009_092900"], "sizeBytes": 1696583736}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:2d711c4c7e94483847d8cf85a3cec57ee63dac3e8c7017f5593dd3c6c8ec1579", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0010_11031815"], "sizeBytes": 1547418834}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:5bf0a024560ddd03192a3664cac21874b36c5c3bd92f7ed485fa73999c478c0c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1535847110}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:19cec444a278531cea656cc354fbc4356aa4679a1e7262cb854fb3cd82643a7e"], "sizeBytes": 1535830580}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:8ddc0faddaf7b79285766691cde9ba511eb39df54627536a8469e85aded8ecbb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0009_09300330"], "sizeBytes": 1530378254}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:5a5e4534ecd9234e3409d06447bcb583b781531013bea8bb86e2e39c103b45f8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0010_11021405"], "sizeBytes": 1496026826}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:aad19ed59b0e80fd5d6f68c823fd7f8e26baab6aa68ad5f533e295b63d0b8b2a", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0009_09300536"], "sizeBytes": 1483802821}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f7c9dc8d2905ef21c68cd3b191a07131a91db53804f762221eedf947f7acfae0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0009_09300530"], "sizeBytes": 1475408329}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:4114a9b8cd05b7a50abe0e5baa402588446b77c75b828824150ec88c9e8b78d6"], "sizeBytes": 1475187390}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:c449e3c5c2588ae8df4c8678fa58706ee779e42337ce7016ccb8aaf08d7a9908", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0009_09291840"], "sizeBytes": 1473453291}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:4cc87a8560fb89cf3b7ab8828993c5d53dba6fc5ddd353824452d32758c47e2d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1473037369}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:bf5f9642502f46a509862b6016e050d8275ebd2f9730a3d05ff1aac3ffcac3ba", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1472786453}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:82ec4acde356514caa77039503d4c49700c559b23087d351911d6d8c6b9f4777"], "sizeBytes": 1460134008}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6235cd4d031d4b43a4e7d47f12696827b971c373b0b741b4241296c9947f8329", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11102300_2017111317"], "sizeBytes": 1350229449}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:0cb382aa5a199a7d1b0029fb0421d7f749d0662dcaa637083effa83a2526bce3", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0009_10202016_2017102511"], "sizeBytes": 1346097982}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:070c72b4d88a8992391e0eee0016a0b27e02a8e75fbfc06339d978da631d8581", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11031800"], "sizeBytes": 1339302858}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:2ce6589d144079bae33dc9ddc8c924f1c9e346fa05cd558343f08a3a70d799bc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1320862914}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:ad8ca18721e8f9932a940be8b33680b53de711334d7f6a58bc70a4c4ca052f94", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1316898047}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:191f93117f651ed9e2f048d18a741b9de7aab0f4b88b4f739e6a473c64ec097f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0010_11172200"], "sizeBytes": 1306224934}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6b154038b64a13d2ceeee6b53592a52fd1041539d4019cfea29c84e41bd6001c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0009_09300310"], "sizeBytes": 1299092342}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:4c85614d3ec7df8eca0643e78dcb23d3de3fafc4bfaa24d6559a750863228825", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11b024"], "sizeBytes": 1295223056}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:d21e6b24e4769111a181baaed7040ea424e0ae1f7fffe786e4f84cc6c8a45857"], "sizeBytes": 1294994735}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:0a482ebf19a8458d4c15cebd23ae0ed0c213ae7065d97494a645619ef7b66262"], "sizeBytes": 1291567500}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:5875de68092a1912924958ee1a083adb01a40b8bd4a7e06c2bd61e5686e227d0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11"], "sizeBytes": 1291515726}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:ebf129985201207bf81fd78db53510900ee07cb11d89434e09fefa7008b78b91"], "sizeBytes": 1277251039}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:d05be33d018496c0fc34be16be6df58bea623f684505e020330b32b8e6c65242", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0009_09291817"], "sizeBytes": 1271551462}], "nodeState": {}, "hostname": "prdocker02"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "8dcfd545-93db-11e7-8ff2-fa163e99a979", "resourceVersion": "41682077", "creationTimestamp": "2017-09-07T14:48:04Z", "labels": {"AppDefaultResourceType": "Normal", "BESResourceType": "Normal", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "SUSE_Linux_Enterprise_Server_12_SP2", "os.version": "4.4.59-92.17-default", "supportContainer": "true"}, "annotations": {"volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "24", "memory": "247115596Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:03Z", "lastTransitionTime": "2017-11-22T07:06:31Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:03Z", "lastTransitionTime": "2017-09-07T14:48:04Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:03Z", "lastTransitionTime": "2017-09-07T14:48:04Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:03Z", "lastTransitionTime": "2017-09-07T14:48:04Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:03Z", "lastTransitionTime": "2017-11-22T07:06:31Z", "reason": "KubeletReady", "message": "kubelet is posting ready status. AppArmor enabled"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "6bf04b4f96ddfa78fc857103599c8e71", "systemUUID": "891043E0-96AC-49D0-AAB0-35AED9666CD1", "bootID": "a71dab1b-172f-4edf-a95f-a612646ec05a", "kernelVersion": "4.4.59-92.17-default", "osImage": "SUSE Linux Enterprise Server 12 SP2", "containerRuntimeVersion": "docker://1.12.6", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:50d05dbca580b0ba3c58c5976d98394e3ecc04d30690f1b57b5d1e7adda2a174", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0008"], "sizeBytes": 3959444519}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:2ad76c611ec100d9756ca6405020e044c11fa3e7d1a96c95fcc653b486f5372f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3884157513}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:86297349ed4267db06361f175258e748b74714b286c002f2ad69eee75cd990bb", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 3061639616}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:4272a71a91738b27f6d83a54017d9f14d1bb8d6c8bb993ba04c677e86c45478c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0008"], "sizeBytes": 2988216417}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:a1cad0635ee5cd7d503a2c1437eb1bd3d412d939942e4e51b9511556abdf10fe", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0008"], "sizeBytes": 2836528915}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:9919eefcff2fbdbaa82593545df371930143822750f27850d0aafe401ee32add", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2811609391}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:f62fe5644220b084eb8931292daab8ae1fe11d2b9a6cbfd6b859f61a3fcc8bdc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2494377012}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:2b732c57b71cc05c7bb051038c398c7ea01f87733dc8cbb93bd32056a0ab7404", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.03-SNAPSHOT"], "sizeBytes": 2493590740}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:f58f8716497ae0743cc58505b3afad38ffd36a9a9c429ddf205c563bdcb26863", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2371593185}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:5ed371dd08dd88cb9e95b422d45fe5ebc2806696db3155d8b84c7b89bf6df754", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2249861035}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:601686830ac50df2a7cd2b33d1ec05f3be63254b164ccb1d808c100fdc16ecf7"], "sizeBytes": 2248052637}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt@sha256:bd52b2923bd9e4f82658606b320855f039290cd2f6a717246f2871a0affe138e", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_adminportal_hkt:8.2.20.LG0010_11031700"], "sizeBytes": 2233091696}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_customerserviceportal_hkt@sha256:6ba98d948aeb945eb803a147a8c5e867a9ba76233c075efe94c180930f674dec"], "sizeBytes": 2212409238}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:71699537fd14a1cac24bc0d7054dce88e16dea5fd361a3219ba47493550a2062", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0009_09300700"], "sizeBytes": 2196050578}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt@sha256:129bbadc35be33e500c278486f5f8356c8b29d3c6c0351e20b3bd24591f79e6b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_osp_hkt:8.2.20.LG0010_11040100"], "sizeBytes": 2192371137}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:428b66ed8fe30ded4a123d2241cdb57d438b8be4f37aa982b39dca0b6137922d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 2149059722}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:e2c8b9218eccb99566c455ada1e51b3e31868cd361e29390cb589f8f56e6be9f", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1734624308}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:70434e954ed031014447cf8dc623d6f6a9e75629271134877e23516b63a2596b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0010_11171500"], "sizeBytes": 1734205878}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:5ba8064b1dac880223b8a7205572b3a250b5f31b04c621abc99c888e2f34d70d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0009_1019_branch_2017102511"], "sizeBytes": 1723580163}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:4dd55e19ba082eaa8d26c05f1d8c0cb679bdcd1f0e025cc47d6e590b2a4d0248", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1666936762}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt@sha256:566c0934d8e267c6b944b01156609761e6be38223aa38ca093da5282d8ae5dcf", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ifengine_hkt:8.2.20.LG0008"], "sizeBytes": 1632244881}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:bd0d16829c3bfdf4cc249b9072959c2d3b17da7c019985210126ecd759e66669", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11151800"], "sizeBytes": 1572610477}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:5bf0a024560ddd03192a3664cac21874b36c5c3bd92f7ed485fa73999c478c0c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1535847110}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt@sha256:271567f4ed1926b21d0831269f6f07b0a5ed09a42ca61818ff74ce5128c85b45", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp3_hkt:8.2.20.LG0009_09300330"], "sizeBytes": 1521806676}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:5a5e4534ecd9234e3409d06447bcb583b781531013bea8bb86e2e39c103b45f8", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0010_11021405"], "sizeBytes": 1496026826}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:edc4f7b16fec29e335508908f6dc2417f145866a48c1d5e5794205f15e0dc57b", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0009_09300530"], "sizeBytes": 1484207289}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt@sha256:4114a9b8cd05b7a50abe0e5baa402588446b77c75b828824150ec88c9e8b78d6", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp1_hkt:8.2.20.LG0009_09300536"], "sizeBytes": 1475187390}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt@sha256:4cc87a8560fb89cf3b7ab8828993c5d53dba6fc5ddd353824452d32758c47e2d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_occoreapp2_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1473037369}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt@sha256:cafdbfe9c5f7299e648c73f48f9ace06cec89209a26e6e753abffb410ef28927", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_cmapp_hkt:8.2.20.LG0009_10202030_2017102511"], "sizeBytes": 1469327042}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:2ea3beb2f52898dcc04b067e9f440b7aa23f0073a368a6e15f91d6fb11ae362c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0010_11172250"], "sizeBytes": 1347384973}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:e0596079828ad21e5e0266c2069f5c06ba1e139cdfcd67197a3f4ca0f694dd43", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0010_11031640"], "sizeBytes": 1339690362}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:2ce6589d144079bae33dc9ddc8c924f1c9e346fa05cd558343f08a3a70d799bc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0010_11021800"], "sizeBytes": 1320862914}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:8b6ff5af3ad385aaf5116b2a9f29682ca2e50eb0408d186d8da2bc4668fe8a68", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0010_1108_2017111317"], "sizeBytes": 1316912058}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:6b154038b64a13d2ceeee6b53592a52fd1041539d4019cfea29c84e41bd6001c", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0009_09300310"], "sizeBytes": 1299092342}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:e0901fae60744cca8cb5e88bc13763a2b62ac767ff789b08d6f1ac48c9ff1978"], "sizeBytes": 1297980157}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:d21e6b24e4769111a181baaed7040ea424e0ae1f7fffe786e4f84cc6c8a45857", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3:v600r001c11b024"], "sizeBytes": 1294994735}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_vsearch_suse11sp3@sha256:0a482ebf19a8458d4c15cebd23ae0ed0c213ae7065d97494a645619ef7b66262"], "sizeBytes": 1291567500}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt@sha256:aceae32196902b5d221c8c3d972e0367a0d42d7b70eaebc9b8ceb45de904960d", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_msp_hkt:8.2.20.LG0010_11022200"], "sizeBytes": 1287603633}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt@sha256:81cf4d768da0be2021ce8cd6adb1fb5fe71a80e9a5de563af5d79b10d87890dc", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_ceapp_hkt:8.2.20.LG0010_11011800"], "sizeBytes": 1280803609}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:ebf129985201207bf81fd78db53510900ee07cb11d89434e09fefa7008b78b91"], "sizeBytes": 1277251039}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:8dd0ea23334b73e20866e9b1fdf0bfca4d7b0180f63709ce580845dc33354978", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0009_09291730"], "sizeBytes": 1274929375}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt@sha256:bb3e93109429e543ab51afac594b8c45b12b585fd90d9b4a271baa522f52f021", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_invapp_hkt:8.2.20.LG0009_09291817"], "sizeBytes": 1258153512}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_jetmq@sha256:2a6afbe4280e6dfb3dc7b1f6aec6c05c19453e097f18d34a015a2e47cd2f1314", "************:20202/tenant_hktvdc01/tenant_hktvdc01/dw_jetmq:v600r001c11"], "sizeBytes": 1239057631}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt@sha256:2c890f0f3538e7e9831f663b86c84acffdd05d82950bc1391bce81cbb1279fd0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_searchcenterapp_hkt:8.2.20.LG0009_09291830"], "sizeBytes": 1237715246}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt@sha256:07e6bc04c33cda21e05b8eec84a208a00fa8301912e959ef167cd513f8322717", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_commonapp_hkt:8.2.20.LG0009"], "sizeBytes": 1236171837}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt@sha256:37ccd4a951c497c7a12b33e8d02d1d2b6f0cbb648003e4e5854ff8c0e39cc2cd", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_usmapp_hkt:8.2.20.LG0009_091945"], "sizeBytes": 1228618435}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:3219ae8690189be14b682c1f1cf9d5a02a866a5d83cefcde61fa62633cf8c2b0", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1225786700}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt@sha256:639cee9f05b8272512af163282f069a0593ac1bf00c035b31f62b8a89684db85", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodcenterapp_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1223145049}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt@sha256:33194a0eab8d60a51aec231c25774cdc7a336b2042d83572a0567e57640eb1ca", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_prodconfapp_hkt:8.2.20.LG0008_2017090914"], "sizeBytes": 1220311071}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt@sha256:44935bf606ec708074572a0292c75e3bd57cb9fb7b3102d0ff443a274e212152", "************:20202/tenant_hktvdc01/tenant_hktvdc01/bes_omapp_hkt:8.2.20.LG0008"], "sizeBytes": 1216896473}], "nodeState": {}, "hostname": "prdocker03"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "ff0f8abb-9a84-11e7-b1a3-fa163e4a66d8", "resourceVersion": "41682105", "creationTimestamp": "2017-09-16T02:16:06Z", "labels": {"beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "EulerOS_2.0_SP2", "os.version": "3.10.0-327.36.58.4.x86_64", "role": "master", "supportContainer": "true"}, "annotations": {"scheduler.alpha.kubernetes.io/taints": "[{\"key\": \"noderole\",\"value\": \"master\",\"effect\": \"NoSchedule\"}]", "volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "8", "memory": "16250964Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "8", "memory": "16250964Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:07Z", "lastTransitionTime": "2017-11-22T07:06:36Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:07Z", "lastTransitionTime": "2017-09-16T02:16:06Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:07Z", "lastTransitionTime": "2017-09-16T02:16:06Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:07Z", "lastTransitionTime": "2017-09-16T02:16:06Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:07Z", "lastTransitionTime": "2017-11-22T07:06:36Z", "reason": "KubeletReady", "message": "kubelet is posting ready status"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "a615fa07e3136775490394f8f1a99aaa", "systemUUID": "A85C52BE-49FE-4A18-AC96-808DBDB1C30A", "bootID": "552ffc2a-8a74-41f0-80d5-319a5469c838", "kernelVersion": "3.10.0-327.58.59.16.h13.x86_64", "osImage": "EulerOS 2.0 (SP2)", "containerRuntimeVersion": "docker://1.11.2", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-etcd:latest"], "sizeBytes": 671050202}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-kube-controller-manager:latest"], "sizeBytes": 610228566}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-dfx-backup:latest"], "sizeBytes": 477974231}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-kube-apiserver:latest"], "sizeBytes": 468217040}, {"names": ["************:20202/op_svc_cfe/default/nginx-ingress-controller:1.0.1"], "sizeBytes": 417769414}, {"names": ["canal-agent:2.3.RC3.SPC3", "canal-agent:latest"], "sizeBytes": 406890280}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-canal-controller:latest"], "sizeBytes": 373677081}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-canal-apiserver:latest"], "sizeBytes": 354049506}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-kube-scheduler:latest"], "sizeBytes": 346674356}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/paas-csms-storagemgr:latest"], "sizeBytes": 345837726}, {"names": ["************:20202/op_svc_cfe/default/keepalived-vip:1.0.0"], "sizeBytes": 342486059}, {"names": ["************:20202/op_svc_cfe/default/cfe-kubedns-amd64:2.10.21"], "sizeBytes": 339559707}, {"names": ["************:20202/op_svc_cfe/default/cfe-exechealthz-amd64:2.10.21"], "sizeBytes": 323729306}, {"names": ["************:20202/op_svc_cfe/default/defaultbackend:1.0.0"], "sizeBytes": 302428176}, {"names": ["************:20202/op_svc_cfe/default/cfe-kube-dnsmasq-amd64:2.10.21"], "sizeBytes": 288343650}, {"names": ["************:20202/op_svc_cfe/default/euleros:2.2.3"], "sizeBytes": 287669367}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender:1.0.4"], "sizeBytes": 78645352}, {"names": ["haproxy:1.5.19-alpine"], "sizeBytes": 12007140}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css:1.0.4"], "sizeBytes": 1094548}, {"names": ["************:20202/op_svc_cfe/default/cfe-pause:2.10.21"], "sizeBytes": 350164}], "nodeState": {}, "hostname": "host-10-209-67-200"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "f79cfe6c-9a84-11e7-8ff2-fa163e99a979", "resourceVersion": "41682102", "creationTimestamp": "2017-09-16T02:15:53Z", "labels": {"beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "EulerOS_2.0_SP2", "os.version": "3.10.0-327.36.58.4.x86_64", "role": "master", "supportContainer": "true"}, "annotations": {"scheduler.alpha.kubernetes.io/taints": "[{\"key\": \"noderole\",\"value\": \"master\",\"effect\": \"NoSchedule\"}]", "volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "8", "memory": "16250964Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "8", "memory": "16250964Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:07Z", "lastTransitionTime": "2017-11-11T09:44:49Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:07Z", "lastTransitionTime": "2017-09-16T02:15:53Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:07Z", "lastTransitionTime": "2017-09-16T02:15:53Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:07Z", "lastTransitionTime": "2017-09-16T02:15:53Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:07Z", "lastTransitionTime": "2017-11-11T09:45:01Z", "reason": "KubeletReady", "message": "kubelet is posting ready status"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "a615fa07e3136775490394f8f1a99aaa", "systemUUID": "B2C2B048-1473-48E6-AA63-DB6FC138BB50", "bootID": "c99a7143-7b8a-4c4c-a5ec-b13aef00f608", "kernelVersion": "3.10.0-327.58.59.16.h13.x86_64", "osImage": "EulerOS 2.0 (SP2)", "containerRuntimeVersion": "docker://1.11.2", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-etcd:latest"], "sizeBytes": 671050202}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-kube-controller-manager:latest"], "sizeBytes": 610228566}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-dfx-backup:latest"], "sizeBytes": 477974231}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-kube-apiserver:latest"], "sizeBytes": 468217040}, {"names": ["************:20202/op_svc_cfe/default/nginx-ingress-controller:1.0.1"], "sizeBytes": 417769414}, {"names": ["canal-agent:2.3.RC3.SPC3", "canal-agent:latest"], "sizeBytes": 406890280}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-canal-controller:latest"], "sizeBytes": 373677081}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-canal-apiserver:latest"], "sizeBytes": 354049506}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-kube-scheduler:latest"], "sizeBytes": 346674356}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/paas-csms-storagemgr:latest"], "sizeBytes": 345837726}, {"names": ["************:20202/op_svc_cfe/default/keepalived-vip:1.0.0"], "sizeBytes": 342486059}, {"names": ["************:20202/op_svc_cfe/default/cfe-kubedns-amd64:2.10.21"], "sizeBytes": 339559707}, {"names": ["************:20202/op_svc_cfe/default/cfe-exechealthz-amd64:2.10.21"], "sizeBytes": 323729306}, {"names": ["************:20202/op_svc_cfe/default/defaultbackend:1.0.0"], "sizeBytes": 302428176}, {"names": ["************:20202/op_svc_cfe/default/cfe-kube-dnsmasq-amd64:2.10.21"], "sizeBytes": 288343650}, {"names": ["************:20202/op_svc_cfe/default/euleros:2.2.3"], "sizeBytes": 287669367}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender:1.0.4"], "sizeBytes": 78645352}, {"names": ["haproxy:1.5.19-alpine"], "sizeBytes": 12007140}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css:1.0.4"], "sizeBytes": 1094548}, {"names": ["************:20202/op_svc_cfe/default/cfe-pause:2.10.21"], "sizeBytes": 350164}], "nodeState": {}, "hostname": "host-10-209-67-201"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "fcb4786c-9a84-11e7-b1a3-fa163e4a66d8", "resourceVersion": "41682074", "creationTimestamp": "2017-09-16T02:16:02Z", "labels": {"beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "EulerOS_2.0_SP2", "os.version": "3.10.0-327.36.58.4.x86_64", "role": "master", "supportContainer": "true"}, "annotations": {"scheduler.alpha.kubernetes.io/taints": "[{\"key\": \"noderole\",\"value\": \"master\",\"effect\": \"NoSchedule\"}]", "volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "8", "memory": "16250964Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "8", "memory": "16250964Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-11-22T07:06:46Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-09-16T02:16:02Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-09-16T02:16:02Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-09-16T02:16:02Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:02Z", "lastTransitionTime": "2017-11-22T07:06:46Z", "reason": "KubeletReady", "message": "kubelet is posting ready status"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "a615fa07e3136775490394f8f1a99aaa", "systemUUID": "1D663ADF-D185-4177-895B-52AEF42C6F53", "bootID": "db0ca34f-382c-4a27-9a30-8f56c53df904", "kernelVersion": "3.10.0-327.58.59.16.h13.x86_64", "osImage": "EulerOS 2.0 (SP2)", "containerRuntimeVersion": "docker://1.11.2", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-etcd:latest"], "sizeBytes": 671050202}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-kube-controller-manager:latest"], "sizeBytes": 610228566}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-dfx-backup:latest"], "sizeBytes": 477974231}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-kube-apiserver:latest"], "sizeBytes": 468217040}, {"names": ["************:20202/op_svc_cfe/default/nginx-ingress-controller:1.0.1"], "sizeBytes": 417769414}, {"names": ["canal-agent:2.3.RC3.SPC3", "canal-agent:latest"], "sizeBytes": 406890280}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-canal-controller:latest"], "sizeBytes": 373677081}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-canal-apiserver:latest"], "sizeBytes": 354049506}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/cfe-kube-scheduler:latest"], "sizeBytes": 346674356}, {"names": ["************:20202/op_svc_cfe/op_svc_cfe/paas-csms-storagemgr:latest"], "sizeBytes": 345837726}, {"names": ["************:20202/op_svc_cfe/default/cfe-kubedns-amd64:2.10.21"], "sizeBytes": 339559707}, {"names": ["************:20202/op_svc_cfe/default/cfe-exechealthz-amd64:2.10.21"], "sizeBytes": 323729306}, {"names": ["************:20202/op_svc_cfe/default/defaultbackend:1.0.0"], "sizeBytes": 302428176}, {"names": ["************:20202/op_svc_cfe/default/cfe-kube-dnsmasq-amd64:2.10.21"], "sizeBytes": 288343650}, {"names": ["************:20202/op_svc_cfe/default/euleros:2.2.3"], "sizeBytes": 287669367}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender:1.0.4"], "sizeBytes": 78645352}, {"names": ["haproxy:1.5.19-alpine"], "sizeBytes": 12007140}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css:1.0.4"], "sizeBytes": 1094548}, {"names": ["************:20202/op_svc_cfe/default/cfe-pause:2.10.21"], "sizeBytes": 350164}], "nodeState": {}, "hostname": "host-10-209-67-202"}}, {"metadata": {"name": "*************", "namespace": "default", "selfLink": "/api/v1/namespaces/default/nodes/*************", "uid": "7414aaa4-b406-11e7-a76d-fa163e99a979", "resourceVersion": "41682097", "creationTimestamp": "2017-10-18T13:15:46Z", "labels": {"application.css": "css", "beta.kubernetes.io/arch": "amd64", "beta.kubernetes.io/os": "linux", "kubernetes.io/hostname": "*************", "os.architecture": "amd64", "os.name": "EulerOS_2.0_SP2", "os.version": "3.10.0-327.36.58.4.x86_64", "role": "master", "supportContainer": "true"}, "annotations": {"scheduler.alpha.kubernetes.io/taints": "[{\"key\":\"noderole\",\"value\":\"twistlock\",\"effect\":\"NoSchedule\"}]", "volumes.kubernetes.io/controller-managed-attach-detach": "true"}, "enable": true}, "spec": {"externalID": "*************", "loginSecret": {}, "schedulerHints": {}}, "status": {"capacity": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "4", "memory": "7993960Ki", "pods": "110"}, "allocatable": {"alpha.kubernetes.io/nvidia-gpu": "0", "cpu": "4", "memory": "7993960Ki", "pods": "110"}, "phase": "Running", "conditions": [{"type": "OutOfDisk", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-11-22T07:06:34Z", "reason": "KubeletHasSufficientDisk", "message": "kubelet has sufficient disk space available"}, {"type": "MemoryPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-10-18T13:15:21Z", "reason": "KubeletHasSufficientMemory", "message": "kubelet has sufficient memory available"}, {"type": "NetworkCardNotFound", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-10-18T13:15:21Z", "reason": "NetworkCardFound", "message": "network card has found"}, {"type": "DiskPressure", "status": "False", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-10-18T13:15:21Z", "reason": "KubeletHasNoDiskPressure", "message": "kubelet has no disk pressure"}, {"type": "Ready", "status": "True", "lastHeartbeatTime": "2017-11-25T07:23:06Z", "lastTransitionTime": "2017-11-22T07:06:34Z", "reason": "KubeletReady", "message": "kubelet is posting ready status"}], "addresses": [{"type": "LegacyHostIP", "address": "*************"}, {"type": "InternalIP", "address": "*************"}, {"type": "Hostname", "address": "*************"}, {"type": "DataIP", "address": "*************"}], "daemonEndpoints": {"kubeletEndpoint": {"Port": 10250}}, "nodeInfo": {"machineID": "a615fa07e3136775490394f8f1a99aaa", "systemUUID": "D697AE35-68B1-4E6C-A7A7-5B37E750EEBD", "bootID": "77d1a242-38bf-413f-9126-77e6e18cf8fb", "kernelVersion": "3.10.0-327.58.59.16.h13.x86_64", "osImage": "EulerOS 2.0 (SP2)", "containerRuntimeVersion": "docker://1.11.2", "kubeletVersion": "v2.10.21-FusionStage2.1-B039-dirty", "kubeProxyVersion": "v2.10.21-FusionStage2.1-B039-dirty", "operatingSystem": "linux", "architecture": "amd64"}, "images": [{"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockconsole:1.0.4"], "sizeBytes": 635504307}, {"names": ["canal-agent:2.3.RC3.SPC3", "canal-agent:latest"], "sizeBytes": 406890280}, {"names": ["canal-agent:2.3.RC3.SPC2.B010"], "sizeBytes": 391548702}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/twistlockdefender:1.0.4"], "sizeBytes": 78645352}, {"names": ["************:20202/tenant_hktvdc01/tenant_hktvdc01/busybox-css:1.0.4"], "sizeBytes": 1094548}, {"names": ["************:20202/op_svc_cfe/default/cfe-pause:2.9.12"], "sizeBytes": 350164}], "nodeState": {}, "hostname": "PR-CR<PERSON>-<PERSON><PERSON><PERSON><PERSON>"}}]}