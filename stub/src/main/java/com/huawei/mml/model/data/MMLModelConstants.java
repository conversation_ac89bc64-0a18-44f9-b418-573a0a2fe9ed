package com.huawei.mml.model.data;

import java.util.HashMap;
import java.util.Map;


public class MMLModelConstants 
{
	public static final HashMap<String, Object> MML_MOUDLE_LIST = new HashMap<String, Object>();
	public static final HashMap<String, Boolean> MML_MOUDLE_STATUS = new HashMap<String, Boolean>();
	public static final String UAP_PRODUCT_UAP6600 = "UAP6600";
	public static final String UAP_PRODUCT_UAP9600 = "UAP9600";
	public static final String UAP_PRODUCT_UAP8100_MGW = "UAP8100_MGW";
	public static final String UAP_PRODUCT_UAP8100_MGC = "UAP8100_MGC";
	public static final String IIN_PRODUCT_SCP_VCP = "VCP";
	public static final String IIN_PRODUCT_SCP_OCG = "OCG";
	public static final String IIN_PRODUCT_SMP_VMP = "VMP";
//	//记录表量的测量任务，每个测量对象一个任务
//	public static Map<String,String> UAP8100_MGC_TRFCCP=new HashMap<String,String>();
//
//	//记录标量的测量任务
//	public static Map<String,String> UAP8100_MGC_TRFINF=new HashMap<String,String>();
	
	public static Map<String,TrafficTaskUAP8100MGC > TRFTASK_UAP8100_MGC = new HashMap<String,TrafficTaskUAP8100MGC>();
	public static Map<String,TrafficTaskUAP8100MGW > TRFTASK_UAP8100_MGW = new HashMap<String,TrafficTaskUAP8100MGW>();
	public static Map<String,TrafficTaskSCP > TRFTASK_SCP = new HashMap<String,TrafficTaskSCP>();
	public static Map<String,TrafficTaskSCP > TRFTASK_OCG = new HashMap<String,TrafficTaskSCP>();
	public static Map<String,TrafficTaskSMP > TRFTASK_SMP = new HashMap<String,TrafficTaskSMP>();
	
}
