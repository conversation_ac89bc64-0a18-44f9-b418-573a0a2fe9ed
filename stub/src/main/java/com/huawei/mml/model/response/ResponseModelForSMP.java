package com.huawei.mml.model.response;

import com.huawei.mml.base.util.Constants;
import com.huawei.mml.base.util.TimeUtil;
import com.huawei.mml.model.data.TrafficTaskSMP;
/**
 * 
 * <一句话功能简述>用于返回MML操作成功的数据包
 * <功能详细描述>
 * 
 * <AUTHOR>
 * @version  [版本号, 2015-7-9]
 * @see  [相关类/方法]
 * @since  [产品/模块版本]
 */
public class ResponseModelForSMP {
    /**
     * <一句话功能简述>将消息体添加消息头和消息体，返回消息包
     * <功能详细描述>
     * @param body 消息体
     * @return 消息包
     * @see [类、类#方法、类#成员]
     */
    private static String addHesdEnd(String body)
    {
        String head="+++    0                                       "+TimeUtil.getSystemTime()+Constants.CHANGE_NEW_LINE;
        String end="---    END"+Constants.CHANGE_NEW_LINE;
        StringBuffer dataPackage=new StringBuffer(head);
        dataPackage.append(body).append(end);
        return dataPackage.toString();
    }
    /**
     * <一句话功能简述>根据NEID将消息体添加消息头和消息体，返回消息包
     * <功能详细描述>
     * @param neID
     * @return
     * @see [类、类#方法、类#成员]
     */
    private static String addHesdEndWithNeID(String neID,String body)
    {
        String head="+++    "+neID+"                                       "+TimeUtil.getSystemTime()+Constants.CHANGE_NEW_LINE;
        String end="---    END"+Constants.CHANGE_NEW_LINE;
        StringBuffer dataPackage=new StringBuffer(head);
        dataPackage.append(body).append(end);
        return dataPackage.toString();
    }

    /**
     * <一句话功能简述>将性能数据封装的消息体
     * <功能详细描述>
     * @param netID
     * @param byody
     * @return
     * @see [类、类#方法、类#成员]
     */
    private static String addHeadEndOfTrafficData(String netID,String body)
    {
        String head="+++    "+netID+"        "+TimeUtil.getCurrSystem5MinuteTime()+Constants.CHANGE_NEW_LINE;
        String end="---    END"+Constants.CHANGE_NEW_LINE;
        StringBuffer dataPackage=new StringBuffer(head);
        dataPackage.append(body).append(end);
        return dataPackage.toString();
    }
    
    /**
     * <一句话功能简述>根据登陆命令返回登陆成功的数据包
     * <功能详细描述>
     * @param order  //login:user="11",pswd="yulkmin2";
     * @return
     * @see [类、类#方法、类#成员]
     */
    public static String cmd_login(String op, String pwd)
    {
        StringBuffer body=new StringBuffer();
        body.append("CONFIG        #1").append(Constants.CHANGE_NEW_LINE)
            .append("%%").append("login:user=\"").append(op).append("\",pswd=\"").append("*****\";%%").append(Constants.CHANGE_NEW_LINE)
            .append("RETCODE = 0  Operator login succeeded").append(Constants.CHANGE_NEW_LINE);
        return addHesdEnd(body.toString());
    }
    
    /**
     * <一句话功能简述>获取query oam route:;命令的数据包
     * <功能详细描述>
     * @param order
     * @return
     * @see [类、类#方法、类#成员]
     */
    public static String cmd_query_oam_route(String order)
    {
        StringBuffer body=new StringBuffer();
        body.append("CONFIG        #60").append(Constants.CHANGE_NEW_LINE)
            .append("%%").append(order).append("%%").append(Constants.CHANGE_NEW_LINE)
            .append("RETCODE = 0  Querying route information succeeded").append(Constants.CHANGE_NEW_LINE)
            .append(Constants.CHANGE_NEW_LINE)
            .append("vertical list attribute").append(Constants.CHANGE_NEW_LINE)
            .append("-------------------------").append(Constants.CHANGE_NEW_LINE)
            .append("             PARNODE             MACHINENM             CHILDNODE                   CHILDIP             CHILDPORT             CHILDSTAT                   CHILDCL               CHILDMS             CHILDTYPE                   CHILDNM                    CHILDDESC                     NETID               CHILDCR").append(Constants.CHANGE_NEW_LINE)
            .append(Constants.CHANGE_NEW_LINE)
//            .append("      00022000000004               node-04        00021001004400             *************                  7904                     1                        -1                     1                  SCDU           SCDU128@node-04").append(MMLContaint.CHANGE_NEW_LINE)
            .append("      00022000000014             node-1-14        00009000101110              ************                  7910                     0                        -1                     1                  ISMP          ISMP10@node-1-14                     SiteA                           268                  0002      ").append(Constants.CHANGE_NEW_LINE)
            .append("(TOTAL RECORD(S)    =    1)").append(Constants.CHANGE_NEW_LINE)
            .append(Constants.CHANGE_NEW_LINE);        
            return addHesdEnd(body.toString());
    }
    
    /**
     * <一句话功能简述>查询版本
     * <功能详细描述>
     * @param order
     * @return
     * @see [类、类#方法、类#成员]
     */
    public static String cmd_query_soft_version(String order)
    {
        StringBuffer body=new StringBuffer();
        body.append("CONFIG        #813").append(Constants.CHANGE_NEW_LINE)
            .append("%%").append(order).append("%%").append(Constants.CHANGE_NEW_LINE)
//            .append("RETCODE = 8  NODEIDCompulsory parameter is not input").append(MMLContaint.CHANGE_NEW_LINE)
		.append("RETCODE = 0  ").append("Operation succeeded").append(Constants.CHANGE_NEW_LINE)
		.append(Constants.CHANGE_NEW_LINE)
		.append("Result of current software query").append(Constants.CHANGE_NEW_LINE)
		.append(" --------------------------------").append(Constants.CHANGE_NEW_LINE)
		.append(" VALUE  =  SCP TEST01").append(Constants.CHANGE_NEW_LINE);
        return addHesdEnd(body.toString());
    }
    
    /**
     * <一句话功能简述>获取性能采集任务下发成功返回的数据包
     * <功能详细描述>
     * @param order
     * @return
     * @see [类、类#方法、类#成员]
     */
    public static String cmd_cre_trf(String order,String netID)
    {

        StringBuffer body=new StringBuffer();
        body.append("TASKCMD       #623").append(Constants.CHANGE_NEW_LINE)
            .append("%%").append(order).append("%%").append(Constants.CHANGE_NEW_LINE)
            .append("RETCODE = 0  CRE TRFCAP Success").append(Constants.CHANGE_NEW_LINE);
            
        return addHesdEndWithNeID(netID,body.toString());
    }

	public static String traffic(TrafficTaskSMP value) 
	{
        String taskID=value.getTaskID();
        String period=value.getPrd();
        String netID=value.getNetID();
        //String srvkey=value.getSrvKey();
		return traffic_SMAPNMB(taskID,period,netID);
	}
	
    //TRFCAPSMS
    public static String traffic_SMAPNMB(String taskID,String period,String netID)
    {
        StringBuffer body=new StringBuffer();
        body.append("TRAFFIC    #").append(taskID).append("        SMAPNMB").append(Constants.CHANGE_NEW_LINE)
            .append(TimeUtil.getServralDayAgo(1)).append("     2037-12-31     ").append(period).append("     ").append(TimeUtil.getSomeTimeAgo5MinuteTime(1)).append(Constants.CHANGE_NEW_LINE)
            .append(Constants.CHANGE_NEW_LINE)
            
            .append("NETID              NODEID            ")
            .append("     SMAPNMB      ")
            .append(Constants.CHANGE_NEW_LINE)
            
            .append(netID).append("        00009000101110     ")
            .append("        1        ")
            .append(Constants.CHANGE_NEW_LINE)
            
            .append(Constants.CHANGE_NEW_LINE)
            .append("1 RECORD(S)   IN REPORT #1    TOTAL 1 RECORD(S)").append(Constants.CHANGE_NEW_LINE);
            
            return addHeadEndOfTrafficData(netID,body.toString());
    }
    
    //"SMPMML/ISMP/SMPSYS/OAMS"
    /**
     * <一句话功能简述>获取告警数据包
     * <功能详细描述>
     * @return
     * @see [类、类#方法、类#成员]
     */
    public static String Alarm_SMP_VMP()
    {

        StringBuffer body=new StringBuffer();
        body.append("ALARM  #407        FAULT      IMPORTANT    SMPSYS      407040000             SERVICE").append(Constants.CHANGE_NEW_LINE)
            .append("           TITLE  =  SMP being disconnected from external nodes").append(Constants.CHANGE_NEW_LINE)
            .append("          REASON  =  Threshold Crossed").append(Constants.CHANGE_NEW_LINE)
            .append("      SUGGESTION  =  NULL").append(Constants.CHANGE_NEW_LINE)
            .append("        HOSTTYPE  =  A").append(Constants.CHANGE_NEW_LINE)
            .append("        LOCATION  =  IIN virture alarm...").append(Constants.CHANGE_NEW_LINE)
            .append("     DESCRIPTION  =  this alarm message is from IIN virture device if you see it that means its channel ok!").append(Constants.CHANGE_NEW_LINE);
        return addHesdEndWithNeID("268",body.toString());
    }
    
	public static String CMD_NULL_RETURN(String recieCole) 
	{
        StringBuffer body=new StringBuffer();
        body.append("TASKCMD       #613").append(Constants.CHANGE_NEW_LINE)
            .append("%%").append(recieCole).append("%%").append(Constants.CHANGE_NEW_LINE)
            .append("RETCODE = 0  Operation succeeded").append(Constants.CHANGE_NEW_LINE);           
        return addHesdEnd(body.toString());     
	}
}

