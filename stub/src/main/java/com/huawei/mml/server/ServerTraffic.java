package com.huawei.mml.server;

import java.io.OutputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;

import com.huawei.mml.base.util.T2000Logger;
import com.huawei.mml.model.data.MMLModelConstants;
import com.huawei.mml.model.manager.SSLServerInterface;
/**
 * 
 * <一句话功能简述>性能服务
 * <功能详细描述>
 * 
 * <AUTHOR>
 * @version  [版本号, 2015-7-9]
 * @see  [相关类/方法]
 * @since  [产品/模块版本]
 */
public class ServerTraffic implements SSLServerInterface, Runnable
{
	private static Logger logger = T2000Logger.getInstance().getT2000Logger(ServerTraffic.class);
    private ServerSocket server = null;
    private String productType = null;
    private final int TRAFFIC_DEFF_PORT;
    private final String protType = "TRAFFIC";
    private boolean runFlag = true;
    private Map<String, Socket> socketList = new HashMap<String, Socket>();
    public ServerTraffic(int port)
    {
    	this.TRAFFIC_DEFF_PORT = port;
        init();
    }
    
    @Override
    public void init()
    {
        try
        {
            server = new ServerSocket(TRAFFIC_DEFF_PORT);
            
        }
        catch (Throwable e)
        {	
        	logger.error("TrafficServer init error is :", e);
            e.printStackTrace();
        }
        
    }
    
    @Override
    public void startServer()
    {
        Socket client = null;
        while (MMLModelConstants.MML_MOUDLE_STATUS.get(productType))
        {
            try
            {
                client = server.accept();
				if (client == null) {
					continue;
				}
				String ipString = client.getInetAddress().toString();
				Socket socket = socketList.get(ipString);
				if (socket!= null) {
					socket.close();
				}
				socketList.put(ipString, client);
				
                //启动线程读取数据
				new Thread(new SSLRead(client,productType,protType)).start();
                //启动定时任务
                new Thread(new SendTrafficData(client,productType)).start();
            }
            catch (Throwable e)
            {
            	logger.error("TrafficServer start error is :", e);
                e.printStackTrace();
                break;
            }
        }
    }
    
    @Override
    public void stopServer()
    {
        runFlag = false;
        //创建虚假连接
        try
        {
			Socket virClient = new Socket("localhost", TRAFFIC_DEFF_PORT);
            OutputStream outputStream = virClient.getOutputStream();
            outputStream.write(-1);
            outputStream.flush();
            outputStream.close();
            virClient.close();
            server.close();
            socketList.clear();
            MMLModelConstants.MML_MOUDLE_STATUS.put(productType, Boolean.valueOf(false));
        }
        catch (Throwable e)
        {
        	logger.error("TrafficServer stop error is :", e);
            e.printStackTrace();
        }
        
    }
    
    @Override
    public void run()
    {
        startServer();
    }

	public void setProductType(String product) {
		productType = product;
		
	}
    
}
