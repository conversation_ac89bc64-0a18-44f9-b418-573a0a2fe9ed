package com.huawei.mml.server.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map.Entry;

import org.apache.log4j.Logger;

import com.huawei.mml.base.util.T2000Logger;
import com.huawei.mml.model.data.MMLModelConstants;
import com.huawei.mml.model.data.TrafficTaskSCP;
import com.huawei.mml.model.data.TrafficTaskSMP;
import com.huawei.mml.model.data.TrafficTaskUAP8100MGC;
import com.huawei.mml.model.data.TrafficTaskUAP8100MGW;
import com.huawei.mml.model.response.ResponseModelForSCP;
import com.huawei.mml.model.response.ResponseModelForSMP;
import com.huawei.mml.model.response.ResponseModelForUAP6600;
import com.huawei.mml.model.response.ResponseModelForUAP8100MGC;
import com.huawei.mml.model.response.ResponseModelForUAP8100MGW;
import com.huawei.mml.model.response.ResponseModelForUAP9600;

/**
 * 
 * <一句话功能简述>用于向I2000上报告警
 * <功能详细描述>
 * 
 * <AUTHOR>
 * @version  [版本号, 2015-7-8]
 * @see  [相关类/方法]
 * @since  [产品/模块版本]
 */
public class ReportUtilTraffic
{
	private static Logger logger = T2000Logger.getInstance().getT2000Logger(ReportUtilTraffic.class);
	public static List<String> getMessages(String product, String adress) 
	{
		List<String> retList = null;
		switch (product) 
		{
			case MMLModelConstants.UAP_PRODUCT_UAP6600:
				retList = getTrafficUAP6600();
				break;
			case MMLModelConstants.UAP_PRODUCT_UAP9600:
				retList = getTrafficUAP9600();
				break;			
			case MMLModelConstants.UAP_PRODUCT_UAP8100_MGW:
				retList = getTrafficUAP8100_MGW();
				break;
			case MMLModelConstants.UAP_PRODUCT_UAP8100_MGC:
				retList = getTrafficUAP8100_MGC();
				break;
			case MMLModelConstants.IIN_PRODUCT_SCP_VCP:
				retList = getTrafficSCP_VCP(adress);
				break;
			case MMLModelConstants.IIN_PRODUCT_SCP_OCG:
				retList = getTrafficSCP_OCG(adress);
				break;
			case MMLModelConstants.IIN_PRODUCT_SMP_VMP:
				retList = getTrafficSMP_VMP(adress);
				break;
			default:
				break;
		}
		return retList;
	}	
    private static List<String> getTrafficSMP_VMP(String adress) 
    {
    	List<String> retList = new ArrayList<String>();
    	logger.info(MMLModelConstants.TRFTASK_SMP);
		for (Entry<String, TrafficTaskSMP> entry : MMLModelConstants.TRFTASK_SMP.entrySet()) 
		{
			if (entry.getKey().contains(adress)) 
			{
				retList.add(ResponseModelForSMP.traffic(entry.getValue()));
			}
		}		
		return retList;
	}
	private static List<String> getTrafficSCP_OCG(String adress) 
    {
    	List<String> retList = new ArrayList<String>();
    	logger.info(MMLModelConstants.TRFTASK_OCG);
		for (Entry<String, TrafficTaskSCP> entry : MMLModelConstants.TRFTASK_OCG.entrySet()) 
		{
			if (entry.getKey().contains(adress)) 
			{
				retList.add(ResponseModelForSCP.traffic(entry.getValue()));
			}			
		}		
		return retList;
	}
	private static List<String> getTrafficSCP_VCP(String adress) 
    {
    	List<String> retList = new ArrayList<String>();
    	logger.info(MMLModelConstants.TRFTASK_SCP);
		for (Entry<String, TrafficTaskSCP> entry : MMLModelConstants.TRFTASK_SCP.entrySet()) 
		{
			if (entry.getKey().contains(adress)) 
			{
				retList.add(ResponseModelForSCP.traffic(entry.getValue()));
			}			
		}		
		return retList;
	}
	private static List<String> getTrafficUAP8100_MGC()
    {
    	List<String> retList = new ArrayList<String>();
    	logger.info(MMLModelConstants.TRFTASK_UAP8100_MGC);
		for (Entry<String, TrafficTaskUAP8100MGC> entry : MMLModelConstants.TRFTASK_UAP8100_MGC.entrySet()) 
		{
			retList.add(ResponseModelForUAP8100MGC.traffic(entry.getValue()));
		}		
		return retList;
	}
	private static List<String> getTrafficUAP9600() 
    {
    	List<String> retList = new ArrayList<String>();
    	retList.add(ResponseModelForUAP9600.traffic_21());
    	retList.add(ResponseModelForUAP9600.traffic_306());
		return retList;
	}
	private static List<String> getTrafficUAP6600() 
	{
    	List<String> retList = new ArrayList<String>();
    	retList.add(ResponseModelForUAP6600.traffic_10000());
    	retList.add(ResponseModelForUAP6600.traffic_10001());
    	retList.add(ResponseModelForUAP6600.traffic_10002());
    	retList.add(ResponseModelForUAP6600.traffic_5_21());
    	retList.add(ResponseModelForUAP6600.traffic_5_41());
    	retList.add(ResponseModelForUAP6600.traffic_6_21());
    	retList.add(ResponseModelForUAP6600.traffic_6_41());
		return retList;
	}
	private static List<String> getTrafficUAP8100_MGW() 
	{
    	List<String> retList = new ArrayList<String>();
    	logger.info(MMLModelConstants.TRFTASK_UAP8100_MGW);
		for (Entry<String, TrafficTaskUAP8100MGW> entry : MMLModelConstants.TRFTASK_UAP8100_MGW.entrySet()) 
		{
			retList.add(ResponseModelForUAP8100MGW.traffic(entry.getValue()));
		}		
		return retList;
	}
}
