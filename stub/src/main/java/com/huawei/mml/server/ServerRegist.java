package com.huawei.mml.server;

import java.io.IOException;
import java.io.OutputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;

import com.huawei.mml.base.util.T2000Logger;
import com.huawei.mml.model.data.MMLModelConstants;
import com.huawei.mml.model.manager.SSLServerInterface;
/**
 * 
 * <一句话功能简述>主服务
 * <功能详细描述>
 * 
 * <AUTHOR>
 * @version  [版本号, 2015-7-9]
 * @see  [相关类/方法]
 * @since  [产品/模块版本]
 */
public class ServerRegist implements SSLServerInterface ,Runnable{
	
	private static Logger logger = T2000Logger.getInstance().getT2000Logger(ServerRegist.class);
	private final String protType = "REGISTER";
	private ServerSocket server=null;
	private final int  REG_DEFF_PORT; 
	private  boolean runFlag=true; 
	private Map<String, Socket> socketList = new HashMap<String, Socket>();
	private String productType = null;
	public ServerRegist(int port)
	{	
		this.REG_DEFF_PORT = port;
		init();
	}

	public void setProductType(String product) {
		productType = product;
	}
	@Override
	public void init() {
		try {
		    logger.info("start port:" + REG_DEFF_PORT);
			server=new ServerSocket(REG_DEFF_PORT);
			socketList.clear();
		} catch (IOException e) {
			logger.error("RegisterServer init error is :", e);
			e.printStackTrace();
		}
	}

	@Override
	public void startServer() 
	{
		Socket client=null;
		while(runFlag)
		{
			try 
			{
				client=server.accept();
				if (client == null) {
					continue;
				}
				String ipString = client.getInetAddress().toString();
				Socket socket = socketList.get(ipString);
				if (socket!= null) {
					socket.close();
				}
				socketList.put(ipString, client);
				//启动线程读取数据
				new Thread(new SSLRead(client,productType,protType)).start();
				
			} catch (Throwable e) {
				logger.error("RegisterServer start error is :",e);
				//e.printStackTrace();
				break;
			}
		}
	}

	@Override
	public void stopServer() {
		runFlag=false;
		//创建虚假连接
		try {
			Socket virClient=new Socket("localhost", REG_DEFF_PORT);
			OutputStream outputStream = virClient.getOutputStream();
			outputStream.write(-1);
			outputStream.flush();
			outputStream.close();
			virClient.close();
			server.close();
			socketList.clear();
			MMLModelConstants.MML_MOUDLE_STATUS.put(productType, Boolean.valueOf(false));
		} catch (Exception e) {
			logger.error("RegisterServer stop error is :",e);
			e.printStackTrace();
		}
		
	}

	@Override
	public void run() {
		startServer();
		
	}

	
}
