package com.huawei.mml.server;

import java.io.OutputStream;
import java.net.Socket;
import java.util.List;

import org.apache.log4j.Logger;

import com.huawei.mml.base.util.T2000Logger;
import com.huawei.mml.model.data.MMLModelConstants;
import com.huawei.mml.server.util.ReportUtilTraffic;
/**
 * 
 * <一句话功能简述>该类用于上报性能
 * <功能详细描述>
 * 
 * <AUTHOR>
 * @version  [版本号, 2015-7-9]
 * @see  [相关类/方法]
 * @since  [产品/模块版本]
 */
public class SendTrafficData implements Runnable {
	private boolean runFlag = true;
	private String productType = null;
	private Socket socket = null;
	private static Logger logger = T2000Logger.getInstance().getT2000Logger(SendTrafficData.class);
	public SendTrafficData(Socket socket, String product)
	{
		this.socket = socket;
		productType = product;
	}

	@Override
	public void run() {

		while (MMLModelConstants.MML_MOUDLE_STATUS.get(productType)) 
		{
			if (socket.isClosed()) 
			{
				break;
			}
			String  adress = socket.getInetAddress().toString()+":"+socket.getPort();
			logger.info(adress+"\r\n");
			List<String>msgs = ReportUtilTraffic.getMessages(productType,socket.getInetAddress().toString());
			if (msgs == null) 
			{
				continue;
			}
			logger.info(productType);
			for (String msg : msgs) {
				OutputStream outputStream = null;
				try 
				{
					if (socket.isClosed()) 
					{
						break;
					}
					outputStream = socket.getOutputStream();
					
					logger.info(msg);
					outputStream.write(msg.getBytes("gbk"));
					outputStream.flush();
				} 
				catch (Exception e) 
				{
					stopTrafficSend();
					e.printStackTrace();
					break;
				}
			}
			logger.debug("性能数据发送完毕！");
			
			try {
				Thread.sleep(1000 * 5 * 60);
			} catch (InterruptedException e) {
				e.printStackTrace();
				break;
			}

		}
	}

	public void stopTrafficSend() {
		this.runFlag = false;
	}

}
