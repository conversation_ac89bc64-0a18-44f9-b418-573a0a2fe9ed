package com.huawei.mml.server;

import org.apache.log4j.Logger;
import org.dom4j.DocumentException;

import com.huawei.mml.base.util.T2000Logger;
import com.huawei.mml.model.data.MMLModelConstants;
import com.huawei.mml.model.data.MMLNeVO;
import com.huawei.mml.model.manager.SSLServerFactory;

/**
 * 
 * <一句话功能简述>用于启动所有服务 <功能详细描述>
 * 
 * <AUTHOR>
 * @version [版本号, 2015-7-9]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
public class MMLServer {
	private static Logger logger = T2000Logger.getInstance().getT2000Logger(MMLServer.class);
	private ServerRegist registServer = null;

	private ServerAlarm alarmServer = null;

	private ServerTraffic trafficServer = null;
	private String moduleName = null;
	MMLNeVO vo = null;
	private SSLServerFactory sslServerFactory = SSLServerFactory.newInstance();
	public boolean status = true;

	
	public MMLServer(String moduleName) {
		this.moduleName =moduleName;
		this.vo = (MMLNeVO)MMLModelConstants.MML_MOUDLE_LIST.get(moduleName);
	}

	public void start(String moduleName) throws DocumentException {
		status = true;
		registServer = sslServerFactory.createRegServer(Integer.parseInt(vo.getRPort()));
		alarmServer = sslServerFactory.createAlarmServer(Integer.parseInt(vo.getAPort()));
		trafficServer = sslServerFactory.createTrafficServer(Integer.parseInt(vo.getTPort()));
		registServer.setProductType(vo.getProduct());
		alarmServer.setProductType(vo.getProduct());
		trafficServer.setProductType(vo.getProduct());
		new Thread(registServer).start();
		new Thread(alarmServer).start();
		new Thread(trafficServer).start();
		MMLModelConstants.MML_MOUDLE_STATUS.put(vo.getProduct(), Boolean.valueOf(true));
	}

	public void sendMessage() 
	{
		if (MMLModelConstants.MML_MOUDLE_STATUS.get(vo.getProduct())) 
		{
			alarmServer.alarm();
		}
		
	}

	public void stop() {
		status = false;
		try 
		{
			trafficServer.stopServer();
			alarmServer.stopServer();
			registServer.stopServer();
			trafficServer = null;
			alarmServer = null;
			registServer = null;
			MMLModelConstants.TRFTASK_UAP8100_MGC.clear();
			MMLNeVO vo = (MMLNeVO)MMLModelConstants.MML_MOUDLE_LIST.get(moduleName);
			MMLModelConstants.MML_MOUDLE_STATUS.put(vo.getProduct(), Boolean.valueOf(false));
		} 
		catch (Exception e) 
		{
			logger.error("Stop MMLServer failed!  "+ e);
		}
	}

}
