package com.huawei.mml.server;

import java.io.IOException;
import java.io.InputStream;
import java.net.Socket;
import java.util.Arrays;

import org.apache.log4j.Logger;

import com.huawei.mml.base.util.T2000Logger;
import com.huawei.mml.model.data.MMLModelConstants;

/**
 * 
 * <一句话功能简述>读取I2000MML命令
 * <功能详细描述>
 * 
 * <AUTHOR>
 * @version  [版本号, 2015-7-9]
 * @see  [相关类/方法]
 * @since  [产品/模块版本]
 */
public class SSLRead implements Runnable
{
    private final static Logger LOGGER = T2000Logger.getInstance().getT2000Logger(SSLRead.class);
    
    private Socket client = null;
    private String productType = null;
    private String protType = null;
    public SSLRead(Socket client, String product,String prot)
    {
        this.client = client;
        productType = product;
        protType = prot;
    }
    
    @Override
    public void run()
    {
        InputStream inputStream = null;
        byte[] data = new byte[1024 * 10];
        while (MMLModelConstants.MML_MOUDLE_STATUS.get(productType))
        {
            try
            {
            	if (client.isClosed()) {
            		break;
				}
                inputStream = client.getInputStream();
                
                int length = inputStream.read(data);
                
                if (length == 1)
                {
                    break;
                }
                if (length != -1)
                {	
                    String msg = new String(Arrays.copyOf(data, length), "gbk");
                    LOGGER.info(protType+":"+client.getPort() + "浏览器发来接收：" + msg + "  " + client.getLocalPort());
                    new Thread(new SSLSend(client, productType,msg)).start();
                    
                }
                
            }
            catch (Throwable e)
            {
                LOGGER.error("读取数据异常！", e);
                LOGGER.error(client.getInetAddress().toString()+":"+client.getPort());
                try {
					client.close();
				} catch (IOException e1) {
					// TODO Auto-generated catch block
					e1.printStackTrace();
				}
                break;
            }
            
        }
        
    }
    
}
