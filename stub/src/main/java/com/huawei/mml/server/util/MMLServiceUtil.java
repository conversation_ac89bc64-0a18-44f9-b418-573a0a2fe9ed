package com.huawei.mml.server.util;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import com.huawei.i2000.snmp.test.ds.inter.SNMPConstants;
import com.huawei.mml.base.util.Config;
import com.huawei.mml.base.util.Constants;
import com.huawei.mml.base.util.FortifyUtil;
import com.huawei.mml.model.data.MMLModelConstants;
import com.huawei.mml.model.data.MMLNeVO;
import com.huawei.t2000.uoa.teststub.core.ne.NeStubConfig;

public class MMLServiceUtil {
	    
	private static String MML_CONFIG_PATH = "/etc/mmlConf/";
	
	static
	{
	    //规避bug：在windows环境中，MML_CONFIG_PATH路径会定位到C:/etc/mmlConf/
	    if('\\' == File.separatorChar)
        {
            String resPath = NeStubConfig.class.getResource("").toString();
            resPath = resPath.substring(resPath.indexOf("/") + 1);
            MML_CONFIG_PATH = resPath.substring(0, resPath.indexOf("/")) + MML_CONFIG_PATH;
            System.out.println("MML_CONFIG_PATH: " + MML_CONFIG_PATH);
        }
	}
	private static  Map<String, Map<String,String>> loadAllMMLModules() 
	{
		Map<String, Map<String,String>> map = new HashMap<String, Map<String,String>>();
		SAXReader saxReader = new SAXReader();
		Document document;
		Config config = null;
		
		final File directory = new File(MML_CONFIG_PATH);
		final String[] dirOfProduct = directory.list();		
		for(String dir:dirOfProduct){
			final File proDir = new File(MML_CONFIG_PATH + dir + Constants.SEPERATOR);
			if(!proDir.isDirectory())
			{
			    continue;
			}
			final String[] filaNameArray = proDir.list();
			for(String filaName:filaNameArray)
			{
			    File f = new File(proDir + Constants.SEPERATOR + filaName);
			    
			    if(f.isDirectory())
			    {
			        continue;
			    }
			    
				Map<String, String> infoMap = new HashMap<String, String>();
				try {
					document = saxReader.read(f);
					final Element ele = document.getRootElement();
					config = new Config(ele).getConfig("agent");
					infoMap.put(Constants.MML_REGIST_PORT, config.getString(Constants.MML_REGIST_PORT));
					infoMap.put(Constants.MML_ALARM_PORT, config.getString(Constants.MML_ALARM_PORT));
					infoMap.put(Constants.MML_TRAFFIC_PORT, config.getString(Constants.MML_TRAFFIC_PORT));
					infoMap.put(Constants.MML_PRODUCT, config.getString(Constants.MML_PRODUCT));
					map.put(config.getString(Constants.MML_MOUDLE_NAME), infoMap);
				} catch (DocumentException e) {
					e.printStackTrace();
				}
			}
		}
		return map;
	}
	public static List<MMLNeVO> getMMLNeList() throws DocumentException{
		MMLModelConstants.MML_MOUDLE_LIST.clear();
		List<MMLNeVO> volist = new ArrayList<MMLNeVO>();
		Map<String, Map<String,String>> uapMoudleMap = loadAllMMLModules();
		//Set<String> keys = moduleMap.keySet();
		Set<String> keys = uapMoudleMap.keySet();
		for(String key:keys)
		{
			MMLNeVO ne =new MMLNeVO();
			ne.setName(key);
			Map<String, String> infoMap = uapMoudleMap.get(key);
			ne.setProduct(infoMap.get(Constants.MML_PRODUCT));
			ne.setRPort(infoMap.get(Constants.MML_REGIST_PORT));
			ne.setAPort(infoMap.get(Constants.MML_ALARM_PORT));
			ne.setTPort(infoMap.get(Constants.MML_TRAFFIC_PORT));
			
//			if(!AgentStatusManager.getAgentStatusMap().containsKey(key)){
//
//				AgentStatusManager.getAgentStatusMap()
//					.put(key, Boolean.valueOf(false));
//				ne.setStatus(false);
//			}
//			else if(AgentStatusManager.getAgentStatusMap()
//						.get(key).equals(Boolean.valueOf(false)))
//				ne.setStatus(false);
//			else
//				ne.setStatus(true);
			ne.setStatus(false);
			volist.add(ne);
			MMLModelConstants.MML_MOUDLE_LIST.put(key, ne);
			MMLModelConstants.MML_MOUDLE_STATUS.put(ne.getProduct(), Boolean.valueOf(false));
		}

		
		return volist;
	}
	
	public static File getXmlFile(final String module){
		
		final File directory = new File(MML_CONFIG_PATH);
		final String[] dirOfProduct = directory.list();
		String moduleDir = null;
		
		for(String dir:dirOfProduct){
			final File proDir = new File(MML_CONFIG_PATH 
					+ dir +Constants.SEPERATOR);
			final String[] filaNameArray = proDir.list();
			if(FortifyUtil.getValidXml(filaNameArray).contains(module)){
				moduleDir = dir;
				break;
			}
		}
		
		final File file = new File(MML_CONFIG_PATH + moduleDir 
				+ Constants.SEPERATOR + module
				+ Constants.XML);
		
		return file;
	}
	
}
