package com.huawei.t2000.uoa.teststub.ui;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.dom4j.DocumentException;

import com.huawei.bme.web.conversation.Conversation;
import com.huawei.bme.web.util.ConversationUtils;
import com.huawei.i2000.snmp.test.ds.inter.SNMPConstants;
import com.huawei.i2000.snmp.test.ui.snmpNeListVO;
import com.huawei.i2000.snmp.test.ui.snmpNeVO;
import com.huawei.i2000.snmp.test.ui.snmpVOUtil;
//import com.huawei.oms.log.OMSLog;
//import com.huawei.oms.log.OMSLogFactory;
import com.huawei.t2000.uoa.teststub.core.ne.NeStub;
import com.huawei.t2000.uoa.teststub.core.ne.NeStubManager;
import com.huawei.t2000.uoa.teststub.core.ne.NeStubManagerImpl;
import com.huawei.t2000.uoa.teststub.core.ne.uoa.UoaNeStub;
import com.huawei.t2000.uoa.util.ServiceUtil;

/**
 * 网元模拟器列表Serivce
 * <AUTHOR>
 * 
 */
public class NeStubListService {
	public final static Map<Class<?>, StubInfo> stubFactory = new LinkedHashMap<Class<?>, StubInfo>();
//	private final static OMSLog LOGGER = OMSLogFactory.getLog(NeStubListService.class);
	/**
	 * 加载列表
	 * @param vo
	 */
	public void loadNeStubs(NeStubListVo vo) {
		vo.getNes().clear();
//		NeStubManager nm = ServiceUtil.getOSGiService(NeStubManager.class);
		NeStubManagerImpl nm  = NeStubManagerImpl.getInstance();
		
//		LOGGER.debug(nm.toString());
		for (NeStub stub : nm.getAllNeStub()) {
			NeStubVo ne = new NeStubVo();
			ne.setAgentInfo(stub.getAgentInfo());
			ne.setDesc(stub.getDesc());
			ne.setEnable(stub.isEnable());
			ne.setMotype(stub.getMotype());
			ne.setName(stub.getName());
			ne.setNodeid(stub.getNodeId());
			ne.setMgrUrl(getMgrUrl(stub));
			vo.getNes().add(ne);
		}
		
		
		//added by wwx286139 for pagination init
		
		/*
		 * 将网元进行排序
		 */
		Collections.sort(nm.getAllNeStub());
		
		vo.getPage().setTotalRecord(nm.getAllNeStub().size());
		vo.getPage().setCurPage(1);
		 if (vo.getPage().getRecordperpage() >= vo.getPage().getTotalRecord())
	     {
			 vo.getPage().setCurPage(1);
	     }
		 int count=nm.getAllNeStub().size();
	     int fromIndex = (vo.getPage().getCurPage() - 1)
	             * vo.getPage().getRecordperpage();
	     int toIndex = vo.getPage().getCurPage()
	             * vo.getPage().getRecordperpage();
	     fromIndex = (fromIndex < 0 ? 0 : fromIndex);
	     toIndex = (toIndex > count ? count : toIndex);

	     List<NeStubVo> list = new ArrayList<NeStubVo>();
	     List<NeStubVo> listr = new ArrayList<NeStubVo>();
	     for (int i = 0; i < count; i++)
	     {
	    	 NeStubVo ne = new NeStubVo();
	    	 NeStub stub = nm.getAllNeStub().get(i);
				ne.setAgentInfo(stub.getAgentInfo());
				ne.setDesc(stub.getDesc());
				ne.setEnable(stub.isEnable());
				ne.setMotype(stub.getMotype());
				ne.setName(stub.getName());
				ne.setNodeid(stub.getNodeId());
				ne.setMgrUrl(getMgrUrl(stub));
	         listr.add(ne);
	     }
	     vo.setallNe(listr);
	     
	     for (int i = fromIndex; i < toIndex; i++)
	     {
	    	 NeStubVo ne = new NeStubVo();
	    	 NeStub stub = nm.getAllNeStub().get(i);
				ne.setAgentInfo(stub.getAgentInfo());
				ne.setDesc(stub.getDesc());
				ne.setEnable(stub.isEnable());
				ne.setMotype(stub.getMotype());
				ne.setName(stub.getName());
				ne.setNodeid(stub.getNodeId());
				ne.setMgrUrl(getMgrUrl(stub));
	         list.add(ne);
	     }
	     
	     vo.ListResult.clear();
	     vo.setListResult(list);

		
		
	}

	/**
	 * 新增一个模拟网元
	 */
	public void add() {
			
	}

	/**
	 * 刷新
	 */
	public void refresh() {
		NeStubListVo vo = (NeStubListVo) ConversationUtils.getConversation().getModel();
		loadNeStubs(vo);
	}

	/**
	 * 重新加载所有配置
	 */
	public void reloadAll() {
		NeStubManagerImpl nm  = NeStubManagerImpl.getInstance();
		nm.reload();
		NeStubListVo vo = (NeStubListVo) ConversationUtils.getConversation().getModel();
		loadNeStubs(vo);
	}

	/**
	 * 删除模拟网元
	 * @param stub
	 * @throws Exception 
	 */
	public void delStub(NeStubVo vo) throws Exception {
		NeStubManagerImpl nm  = NeStubManagerImpl.getInstance();
		NeStub stub = nm.getNeStub(vo.getName());
		if (stub == null)
			throw new Exception("stub " + vo.getName() + " is not exists.");
		nm.removeStub(stub);
		NeStubListVo oo = (NeStubListVo) ConversationUtils.getConversation().getModel();
		oo.getNes().remove(vo);
	}

	public String getMgrUrl(NeStub stub) {
		StubInfo info = stubFactory.get(stub.getClass());
		if (info == null)
			return null;
		return info.mgrUrl + "&&name=" + stub.getName();
	}
	
	
	
	
	
	/**
	 *  added by wwx286139 for pagination 
	 */
	public void query(){ 
		
		List<NeStubVo> listr = new ArrayList<NeStubVo>();
	     final Conversation cs = ConversationUtils.getConversation();
	     final NeStubListVo modo = (NeStubListVo)cs.getModel();
	     listr=modo.getallNe();
	     System.out.println(listr.size());
	     	int count=0;
	    	if(0!=listr.size())	 
	    	{
	    		count =listr.size();
	    	}
	    		 
	    		 modo.getPage().setTotalRecord(count);
	     if (modo.getPage().getRecordperpage() >= modo.getPage().getTotalRecord())
	     {
	         modo.getPage().setCurPage(1);
	     }

	     int fromIndex = (modo.getPage().getCurPage() - 1)
	             * modo.getPage().getRecordperpage();
	     int toIndex = modo.getPage().getCurPage()
	             * modo.getPage().getRecordperpage();
	     fromIndex = (fromIndex < 0 ? 0 : fromIndex);
	     toIndex = (toIndex > count ? count : toIndex);

	     List<NeStubVo> list = new ArrayList<NeStubVo>();
	     
	     for (int i = fromIndex; i < toIndex; i++)
	     {
	    	 
	         list.add(listr.get(i));
	     }
	     modo.ListResult.clear();
	     
	     modo.setListResult(list);
	 }
	
	

	public static class StubInfo {
		Class<?> clazz;
		String type;
		String mgrUrl;
		String createUrl;

		StubInfo(Class<?> clazz, String type, String mgrUrl, String createUrl) {
			this.clazz = clazz;
			this.type = type;
			this.mgrUrl = mgrUrl;
			this.createUrl = createUrl;
		}

		public Class<?> getClazz() {
			return clazz;
		}

		public void setClazz(Class<?> clazz) {
			this.clazz = clazz;
		}

		public String getType() {
			return type;
		}

		public void setType(String type) {
			this.type = type;
		}

		public String getMgrUrl() {
			return mgrUrl;
		}

		public void setMgrUrl(String mgrUrl) {
			this.mgrUrl = mgrUrl;
		}

		public String getCreateUrl() {
			return createUrl;
		}

		public void setCreateUrl(String createUrl) {
			this.createUrl = createUrl;
		}
	}

	static {
	
		stubFactory.put(UoaNeStub.class, new StubInfo(UoaNeStub.class, "Uoa模拟网元",
				"business.action?BMEBusiness=bcm.teststub.uoanestub&&targetStep=start", "business.action?BMEBusiness=bcm.teststub.uoanestub&&targetStep=add"));
	}
	
	public void serachBy() throws DocumentException{
		NeStubManagerImpl nm  = NeStubManagerImpl.getInstance();
		NeStubListVo vo = (NeStubListVo) ConversationUtils.getConversation().getModel();
		vo.getNes().clear();
		if(vo.getSearchContent().equals("")){
			System.out.println("nosearch");
			refresh();
			return;
		}
		List<NeStubVo> list = new ArrayList<NeStubVo>();
		int count = nm.getAllNeStub().size();
		
	     System.out.println("search="+vo.getSearchContent()+"in"+count);
		for(int    i=0;    i<count;i++){
			NeStub stub = nm.getAllNeStub().get(i);
			
			if (stub.getName().toUpperCase().contains(vo.getSearchContent().toUpperCase())){
				NeStubVo ne = new NeStubVo();
				
				ne.setAgentInfo(stub.getAgentInfo());
				ne.setDesc(stub.getDesc());
				ne.setEnable(stub.isEnable());
				ne.setMotype(stub.getMotype());
				ne.setName(stub.getName());
				ne.setNodeid(stub.getNodeId());
				ne.setMgrUrl(getMgrUrl(stub));
	    	 
	         list.add(ne);
			}
			 
		}
		
		System.out.println(list.size());
	     
	    vo.setallNe(list);
	    query();
		
	 }
}
