package com.huawei.imsc.pm;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.log4j.Logger;
import org.dom4j.DocumentException;

import com.huawei.imsc.util.XMLUtil;
import com.huawei.t2000.utils.T2000Logger;


public class IMSCTrafficMsgCreator extends IMSCMMLMsgCreator
{
    /**
     * 日志记录器。
     */
    private static final Logger logger = T2000Logger.getInstance()
            .getT2000Logger(IMSCTrafficMsgCreator.class);
    
    public static String getTrafficMsg(int trafficTaskId)
    {
        IMSCTrafficTask trafficTask = IMSCTrafficTaskListManager.getInstance().getTaskListMap().get(trafficTaskId);
        List<IMSCTrafficUnit> trafficUnitList = null;
        try
        {
            trafficUnitList = XMLUtil.getTrafficUnitList(trafficTask.getTaskType());
        }
        catch (DocumentException e)
        {
            logger.error("Parse traffic unit list failed!", e);
        }
        
        //根据taskid找到对应task，对virsmcid进行处理
        if(trafficTask.getVirSmcId() != null && !"<NULL>".equals(trafficTask.getVirSmcId()))
        {
            trafficUnitList.get(0).getItemValue().put("VirSmcID", trafficTask.getVirSmcId());
        }
        
        String head = getTrfHead(trafficTask, trafficTask.getTaskType());
        String body = getBody(trafficTask.getNetId(), trafficUnitList);
        String tail = getTail();
        return head + body + tail;
    }

    /**
     * 性能报文头
     * 
     * @param trafficTask
     * @param trafficUnitId
     * @return
     */
    public static String getTrfHead(IMSCTrafficTask trafficTask, String trafficUnitId)
    {
        StringBuffer sb = new StringBuffer();
        sb.append(getHead(trafficTask.getPeriod()))
            .append(TRAFFIC_REPORT_IDENTIFER).append(SPACES_4).append(trafficTask.getId()).append(SPACES_8).append(trafficUnitId).append(NEWLINE)
            .append(trafficTask.getStartDate()).append(SPACES_4).append(trafficTask.getEndDate()).append(SPACES_4).append(trafficTask.getPeriod()).append(SPACES_4).append(getDate()).append(SPACE_1).append(getTimeMinusOnePrd(trafficTask.getPeriod())).append(NEWLINE)
            .append(NEWLINE);
            
        return sb.toString();
    }
    
    /**
     * 性能报文测量单元信息内容
     * 
     * @return
     */
    public static String getBody(String netId, List<IMSCTrafficUnit> trafficUnitList)
    {
        StringBuffer sb = new StringBuffer();
        //unit item name 行
        sb.append(SPACE_1).append(NETID);
        Set<String> itemNameSet = trafficUnitList.get(0).getItemValue().keySet();
        for(String itemName : itemNameSet)
        {
            sb.append(SPACES_4).append(itemName);
        }
        sb.append(NEWLINE);
        
        // unit item 多行信息
        Map<String, String> itemValue;
        for(int i=0; i<trafficUnitList.size(); i++)
        {
            sb.append(SPACE_1).append(netId);
            itemValue = trafficUnitList.get(i).getItemValue();
            for(String itemName : itemNameSet)
            {
                sb.append(SPACES_4).append(itemValue.get(itemName));
            }
            sb.append(NEWLINE);
        }
        //累计条目数：1    RECORD(S)    IN    REPORT    #1    TOTAL    1    RECORD(S)
        sb.append(trafficUnitList.size()).append(SPACES_4).append(RECORDS).append(SPACES_4).append(IN).append(SPACES_4).append(REPORT)
            .append(SPACES_4).append("#1").append(SPACES_4).append(TOTAL).append(SPACES_4).append(trafficUnitList.size()).append(SPACES_4)
            .append(RECORDS).append(NEWLINE);
        
        
        return sb.toString();
    }
    
    /**
     * 性能上报时间中分钟数按照性能任务时间间隔归整后的时间
     * 
     * @param prd
     * @return
     */
    public static String getHead(int prd)
    {
        StringBuffer sb = new StringBuffer();
        sb.append(START_IDENTIFER).append(SPACES_4).append(SOURCE_IDENTIFER)
                .append(SPACES_8).append(getDate()).append(SPACE_1)
                .append(getTime(prd)).append(NEWLINE);
        return sb.toString();
    }
    
    
}
