package com.huawei.imsc.pm;

import java.util.List;

import org.apache.log4j.Logger;

import com.huawei.t2000.utils.T2000Logger;

/**
 * 命令回复报文生成器
 * 
 * <AUTHOR> 2015-12-11
 */
public class IMSCCMDMsgCreator extends IMSCMMLMsgCreator
{
    /**
     * 日志记录器。
     */
    private static final Logger logger = T2000Logger.getInstance()
            .getT2000Logger(IMSCCMDMsgCreator.class);
    
    /**
     * LSTTRFTSK
     * 
     * @return
     */
    public static String LSTTRFTSK(String code)
    {
        StringBuffer sb = new StringBuffer();
        sb.append(getHead())
            .append(TASK_COMMAND_IDENTIFER).append(SPACES_4).append("#1").append(NEWLINE)
            .append("%%").append(code).append("%%").append(NEWLINE)
            .append(SUCCESS_RETURN_CODE).append(NEWLINE)
            .append(NEWLINE);
        //插入各字段名
        String[] fieldNames = IMSCTrafficTask.fieldNames;
        sb.append(fieldNames[0]);
        for(int i=1; i<fieldNames.length; i++)
        {
            sb.append(SPACES_4).append(fieldNames[i]);
        }
        sb.append(NEWLINE);
        sb.append(NEWLINE);
        //依次插入各字段
        List<IMSCTrafficTask> taskList = IMSCTrafficTaskListManager.getInstance().getTaskList();
        for(IMSCTrafficTask task : taskList)
        {
            sb.append(task.toString()).append(NEWLINE);
        }
        sb.append("(total = " + taskList.size() + ")").append(NEWLINE);
        sb.append(getTail());
        return sb.toString();
    }
}
