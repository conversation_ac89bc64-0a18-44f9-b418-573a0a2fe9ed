package com.huawei.imsc.pm;

import org.apache.log4j.Logger;

import com.huawei.t2000.utils.T2000Logger;

/**
 * 性能上报任务 entity
 * 
 * <AUTHOR> 2015-12-9
 */
public class IMSCTrafficTask
{
    /**
     * 日志记录器。
     */
    private static final Logger logger = T2000Logger.getInstance()
            .getT2000Logger(IMSCTrafficTask.class);

    private int id;

    private String startDate;

    private String endDate;

    private String startTimeI;

    private String endTimeI;

    private String startTimeII;

    private String endTimeII;

    private String startTimeIII;

    private String endTimeIII;

    private String weekday;

    private int period;

    private String taskType;

    private String state;

    private String netId;

    private String virSmcId;

    public static final String[] fieldNames = {"taskid", "sd", "ed", "st1",
        "et1", "st2", "et2", "st3", "et3", "weekday", "prd", "tsktype",
        "state", "netid", "VirSmcID"};

    public int getId()
    {
        return id;
    }

    public void setId(int id)
    {
        this.id = id;
    }

    public String getStartDate()
    {
        return startDate;
    }

    public void setStartDate(String startDate)
    {
        this.startDate = startDate;
    }

    public String getEndDate()
    {
        return endDate;
    }

    public void setEndDate(String endDate)
    {
        this.endDate = endDate;
    }

    public String getStartTimeI()
    {
        return startTimeI;
    }

    public void setStartTimeI(String startTimeI)
    {
        this.startTimeI = startTimeI;
    }

    public String getEndTimeI()
    {
        return endTimeI;
    }

    public void setEndTimeI(String endTimeI)
    {
        this.endTimeI = endTimeI;
    }

    public String getStartTimeII()
    {
        return startTimeII;
    }

    public void setStartTimeII(String startTimeII)
    {
        this.startTimeII = startTimeII;
    }

    public String getEndTimeII()
    {
        return endTimeII;
    }

    public void setEndTimeII(String endTimeII)
    {
        this.endTimeII = endTimeII;
    }

    public String getStartTimeIII()
    {
        return startTimeIII;
    }

    public void setStartTimeIII(String startTimeIII)
    {
        this.startTimeIII = startTimeIII;
    }

    public String getEndTimeIII()
    {
        return endTimeIII;
    }

    public void setEndTimeIII(String endTimeIII)
    {
        this.endTimeIII = endTimeIII;
    }

    public String getWeekday()
    {
        return weekday;
    }

    public void setWeekday(String weekday)
    {
        this.weekday = weekday;
    }

    public int getPeriod()
    {
        return period;
    }

    public void setPeriod(int period)
    {
        this.period = period;
    }

    public String getTaskType()
    {
        return taskType;
    }

    public void setTaskType(String taskType)
    {
        this.taskType = taskType;
    }

    public String getState()
    {
        return state;
    }

    public void setState(String state)
    {
        this.state = state;
    }

    public String getNetId()
    {
        return netId;
    }

    public void setNetId(String netId)
    {
        this.netId = netId;
    }

    public String getVirSmcId()
    {
        return virSmcId;
    }

    public void setVirSmcId(String virSmcId)
    {
        this.virSmcId = virSmcId;
    }

    @Override
    public String toString()
    {
        return id + "    " + startDate + "    " + endDate + "    " + startTimeI
                + "    " + endTimeI + "    " + startTimeII + "    " + endTimeII
                + "    " + startTimeIII + "    " + endTimeIII + "    "
                + weekday + "    " + period + "    " + taskType + "    "
                + state + "    " + netId + "    " + virSmcId;
    }

}
