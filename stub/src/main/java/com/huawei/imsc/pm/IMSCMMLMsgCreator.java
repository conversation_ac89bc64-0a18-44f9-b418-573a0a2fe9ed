package com.huawei.imsc.pm;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * SMSC MML报文生成器
 * 
 * <AUTHOR> 2015-12-10
 */
public class IMSCMMLMsgCreator
{
    protected static final String START_IDENTIFER = "+++";

    protected static final String END_IDENTIFER = "---";

    protected static final String END = "END";

    protected static final String NETID = "netid";

    protected static final String SOURCE_IDENTIFER = "SMSYS001";

    protected static final String TRAFFIC_REPORT_IDENTIFER = "TRAFFIC";

    protected static final String TASK_COMMAND_IDENTIFER = "TASKCMD";

    protected static final String SUCCESS_RETURN_CODE = "RETCODE = 0  <Success>";

    protected static final String REPORT = "REPORT";

    protected static final String RECORDS = "RECORD(S)";

    protected static final String IN = "IN";

    protected static final String TOTAL = "TOTAL";

    protected static final String NEWLINE = "\r\n";

    protected static final String SPACE_1 = " ";

    protected static final String SPACES_2 = "  ";

    protected static final String SPACES_4 = "    ";

    protected static final String SPACES_5 = "     ";

    protected static final String SPACES_8 = "        ";

    public static String getDate()
    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        return sdf.format(cal.getTime());
    }

    /**
     * 时间秒数归零，小于30s，归零；大于等于30s，归零，分钟数增1；
     * 
     * @return
     */
    public static String getTime()
    {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        SimpleDateFormat sdf1 = new SimpleDateFormat("HH");
        SimpleDateFormat sdf2 = new SimpleDateFormat("mm");
        SimpleDateFormat sdf3 = new SimpleDateFormat("ss");
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        int hour = Integer.parseInt(sdf1.format(date));
        int minute = Integer.parseInt(sdf2.format(date));
        int second = Integer.parseInt(sdf3.format(date));
        if(second > 30)
        {
            if(minute == 59)
            {
                hour++;
                minute = 0;
            }
        } 
        second = 0;
        cal.set(0, 0, 0, hour, minute, second);
        return sdf.format(cal.getTime());
    }
    
    /**
     * 时间秒数归零，小于30s，归零；大于等于30s，归零，分钟数增1；
     * 分钟数按性能任务间隔归整
     * 
     * @return
     */
    public static String getTime(int prd)
    {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        SimpleDateFormat sdf1 = new SimpleDateFormat("HH");
        SimpleDateFormat sdf2 = new SimpleDateFormat("mm");
        SimpleDateFormat sdf3 = new SimpleDateFormat("ss");
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        int hour = Integer.parseInt(sdf1.format(date));
        int minute = Integer.parseInt(sdf2.format(date));
        int second = Integer.parseInt(sdf3.format(date));
        if(second > 30)
        {
            if(minute == 59)
            {
                hour++;
                minute = 0;
            }
        } 
        minute = minute - (minute % prd);
        second = 0;
        cal.set(0, 0, 0, hour, minute, second);
        return sdf.format(cal.getTime());
    }
    
    /**
     * 暂用方法，原时间上再减少一个周期
     * 
     * @param prd
     * @return
     */
    public static String getTimeMinusOnePrd(int prd)
    {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        String[] strs = getTime(prd).split(":");
        int hour = Integer.parseInt(strs[0]);
        int minute = Integer.parseInt(strs[1]);
        int second = Integer.parseInt(strs[2]);
        if(minute < prd)
        {
            minute = 60 - prd;
            hour--;
        }
        else
        {
            minute -= prd;
        }
        Calendar cal = Calendar.getInstance();
        cal.set(0, 0, 0, hour, minute, second);
        return sdf.format(cal.getTime());
    }

    /**
     * 报文头
     * 
     * @return
     */
    public static String getHead()
    {
        StringBuffer sb = new StringBuffer();
        sb.append(START_IDENTIFER).append(SPACES_4).append(SOURCE_IDENTIFER)
                .append(SPACES_8).append(getDate()).append(SPACE_1)
                .append(getTime()).append(NEWLINE);
        return sb.toString();
    }

    /**
     * 报文尾
     * 
     * @return
     */
    public static String getTail()
    {
        StringBuffer sb = new StringBuffer();
        sb.append(END_IDENTIFER).append(SPACES_4).append(END);
        return sb.toString();
    }
}
