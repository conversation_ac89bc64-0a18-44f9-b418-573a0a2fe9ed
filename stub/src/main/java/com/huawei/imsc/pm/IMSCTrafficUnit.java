package com.huawei.imsc.pm;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.log4j.Logger;

import com.huawei.t2000.utils.T2000Logger;

public class IMSCTrafficUnit
{
    /**
     * 日志记录器。
     */
    private static final Logger logger = T2000Logger.getInstance()
            .getT2000Logger(IMSCTrafficUnit.class);

    /**
     * 测量单元id，如：TRFCTLONE
     */
    private String id;
    
    /**
     * 测量对象字段名
     */
    private List<String> meaObjItemList;

    /**
     * 各个指标（对象）对应的值
     */
    private Map<String, String> itemValue;
    
    /**
     * 各个指标（对象 ）对应的值的类型
     */
    private Map<String, String> itemValueType;
    
    /**
     * 构造函数。
     * 
     */
    public IMSCTrafficUnit()
    {
        meaObjItemList = new ArrayList<String>();
        itemValue = new LinkedHashMap<String, String>();
        itemValueType = new LinkedHashMap<String, String>();
    }

    public List<String> getMeaObjItemList()
    {
        return meaObjItemList;
    }

    public void setMeaObjItemList(List<String> meaObjItemList)
    {
        this.meaObjItemList = meaObjItemList;
    }

    public Map<String, String> getItemValueType()
    {
        return itemValueType;
    }

    public void setItemValueType(Map<String, String> itemValueType)
    {
        this.itemValueType = itemValueType;
    }

    public String getId()
    {
        return id;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public Map<String, String> getItemValue()
    {
        return itemValue;
    }

    public void setItemValue(Map<String, String> itemValue)
    {
        this.itemValue = itemValue;
    }

    @Override
    public String toString()
    {
        Set<String> nameSet = itemValue.keySet();
        StringBuffer sb = new StringBuffer();
        for(String name : nameSet)
        {
            sb.append(name).append("    ");
        }
        sb = new StringBuffer(sb.toString().trim());
        sb.append("\r\n");
        for(String name : nameSet)
        {
            sb.append(itemValue.get(name)).append("    ");
        }
        return sb.toString().trim();
    }

    @Override
    protected Object clone() throws CloneNotSupportedException
    {
        IMSCTrafficUnit unitClone = new IMSCTrafficUnit();
        String newId = new String(this.id);
        Map<String, String> newItemValue = new LinkedHashMap<String, String>(); 
        Map<String, String> newItemValueType = new LinkedHashMap<String, String>();
        unitClone.setId(newId);
        return super.clone();
    }
    
}
