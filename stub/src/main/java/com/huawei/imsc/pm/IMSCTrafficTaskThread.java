package com.huawei.imsc.pm;

import java.io.IOException;
import java.io.OutputStream;
import java.net.Socket;

import org.apache.log4j.Logger;
import org.dom4j.DocumentException;

import com.huawei.imsc.manager.IMSCClientManager;
import com.huawei.t2000.utils.T2000Logger;

/**
 * 执行性能任务的线程
 * 
 * <AUTHOR> 2015-12-10
 */
public class IMSCTrafficTaskThread implements Runnable
{
    /**
     * 日志记录器。
     */
    private static final Logger logger = T2000Logger.getInstance()
            .getT2000Logger(IMSCTrafficTaskThread.class);

    /**
     * 所需执行的性能任务
     */
    private IMSCTrafficTask trafficTask;

    /**
     * 性能通道socket
     */
    private Socket socket;

    /**
     * 控制循环任务启停
     */
    private boolean runningFlag;

    /**
     * 构造函数。
     * 
     * @throws DocumentException
     * 
     */
    public IMSCTrafficTaskThread(int taskId) throws DocumentException
    {
        trafficTask = IMSCTrafficTaskListManager.getInstance()
                .getTaskListMap().get(taskId);
        runningFlag = true;
    }

    public void run()
    {
        logger.info(Thread.currentThread().getName() + " 已启动");
        // 性能上报间隔时长
        int taskPeriod = trafficTask.getPeriod() * 60 * 1000;

        while (runningFlag)
        {
            logger.info("new loop round! runningFlag is:" + runningFlag);
            String trafficMsg = IMSCTrafficMsgCreator.getTrafficMsg(trafficTask
                    .getId());
            OutputStream out = null;
            socket = IMSCClientManager.getInstance().getTrafficSocket();
            logger.info("traffic socket: " + socket);
            if (socket != null && !"127.0.0.1".equals(socket.getInetAddress().toString()))
            {
                try
                {
                    out = socket.getOutputStream();
                    out.write(trafficMsg.getBytes());
                    out.flush();
                    logger.info("IMSC traffic msg sent：\n" + trafficMsg);
                }
                catch (IOException e)
                {
                    logger.error("发送性能报文失败", e);
                }
            }
            // 定时
            try
            {
                Thread.sleep(taskPeriod);
            }
            catch (InterruptedException e)
            {
                logger.error("线程sleep被中断!");
            }
        }
        logger.info(Thread.currentThread().getName() + " 已结束");

    }

    @Override
    protected void finalize() throws Throwable
    {
        super.finalize();
        logger.info(Thread.currentThread() + " says byebye to memory~~~");
    }

    public void stopTask()
    {
        runningFlag = false;
        logger.debug(Thread.currentThread().getName()
                + "'s runningFlag is setting to false");
    }

}
