package com.huawei.imsc.pm;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.log4j.Logger;
import org.dom4j.DocumentException;

import com.huawei.t2000.utils.T2000Logger;

/**
 * 全局唯一对象：性能任务列表，管理性能任务，增加，删除，active和deactive等
 * 
 * <AUTHOR> 2015-12-10
 */
public class IMSCTrafficTaskListManager
{
    /**
     * 日志记录器。
     */
    private static final Logger logger = T2000Logger.getInstance()
            .getT2000Logger(IMSCTrafficTaskListManager.class);

    /**
     * 性能任务列表
     * 
     * 此map存储所有性能任务信息，包括active和deactive的
     */
    private Map<Integer, IMSCTrafficTask> taskListMap;

    /**
     * 执行单一性能任务的线程列表
     * 
     * 此map存储正在运行的性能任务
     */
    private Map<Integer, IMSCTrafficTaskThread> taskThreadListMap;

    private static IMSCTrafficTaskListManager instance;

    private IMSCTrafficTaskListManager()
    {
        taskListMap = new LinkedHashMap<Integer, IMSCTrafficTask>();

        taskThreadListMap = new LinkedHashMap<Integer, IMSCTrafficTaskThread>();
    }

    public Map<Integer, IMSCTrafficTask> getTaskListMap()
    {
        return taskListMap;
    }

    /**
     * 获取任务列表的List
     * 
     * @return
     */
    public List<IMSCTrafficTask> getTaskList()
    {
        List<IMSCTrafficTask> taskList = new ArrayList<IMSCTrafficTask>();
        Set<Integer> taskIdSet = taskListMap.keySet();

        for (Integer id : taskIdSet)
        {
            taskList.add(taskListMap.get(id));
        }
        logger.info("taskList: " + taskList.size());
        return taskList;
    }

    public static IMSCTrafficTaskListManager getInstance()
    {
        if (instance == null)
        {
            instance = new IMSCTrafficTaskListManager();
        }
        return instance;
    }

    /**
     * 添加性能任务
     * 
     * @param task
     *            所需添加 的任务
     * @return 成功则true;否则false;
     */
    public boolean createTask(IMSCTrafficTask task)
    {
        if (isTaskIdExists(task.getId()))
        {
            logger.error("Parameter task'id already exists, change a task id!");
            return false;
        }
        taskListMap.put(task.getId(), task);
        //启动前判断是否为“active”
        if (IMSCTrafficConstants.TASK_STATE_ACTIVED.equals(task.getState()))
        {
            // 启动线程任务
            try
            {
                if (taskThreadListMap.get(task.getId()) != null)
                {
                    taskThreadListMap.get(task.getId()).stopTask();
                }
                IMSCTrafficTaskThread taskThread = new IMSCTrafficTaskThread(
                        task.getId());
                new Thread(taskThread, "性能任务|id:" + task.getId()).start();
                taskThreadListMap.put(task.getId(), taskThread);
            }
            catch (DocumentException e)
            {
                logger.error("启动性能任务线程失败！", e);
            }
        }
        return true;
    }

    /**
     * 删除性能任务
     * 
     * @param id
     *            任务id
     * @return 成功则true;否则false;
     */
    public boolean delTask(int id)
    {
        if (!isTaskIdExists(id))
        {
            logger.error("Parameter task id does not exist!");
            return false;
        }
        if (taskThreadListMap.containsKey(id))
        {
            deactiveTask(id);
            taskListMap.remove(id);
            taskThreadListMap.remove(id);
        }
        return true;
    }

    /**
     * 激活性能任务
     * 
     * @param id
     *            任务id
     * @return 成功则true;否则false;
     */
    public boolean activeTask(int id)
    {
        if (!isTaskIdExists(id))
        {
            logger.error("Parameter task id does not exist!");
            return false;
        }
        taskListMap.get(id).setState(IMSCTrafficConstants.TASK_STATE_ACTIVED);
        // 启动线程任务(可能该性能任务的上个执行线程还未结束，二者共存不影响)
        try
        {
            IMSCTrafficTaskThread taskThread = new IMSCTrafficTaskThread(id);
            new Thread(taskThread, "性能任务|id:" + id).start();
            taskThreadListMap.put(id, taskThread);
        }
        catch (DocumentException e)
        {
            logger.error("启动性能任务线程失败！", e);
        }
        return true;
    }

    /**
     * 去激活性能任务
     * 
     * @param id
     *            任务id
     * @return 成功则true;否则false;
     */
    public boolean deactiveTask(int id)
    {
        if (!isTaskIdExists(id))
        {
            logger.error("Parameter task id:" + id + " does not exist!");
            return false;
        }
        taskListMap.get(id).setState(IMSCTrafficConstants.TASK_STATE_DEACTIVED);
        taskThreadListMap.get(id).stopTask();
        return true;
    }

    /**
     * 任务id是否存在
     * 
     * @param id
     *            任务id
     * @return 存在则true;否则false;
     */
    public boolean isTaskIdExists(int id)
    {
        if (taskListMap.get(id) == null)
        {
            return false;
        }
        return true;
    }

    /**
     * 任务是否已激活
     * 
     * @param id
     *            任务id
     * @return 若已激活则true，否则false
     */
    public boolean isTaskActive(int id)
    {
        if (!isTaskIdExists(id))
        {
            logger.error("Parameter task id:"+ id +" does not exist!");
            return false;
        }
        if (IMSCTrafficConstants.TASK_STATE_ACTIVED.equals(taskListMap.get(id)
                .getState()))
        {
            return true;
        }
        return false;
    }
    
    /**
     * 去激活所有任务，返回此操作去激活的任务数
     * 
     * @return
     */
    public int deactiveAll()
    {
        int count = 0;
        Set<Integer> idSet = taskListMap.keySet();
        for(int id : idSet)
        {
            if(isTaskActive(id))
            {
                if(deactiveTask(id))
                {
                    count++;
                }
            }
        }
        return count;
    }
    
    /**
     * 删除所有任务，返回删除掉的任务数
     * 
     * @return
     */
    public int delAll()
    {
        int count = taskListMap.size();
        deactiveAll();
        taskListMap.clear();
        return count;
    }
}
