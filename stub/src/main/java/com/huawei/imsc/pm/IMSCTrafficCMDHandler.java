package com.huawei.imsc.pm;

import org.apache.log4j.Logger;

import com.huawei.t2000.utils.T2000Logger;

/**
 * IMSC 性能任务命令解析器
 * 
 * <AUTHOR> 2015-12-11
 */
public class IMSCTrafficCMDHandler
{
    /**
     * 日志记录器。
     */
    private static final Logger logger = T2000Logger.getInstance()
            .getT2000Logger(IMSCTrafficCMDHandler.class);

    /*
     * CRE TRFCTLONE : netid=75501,taskid = 192,prd=5;
     */
    public static boolean createTask(String cmd)
    {
        IMSCTrafficTask task = new IMSCTrafficTask();
        String taskType = cmd.split(":")[0].replaceAll("CRE", "").trim();
        String netId = null;
        String taskId = null;
        String prd = null;
        String virSmcId = "<NULL>";
        for (String str : cmd.replace(";", "").split(":")[1].split(","))
        {
            if (str.contains("NETID"))
            {
                netId = str.split("=")[1].trim();
            }
            if (str.contains("TASKID"))
            {
                taskId = str.split("=")[1].trim();
            }
            if (str.contains("PRD"))
            {
                prd = str.split("=")[1].trim();
            }
            if (str.contains("VIRSMCID"))
            {
                virSmcId = str.split("=")[1].trim();
            }
        }
        try
        {
            task.setId(Integer.valueOf(taskId));
            task.setStartDate(IMSCMMLMsgCreator.getDate());
            task.setEndDate("2037-12-31");
            task.setStartTimeI("00:00:00");
            task.setEndTimeI("23:59:00");
            task.setStartTimeII("<NULL>");
            task.setEndTimeII("<NULL>");
            task.setStartTimeIII("<NULL>");
            task.setEndTimeIII("<NULL>");
            task.setWeekday("0,1,2,3,4,5,6");
            if (prd == null || prd.equals(""))
            {
                task.setPeriod(5);
            }
            else
            {
                task.setPeriod(Integer.valueOf(prd));
            }
            task.setTaskType(taskType);
            task.setState(IMSCTrafficConstants.TASK_STATE_ACTIVED);
            task.setNetId(netId);
            task.setVirSmcId(virSmcId);
        }
        catch (Exception e)
        {
            logger.error("CREATE TASK FAILED!", e);
            return false;
        }

        return IMSCTrafficTaskListManager.getInstance().createTask(task);
    }

    /**
     * 删除性能任务命令处理
     * 
     * deltrftsk:taskid=4;
     * 
     * @param cmd
     * @return
     */
    public static boolean deleteTask(String cmd)
    {
        try
        {
            int taskId = Integer.parseInt(cmd.substring(cmd.indexOf("=") + 1,
                    cmd.indexOf(";")));
            return IMSCTrafficTaskListManager.getInstance().delTask(taskId);
        }
        catch (NumberFormatException e)
        {
            logger.error("删除性能任务读取命令失败", e);
            return false;
        }
    }
}
