package com.huawei.imsc.util;

import com.huawei.imsc.manager.FourPorts;
import com.huawei.t2000.utils.T2000Logger;

import org.apache.log4j.Logger;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.net.Socket;
import java.sql.Timestamp;
import java.util.Arrays;

/**
 * 
 * <一句话功能简述>用于接收I2000发送的数据 <功能详细描述>
 * 
 * <AUTHOR>
 * @version [版本号, 2015-7-7]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
public class IMSCRead implements Runnable
{
    /**
     * 日志记录器。
     */
    private static final Logger logger = T2000Logger.getInstance()
            .getT2000Logger(IMSCRead.class);

    private Socket client = null;

    public IMSCRead(Socket client)
    {
        this.client = client;
    }

    @Override
    public void run()
    {

        InputStream inputStream = null;
        byte[] data = new byte[1024 * 10];
        while (true)
        {
            try
            {
                inputStream = client.getInputStream();
                int length = inputStream.read(data);
                if (length != -1)
                {
                    // 根据端口判断是MML还是MT协议
                    if (FourPorts.MTPORT == client.getLocalPort())
                    {
                        byte[] MTCODE = Arrays.copyOf(data, length);
                        logger.info("Port:" + client.getLocalPort() + " got a msg:\n" + Arrays.toString(MTCODE));
                        printLog(MTCODE);
                        new Thread(new IMSCSend(client, MTCODE)).start();
                    }
                    // MML协议
                    else
                    {
                        String msg = new String(Arrays.copyOf(data, length));
                        logger.info("Port:" + client.getLocalPort()
                                + "　got a msg: " + msg);
                        printLog(msg);
                        new Thread(new IMSCSend(client, msg)).start();
                    }
                }

            }
            catch (IOException e)
            {
                // 将客户端从性能上报工具类中移除
                break;
            }

        }

    }
    
    public void printLog(byte[] b)
    {
        String pathname = "D:\\IMSCmtlog.txt";
        File file = new File(pathname);
        try
        {
            if(!file.exists())
            {
                file.createNewFile();
            }
            FileWriter fw = new FileWriter(file, true);
            PrintWriter pw = new PrintWriter(fw);
            Timestamp time = new Timestamp(System.currentTimeMillis());
            pw.println(time);
            pw.println(CmdIdUtill.getCmdID(Arrays.copyOfRange(b, 4, 8)));
            pw.println(Arrays.toString(b));
            pw.println(); 
            pw.flush();
            pw.close();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }
    
    public void printLog(String msg)
    {
        String pathname = "D:\\IMSCMMLlog.txt";
        File file = new File(pathname);
        try
        {
            if(!file.exists())
            {
                file.createNewFile();
            }
            FileWriter fw = new FileWriter(file, true);
            PrintWriter pw = new PrintWriter(fw);
            Timestamp time = new Timestamp(System.currentTimeMillis());
            pw.println(time);
            pw.println(msg);
            pw.println(); 
            pw.flush();
            pw.close();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

}
