/*
 * 文件名：IMSCStubService.java
 * 版权：Copyright 2006-2016 Huawei Tech. Co. Ltd. All Rights Reserved.
 * 描述： IMSCStubService.java
 * 修改人：sWX315038
 * 修改时间：2016-1-4
 * 修改内容：新增
 */

package com.huawei.imsc.vo;

import com.huawei.bme.web.util.ConversationUtils;
import com.huawei.imsc.manager.FourPorts;
import com.huawei.imsc.manager.IMSCClientManager;
import com.huawei.imsc.manager.IMSCServerManager;
import com.huawei.imsc.model.IMSCMMLModel;
import com.huawei.imsc.pm.IMSCTrafficTaskListManager;
import com.huawei.oms.log.OMSLog;
import com.huawei.oms.log.OMSLogFactory;
import com.huawei.smsc.pm.SMSCTrafficTaskListManager;
import com.huawei.smsc.utill.TimeUtill;

import java.io.IOException;
import java.io.OutputStream;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.Executors;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * IMSC前台服务类
 *
 * <AUTHOR> 2016-1-4
 */
public class IMSCStubService {
    private static boolean running = false;

    private final static OMSLog LOGGER = OMSLogFactory.getLog(IMSCStubService.class);

    private static Timer timer = new Timer();

    /**
     * 告警发送速率，默认1000条/秒
     */
    private int alarmRate = 1000;

    /**
     * 告警压测任务持续时长，默认10秒
     */
    private int seconds = 10;

    /**
     * 模块ID，告警挂载的模块ID
     */
    private int moduleId = 12345;

    public void init() {

        IMSCStubVO vo = (IMSCStubVO) ConversationUtils.getConversation().getModel();
        vo.setRunning(running);
        vo.setMTPort(FourPorts.MTPORT);
        vo.setAlarmPort(FourPorts.ALARMPORT);
        vo.setTrafficPort(FourPorts.TRAFFICPORT);
        vo.setMaintePort(FourPorts.MAINTEPORT);
        vo.setTaskList(IMSCTrafficTaskListManager.getInstance().getTaskList());
        if (IMSCClientManager.getInstance().getMTSocket() != null) {
            vo.setClientAddr(
                IMSCClientManager.getInstance().getMTSocket().getInetAddress().toString().replaceFirst("/", ""));
        } else {
            vo.setClientAddr("disconnect");
        }
    }

    public void start() {
        if (!running) {
            IMSCStubVO vo = (IMSCStubVO) ConversationUtils.getConversation().getModel();
            FourPorts.setPorts(vo.getMTPort(), vo.getAlarmPort(), vo.getTrafficPort(), vo.getMaintePort());
            IMSCServerManager.start(vo.isSecure(), vo.isTrustAll());
            IMSCStubService.setRunning(true);
            vo.setRunning(running);
        }
    }

    public void stop() {
        if (running) {
            IMSCStubVO vo = (IMSCStubVO) ConversationUtils.getConversation().getModel();
            IMSCServerManager.stop();
            IMSCStubService.setRunning(false);
            vo.setRunning(running);
        }
    }

    /**
     * 刷新模拟桩任务列表
     */
    public void refreshTrfList() {
        IMSCStubVO vo = (IMSCStubVO) ConversationUtils.getConversation().getModel();
        vo.setRunning(running);
        vo.setMTPort(FourPorts.MTPORT);
        vo.setAlarmPort(FourPorts.ALARMPORT);
        vo.setTrafficPort(FourPorts.TRAFFICPORT);
        vo.setMaintePort(FourPorts.MAINTEPORT);
        vo.setTaskList(IMSCTrafficTaskListManager.getInstance().getTaskList());
        if (IMSCClientManager.getInstance().getMTSocket() != null) {
            vo.setClientAddr(
                IMSCClientManager.getInstance().getMTSocket().getInetAddress().toString().replaceFirst("/", ""));
        } else {
            vo.setClientAddr("disconnect");
        }
    }

    /**
     * 单次发送告警的方法
     */
    public void sendAlarm() {
        Socket alarmSocket = IMSCClientManager.getInstance().getAlarmSocket();
        try (OutputStream os = alarmSocket.getOutputStream()) {
            String alarmMessage = IMSCMMLModel.getTestAlarm();
            os.write(alarmMessage.getBytes(StandardCharsets.UTF_8));
            LOGGER.info("==============:alarm sent:===============");
        } catch (IOException e) { //mark
            LOGGER.error("fail to send alarm: {}", e.getMessage());
        }
    }

    /**
     * 按照给定的告警速率发送告警
     *
     * @param rate 告警发送速率
     * @param batchId 当前告警任务中第几次发送
     * @throws IOException
     */
    public void sendAlarmByRate(int rate, int batchId) throws IOException {
        LOGGER.info("sendAlarmByRate start: ");

        for (int i = 0; i < rate; i++) {
            String alarmMessage = IMSCMMLModel.getTestAlarm((batchId - 1) * rate + i + 1, moduleId);
            os.write(alarmMessage.getBytes(StandardCharsets.UTF_8));
        }
        os.flush();

        System.out.println(
            "===ALARM TEST LOG===:" + rate + " alarm sent, mission start time: " + IMSCMMLModel.getTriggerTime()
                + ", current time: " + TimeUtill.getTIME() + ", batch id " + batchId);
    }


    private static OutputStream os;

    ScheduledExecutorService service;

    private static ThreadPoolExecutor pool;

    /**
     * 前台的自定义告警 开始发送 按钮对应的服务接口
     * <p>
     * 将读取前台传入的参数，并启动定时任务发送告警
     *
     * @throws IOException the io exception
     * @throws InterruptedException the interrupted exception
     */
    public void startSendingAlarm() {
        // 获取前台页面输入的告警速率、压测时长、挂载模块ID、任务起始时间
        IMSCStubVO vo = (IMSCStubVO) ConversationUtils.getConversation().getModel();

        //参数校验
        if (!isValidVoParam(vo)) {
            LOGGER.error("verify params failed!");
            return;
        }

        alarmRate = vo.getAlarmRate();
        seconds = vo.getSeconds();
        moduleId = vo.getModuleId();
        IMSCMMLModel.setTriggerTime(TimeUtill.getTIME());

        try {
            // 取得socket对象，此处使用长连接，长期持有避免通道断开
            Socket socket = IMSCClientManager.getInstance().getAlarmSocket();
            os = socket.getOutputStream();

            //打印日志方便定位
            LOGGER.info("start sending alarms with rate: " + alarmRate + ", for " + seconds + " seconds.");

            pool = new ThreadPoolExecutor(1, 60, 5000,
                TimeUnit.MILLISECONDS, new PriorityBlockingQueue<Runnable>(),
                Executors.defaultThreadFactory(), new ThreadPoolExecutor.AbortPolicy());

            for (int i = 0; i < seconds; i++) {
                pool.execute(alarmTask);
            }

            LOGGER.info("sending alarms schedule finished, total alarms number is: " + alarmRate * seconds);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 校验前台参数
     *
     * @param vo 页面对象
     * @throws IllegalArgumentException
     */
    private boolean isValidVoParam(IMSCStubVO vo) {
        if (null == vo) {
            LOGGER.error("failed to get IMSCStubVO is null");
            return false;
        }

        if (vo.getAlarmRate() <= 0) {
            LOGGER.error("given alarm sending rate is not valid");
            return false;
        }

        if (vo.getSeconds() <= 0) {
            LOGGER.error("given alarm sending period is not valid");
            return false;
        }

        if (vo.getModuleId() <= 0) {
            LOGGER.error("given module ID is not valid");
            return false;
        }

        return true;
    }

    /**
     * 停止自定义告警发送任务
     */
    public void stopSendingAlarm() {
        service.shutdownNow();
        LOGGER.info("alarmTask terminated by user.");
    }

    Runnable alarmTask = new Runnable() {
        int count = 0;

        public void run() {
            LOGGER.debug("alarmTask started.");
            if (count++ < seconds) {
                try {
                    sendAlarmByRate(alarmRate, count);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } else {
                service.shutdownNow();
                LOGGER.info("alarmTask shutdownNow.");
            }
        }
    };

    /**
     * @Description 告警定时任务线程
     * <AUTHOR>
     * @Since 2020/9/15
     */
    public class AlarmSchedule extends TimerTask {
        int count = 0;

        @Override
        public void run() {
            System.out.println("AlarmSchedule started.");
            if (count++ < seconds) {
                try {
                    sendAlarmByRate(alarmRate, count);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } else {
                timer.cancel();
                timer.purge();
                System.out.println("sending alarms schedule finished, total alarms number is: " + alarmRate * seconds);
            }
        }
    }

    public void delete(int id) {
        System.out.println(id);
    }

    public void delete(final String id) {
        System.out.println("final String : " + id);
    }

    public static void setRunning(boolean running) {
        IMSCStubService.running = running;
    }

}
