/*
 * 文件名：IMSCStubVO.java
 * 版权：Copyright 2006-2016 Huawei Tech. Co. Ltd. All Rights Reserved. 
 * 描述： IMSCStubVO.java
 * 修改人：sWX315038
 * 修改时间：2016-1-4
 * 修改内容：新增
 */
package com.huawei.imsc.vo;

import com.huawei.imsc.pm.IMSCTrafficTask;

import java.util.List;

/**
 * IMSC前台展示类
 * 
 * <AUTHOR> 2016-1-4
 */
public class IMSCStubVO
{
    /**
     * 维测端口
     */
    private int MTPort;
    
    /**
     * 告警端口
     */
    private int alarmPort;
    
    /**
     * 性能端口
     */
    private int trafficPort;
    
    /**
     * 维护端口
     */
    private int maintePort;

    /**
     * 自定义告警速率
     */
    private int alarmRate;

    /**
     * 自定义告警发送时长
     */
    private int seconds;

    /**
     * 自定义模块id
     */
    private int moduleId;

    /**
     * 性能任务列表
     */
    private List<IMSCTrafficTask> taskList;
    
    private boolean running;

    private boolean secure;

    private boolean trustAll;

    public boolean isSecure() {
        return secure;
    }

    public void setSecure(boolean secure) {
        this.secure = secure;
    }

    public boolean isTrustAll() {
        return trustAll;
    }

    public void setTrustAll(boolean trustAll) {
        this.trustAll = trustAll;
    }

    public String getClientAddr()
    {
        return clientAddr;
    }

    public void setClientAddr(String clientAddr)
    {
        this.clientAddr = clientAddr;
    }

    private String clientAddr;
    
    
    public int getMTPort()
    {
        return MTPort;
    }

    public void setMTPort(int mTPort)
    {
        MTPort = mTPort;
    }

    public int getAlarmPort()
    {
        return alarmPort;
    }

    public void setAlarmPort(int alarmPort)
    {
        this.alarmPort = alarmPort;
    }

    public int getTrafficPort()
    {
        return trafficPort;
    }

    public void setTrafficPort(int trafficPort)
    {
        this.trafficPort = trafficPort;
    }

    public int getMaintePort()
    {
        return maintePort;
    }

    public void setMaintePort(int maintePort)
    {
        this.maintePort = maintePort;
    }

    public boolean isRunning()
    {
        return running;
    }

    public void setRunning(boolean running)
    {
        this.running = running;
    }

    public List<IMSCTrafficTask> getTaskList()
    {
        return taskList;
    }

    public void setTaskList(List<IMSCTrafficTask> taskList)
    {
        this.taskList = taskList;
    }

    public int getAlarmRate()
    {
        return alarmRate;
    }

    public void setAlarmRate(int alarmRate)
    {
        this.alarmRate = alarmRate;
    }

    public int getSeconds()
    {
        return seconds;
    }

    public void setSeconds(int seconds)
    {
        this.seconds = seconds;
    }

    public int getModuleId()
    {
        return moduleId;
    }

    public void setModuleId(int moduleId)
    {
        this.moduleId = moduleId;
    }
}
