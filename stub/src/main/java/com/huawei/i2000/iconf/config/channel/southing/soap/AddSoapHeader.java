package com.huawei.i2000.iconf.config.channel.southing.soap;

import java.util.List;
import javax.xml.namespace.QName;

// import org.apache.cxf.binding.soap.SoapHeader;
// import org.apache.cxf.binding.soap.SoapMessage;
// import org.apache.cxf.binding.soap.interceptor.AbstractSoapInterceptor;
// import org.apache.cxf.binding.soap.saaj.SAAJInInterceptor;
// import org.apache.cxf.headers.Header;
// import org.apache.cxf.helpers.DOMUtils;
// import org.apache.cxf.interceptor.Fault;
// import org.apache.cxf.phase.AbstractPhaseInterceptor;
// import org.apache.cxf.phase.Phase;
// import org.w3c.dom.Document;
// import org.w3c.dom.Element;
/**

 * 

 * @Title:获取soap头信息

 * 

 * @Description:

 * 

 * @Copyright:

 * 

 * <AUTHOR>

 * @version 1.00.000

 * 

 */

// public class AddSoapHeader  extends AbstractSoapInterceptor  {



    // public AddSoapHeader() {
	// 	super(Phase.WRITE);
	// }


//     public void handleMessage(SoapMessage message) throws Fault {
//
//     	System.out.println("is peogress*****************");
//
//     	QName qName = new QName("CertificationProxy");
//
// 		Document doc = DOMUtils.createDocument();
// 		 // 验证用户名
// 		Element id = doc.createElement("userid");
// 		id.setTextContent("xxx");
// 		// 验证密码
// 		Element pwd = doc.createElement("userpwd");
// 		pwd.setTextContent("xxx");
//
// 		Element root = doc.createElementNS("http://www.huawei.com/","CertificationProxy");
// //		root.appendChild(id);
// //		root.appendChild(pwd);
// 		// 创建SoapHeader内容
// 		SoapHeader header = new SoapHeader(qName, root);
// 		// 添加SoapHeader内容
// 		List<Header> headers = message.getHeaders();
// 		headers.add(header);
//     }


// }