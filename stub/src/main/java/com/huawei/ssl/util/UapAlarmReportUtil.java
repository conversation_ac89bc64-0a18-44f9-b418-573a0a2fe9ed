/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.ssl.util;

import com.huawei.oms.log.OMSLog;
import com.huawei.oms.log.OMSLogFactory;
import com.huawei.ssl.model.RequestModel;

import java.io.OutputStream;
import java.net.Socket;
import java.util.HashSet;
import java.util.Set;

/**
 * @Description 用于向I2000上报告警
 * <AUTHOR>
 * @Since 2021/2/27
 */
public class UapAlarmReportUtil implements Runnable {
    private final static OMSLog LOGGER = OMSLogFactory.getLog(UapAlarmReportUtil.class);
    /**
     * 存储所有告警socket
     */
    private static Set<Socket> alarmSockets = new HashSet<Socket>();

    private static Object lock = new Object();

    private static int alarmCount = 10;

    public static void addAlarmSocket(Socket socket) {
        synchronized (lock) {
            alarmSockets.add(socket);
        }
    }

    public static void removeAlarmSocket(Socket socket) {
        synchronized (lock) {
            alarmSockets.remove(socket);
        }
    }

    @Override
    public void run() {
        sendAlarm();

    }

    public void start() {
        new Thread(this).start();
    }

    /**
     * <一句话功能简述>上报告警
     * <功能详细描述>
     *
     * @see [类、类#方法、类#成员]
     */
    public void sendAlarm() {

        String alarms[] = {RequestModel.UAP6600Alarm(), RequestModel.UAP8100Alarm(), RequestModel.UAP9600Alarm()};
        OutputStream out = null;
        Set<Socket> badSockets = new HashSet<Socket>();
        Socket badsocket = null;
        while (alarmCount-- > 0) {
            try {
                for (Socket alarmSocket : alarmSockets) {
                    out = alarmSocket.getOutputStream();
                    for (String alarm : alarms) {
                        System.out.println("gap" + alarm);
                        out.write(alarm.getBytes("gbk"));
                        out.flush();
                    }

                }
            } catch (Exception e) {
                badSockets.add(badsocket);
            }

            for (Socket s : badSockets) {
                removeAlarmSocket(s);
            }

            try {
                Thread.sleep(60 * 1000);
            } catch (InterruptedException e) {
                LOGGER.error("thread interrupted", e);
            }

        }

        alarmCount = 10;

    }

}