/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.ssl.util;

import com.huawei.oms.log.OMSLog;
import com.huawei.oms.log.OMSLogFactory;

import org.apache.commons.io.FileUtils;
import org.osgi.framework.Bundle;
import org.osgi.framework.FrameworkUtil;
import org.osgi.framework.Version;

import java.io.File;

/**
 * @Description fortify工具类
 * <AUTHOR>
 * @Since 2021/2/27
 */
public abstract class FortifyUtil {
    private static final OMSLog logger = OMSLogFactory.getLog(FortifyUtil.class);

    /**
     * @param path path
     * @return File File
     */
    public static File fortifyGetFile(String path) {
        return FileUtils.getFile(path);
    }

    /**
     * 测试同一个类，被不同的bundle export出来，是否会被正确调用
     *
     * <AUTHOR>
     */
    public static void getZoneSolution(String zoneSolution) {
        Bundle theBundle = getBundle(FortifyUtil.class);
        String bundleSymbolicName = "";
        Long bundleId = 0l;
        org.osgi.framework.Version bundleVer = new Version(0, 0, 0);
        if (theBundle != null) {
            bundleSymbolicName = theBundle.getSymbolicName();
            bundleId = theBundle.getBundleId();
            bundleVer = theBundle.getVersion();
        }
        logger.debug(
            "currnet zone is zoneSolution = {}, this classpath = {}, this bundle = {}, bundle's name = {}, bundle's id = {}, bundle's version = {}",
            zoneSolution, FortifyUtil.class.getPackage(), theBundle, bundleSymbolicName, bundleId, bundleVer);
        logger.debug("ttttt,vas solution,vas zone");
    }

    private static Bundle getBundle(Class<?> tt) {
        return FrameworkUtil.getBundle(tt);
    }
}
