/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.ssl.util;

import com.huawei.oms.log.OMSLog;
import com.huawei.oms.log.OMSLogFactory;
import com.huawei.ssl.model.RequestModel;

import java.util.Arrays;

/**
 * @Description 该类用于处理MML命令
 * <AUTHOR>
 * @Since 2021/2/27
 */
public class ReturnCodeUtil {
    private final static OMSLog LOGGER = OMSLogFactory.getLog(ReturnCodeUtil.class);

    public static String getReturnCode(String receiveCode) {
        if (null == receiveCode) {
            return UapConstants.EMPTY_STRING;
        }
        //获取网元返回码
        if (receiveCode.startsWith("LST ME:;")) {
            return RequestModel.getNeCode();
        }
        if (receiveCode.startsWith("LST WKSP:;")) {
            return RequestModel.getNeInfoCode();
        }
        //SHK HANDLE:;
        if (receiveCode.startsWith("SHK HANDLE:")) {
            return RequestModel.SHKHANDEL();
        }
        //LST BRD:;
        if (receiveCode.startsWith("LST BRD:;")) {
            return RequestModel.BRD();
        }
        //LST MEMLNK
        if (receiveCode.startsWith("LST MEMLNK")) {
            return RequestModel.LSTMEMLNK();
        }
        //LST VER:;
        if (receiveCode.startsWith("LST VER:;")) {
            return RequestModel.LST8100VER();
        }
        //LST FRM:;
        if (receiveCode.startsWith("LST FRM:;")) {
            return RequestModel.LST8100FRM();
        }
        //LST TSKOBJ:MU=OSCPURATE;
        if (receiveCode.startsWith("LST TSKOBJ:MU=OSCPURATE;")) {
            return RequestModel.LSTTSKOBJ8100();
        }
        //CRE TSK:TSKN="3141221056"
        if (receiveCode.startsWith("CRE TSK:TSKN=")) {
            return RequestModel.TSKTSKN(receiveCode);
        }
        //SHK HAND:;
        if (receiveCode.startsWith("SHK HAND:;")) {
            return RequestModel.SHKHAND8100();
        }
        //LST TSK:;
        if (receiveCode.startsWith("LST TSK:;")) {
            //return RequestModel.LSTTSK8100();
        }
        //LST MDU:;
        if (receiveCode.startsWith("LST MDU:;")) {
            return RequestModel.LSTMDU8100();
        }
        //LST M3LNK:;LST N7LNK:;
        if (receiveCode.startsWith("LST N7LKS:;") || receiveCode.startsWith("LST M3LNK:;") || receiveCode.startsWith(
            "LST M3LKS:;") || receiveCode.startsWith("LST N7LNK:;")) {
            return RequestModel.LSTLNK(receiveCode);
        }
        //LST TRFINF:;
        if (receiveCode.startsWith("LST TRFINF:;")) {
            return RequestModel.LSKTRFINF();
        }

        try {
            //注册返回码
            if (receiveCode.startsWith("LGI:OP=")) {

                receiveCode = receiveCode.replaceAll(UapConstants.CITATION_STRING, UapConstants.EMPTY_STRING);
                String[] temp = receiveCode.split(UapConstants.EQUAL_STRING);
                String op = temp[1].split(UapConstants.COMMA_STRING)[0];
                String pwd = temp[2].split(UapConstants.SEMICOLON_STRING)[0];
                System.out.println(Arrays.toString(temp));
                if (temp.length > 4) {
                    //[LGI:OP, admin,PWD, Uapurp9600,PID, 8940,CLT, LMT;]
                    String PID = temp[3].split(UapConstants.COMMA_STRING)[0];
                    String CLT = temp[4].replaceAll(UapConstants.SEMICOLON_STRING, UapConstants.EMPTY_STRING);

                    return RequestModel.registReturn8100(op, pwd, PID, CLT);
                }
                if ("admin8100USAU".equals(op)) {
                    return RequestModel.regist8100Usau(op, pwd);
                }
                return RequestModel.registRetrun(op, pwd);
            }
            if (receiveCode.startsWith("USE ME:MEID")) {
                String MEID = receiveCode.split(UapConstants.EQUAL_STRING)[1].replaceAll(UapConstants.SEMICOLON_STRING,
                    UapConstants.EMPTY_STRING);
                return RequestModel.changeNe(MEID);
            }
            //LST ALMLOG:MEID=6,CLRFLG=UNCLEARED,SRTORD=ASC,CNT=1000;
            if (receiveCode.startsWith("LST ALMLOG:")) {
                String[] params = receiveCode.split(UapConstants.COLON_STRING)[1].replaceAll(UapConstants.SEMICOLON_STRING,
                    UapConstants.EMPTY_STRING).split(UapConstants.COMMA_STRING);
                String MEID = params[0].split(UapConstants.EQUAL_STRING)[1];
                String CLRFLG = params[1].split(UapConstants.EQUAL_STRING)[1];
                String SRTORD = params[2].split(UapConstants.EQUAL_STRING)[1];
                String CNT = params[3].split(UapConstants.EQUAL_STRING)[1];
                return RequestModel.alarmLog(MEID, CLRFLG, SRTORD, CNT);
            }
            //LST MEAOBJ:MEID=5,MOCID=83886974, OBJFLAG=NO;
            if (receiveCode.startsWith("LST MEAOBJ:")) {
                String[] params = receiveCode.split(UapConstants.COLON_STRING)[1].replaceAll(UapConstants.SEMICOLON_STRING,
                    UapConstants.EMPTY_STRING).split(UapConstants.COMMA_STRING);
                String MEID = params[0].split(UapConstants.EQUAL_STRING)[1];
                String MOCID = params[1].split(UapConstants.EQUAL_STRING)[1];
                String OBJFLAG = params[2].split(UapConstants.EQUAL_STRING)[1];
                //9600
                if ("84886986".equals(MOCID)) {
                    return RequestModel.MEAOBJ9600(MEID, MOCID, OBJFLAG);
                }

                return RequestModel.MEAOBJ(MEID, MOCID, OBJFLAG);
            }
            if (receiveCode.startsWith("MOD MEASTSK:MEID")) {//MOD MEASTSK:MEID=5,MUID=21,MUPERIOD=5;
                String[] params = receiveCode.split(UapConstants.COLON_STRING)[1].replaceAll(UapConstants.SEMICOLON_STRING,
                    UapConstants.EMPTY_STRING).split(UapConstants.COMMA_STRING);
                String MEID = params[0].split(UapConstants.EQUAL_STRING)[1];
                String MUID = params[1].split(UapConstants.EQUAL_STRING)[1];
                String MUPERIOD = params[2].split(UapConstants.EQUAL_STRING)[1];
                return RequestModel.modMeastst(MEID, MUID, MUPERIOD);
            }
            //CRE TRFCCP
            if (receiveCode.startsWith("CRE TRFCCP:TSKN=")) {
                String temp[] = receiveCode.split(UapConstants.COMMA_STRING);
                String TSKN = temp[0].split(UapConstants.EQUAL_STRING)[1].replaceAll(UapConstants.CITATION_STRING,
                    UapConstants.EMPTY_STRING);
                String MODU = temp[6].split(UapConstants.EQUAL_STRING)[1];
                String add = TSKN + UapConstants.EQUAL_STRING + MODU;
                if (!RequestModel.uap8100Traf.contains(add)) {
                    RequestModel.uap8100Traf.add(add);
                }
                return RequestModel.CRETRFCCP8100(TSKN, MODU, receiveCode);
            }
        } catch (NullPointerException e) {
            LOGGER.error("invalid format of receiveCode.");
        }
        return UapConstants.EMPTY_STRING;
    }
}
