/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.ssl.util;

import com.huawei.oms.log.OMSLog;
import com.huawei.oms.log.OMSLogFactory;
import com.huawei.oms.util.OmsConstant;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Properties;

/**
 * @Description 获取版本号
 * <AUTHOR>
 * @Since 2021/2/27
 */
public class GetVersionUtil {

    private static final OMSLog logger = OMSLogFactory.getLog(GetVersionUtil.class);

    /**
     * 获取版本号
     * 从后台目录文件读取指定路径内国际化文件中指定key值的版本号
     *
     * @param proName 产品名（作为路径中etc目录下用于存放该产品的版本号国际化资源文件）
     * @param proVersion 国际化资源文件中的key
     * @return
     * @see [类、类#方法、类#成员]
     */
    public static String getVersion(String proName, String proVersion) {

        String etcPath = System.getProperty(OmsConstant.OMS_PATH_ETC);

        if (etcPath == null) {
            logger.error("Failed to get path!");
            return "";
        }

        String versionFile = etcPath + File.separator + proName + File.separator + "version.properties";

        return getInfoFromFile(versionFile, proVersion);
    }

    private static String getInfoFromFile(String filePath, String key) {
        String value = null;
        FileInputStream infoInStream = null;

        try {
            File infoFile = FortifyUtil.fortifyGetFile(filePath);
            if (null == infoFile || !infoFile.exists()) {
                logger.debug(filePath + " not exists and return null");
                return "";
            }

            infoInStream = new FileInputStream(infoFile);

            Properties infoProp = new Properties();
            infoProp.load(infoInStream);
            value = infoProp.getProperty(key);

        } catch (FileNotFoundException e) {
            logger.error("Failed to find the file in this path!");
            return "";
        } catch (IOException e) {
            logger.error("exception when get info: " + e.getMessage());
            return "";
        } finally {
            //FileOperUtils.closeFileInputStream(infoInStream);
            if (null != infoInStream) {
                try {
                    infoInStream.close();
                } catch (IOException e) {
                    logger.error("Fail to close FileInputStream...");
                }
            }
        }

        return value;
    }
}