/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.ssl.server;

import com.huawei.oms.log.OMSLog;
import com.huawei.oms.log.OMSLogFactory;
import com.huawei.ssl.view.UapPorts;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;

/**
 * @Description 性能服务
 * <AUTHOR>
 * @Since 2021/2/27
 */
public class TrafficServer implements SSLServer, Runnable {
    private final static OMSLog LOGGER = OMSLogFactory.getLog(TrafficServer.class);

    private ServerSocket server = null;

    private boolean runFlag = true;

    public TrafficServer() {
        init();
    }

    @Override
    public void init() {
        try {
            server = new ServerSocket(UapPorts.TRAFFIC_PORT);

        } catch (IOException e) {
            LOGGER.error("fail to create server socket", e);
        }

    }

    @Override
    public void startServer() {
        Socket client = null;
        while (runFlag) {
            try {
                client = server.accept();
                LOGGER.info("TrafficServer got a socket: {}:{}", client.getInetAddress(), client.getPort());
                ClientManager.getInstance().setTrafficSocket(client);
                //启动线程读取数据
                new Thread(new SSLRead(client)).start();
            } catch (IOException e) {
                LOGGER.error("fail to to read from client", e);
                stopServer();
                break;
            }
        }
    }

    @Override
    public void stopServer() {
        runFlag = false;
        //创建虚假连接
        try {
            if (server != null){
                server.close();
            }
        } catch (Exception e) {
            LOGGER.error("fail to close server socket", e);
        }
    }

    @Override
    public void run() {
        startServer();
    }

}