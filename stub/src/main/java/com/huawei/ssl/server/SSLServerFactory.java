/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.ssl.server;

/**
 * @Description 服务端工厂
 * <AUTHOR>
 * @Since 2021/2/27
 */
public class SSLServerFactory {
    private static SSLServerFactory ssf = null;

    private SSLServerFactory() {

    }

    public static SSLServerFactory newInstance() {
        if (ssf == null) {
            ssf = new SSLServerFactory();
        }
        return ssf;
    }

    public MaintainServer createRegServer() {
        return new MaintainServer();
    }

    public AlarmServer createAlarmServer() {
        return new AlarmServer();
    }

    public TrafficServer createTrafficServer() {
        return new TrafficServer();
    }

}
