/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.ssl.server;

import com.huawei.oms.log.OMSLog;
import com.huawei.oms.log.OMSLogFactory;

import java.io.IOException;
import java.net.Socket;

/**
 * @Description 客户端管理类
 * <AUTHOR>
 * @Since 2021/2/27
 */
public class ClientManager {

    private static final OMSLog LOGGER = OMSLogFactory.getLog(ClientManager.class);

    private Socket mainSocket;

    private Socket alarmSocket;

    private Socket trafficSocket;

    private static ClientManager instance;

    private ClientManager() {

    }

    public static ClientManager getInstance() {
        if (instance == null) {
            instance = new ClientManager();
        }
        return instance;
    }

    public Socket getMainSocket() {
        return mainSocket;
    }

    public void setMainSocket(Socket mainSocket) {
        try
        {
            if(this.mainSocket != null)
            {
                this.mainSocket.close();
            }
        }
        catch (IOException e)
        {
            LOGGER.error("mainSocket close failed.", e);
        }
        this.mainSocket = mainSocket;
    }

    public Socket getAlarmSocket() {
        return alarmSocket;
    }

    public void setAlarmSocket(Socket alarmSocket) {
        try
        {
            if(this.alarmSocket != null)
            {
                this.alarmSocket.close();
            }
        }
        catch (IOException e)
        {
            LOGGER.error("alarmSocket close failed.", e);
        }
        this.alarmSocket = alarmSocket;
    }

    public Socket getTrafficSocket() {
        return trafficSocket;
    }

    public void setTrafficSocket(Socket trafficSocket) {
        try
        {
            if(this.trafficSocket != null)
            {
                this.trafficSocket.close();
            }
        }
        catch (IOException e)
        {
            LOGGER.error("trafficSocket close failed.", e);
        }
        this.trafficSocket = trafficSocket;
    }
}