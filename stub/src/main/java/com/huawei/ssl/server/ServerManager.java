/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.ssl.server;

/**
 * @Description 服务端管理类
 * <AUTHOR>
 * @Since 2021/2/27
 */
public class ServerManager {
    private static MaintainServer maintainServer = null;

    private static AlarmServer alarmServer = null;

    private static TrafficServer trafficServer = null;

    public static boolean status = true;

    public static void start() {
        status = true;
        SSLServerFactory sslServerFactory = SSLServerFactory.newInstance();
        maintainServer = sslServerFactory.createRegServer();
        alarmServer = sslServerFactory.createAlarmServer();
        trafficServer = sslServerFactory.createTrafficServer();

        new Thread(maintainServer).start();
        new Thread(alarmServer).start();
        new Thread(trafficServer).start();
    }

    public static void stop() {
        status = false;
        trafficServer.stopServer();
        alarmServer.stopServer();
        maintainServer.stopServer();

    }
}
