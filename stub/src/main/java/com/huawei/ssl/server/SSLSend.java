/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.ssl.server;

import com.huawei.oms.log.OMSLog;
import com.huawei.oms.log.OMSLogFactory;
import com.huawei.ssl.util.ReturnCodeUtil;

import java.io.IOException;
import java.io.OutputStream;
import java.net.Socket;

/**
 * @Description 发送
 * <AUTHOR>
 * @Since 2021/2/27
 */
public class SSLSend implements Runnable {
    private final static OMSLog LOGGER = OMSLogFactory.getLog(SSLSend.class);

    private String ReceCodes = null;

    private Socket client;

    private final static String CHAR_SET = "gbk";

    public SSLSend(Socket client) {
        this.client = client;
    }

    public SSLSend(Socket client, String ReceCode) {
        this.client = client;
        this.ReceCodes = ReceCode;
    }

    @Override
    public void run() {
        OutputStream outputStream;
        try {
            LOGGER.debug("begin to send from localPort:{} to client {}:{}", client.getLocalPort(),
                client.getInetAddress(), client.getPort());

            String returnCode = ReturnCodeUtil.getReturnCode(ReceCodes);
            outputStream = client.getOutputStream();

            outputStream.write(returnCode.getBytes(CHAR_SET));
            LOGGER.debug("finish sending, return code is:{}", returnCode);
            outputStream.flush();
        } catch (IOException e) {
            LOGGER.error("fail to send to client", e);
        }

    }
}