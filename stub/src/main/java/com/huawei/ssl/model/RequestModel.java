package com.huawei.ssl.model;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Properties;
import java.util.Set;

import com.huawei.oms.util.OmsConstant;
/**
 *
 * <一句话功能简述>用于返回MML操作成功的数据包
 * <功能详细描述>
 *
 * <AUTHOR>
 * @version  [版本号, 2015-7-9]
 * @see  [相关类/方法]
 * @since  [产品/模块版本]
 */
public class RequestModel {

    private static Properties pro=null;
    public static Set<String> uap8100Traf=new HashSet<String>();
    private static final String  CHAN_LINE="\r\n";
    static{
        pro=new Properties();
        try {
            String etcPath = System.getProperty(OmsConstant.OMS_PATH_ETC)+"/i2000.ds/resource/resource.properties";
            pro.load(new FileInputStream(etcPath));
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static String registRetrun(String op,String pwd)
    {

        StringBuffer returnCode=new StringBuffer();
        returnCode.append("+++    UNKNOWN        ").append(timeUtill()).append(CHAN_LINE)
            .append("O&M    #2273").append(CHAN_LINE)
            .append("%%").append(CHAN_LINE)
            .append("LGI:OP=\"").append(op).append("\",PWD=\"").append("*****\";%%").append(CHAN_LINE)
            .append("RETCODE = 0  ").append(pro.getProperty("OPER_SUCCESS")).append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);



        return returnCode.toString();
    }
    public static String regist8100Usau(String op,String pwd)
    {
		/*
+++    Anonymous        2015-06-24 12:14:58+08:00
O&M    #145374
%%LGI:OP="admin",PWD="*****";%%
RETCODE = 0  Operation succeeded

admin logged in successfully
---    END

		 */
        StringBuffer returnCode=new StringBuffer();
        returnCode.append("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("Welcome to USAU Operation and Maintenance Center").append(CHAN_LINE)
            .append("Version : USAU V300R005").append(CHAN_LINE)
            .append("Copyright(c) HuaWei. All Rights Reserved").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("+++    Anonymous        ").append(timeUtill()).append(CHAN_LINE)
            .append("O&M    #2273").append(CHAN_LINE)
            .append("%%").append(CHAN_LINE)
            .append("LGI:OP=\"").append(op).append("\",PWD=\"").append("*****\";%%").append(CHAN_LINE)
            .append("RETCODE = 0  ").append("Operation succeeded").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append(op).append(" logged in successfully").append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);

        return returnCode.toString();
    }
    public static String registReturn8100(String op,String pwd,String PID,String CLT)
    {
        StringBuffer returnCode=new StringBuffer();
        returnCode.append("+++    HUAWEI UMG8900        ").append(timeUtill8100()).append(CHAN_LINE)
            .append("O&M    #3").append(CHAN_LINE)
            .append("%%").append("LGI:OP=\"").append(op).append("\",PWD=\"").append("*****\",PID=").append(PID).append(",CLT=LMT;%%").append(CHAN_LINE)
            .append("RETCODE = 0  ").append("accomplished").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);

        return returnCode.toString();
    }
    //LST FRM:;
    public static String LST8100FRM()
    {
        StringBuffer returnCode=new StringBuffer();
        returnCode.append("+++    HUAWEI UMG8900        ").append(timeUtill8100()).append(CHAN_LINE)
            .append("O&M    #8").append(CHAN_LINE)
            .append("%%LST FRM:;%%").append(CHAN_LINE)
            .append("RETCODE = 0  ").append("accomplished").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("Table of frame record").append(CHAN_LINE)
            .append("---------------------").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("                       Frame No. = 1").append(CHAN_LINE)
            .append("               Frame Version No. = SSM-256").append(CHAN_LINE)
            .append("                      Frame Type = The main control frame").append(CHAN_LINE)
            .append("                     Cabinet No. = 0").append(CHAN_LINE)
            .append("                  Frame Position = MIDDLE").append(CHAN_LINE)
            .append("                     Frame Name = NULL").append(CHAN_LINE)
            .append("               Frame Description = NULL").append(CHAN_LINE)
            .append("(Number of results = 1)").append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);


        return returnCode.toString();

    }
    //LST N7LNK:;
    public static String LSTLNK(String str)
    {
		/*
+++    Anonymous        2015-06-24 13:02:16+08:00
O&M    #145431
%%LST M3LNK:;%%
RETCODE = 1515  Insufficient authority

---    END
		 */
        StringBuffer returnCode=new StringBuffer();
        returnCode
            .append("+++    Anonymous        ").append(timeUtill()).append(CHAN_LINE)
            .append("O&M    #145431").append(CHAN_LINE)
            .append("%%").append(str).append("%%").append(CHAN_LINE)
            .append("RETCODE = 1515  ").append("Insufficient authority").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);
        return returnCode.toString();

    }
    //LST MDU:;
    public static String LSTMDU8100()
    {
		/*
		+++    Anonymous        2015-06-24 15:26:44+08:00
O&M    #145533
%%LST MDU:;%%
RETCODE = 0  Operation succeeded

Module Information
------------------
 Module number  Board type  Frame number  Slot number  Assistant slot number

 2              SMU         0             6            8
 35             IFM         0             11           InValid
 36             BSG         0             12           InValid
 71             IFM         0             10           InValid
 73             CSU         0             5            InValid
(Number of results = 5)

---    END
		 */
        StringBuffer returnCode=new StringBuffer();
        returnCode.append("+++    Anonymous        ").append(timeUtill()).append(CHAN_LINE)
            .append("O&M    #145533").append(CHAN_LINE)
            .append("%%LST MDU:;%%").append(CHAN_LINE)
            .append("RETCODE = 0  ").append("Operation succeeded").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("Module Information").append(CHAN_LINE)
            .append("------------------").append(CHAN_LINE)
            .append(" Module number  Board type  Frame number  Slot number  Assistant slot number ").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append(" 2              SMU         0             6            8                     ").append(CHAN_LINE)
            .append(" 35             IFM         0             11           InValid               ").append(CHAN_LINE)
            .append(" 36             BSG         0             12           InValid               ").append(CHAN_LINE)
            .append(" 71             IFM         0             10           InValid               ").append(CHAN_LINE)
            .append(" 73             CSU         0             5            InValid               ").append(CHAN_LINE)
            .append("(Number of results = 5)").append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);
        return returnCode.toString();
    }

    //LST VER:;
    public static String LST8100VER()
    {
        /**

         +++    Anonymous        2015-06-24 12:15:52+08:00
         O&M    #145375
         %%LST VER:;%%
         RETCODE = 0  Operation succeeded

         Result of current software query
         --------------------------------
         Current Software Version  =  USAU V300R005C55SPC100
         Current Software Status  =  Normal
         ---    END

         */
        StringBuffer returnCode=new StringBuffer();
        returnCode

            .append("+++    Anonymous        ").append(timeUtill()).append(CHAN_LINE)
            .append("O&M    #145375").append(CHAN_LINE)
            .append("%%LST VER:;%%").append(CHAN_LINE)
            .append("RETCODE = 0  ").append("Operation succeeded").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("Result of current software query").append(CHAN_LINE)
            .append(" --------------------------------").append(CHAN_LINE)
            .append(" Current Software Version  =  USAU V300R005C55SPC100").append(CHAN_LINE)
            .append(" Current Software Status  =  Normal").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);
        return returnCode.toString();
    }
    //%%LST TSKOBJ:MU=OSCPURATE;%%

    public static String LSTTSKOBJ8100()
    {
        StringBuffer returnCode=new StringBuffer();

        returnCode.append("+++    HUAWEI UMG8900        ").append(timeUtill8100()).append(CHAN_LINE)
            .append("O&M    #7").append(CHAN_LINE)
            .append("%%LST TSKOBJ:MU=OSCPURATE;%%").append(CHAN_LINE)
            .append("RETCODE = 0  ").append("accomplished").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("Perf Obj Inf").append(CHAN_LINE)
            .append("------------                                                                  ").append(CHAN_LINE)
            .append(" Obj SN  Board Type  Board No.  Master/Slave  Obj Name           Obj Para     ").append(CHAN_LINE)
            .append(CHAN_LINE)

            .append(" 1       HRB         14         0             Board Information  HRB:14:0     ").append(CHAN_LINE)
            .append(" 2       HRB         14         1             Board Information  HRB:14:1     ").append(CHAN_LINE)
            .append(" 3       HRB         12         1             Board Information  HRB:12:1    ").append(CHAN_LINE)
            .append(" 4       S2L         10         0             Board Information  S2L:10:0    ").append(CHAN_LINE)
            .append(" 5       TNU         1          1             Board Information  TNU:1:1   ").append(CHAN_LINE)
            .append(" 6       PPU         3          0             Board Information  PPU:3:0 ").append(CHAN_LINE)
            .append(" 7       PPU         2          0             Board Information  PPU:2:0   ").append(CHAN_LINE)
            .append(" 8       CLK         0          1             Board Information  CLK:0:1      ").append(CHAN_LINE)
            .append(" 9       CLK         0          0             Board Information  CLK:0:0      ").append(CHAN_LINE)
            .append(" 10      CMU         34         1             Board Information  CMU:34:1     ").append(CHAN_LINE)
            .append(" 11      SPF         0          0             Board Information  SPF:0:0      ").append(CHAN_LINE)
            .append(" 12      VPU         12         0             Board Information  VPU:12:0     ").append(CHAN_LINE)
            .append(" 13      CMU         33         1             Board Information  CMU:33:1     ").append(CHAN_LINE)
            .append(" 14      VPU         9          0             Board Information  VPU:9:0      ").append(CHAN_LINE)
            .append(" 15      VPU         6          0             Board Information  VPU:6:0      ").append(CHAN_LINE)
            .append(" 16      VPU         5          0             Board Information  VPU:5:0      ").append(CHAN_LINE)
            .append(" 17      VPU         4          0             Board Information  VPU:4:0      ").append(CHAN_LINE)
            .append(" 18      CMU         31         1             Board Information  CMU:31:1     ").append(CHAN_LINE)
            .append(" 19      CMU         30         1             Board Information  CMU:30:1     ").append(CHAN_LINE)
            .append(" 20      CMU         30         0             Board Information  CMU:30:0     ").append(CHAN_LINE)
            .append(" 21      NET         1          1             Board Information  NET:1:1      ").append(CHAN_LINE)
            .append(" 22      OMU         0          0             Board Information  OMU:0:0      ").append(CHAN_LINE)
            .append(" 23      OMU         0          1             Board Information  OMU:0:1      ").append(CHAN_LINE)
            .append("(Number of results = 23)").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);


        //一次测量单元调试
		/*
		returnCode.append("+++    HUAWEI UMG8900        ").append(timeUtill8100()).append(CHAN_LINE)
		.append("O&M    #7").append(CHAN_LINE)
		.append("%%LST TSKOBJ:MU=OSCPURATE;%%").append(CHAN_LINE)
		.append("RETCODE = 0  ").append("accomplished").append(CHAN_LINE)
		.append(CHAN_LINE)
		.append(CHAN_LINE)
		.append("Perf Obj Inf").append(CHAN_LINE)
		.append("------------                                                                  ").append(CHAN_LINE)
		.append(" Obj SN  Board Type  Board No.  Master/Slave  Obj Name           Obj Para     ").append(CHAN_LINE)
		.append(CHAN_LINE)

		.append(" 1       HRB         14         1             Board Information  HRB:14:1     ").append(CHAN_LINE)
		.append("(Number of results = 1)").append(CHAN_LINE)
		.append(CHAN_LINE)
		.append(CHAN_LINE)
		.append("---    END").append(CHAN_LINE);
		*/

        return returnCode.toString();
    }

    public static String Traffic8100CPU(String tnkname,String MODU)
    {/*
		+++    Anonymous                               2015-06-24 10:59:59+08:00
		TRAFFIC    4103903481        CPU
		<NULL>         <NULL>         5        2015-06-24 11:00:00

		MODU              SEIZR      CG      CGT      OL      OLT      ORC      ORCOL      TEC      PEAKS      PEAKT      PEAKD
		36                2      0      0      0      0      0      0      0      2      24D 10:55:00+08:00          300

		     1 RECORD(S)   IN REPORT #1      TOTAL     1 RECORD(S)
		---    END
		*/
        StringBuffer returnCode=new StringBuffer();
        returnCode.append("+++    Anonymous        ").append(timeUtill()).append(CHAN_LINE)
            .append("TRAFFIC    ").append(tnkname).append("        CPU").append(CHAN_LINE)
            .append("<NULL>         <NULL>         5        ").append(minuteBefore5MUtillNoZ()).append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("MODU              SEIZR      CG      CGT      OL      OLT      ORC      ORCOL      TEC      PEAKS      PEAKT      PEAKD      ").append(CHAN_LINE)
            .append(MODU).append("                2      0      0      0      0      0      0      0      2      24D ").append(minuteBefore5MUtill()).append("          300      ").append(CHAN_LINE)
            .append("     1 RECORD(S)   IN REPORT #1      TOTAL     1 RECORD(S)").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);

        return returnCode.toString();
    }

    public static String Traffic8100HRB()
    {


        StringBuffer returnCode=new StringBuffer();

		/*
		returnCode.append("+++    UMG8900                                 ").append(timeUtill8100()).append(CHAN_LINE)
					.append("TRAFFIC").append(CHAN_LINE)
					.append(CHAN_LINE)
					.append("-----------").append(CHAN_LINE)
					.append("                                         Task name = 3182258450").append(CHAN_LINE)
					.append("                                         Unit name = CPU Occupation Rate Statistics").append(CHAN_LINE)
					.append("                                 Statistics period = 5minute").append(CHAN_LINE)
					.append("                            Result sequence number = 12323").append(CHAN_LINE)
					.append("                           Whether result reliable = TRUE").append(CHAN_LINE)
					.append("                                   Object instance = Obj name = Board Information  Board Type = HRB  Board No. = 14  Master or slave = 1  Obj para = HRB:14:1").append(CHAN_LINE)
					.append("                             Measure starting time = ").append(minute8100Utill()).append(CHAN_LINE)
					.append("                   Average Occupied Rate of CPU(%) = 3").append(CHAN_LINE)
					.append("                       Duration of CPU Overload(s) = 0").append(CHAN_LINE)
					.append("                            Number of CPU Overload = 0").append(CHAN_LINE)
					.append("                     Duration of CPU Congestion(s) = 0").append(CHAN_LINE)
					.append("                          Number of CPU Congestion = 0").append(CHAN_LINE)
					.append(CHAN_LINE)
					.append("---    END").append(CHAN_LINE);
		*/
        return returnCode.toString();
    }


    //MOD MEASTSK:MEID=5,MUID=21,MUPERIOD=5;
    public static String  modMeastst(String MEID,String MUID,String MUPERIOD)
    {


        StringBuffer body=new StringBuffer();
        body.append("O&M    #805").append(CHAN_LINE)
            .append("%%MOD MEASTSK:MEID=").append(MEID).append(",MUID=").append(MUID).append(",MUPERIOD=").append(MUPERIOD).append(";%%").append(CHAN_LINE)
            .append("RETCODE = 0  操作成功").append(CHAN_LINE)
            .append(CHAN_LINE);


        return addHeadAndeEnd(body.toString());
    }
    public static String getNeCode()
    {


        StringBuffer returnCode=new StringBuffer();
        returnCode.append("+++    CDE        ").append(timeUtill()).append(CHAN_LINE)
            .append("O&M    #1868").append(CHAN_LINE)
            .append("%%LST ME:;%%").append(CHAN_LINE)
            .append("RETCODE = 0  ").append(pro.getProperty("OPER_SUCCESS")).append(CHAN_LINE)
            .append(CHAN_LINE)
            .append(pro.getProperty("OPER_RESULT")).append(CHAN_LINE)
            .append("------------").append(CHAN_LINE)
            .append(pro.getProperty("NEINFO")).append(CHAN_LINE)
            //				.append(" \u57DF\u6807\u8BC6  \u5B50\u7F51\u6807\u8BC6  \u7F51\u5143ID  \u7F51\u5143\u540D\u79F0  \u7F51\u5143\u7C7B\u578B  \u7F51\u5143\u7248\u672C         \u7F51\u5143\u5236\u9020\u5546\u540D\u79F0  \u7F51\u5143\u81EA\u5B9A\u4E49\u72B6\u6001  \u7F51\u5143\u672C\u5730\u540D\u79F0  \u7F51\u5143\u9501\u5B9A\u72B6\u6001  \u7F51\u5143\u56FD\u5BB6\u7801  \u7F51\u5143\u63A5\u53E3ID         \u7F51\u5143\u8BED\u8A00  \u7248\u672C\u5B89\u88C5\u65F6\u95F4\r\n")
            .append(CHAN_LINE)
            .append("1       0         0       CDE       CDE       V100R003C32B010  华为            正常            NULL          解锁          中国        CDEV100R003C32B010  中文      2015-06-05 14:06:50\r\n")
            .append(" 1       0         5       UAP_ME    UAP       V100R003C32B010  HUAWEI          正常            NULL          解锁          NULL        UAPV100R003C32B010  中文      2015-06-07 23:02:06\r\n")
            .append(" 1       0         6       URP_ME    URP       V100R003C32B010  HUAWEI          正常            NULL          解锁          NULL        URPV100R003C32B010  中文      2015-06-07 23:18:16\r\n")
            .append("(结果个数 = 3)\r\n")
            .append("\r\n")
            .append("---    END\r\n");

        return returnCode.toString();


    }

    public static String getNeInfoCode()
    {

        StringBuffer returnCode=new StringBuffer();
        returnCode.append("+++    CDE        ").append(timeUtill()).append("\r\n")
            .append("O&M    #972").append("\r\n")
            .append("%%LST WKSP:;%%").append("\r\n")
            .append("RETCODE = 0  操作成功").append("\r\n")
            .append("\r\n")
            .append("操作结果如下").append("\r\n")
            .append("------------").append("\r\n")
            .append(" 域标识  子网标识  网元ID  工作区标识  网元版本       工作区状态  网元类型  锁定状态").append("\r\n")
            .append("\r\n")
            .append("1       0         0         工作区1     V100R003C32B010  主用        CDE网元   解锁状态\r\n")
            .append(" 1       0         5       工作区1     V100R003C32B010  主用        UAP       解锁状态\r\n")
            .append(" 1       0         6       工作区1     V100R003C32B010  主用        URP       解锁状态\r\n")
            .append("(结果个数 = 3)\r\n")
            .append("\r\n")
            .append("---    END\r\n");

		/*
+++    CDE        2015-06-19 15:25:25+08:00
O&M    #1325
%%LST WKSP:;%%
RETCODE = 0  Operation succeeded.

The result is as follows
------------------------
 Domain ID  Subnet ID  ME ID   Workspace ID  ME version       Workspace state  ME type  Lock state

 1          0          0       Workspace1    V100R003C31B200  Active           CDE      UNLOCK
 1          0          5       Workspace1    V100R003C31B200  Active           UAP      UNLOCK
(Number of results = 2)

---    END
		 */

        return returnCode.toString();
    }


    public static String changeNe(String MEID)
    {

        StringBuffer returnCode=new StringBuffer();
        returnCode.append("+++    CDE        ").append(timeUtill()).append(CHAN_LINE)
            //					.append("O&M    #1278").append(CHAN_LINE)
            .append("O&M    #1311").append(CHAN_LINE)
            .append("%%USE ME:MEID=").append(MEID).append(";%%").append(CHAN_LINE)
            //					.append("RETCODE = 0  切换网元成功").append(CHAN_LINE)
            .append("RETCODE = 0  switch ME succeeded.").append(CHAN_LINE)

            .append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);



        return returnCode.toString();
    }

    //LST ALMLOG:MEID=6,CLRFLG=UNCLEARED,SRTORD=ASC,CNT=1000;
    public static String alarmLog(String MEID,String CLRFLG,String SRTORD,String CNT )
    {

        StringBuffer body=new StringBuffer();
        body.append("O&M    #1302").append(CHAN_LINE)
            .append("%%LST ALMLOG:").append("MEID=").append(MEID).append(",CLRFLG=").append(CLRFLG).append(",SRTORD=").append(SRTORD).append(",CNT=").append(CNT).append(";%%").append(CHAN_LINE)
            .append("RETCODE = 0  操作成功。").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("没有查到相应的结果").append(CHAN_LINE);


        return addHeadAndeEnd(body.toString());
    }


    public static String SHKHANDEL()
    {

        StringBuffer body=new StringBuffer();
        body.append("O&M    #1304").append(CHAN_LINE)
            .append("%%SHK HANDLE:;%%").append(CHAN_LINE)
            .append("RETCODE = 0  Operation succeeded.").append(CHAN_LINE)
            .append(CHAN_LINE);
        return addHeadAndeEnd(body.toString());
    }

    public static String MEAOBJ9600(String MEID,String MOCID,String OBJFLAG)
    {
		/*
		 +++    CDE        2015-06-19 13:31:54+08:00
O&M    #1230
%%LST MEAOBJ:MEID=5,MOCID=84886986, OBJFLAG=NO;%%
RETCODE = 0  Operation succeeded.

The result is as follows
-------------
MOIID  =  216,216
 MOIIDNAME  =  BSG,OBJ
(Number of results = 1)

---    END
		 */
        StringBuffer body=new StringBuffer();
        body.append("O&M    #1230").append(CHAN_LINE)
            .append("%%LST MEAOBJ:MEID=").append(MEID).append(",MOCID=").append(MOCID).append(", OBJFLAG=").append(OBJFLAG).append(";%%").append(CHAN_LINE)
            .append("RETCODE = 0  Operation succeeded.").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("The result is as follows").append(CHAN_LINE)
            .append("-------------").append(CHAN_LINE)
            .append("MOIID  =  216,216").append(CHAN_LINE)
            .append("MOIIDNAME  =  BSG,OBJ").append(CHAN_LINE)
            .append("(Number of results  = 1)").append(CHAN_LINE);

        return addHeadAndeEnd(body.toString());
    }

    //9600 LST MEMLNK:;

    public static String LSTMEMLNK(){
		/*
+++    UAP9600        2015-06-19 12:22:23+08:00
O&M    #917
%%LST MEMLNK:;%%
RETCODE = 0  Operation succeeded.

List MEM Link Information
-------------------------
 Service device number  IFM module number  Link name  Link number  Protocol type  Local IP address  Remote IP address1  Is support backup  N+1 check time  Remote IP address2  Remote IP port  Start dialog number  End dialog number  SCP type  ACN length  CTI capability  Userinfo length  SMC order type  SMC module number  SCP redirect ID  DR CTI Link  Local link number

 0                      9                  call_CI    0            FEP            ***********       ***********         NO                 0               0.0.0.0             6800            NULL                 NULL               NULL      NULL        100             NULL             NULL            8                  0                NO           0
 4                      9                  ivr_CI     1            FEP            ***********       ***********         NO                 0               0.0.0.0             5804            NULL                 NULL               NULL      NULL        100             NULL             NULL            8                  0                NO           1
 5                      9                  agent_CI   2            FEP            ***********       ***********         NO                 0               0.0.0.0             5802            NULL                 NULL               NULL      NULL        100             NULL             NULL            8                  0                NO           2
 6                      9                  conf_CI    3            FEP            ***********       ***********         NO                 0               0.0.0.0             8000            NULL                 NULL               NULL      NULL        100             NULL             NULL            8                  0                NO           3
(Number of results = 4)

---    END

		 */

        StringBuffer rcode=new StringBuffer();

        rcode.append("+++    UAP9600        "+timeUtill()+CHAN_LINE)
            .append("O&M    #917").append(CHAN_LINE)
            .append("%%LST MEMLNK:;%%").append(CHAN_LINE)
            .append("RETCODE = 0  操作成功。").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("操作结果如下").append(CHAN_LINE)
            .append("-------------").append(CHAN_LINE)
            .append("Service device number  IFM module number  Link name  Link number  Protocol type  Local IP address  Remote IP address1  Is support backup  N+1 check time  Remote IP address2  Remote IP port  Start dialog number  End dialog number  SCP type  ACN length  CTI capability  Userinfo length  SMC order type  SMC module number  SCP redirect ID  DR CTI Link  Local link number").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("0                      9                  call_CI    0            FEP            ***********       ***********         NO                 0               0.0.0.0             6800            NULL                 NULL               NULL      NULL        100             NULL             NULL            8                  0                NO           0                ").append(CHAN_LINE)
            .append("4                      9                  ivr_CI     1            FEP            ***********       ***********         NO                 0               0.0.0.0             5804            NULL                 NULL               NULL      NULL        100             NULL             NULL            8                  0                NO           1                ").append(CHAN_LINE)
            .append("5                      9                  agent_CI   2            FEP            ***********       ***********         NO                 0               0.0.0.0             5802            NULL                 NULL               NULL      NULL        100             NULL             NULL            8                  0                NO           2                ").append(CHAN_LINE)
            .append("6                      9                  conf_CI    3            FEP            ***********       ***********         NO                 0               0.0.0.0             8000            NULL                 NULL               NULL      NULL        100             NULL             NULL            8                  0                NO           3                ").append(CHAN_LINE)
            .append("(结果个数 = 4)").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);


        return rcode.toString();
    }



    ////LST MEAOBJ:MEID=5,MOCID=83886974, OBJFLAG=NO;
    public static String MEAOBJ(String MEID,String MOCID,String OBJFLAG)
    {
        StringBuffer body=new StringBuffer();
        body.append("O&M    #2315").append(CHAN_LINE)
            .append("%%LST MEAOBJ:MEID=").append(MEID).append(",MOCID=").append(MOCID).append(", OBJFLAG=").append(OBJFLAG).append(";%%").append(CHAN_LINE)
            .append("RETCODE = 0  操作成功。").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("操作结果如下").append(CHAN_LINE)
            .append("-------------").append(CHAN_LINE)
            .append("MOIID  MOIIDNAME").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("0              SIP ").append(CHAN_LINE)
            .append("1              SIP_AUTOSPACE  ").append(CHAN_LINE)
            .append("2              ISUP_M3UA    ").append(CHAN_LINE)
            .append("3              BICC_M3UA  ").append(CHAN_LINE)
            .append("4              PRA    ").append(CHAN_LINE)
            .append("5              Conf     ").append(CHAN_LINE)
            .append("6              tts       ").append(CHAN_LINE)
            .append("7              agent   ").append(CHAN_LINE)
            .append("8              call   ").append(CHAN_LINE)
            .append("(结果个数 = 9)").append(CHAN_LINE);

        return addHeadAndeEnd(body.toString());
    }


    public static String BRD(){
        StringBuffer body=new StringBuffer();
        body.append("O&M    #811").append(CHAN_LINE)
            .append("%%LST BRD:;%%").append(CHAN_LINE)
            .append("RETCODE = 0  操作成功。").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("操作结果如下").append(CHAN_LINE)
            .append("------------").append(CHAN_LINE)
            .append("框号    槽号    机架号  位置号  位置    网元类型  单板物理类型  右相邻上半槽位的单板类型  右相邻下半槽位的单板类型  应用类型  配置管理状态  单板可重装标识  对象组名称  引用标志").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append(" 1       1       1       1       前插板  公共      OMU单板       NULL                      HDU单板                   OMUDLAR   未锁定        不允许重装单板  PUBLIC      可引用  ").append(CHAN_LINE)
            .append(" 1       2       1       1       前插板  公共      HDU单板       NULL                      HDU单板                   OMUDLAR   未锁定        不允许重装单板  PUBLIC      可引用  ").append(CHAN_LINE)
            .append(" 1       3       1       1       前插板  UAP       ASGU0单板     NULL                      NULL                      AUAPP     未锁定        不允许重装单板  PUBLIC      可引用  ").append(CHAN_LINE)
            .append("  1       4       1       1       前插板  URP       RSGU0单板     NULL                      NULL                      UAPP      未锁定        不允许重装单板  PUBLIC      可引用  ").append(CHAN_LINE)
            .append(" 1       5       1       1       前插板  UAP       AMSU0单板     NULL                      NULL                      AUAPP     未锁定        不允许重装单板  PUBLIC      可引用  ").append(CHAN_LINE)
            .append(" 1       6       1       1       前插板  URP       RMSU0单板     NULL                      NULL                      UAPP      未锁定        不允许重装单板  PUBLIC      可引用  ").append(CHAN_LINE)
            .append(" 1       7       1       1       前插板  公共      SMU单板       NULL                      NULL                      COMM      未锁定        不允许重装单板  PUBLIC      可引用  ").append(CHAN_LINE)
            .append(" 1       8       1       1       前插板  公共      SMU单板       NULL                      NULL                      COMM      未锁定        不允许重装单板  PUBLIC      可引用  ").append(CHAN_LINE)
            .append("  1       15      1       1       前插板  公共      PWR单板       NULL                      NULL                      COMM      未锁定        不允许重装单板  PUBLIC      可引用  ").append(CHAN_LINE)
            .append(" 1       16      1       1       前插板  公共      PWR单板       NULL                      NULL                      COMM      未锁定        不允许重装单板  PUBLIC      可引用  ").append(CHAN_LINE)
            .append(" 1       17      1       1       前插板  UAP       ACIU单板      NULL                      NULL                      AUAPP     未锁定        不允许重装单板  PUBLIC      可引用  ").append(CHAN_LINE)
            .append("  1       18      1       1       前插板  URP       RCIU单板      NULL                      NULL                      UAPP      未锁定        不允许重装单板  PUBLIC      可引用  ").append(CHAN_LINE)
            .append("  1       27      1       1       前插板  公共      PWR单板       NULL                      NULL                      COMM      未锁定        不允许重装单板  PUBLIC      可引用  ").append(CHAN_LINE)
            .append("1       28      1       1       前插板  公共      PWR单板       NULL                      NULL                      COMM      未锁定        不允许重装单板  PUBLIC      可引用  ").append(CHAN_LINE)
            .append("(结果个数 = 14)").append(CHAN_LINE)
            .append(CHAN_LINE);


        return 	addHeadAndeEnd(body.toString());

    }

    public static String traffic()
    {


        StringBuffer body=new StringBuffer();
        body.append("TRAFFIC    #21    21").append(CHAN_LINE)
            .append(minuteBefore5MUtill()).append("    5    ").append(minuteBefore5MUtill()).append(CHAN_LINE)
            .append("MEID    TIME    所有话单        Period    MOIID    Credible    RET    RT    LRUOFL    MNTCIR    SERLA    NANS    ANS    BID    FWCTLCL    CTB    RERL    BRGABN    SERL    SEIZ    CNTR    应答话务量辅助量 (次)        CBSY    CLB    ALBLCL    CNT    ANSR ").append(CHAN_LINE)
            .append("5    ").append(timeChu()).append("    0    5    0    Credible    ").append(minuteUtill()).append("    ").append(minuteBefore5MUtill()).append("    0    0    4294967295    0    0    0    0    0    0.000000    0    0.000000    0    0    4294967295    0    0    4294967295    0    0").append(CHAN_LINE)
            .append("(Number of results = 1)").append(CHAN_LINE)
            .append(CHAN_LINE);

        return addHeadAndeEnd(body.toString());
    }


    public static String traffic9600Unit()
    {
		/*
							 +++    CDE        2015-06-19 12:37:10+08:00
					TRAFFIC    #306    306
					2015-06-19 12:30:00+08:00    5    2015-06-19 12:30:00+08:00
					MEID    Time        Period        MOIID    Credible        BSG module number        RET    RT    NOAUMFMTS12    NOIUMFSTM11    NOIEMFSTM9    NOARMFSTM25    NOIUMFMTS5    NOACMFMTS15    NOIUMFSTM6    NOACMFSTM24    NOAQMFSTM21    NOAPMFMTS18    NOIBMFMTS1    NOACMFSTM23    NOACMFMTS16    NOICMFSTM8    NOAUMFSTM20    NOIPMFMTS4    NOAUMFSTM27    NOAQMFMTS14    NOICMFMTS2    NOAQMFSTM22    NOAUMFMTS19    NOARMFMTS17    NOIPMFSTM10    NOIBMFSTM7    NOIEMFMTS3    NOAPMFSTM26    NOIUMFMTS0    NOAQMFMTS13
					5    1230    5    216,216    Credible    216,216    2015-06-19 12:35:00 +08:00    2015-06-19 12:30:00 +08:00    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0
					(Number of results = 1)

---    END
		 */
        StringBuffer body=new StringBuffer();

        body
            .append("+++    CDE        "+timeUtill()+CHAN_LINE)
            .append("TRAFFIC    #306    306").append(CHAN_LINE)
            .append(minuteBefore5MUtill()).append("    5    ").append(minuteBefore5MUtill()).append(CHAN_LINE)
            .append("MEID    Time        Period        MOIID    Credible        BSG module number        RET    RT    NOAUMFMTS12    NOIUMFSTM11    NOIEMFSTM9    NOARMFSTM25    NOIUMFMTS5    NOACMFMTS15    NOIUMFSTM6    NOACMFSTM24    NOAQMFSTM21    NOAPMFMTS18    NOIBMFMTS1    NOACMFSTM23    NOACMFMTS16    NOICMFSTM8    NOAUMFSTM20    NOIPMFMTS4    NOAUMFSTM27    NOAQMFMTS14    NOICMFMTS2    NOAQMFSTM22    NOAUMFMTS19    NOARMFMTS17    NOIPMFSTM10    NOIBMFSTM7    NOIEMFMTS3    NOAPMFSTM26    NOIUMFMTS0    NOAQMFMTS13  ").append(CHAN_LINE)
            .append("5    ").append(timeChu()).append("    5    216,216    Credible    216,216    ").append(minutePartUtill()).append("    ").append(minutePartBefore5MUtill()).append("    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    0    ").append(CHAN_LINE)
            .append("(Number of results = 1)").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("---    END"+CHAN_LINE)
        ;

        return body.toString();
    }


    public static String traffic9600NoUnit(){
		/*
		 +++    CDE        2015-06-19 10:12:10+08:00
TRAFFIC    #21    21
2015-06-19 10:05:00+08:00    5    2015-06-19 10:05:00+08:00
MEID    Time        all tickets        Period        MOIID    Credible        RET    RT    LROT8    MCN16    STA13    CNAT4    AT3    BT0    FCCLT7    CTBT10    AT14    ABRT5    ST12    ST1    CR17    ATA15    CBT9    CLB11    BBACLT6    CCT2    AR18
5    1005    0    5    0    Credible    2015-06-19 10:10:00 +08:00    2015-06-19 10:05:00 +08:00    0    0    4294967295    0    0    0    0    0    0.000000    0    0.000000    0    0    4294967295    0    0    4294967295    0    0
(Number of results = 1)

---    END
		 */

        StringBuffer body=new StringBuffer();
        body.append("TRAFFIC    #21    21").append(CHAN_LINE)
            .append(minuteBefore5MUtill()).append("    5    ").append(minuteBefore5MUtill()).append(CHAN_LINE)
            .append("MEID    TIME    所有话单        Period        MOIID    Credible        RET    RT    LROT8    MCN16    STA13    CNAT4    AT3    BT0    FCCLT7    CTBT10    AT14    ABRT5    ST12    ST1    CR17    ATA15    CBT9    CLB11    BBACLT6    CCT2    AR18 ").append(CHAN_LINE)
            .append("5    ").append(timeChu()).append("    0    5    0    Credible    ").append(minuteUtill()).append("    ").append(minuteBefore5MUtill()).append("    0    0    4294967295    0    0    0    0    0    0.000000    0    0.000000    0    0    4294967295    0    0    4294967295    0    0").append(CHAN_LINE)
            .append("(Number of results = 1)").append(CHAN_LINE)
            .append(CHAN_LINE);


        return  addHeadAndeEnd(body.toString());
    }



    //LST TSK:;
    public static String LSTTSK8100()
    {
        StringBuffer returnCode=new StringBuffer();
        returnCode.append("+++    HUAWEI UMG8900        ").append(timeUtill8100()).append(CHAN_LINE)
            .append("O&M    #3").append(CHAN_LINE)
            .append("%%LST TSK:;%%").append(CHAN_LINE)
            .append("RETCODE = 0  ").append("accomplished").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("Task basic info").append(CHAN_LINE)
            .append("---------------").append(CHAN_LINE)

            //1
            .append("                                         Task name = 7173305188").append(CHAN_LINE)
            .append("                                       Create time = 2015-02-27 17:37:45").append(CHAN_LINE)
            .append("                                        Task State = Ready").append(CHAN_LINE)
            .append("                                         Unit name = CPU Occupation Rate Statistics").append(CHAN_LINE)
            .append(CHAN_LINE)
            //2
            .append("                                         Task name = 7173305401").append(CHAN_LINE)
            .append("                                       Create time = 2015-02-27 17:37:45").append(CHAN_LINE)
            .append("                                        Task State = Ready").append(CHAN_LINE)
            .append("                                         Unit name = CPU Occupation Rate Statistics").append(CHAN_LINE)
            .append(CHAN_LINE)
            //3
            .append("                                         Task name = 7173305613").append(CHAN_LINE)
            .append("                                       Create time = 2015-02-27 17:37:45").append(CHAN_LINE)
            .append("                                        Task State = Ready").append(CHAN_LINE)
            .append("                                         Unit name = CPU Occupation Rate Statistics").append(CHAN_LINE)
            .append(CHAN_LINE)
            //4
            .append("                                         Task name = 7173305836").append(CHAN_LINE)
            .append("                                       Create time = 2015-02-27 17:37:45").append(CHAN_LINE)
            .append("                                        Task State = Ready").append(CHAN_LINE)
            .append("                                         Unit name = CPU Occupation Rate Statistics").append(CHAN_LINE)
            .append(CHAN_LINE)
            //5
            .append("                                         Task name = 7173305836").append(CHAN_LINE)
            .append("                                       Create time = 2015-02-27 17:37:45").append(CHAN_LINE)
            .append("                                        Task State = Ready").append(CHAN_LINE)
            .append("                                         Unit name = CPU Occupation Rate Statistics").append(CHAN_LINE)
            .append(CHAN_LINE)
            //6
            .append("                                         Task name = 7173305836").append(CHAN_LINE)
            .append("                                       Create time = 2015-02-27 17:37:45").append(CHAN_LINE)
            .append("                                        Task State = Ready").append(CHAN_LINE)
            .append("                                         Unit name = CPU Occupation Rate Statistics").append(CHAN_LINE)
            .append(CHAN_LINE)
            //7
            .append("                                         Task name = 7173306070").append(CHAN_LINE)
            .append("                                       Create time = 2015-02-27 17:37:45").append(CHAN_LINE)
            .append("                                        Task State = Ready").append(CHAN_LINE)
            .append("                                         Unit name = CPU Occupation Rate Statistics").append(CHAN_LINE)
            .append(CHAN_LINE)
            //8
            .append("                                         Task name = 7173306586").append(CHAN_LINE)
            .append("                                       Create time = 2015-02-27 17:37:45").append(CHAN_LINE)
            .append("                                        Task State = Ready").append(CHAN_LINE)
            .append("                                         Unit name = CPU Occupation Rate Statistics").append(CHAN_LINE)
            .append(CHAN_LINE)
            //9
            .append("                                         Task name = 7173306830").append(CHAN_LINE)
            .append("                                       Create time = 2015-02-27 17:37:45").append(CHAN_LINE)
            .append("                                        Task State = Ready").append(CHAN_LINE)
            .append("                                         Unit name = CPU Occupation Rate Statistics").append(CHAN_LINE)
            .append(CHAN_LINE)
            //10
            .append("                                         Task name = 7173307051").append(CHAN_LINE)
            .append("                                       Create time = 2015-02-27 17:37:45").append(CHAN_LINE)
            .append("                                        Task State = Ready").append(CHAN_LINE)
            .append("                                         Unit name = CPU Occupation Rate Statistics").append(CHAN_LINE)
            .append(CHAN_LINE)
            //11
            .append("                                         Task name = 7173307335").append(CHAN_LINE)
            .append("                                       Create time = 2015-02-27 17:37:45").append(CHAN_LINE)
            .append("                                        Task State = Ready").append(CHAN_LINE)
            .append("                                         Unit name = CPU Occupation Rate Statistics").append(CHAN_LINE)
            .append(CHAN_LINE)
            //12
            .append("                                         Task name = 7173307551").append(CHAN_LINE)
            .append("                                       Create time = 2015-02-27 17:37:45").append(CHAN_LINE)
            .append("                                        Task State = Ready").append(CHAN_LINE)
            .append("                                         Unit name = CPU Occupation Rate Statistics").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("(Number of results = 40)").append(CHAN_LINE)
            .append("4 reports in total").append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);
        return returnCode.toString();
    }

    //SHK HAND:;
    public static String SHKHAND8100()
    {
		/*
+++    Anonymous        2015-06-24 12:52:35+08:00
O&M    #0
%%SHK HAND:;%%
RETCODE = 0  Operation succeeded

---    END

		 */
        StringBuffer returnCode=new StringBuffer();
        returnCode.append("+++    Anonymous        ").append(timeUtill()).append(CHAN_LINE)
            .append("O&M    #0").append(CHAN_LINE)
            .append("%%SHK HAND:;%%").append(CHAN_LINE)
            .append("RETCODE = 0  ").append("Operation succeeded").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);


        return returnCode.toString();
    }

    public static String trafficUnit()
    {
		/*
		 +++    CDE        2015-06-16 15:23:30+00:00

TRAFFIC    #10000    10000
2015-06-16 15:10:00+00:00    5    2015-06-16 15:10:00+00:00

												MEID    TIME    槽        机框        Period    MOIID    Credible    RET    RT    单板        CPU峰值出现时间 (秒)        MACU    DPCU    CPU平均占用率辅助量 (次)        MECU
												0    1510    3    1    5    1:3:0    Credible    2015-06-16 15:15:00 +00:00    2015-06-16 15:10:00 +00:00    前插板(0)    4294967295    2    3    4294967295    1
												0    1510    17    1    5    1:17:0    Credible    2015-06-16 15:15:00 +00:00    2015-06-16 15:10:00 +00:00    前插板(0)    4294967295    8    300    4294967295    8

												0    1510    18    1    5    1:18:0    Credible    2015-06-16 15:15:00 +00:00    2015-06-16 15:10:00 +00:00    前插板(0)    4294967295    8    300    4294967295    8
												0    1510    7    1    5    1:7:0    Credible    2015-06-16 15:15:00 +00:00    2015-06-16 15:10:00 +00:00    前插板(0)    4294967295    17    300    4294967295    17
												0    1510    4    1    5    1:4:0    Credible    2015-06-16 15:15:00 +00:00    2015-06-16 15:10:00 +00:00    前插板(0)    4294967295    2    45    4294967295    1
												0    1510    5    1    5    1:5:0    Credible    2015-06-16 15:15:00 +00:00    2015-06-16 15:10:00 +00:00    前插板(0)    4294967295    17    91    4294967295    16
												0    1510    6    1    5    1:6:0    Credible    2015-06-16 15:15:00 +00:00    2015-06-16 15:10:00 +00:00    前插板(0)    4294967295    17    93    4294967295    16
												0    1510    1    1    5    1:1:0    Credible    2015-06-16 15:15:00 +00:00    2015-06-16 15:10:00 +00:00    前插板(0)    4294967295    33    3    4294967295    21
												0    1510    8    1    5    1:8:0    Credible    2015-06-16 15:15:00 +00:00    2015-06-16 15:10:00 +00:00    前插板(0)    4294967295    14    300    4294967295    14

(Number of results = 9)

---    END
		 */

        StringBuffer body=new StringBuffer();
        body.append("TRAFFIC    #10000    10000").append(CHAN_LINE)
            .append(minuteBefore5MUtill()).append("    5    ").append(minuteBefore5MUtill()).append(CHAN_LINE)
            .append("MEID    TIME    槽        机框        Period    MOIID    Credible    RET    RT    单板        CPU峰值出现时间 (秒)        MACU    DPCU    CPU平均占用率辅助量 (次)        MECU     ").append(CHAN_LINE)
            .append("0    ").append(timeChu()).append("    3    1    5    1:3:0    Credible        ").append(minuteUtill()).append("    ").append(minuteBefore5MUtill()).append("    前插板(0)    4294967295    2    3    4294967295    1    ").append(CHAN_LINE)
            .append("0    ").append(timeChu()).append("    17    1    5    1:17:0    Credible        ").append(minuteUtill()).append("    ").append(minuteBefore5MUtill()).append("    前插板(0)    4294967295    8    300    4294967295    8     ").append(CHAN_LINE)
            .append("0    ").append(timeChu()).append("    18    1    5    1:18:0    Credible        ").append(minuteUtill()).append("    ").append(minuteBefore5MUtill()).append("    前插板(0)    4294967295    8    300    4294967295    8    ").append(CHAN_LINE)
            .append("0    ").append(timeChu()).append("    7    1    5    1:7:0    Credible        ").append(minuteUtill()).append("    ").append(minuteBefore5MUtill()).append("    前插板(0)    4294967295    17    300    4294967295    17    ").append(CHAN_LINE)
            .append("0    ").append(timeChu()).append("    4    1    5    1:4:0    Credible        ").append(minuteUtill()).append("    ").append(minuteBefore5MUtill()).append("    前插板(0)    4294967295    2    45    4294967295    1    ").append(CHAN_LINE)
            .append("0    ").append(timeChu()).append("    5    1    5    1:5:0    Credible        ").append(minuteUtill()).append("    ").append(minuteBefore5MUtill()).append("    前插板(0)    4294967295    17   91    4294967295    16    ").append(CHAN_LINE)
            .append("0    ").append(timeChu()).append("    6    1    5    1:6:0    Credible        ").append(minuteUtill()).append("    ").append(minuteBefore5MUtill()).append("    前插板(0)    4294967295    17    93    4294967295    16    ").append(CHAN_LINE)
            .append("0    ").append(timeChu()).append("    1    1    5    1:1:0    Credible        ").append(minuteUtill()).append("    ").append(minuteBefore5MUtill()).append("    前插板(0)    4294967295    33    3    4294967295    21    ").append(CHAN_LINE)
            .append("0    ").append(timeChu()).append("    8    1    5    1:8:0    Credible        ").append(minuteUtill()).append("    ").append(minuteBefore5MUtill()).append("    前插板(0)    4294967295    14    300    4294967295    14    ").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("(Number of results = 9)").append(CHAN_LINE)
            .append(CHAN_LINE);

        return addHeadAndeEnd(body.toString());
    }

    //LST TRFINF:;
    public static String LSKTRFINF()
    {

        StringBuffer returnCode=new StringBuffer();
        returnCode.append("+++    Anonymous        ").append(timeUtill()).append(CHAN_LINE)
            .append("O&M    #145535").append(CHAN_LINE)
            .append("%%LST TRFINF:;%%").append(CHAN_LINE)
            .append("RETCODE = 0  ").append("Operation succeeded").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("List information of traffic task").append(CHAN_LINE)
            .append("---------------").append(CHAN_LINE)
            .append(CHAN_LINE)
            //1
            .append("                       TaskName = 1162153616").append(CHAN_LINE)
            .append("                TaskDescription = Empty").append(CHAN_LINE)
            .append("                         Status = Running").append(CHAN_LINE)
            .append("                    MeasureUnit = M3UA Llinkset Traffic").append(CHAN_LINE)
            .append(CHAN_LINE)
            //2
            .append("                       TaskName = 1162154240").append(CHAN_LINE)
            .append("                TaskDescription = Empty").append(CHAN_LINE)
            .append("                         Status = Running").append(CHAN_LINE)
            .append("                    MeasureUnit = M3UA Llinkset Traffic").append(CHAN_LINE)
            .append(CHAN_LINE)
            //3
            .append("                       TaskName = 4105850281").append(CHAN_LINE)
            .append("                TaskDescription = Empty").append(CHAN_LINE)
            .append("                         Status = Running").append(CHAN_LINE)
            .append("                    MeasureUnit = M3UA Llinkset Traffic").append(CHAN_LINE)
            .append(CHAN_LINE)
            //4
            .append("                       TaskName = 4105851707").append(CHAN_LINE)
            .append("                TaskDescription = Empty").append(CHAN_LINE)
            .append("                         Status = Running").append(CHAN_LINE)
            .append("                    MeasureUnit = M3UA Llinkset Traffic").append(CHAN_LINE)
            .append(CHAN_LINE)
            //5
            .append("                       TaskName = 4151846130").append(CHAN_LINE)
            .append("                TaskDescription = Empty").append(CHAN_LINE)
            .append("                         Status = Running").append(CHAN_LINE)
            .append("                    MeasureUnit = CPU Usage").append(CHAN_LINE)
            .append(CHAN_LINE)
            //6
            .append("                       TaskName = 74151847063").append(CHAN_LINE)
            .append("                TaskDescription = Empty").append(CHAN_LINE)
            .append("                         Status = Running").append(CHAN_LINE)
            .append("                    MeasureUnit = CPU Usage").append(CHAN_LINE)
            .append(CHAN_LINE)
            //7
            .append("                       TaskName = 4151847879").append(CHAN_LINE)
            .append("                TaskDescription = Empty").append(CHAN_LINE)
            .append("                         Status = Running").append(CHAN_LINE)
            .append("                    MeasureUnit = CPU Usage").append(CHAN_LINE)
            .append(CHAN_LINE)
            //8
            .append("                       TaskName = 4151848746").append(CHAN_LINE)
            .append("                TaskDescription = Empty").append(CHAN_LINE)
            .append("                         Status = Running").append(CHAN_LINE)
            .append("                    MeasureUnit = CPU Usage").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("(Number of results = 8)").append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);
        return returnCode.toString();
    }

    //%CRE TSK:TSKN
    public static String TSKTSKN(String str)
    {

        StringBuffer returnCode=new StringBuffer();
        returnCode.append("+++    HUAWEI UMG8900        ").append(timeUtill8100()).append(CHAN_LINE)
            .append("O&M    #3").append(CHAN_LINE)
            .append("%%").append(str).append("%%").append(CHAN_LINE)
            //.append("%%CRE TSK:TSKN=\"3141221056\",OPD=ALL,MU=OSCPURATE,TSKTP=PERM,TT=DAY,SPD=1,SP1S=0&0,SP1E=0&0,PRD=M5,ITMID=E134&E136&E135&E137&E133,OBJS=\"HRB:14:1\";%%").append(CHAN_LINE)
            .append("RETCODE = 0  ").append("accomplished").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);
        return returnCode.toString();


    }
    //CRE TRFCCP
    public static String CRETRFCCP8100(String TSKN,String MODU, String str)
    {

        StringBuffer returnCode=new StringBuffer();
        returnCode.append("+++    Anonymous        ").append(timeUtill()).append(CHAN_LINE)
            .append("O&M    #145565").append(CHAN_LINE)
            .append("%%").append(str).append("%%").append(CHAN_LINE)
            .append("RETCODE = 0  ").append("Operation succeeded").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("Create traffic task").append(CHAN_LINE)
            .append("-------------------").append(CHAN_LINE)
            .append(" TaskName                                             Module   Result").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append(TSKN).append("                                           35       Success").append(CHAN_LINE)
            .append(TSKN).append("                                           36       Success").append(CHAN_LINE)
            .append(TSKN).append("                                           71       Success").append(CHAN_LINE)
            .append(TSKN).append("                                           73       Success").append(CHAN_LINE)
            .append("(Number of results = 4)").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("Add object instance ").append(CHAN_LINE)
            .append("-------------------").append(CHAN_LINE)
            .append(" TaskName                                            Restriction  ObjectInstance ").append(CHAN_LINE)
            .append(CHAN_LINE)
            .append(TSKN).append("                                          0            Module number--").append(MODU).append(CHAN_LINE)
            .append(CHAN_LINE)
            .append("---    END").append(CHAN_LINE);
        return returnCode.toString();
    }



    public static String UAP6600Alarm()
    {
        /**
         +++    HW        2015-07-08 08:56:33+00:00
         ALARM  529         故障   次要告警    CDE       5228          软件系统
         网元ID  =  000
         同步流水号  =  1050
         告警名称  =  禁止格式化转换
         告警发生时间  =  2015-07-08 08:56:33+00:00
         定位信息  =  网元ID=5
         可自动清除  =  是
         父告警流水号  =  0
         告警节点号  =  0
         告警模块号  =  12
         重复告警次数  =  0



         ---    END
         */
        String s="+++    HW        "+timeUtill8100()+"+00:00"+CHAN_LINE+
            "ALARM  529         故障   次要告警    CDE       5228          软件系统"+ CHAN_LINE+
            "               网元ID  =  000"+ CHAN_LINE+
            "           同步流水号  =  1050"+ CHAN_LINE+
            "             告警名称  =  禁止格式化转换"+ CHAN_LINE+
            "         告警发生时间  =  "+timeUtill8100()+"+00:00"+CHAN_LINE+
            "             定位信息  =  网元ID=5"+CHAN_LINE+
            "           可自动清除  =  是"+CHAN_LINE+
            "         父告警流水号  =  0"+CHAN_LINE+
            "           告警节点号  =  0"+CHAN_LINE+
            "           告警模块号  =  12"+CHAN_LINE+
            "         重复告警次数  =  0"+CHAN_LINE+
            CHAN_LINE+
            CHAN_LINE+
            CHAN_LINE+
            "---    END"+CHAN_LINE;



        return s;
    }


    public static String UAP8100Alarm()
    {
        /**
         +++    Anonymous        2015-07-08 10:17:50+08:00
         ALARM  854436      Fault  Major       Exchange  2039    Hardware
         Sync serial No.  =  1708844
         Alarm name  =  FE port fault
         Alarm raised time  =  2015-07-08 10:17:22+08:00
         Location info  =  Board Type=HSC, Shelf Number=0, Frame Number=0, Slot
         Number=9, Port Number=1
         Auto Clear  =  true
         Cleared type  =  Normal cleared
         Cleared time  =  2015-07-08 10:17:46+08:00
         Module No.  =  2
         Alarm cause  =  Ethernet  Port restored
         Repair actions  =  No proposal available
         ---    END
         */

        String s="+++    Anonymous        "+timeUtill8100()+"+08:00"+CHAN_LINE+
            "ALARM  854436      Fault  Major       Exchange  2039    Hardware" +CHAN_LINE+
            "     Sync serial No.  =  1708844"+CHAN_LINE+
            "        Alarm name  =  FE port fault"+CHAN_LINE+
            "   Alarm raised time  =  "+timeUtill8100()+"+08:00"+CHAN_LINE+
            "       Location info  =  Board Type=HSC, Shelf Number=0, Frame Number=0, Slot"+CHAN_LINE+
            "                    Number=9, Port Number=1"+CHAN_LINE+
            "          Auto Clear  =  true"+CHAN_LINE+
            "        Cleared type  =  Normal cleared"+CHAN_LINE+
            "        Cleared time  =  "+timeUtill8100()+"+08:00"+CHAN_LINE+
            "          Module No.  =  2"+CHAN_LINE+
            "         Alarm cause  =  Ethernet  Port restored"+CHAN_LINE+
            "         Alarm cause  =  Ethernet  Port restored"+CHAN_LINE+
            "---    END"+CHAN_LINE;


        return s;
    }
    public static String UAP9600Alarm()
    {
/*
+++    HW        2015-07-11 05:43:16+00:00
ALARM  5           Fault  Major       UAP       202754        Signaling
                 MEID  =  005
      Sync serial No.  =  6
           Alarm name  =  SCCP SSN Paused
    Alarm raised time  =  2015-07-07 14:45:00+00:00
        Location info  =  SSN name=SCCP SSN(1), NI=National network, SPC=0x3C1000, SSN=0x1, OPC=0x49000
           Auto Clear  =  Yes
    Parent serial No.  =  0
           Subnet No.  =  0
           Module No.  =  56
         Repeat times  =  0



---    END


 */

        String alarm="+++    HW        "+timeUtill8100()+"+00:00"+CHAN_LINE+
            "ALARM  5           Fault  Major       UAP       202754        Signaling"+CHAN_LINE+
            "                 MEID  =  005"+CHAN_LINE+
            "      Sync serial No.  =  6"+CHAN_LINE+
            "           Alarm name  =  SCCP SSN Paused"+CHAN_LINE+
            "    Alarm raised time  =  "+timeUtill8100()+"+00:00"+CHAN_LINE+
            "        Location info  =  SSN name=SCCP SSN(1), NI=National network, SPC=0x3C1000, SSN=0x1, OPC=0x49000"+CHAN_LINE+
            "           Auto Clear  =  Yes"+CHAN_LINE+
            "    Parent serial No.  =  0"+CHAN_LINE+
            "           Subnet No.  =  0"+CHAN_LINE+
            "           Module No.  =  56"+CHAN_LINE+
            "        Repeat times  =  0"+CHAN_LINE+
            CHAN_LINE+
            CHAN_LINE+
            CHAN_LINE+
            "---    END"+CHAN_LINE;
        return alarm;
    }


    private static String getTIME(){
        Date date=new Date();

        DateFormat df=new SimpleDateFormat("HH:mm");
        String temp=df.format(date);

        return null;
    }






    private static String timeChu(){
        Date date=new Date();
        int minuet=date.getMinutes();
        int temp=minuet%5;
        date.setTime(date.getTime()-temp*1000*60-5*1000*60);


        DateFormat df=new SimpleDateFormat("HHmm");
        return df.format(date);
    }
    private static String timeUtill(){
        Date date=new Date();
        DateFormat df=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(date)+"+08:00";
    }
    private static String timeUtill8100()
    {
        Date date=new Date();
        DateFormat df=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(date);
    }

    private static String minuteUtill()
    {
        Date date=new Date();
        int minuet=date.getMinutes();
        int temp=minuet%5;
        date.setTime(date.getTime()-temp*1000*60);


        DateFormat df=new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return df.format(date)+":00+08:00";
    }
    private static String minute8100Utill()
    {
        Date date=new Date();
        int minuet=date.getMinutes();
        int temp=minuet%5;
        date.setTime(date.getTime()-temp*1000*60);


        DateFormat df=new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return df.format(date)+":00";
    }

    private static String minutePartUtill()
    {
        Date date=new Date();
        int minuet=date.getMinutes();
        int temp=minuet%5;
        date.setTime(date.getTime()-temp*1000*60);


        DateFormat df=new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return df.format(date)+":00 +08:00";
    }
    private static String minutePartBefore5MUtill()
    {
        Date date=new Date();
        int minuet=date.getMinutes();
        int temp=minuet%5;
        date.setTime(date.getTime()-temp*1000*60-5*1000*60);
        DateFormat df=new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return df.format(date)+":00 +08:00";
    }

    private static String minuteBefore5MUtill()
    {
        Date date=new Date();
        int minuet=date.getMinutes();
        int temp=minuet%5;
        date.setTime(date.getTime()-temp*1000*60-5*1000*60);
        DateFormat df=new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return df.format(date)+":00+08:00";
    }
    private static String minuteBefore5MUtillNoZ()
    {
        Date date=new Date();
        int minuet=date.getMinutes();
        int temp=minuet%5;
        date.setTime(date.getTime()-temp*1000*60-5*1000*60);
        DateFormat df=new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return df.format(date)+":00";
    }

    private static String addHeadAndeEnd(String body)
    {
        String head="+++    CDE        "+timeUtill()+CHAN_LINE;
        String end="---    END"+CHAN_LINE;
        StringBuffer returnCode=new StringBuffer(head);
        returnCode.append(body).append(end);
        return returnCode.toString();
    }


    //	public static void main(String[] args) {
    //		System.out.println(Traffic8100CPU("55","38"));
    //	}
}
