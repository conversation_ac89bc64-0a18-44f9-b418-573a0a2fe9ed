/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.ssl.view;

/**
 * @Description UAP模拟桩 VO
 * <AUTHOR>
 * @Since 2021/2/27
 */
public class UapStubVo {
    public boolean running;

    private String name = null;

    private String address = null;

    private int mainPort;

    private int alarmPort;

    private int trafficPort;

    public boolean isRunning() {
        return running;
    }

    public void setRunning(boolean running) {
        this.running = running;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public int getMainPort() {
        return mainPort;
    }

    public void setMainPort(int mainPort) {
        this.mainPort = mainPort;
    }

    public int getAlarmPort() {
        return alarmPort;
    }

    public void setAlarmPort(int alarmPort) {
        this.alarmPort = alarmPort;
    }

    public int getTrafficPort() {
        return trafficPort;
    }

    public void setTrafficPort(int trafficPort) {
        this.trafficPort = trafficPort;
    }
}
