/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.ssl.service;

import com.huawei.bme.web.util.ConversationUtils;
import com.huawei.mml.model.response.ResponseModelForUAP9600;
import com.huawei.oms.log.OMSLog;
import com.huawei.oms.log.OMSLogFactory;
import com.huawei.ssl.server.ClientManager;
import com.huawei.ssl.server.ServerManager;
import com.huawei.ssl.view.UapPorts;
import com.huawei.ssl.view.UapStubVo;

import java.io.IOException;
import java.io.OutputStream;
import java.net.Socket;
import java.nio.charset.StandardCharsets;

/**
 * @Description UAP模拟桩服务类
 * <AUTHOR>
 * @Since 2021/2/27
 */
public class UapStubService {
    private final static OMSLog LOGGER = OMSLogFactory.getLog(UapStubService.class);

    private static boolean running = false;

    public void init() {
        LOGGER.info("uap stub init...");

        UapStubVo vo = (UapStubVo) ConversationUtils.getConversation().getModel();
        vo.setRunning(running);
        vo.setMainPort(UapPorts.MAIN_PORT);
        vo.setAlarmPort(UapPorts.ALARM_PORT);
        vo.setTrafficPort(UapPorts.TRAFFIC_PORT);

        if (ClientManager.getInstance().getMainSocket() != null) {
            vo.setAddress(
                ClientManager.getInstance().getMainSocket().getInetAddress().toString().replaceFirst("/", ""));
        } else {
            vo.setAddress("disconnect");
        }
    }

    public void start() {
        LOGGER.info("uap service start...");
        if (!running) {
            UapStubVo vo = (UapStubVo) ConversationUtils.getConversation().getModel();
            UapPorts.setPorts(vo.getMainPort(), vo.getAlarmPort(), vo.getTrafficPort());
            ServerManager.start();
            running = true;
            vo.setRunning(running);
        }
    }

    public void stop() {
        LOGGER.info("uap service stop...");
        if (running) {
            UapStubVo vo = (UapStubVo) ConversationUtils.getConversation().getModel();
            ServerManager.stop();
            running = false;
            vo.setRunning(running);
        }
    }

    public void sendAlarm() {
        String alarmMessage = ResponseModelForUAP9600.UAP9600Alarm();

        Socket alarmSocket = ClientManager.getInstance().getAlarmSocket();
        try (OutputStream os = alarmSocket.getOutputStream()) {
            os.write(alarmMessage.getBytes(StandardCharsets.UTF_8));
            LOGGER.info("==============:alarm sent:===============");
        } catch (IOException e) {
            LOGGER.error("fail to send alarm: {}", e.getMessage());
        }
    }
}