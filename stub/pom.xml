<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>main-bundles</artifactId>
        <groupId>com.huawei.bme</groupId>
        <version>1.2.26B073</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>com.huawei.bme.webapp.basic</artifactId>
    <packaging>war</packaging>
    <name>com.huawei.bme.webapp.basic</name>
    <version>3.3.21.B133</version>
    <description>bme webapp basic</description>
    
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>

        <plugins>
            <!-- JavaScript compression plugin disabled due to compatibility issues
                 The maven-javascript-plugin 3.3.3.Final has dependency conflicts with Maven 3.8.3
                 and Java 17. The plugin is missing org.codehaus.plexus.util.DirectoryScanner class.
                 
                 To re-enable JavaScript compression, consider using:
                 1. yuicompressor-maven-plugin
                 2. minify-maven-plugin  
                 3. frontend-maven-plugin with webpack/gulp
            -->
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.7.1</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.1.1</version>
                <configuration>
                    <webXml>src\main\webapp\WEB-INF\web.xml</webXml>
                </configuration>
            </plugin>
        </plugins>
    </build>
    
    <dependencies>
        <dependency>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-utils</artifactId>
            <version>3.0.24</version>
        </dependency>
        
        <!-- Add other dependencies from original pom.xml as needed -->
        
    </dependencies>
</project>
