<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>main-bundles</artifactId>
        <groupId>com.huawei.bme</groupId>
        <version>1.2.26B073</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>com.huawei.bme.webapp.basic</artifactId>
    <packaging>war</packaging>
    <name>com.huawei.bme.webapp.basic</name>
    <version>3.3.21.B133</version>
    <description>bme webapp basic</description>
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.richfaces.cdk</groupId>
                <artifactId>maven-javascript-plugin</artifactId>
                <version>3.3.3.Final</version>
                <executions>
                    <execution>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>compress</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <!-- If no &quot;suffix&quot; must be add to output filename (maven&apos;s
                        configuration manage empty suffix like default). -->
                    <nosuffix>true</nosuffix>

                    <!-- [js only] Display possible errors in the code -->
                    <jswarn>false</jswarn>

                    <!-- Read the input file using &quot;encoding&quot;. -->
                    <encoding>UTF-8</encoding>

                    <!-- Insert line breaks in output after the specified column number. -->
                    <linebreakpos>3000</linebreakpos>
                    <outputDirectory>
                        target/
                    </outputDirectory>
<!--                    <aggregations>-->

                        <!-- ////////////////////////////////////////////////////////////////////////////////// -->
                        <!--[Theme: default] -->
                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/default/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/default/js/bme.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plugins from public, including bug fix by BME team.>>> -->
                                <include>**/default/js/jquery-1.8.3.bmefix.js</include>
                                <include>**/default/js/gcu_lib.js</include>
                                <include>**/default/js/ui.datetimepicker.bmefix.js</include>
                                <include>**/default/js/ui.datetimepicker.gcucal.js</include>
                                <include>**/default/js/jquery.ui.i18n.all.bmefix.js</include>
                                <include>**/default/js/jquery.jstree.bmefix.js</include>
                                <include>**/default/js/jquery.cookie.js</include>
                                <!-- <<<jQuery plugins from BME team.>>> -->
                                <include>**/default/js/jquery.bme.region.js</include>
                                <include>**/default/js/jquery.bme.validate.js</include>
                                <include>**/default/js/jquery.bme.uicommon.js</include>
                                <include>**/default/js/jquery.bme.tools.js</include>
                                <!-- <<<Normal JS from BME team.>>> -->
                                <include>**/default/js/jbme.js</include>
                                <include>**/default/js/jbme.theme.js</include>
                                <include>**/default/js/jbme.validate.rules.js</include>
                                <include>**/default/js/jbme.tab.js</include>
                                <include>**/default/js/jbme.layout.js</include>
                                <include>**/default/js/jbme.grid.js</include>
                                <include>**/default/js/jbme.spinner.js</include>
                                <include>**/default/js/jbme.treetable.js</include>
                                <include>**/default/js/jbme.portlet.js</include>
                                <include>**/default/js/jbme.ui.common.js</include>
                                <include>**/default/js/jbme.ui.popwin.js</include>
                                <include>**/default/js/jbme.ui.accordion.js</include>
                                <include>**/default/js/jbme.ui.autocomplete.js</include>
                                <include>**/default/js/jbme.ui.dropdown.js</include>
                                <include>**/default/js/jbme.multimenu.js</include>
                                <include>**/default/js/jbme.imageslider.js</include>
                                <include>**/default/js/jbme.ui.selectlist.js</include>
                                <include>**/default/js/jbme.number.js</include>
                                <include>**/default/js/jbme.selectbox.js</include>
                                <include>**/default/js/jbme.selectpanel.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/default/js/bme.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/default/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/default/js/bme-migrate.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plugins from public, including bug fix by BME team.>>> -->
                                <include>**/default/js/jquery-1.4.4.bmefix.js</include>
                                <include>**/default/js/gcu_lib.js</include>
                                <include>**/default/js/ui.datetimepicker.bmefix.js</include>
                                <include>**/default/js/ui.datetimepicker.gcucal.js</include>
                                <include>**/default/js/jquery.ui.i18n.all.bmefix.js</include>
                                <include>**/default/js/jquery.jstree.bmefix.js</include>
                                <include>**/default/js/jquery.cookie.js</include>
                                <!-- <<<jQuery plugins from BME team.>>> -->
                                <include>**/default/js/jquery.bme.region.js</include>
                                <include>**/default/js/jquery.bme.validate.js</include>
                                <include>**/default/js/jquery.bme.uicommon.js</include>
                                <include>**/default/js/jquery.bme.tools.js</include>
                                <!-- <<<Normal JS from BME team.>>> -->
                                <include>**/default/js/jbme.js</include>
                                <include>**/default/js/jbme.theme.js</include>
                                <include>**/default/js/jbme.validate.rules.js</include>
                                <include>**/default/js/jbme.tab.js</include>
                                <include>**/default/js/jbme.layout.js</include>
                                <include>**/default/js/jbme.grid.js</include>
                                <include>**/default/js/jbme.spinner.js</include>
                                <include>**/default/js/jbme.treetable.js</include>
                                <include>**/default/js/jbme.portlet.js</include>
                                <include>**/default/js/jbme.ui.common.js</include>
                                <include>**/default/js/jbme.ui.popwin.js</include>
                                <include>**/default/js/jbme.ui.accordion.js</include>
                                <include>**/default/js/jbme.ui.autocomplete.js</include>
                                <include>**/default/js/jbme.ui.dropdown.js</include>
                                <include>**/default/js/jbme.multimenu.js</include>
                                <include>**/default/js/jbme.imageslider.js</include>
                                <include>**/default/js/jbme.ui.selectlist.js</include>
                                <include>**/default/js/jbme.number.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/default/js/bme-migrate.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/default/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/default/js/bme.imagecropper.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery imagecropper plugins from public,>>> -->
                                <include>**/default/js/jquery.Jcrop.js</include>
                                <include>**/default/js/jquery.color.js</include>
                                <include>**/default/js/jbme.imagecropper.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/default/js/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/default/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/default/js/chart/bme.chart.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plot plugins from public,>>> -->
                                <include>**/default/js/chart/jquery.jqplot.js</include>
                                <include>**/default/js/chart/jbme.jqplot.js</include>
                                <include>**/default/js/chart/jqplot.barrenderer.js</include>
                                <include>**/default/js/chart/jqplot.pierenderer.js</include>
                                <include>**/default/js/chart/jqplot.donutRenderer.js</include>
                                <include>**/default/js/chart/jqplot.metergaugerenderer.js</include>
                                <include>**/default/js/chart/jbme.lineargauge.js</include>
                                <include>**/default/js/chart/jqplot.categoryaxisrenderer.js</include>
                                <include>**/default/js/chart/jqplot.enhancedlegendrenderer.js</include>
                                <include>**/default/js/chart/jqplot.canvasaxistickrenderer.js</include>
                                <include>**/default/js/chart/jqplot.canvastextrenderer.js</include>
                                <include>**/default/js/chart/jqplot.canvasaxislabelrenderer.js</include>
                                <include>**/default/js/chart/jqplot.showtip.js</include>
                                <include>**/default/js/chart/jqplot.pointlabels.js</include>
                                <include>**/default/js/chart/jqplot.barlabel.js</include>
                                <include>**/default/js/chart/jbme.chart.js</include>

                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/default/js/chart/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/default/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/default/js/chart/excanvas.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plot plugins from public,>>> -->
                                <include>**/default/js/chart/excanvas.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/default/js/chart/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/default/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/default/js/bme.main.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plugins from public, including bug fix by BME team.>>> -->
                                <include>**/default/js/jquery-1.8.3.bmefix.js</include>
                                <include>**/default/js/jquery.cookie.js</include>
                                <include>**/default/js/frameset.js</include>
                                <include>**/default/js/jbme.js</include>
                                <include>**/default/js/jbme.ui.common.js</include>
                                <include>**/default/js/jbme.tab.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/default/js/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/default/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/default/js/bme-migrate.main.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plugins from public, including bug fix by BME team.>>> -->
                                <include>**/default/js/jquery-1.4.4.bmefix.js</include>
                                <include>**/default/js/jquery.cookie.js</include>
                                <include>**/default/js/frameset.js</include>
                                <include>**/default/js/jbme.js</include>
                                <include>**/default/js/jbme.ui.common.js</include>
                                <include>**/default/js/jbme.tab.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/default/js/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/default/css/bme.min_zh.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/default/css/bme.main.min_zh.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/default/css/bme.page_zh.css</include>
                                <include>**/default/css/frameset.css</include>
                                <include>**/default/css/bme.css</include>
                                <include>**/default/css/bme.tab.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/default/css/bme.main.min_zh.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/default/css/bme.min_zh.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/default/css/bme.min_zh.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/default/css/bme.page_zh.css</include>
                                <include>**/default/css/bme.css</include>
                                <include>**/default/css/bme.panel.css</include>
                                <include>**/default/css/bme.spinner.css</include>
                                <include>**/default/css/bme.datetimepicker.css</include>
                                <include>**/default/css/bme.tab.css</include>
                                <include>**/default/css/bme.popmenu.css</include>
                                <include>**/default/css/bme.layout.css</include>
                                <include>**/default/css/bme.tree.css</include>
                                <include>**/default/css/bme.grid.css</include>
                                <include>**/default/css/bme.portlet.css</include>
                                <include>**/default/css/bme.tools.css</include>
                                <include>**/default/css/bme.imagecropper.css</include>
                                <include>**/default/css/jquery.jqplot.css</include>
                                <include>**/default/css/jquery.Jcrop.css</include>
                                <include>**/default/css/bme.imageslider.css</include>
                                <include>**/default/css/bme.selectbox.css</include>
                                <include>**/default/css/bme.selectpanel.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/default/css/bme.min_zh.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/default/css/bme.min_zh.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/default/css/bme.main.min_en.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/default/css/bme.page_en.css</include>
                                <include>**/default/css/frameset.css</include>
                                <include>**/default/css/bme.css</include>
                                <include>**/default/css/bme.tab.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/default/css/bme.main.min_en.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/default/css/bme.min_en.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/default/css/bme.min_en.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/default/css/bme.page_en.css</include>
                                <include>**/default/css/bme.css</include>
                                <include>**/default/css/bme.panel.css</include>
                                <include>**/default/css/bme.spinner.css</include>
                                <include>**/default/css/bme.datetimepicker.css</include>
                                <include>**/default/css/bme.tab.css</include>
                                <include>**/default/css/bme.popmenu.css</include>
                                <include>**/default/css/bme.layout.css</include>
                                <include>**/default/css/bme.tree.css</include>
                                <include>**/default/css/bme.grid.css</include>
                                <include>**/default/css/bme.portlet.css</include>
                                <include>**/default/css/bme.tools.css</include>
                                <include>**/default/css/bme.imagecropper.css</include>
                                <include>**/default/css/jquery.jqplot.css</include>
                                <include>**/default/css/jquery.Jcrop.css</include>
                                <include>**/default/css/bme.imageslider.css</include>
                                <include>**/default/css/bme.selectbox.css</include>
                                <include>**/default/css/bme.selectpanel.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/default/css/bme.min_en.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <!-- ////////////////////////////////////////////////////////////////////////////////// -->
                        <!--[Theme: arab] -->
                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/default/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/arab/js/bme.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plugins from public, including bug fix by BME team.>>> -->
                                <include>**/arab/js/jquery-1.4.4.bmefix.js</include>
                                <include>**/arab/js/gcu_lib.js</include>
                                <include>**/arab/js/ui.datetimepicker.bmefix.js</include>
                                <include>**/arab/js/ui.datetimepicker.gcucal.js</include>
                                <include>**/arab/js/jquery.ui.i18n.all.bmefix.js</include>
                                <include>**/arab/js/jquery.jstree.bmefix.js</include>
                                <include>**/arab/js/jquery.cookie.js</include>
                                <!-- <<<jQuery plugins from BME team.>>> -->
                                <include>**/arab/js/jquery.bme.region.js</include>
                                <include>**/arab/js/jquery.bme.validate.js</include>
                                <include>**/arab/js/jquery.bme.uicommon.js</include>
                                <include>**/arab/js/jquery.bme.tools.js</include>
                                <!-- <<<Normal JS from BME team.>>> -->
                                <include>**/arab/js/jbme.js</include>
                                <include>**/arab/js/jbme.theme.js</include>
                                <include>**/arab/js/jbme.validate.rules.js</include>
                                <include>**/arab/js/jbme.tab.js</include>
                                <include>**/arab/js/jbme.layout.js</include>
                                <include>**/arab/js/jbme.grid.js</include>
                                <include>**/arab/js/jbme.treetable.js</include>
                                <include>**/arab/js/jbme.portlet.js</include>
                                <include>**/arab/js/jbme.ui.common.js</include>
                                <include>**/arab/js/jbme.ui.popwin.js</include>
                                <include>**/arab/js/jbme.ui.accordion.js</include>
                                <include>**/arab/js/jbme.ui.autocomplete.js</include>
                                <include>**/arab/js/jbme.multimenu.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/arab/js/bme.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/default/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/arab/js/chart/bme.chart.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plot plugins from public,>>> -->
                                <include>**/arab/js/chart/jquery.jqplot.js</include>
                                <include>**/arab/js/chart/jbme.jqplot.js</include>
                                <include>**/arab/js/chart/jqplot.barrenderer.js</include>
                                <include>**/arab/js/chart/jqplot.pierenderer.js</include>
                                <include>**/arab/js/chart/jqplot.metergaugerenderer.js</include>
                                <include>**/arab/js/chart/jbme.lineargauge.js</include>
                                <include>**/arab/js/chart/jqplot.categoryaxisrenderer.js</include>
                                <include>**/arab/js/chart/jqplot.enhancedlegendrenderer.js</include>
                                <include>**/arab/js/chart/jqplot.canvasaxistickrenderer.js</include>
                                <include>**/arab/js/chart/jqplot.canvastextrenderer.js</include>
                                <include>**/arab/js/chart/jqplot.canvasaxislabelrenderer.js</include>
                                <include>**/arab/js/chart/jqplot.showtip.js</include>
                                <include>**/arab/js/chart/jqplot.pointlabels.js</include>
                                <include>**/arab/js/chart/jqplot.barlabel.js</include>
                                <include>**/arab/js/chart/jbme.chart.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/arab/js/chart/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/default/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/arab/js/chart/excanvas.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plot plugins from public,>>> -->
                                <include>**/arab/js/chart/excanvas.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/arab/js/chart/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/arab/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/arab/js/bme.main.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plugins from public, including bug fix by BME team.>>> -->
                                <include>**/arab/js/jquery-1.4.4.bmefix.js</include>
                                <include>**/arab/js/jquery.cookie.js</include>
                                <include>**/arab/js/frameset.js</include>
                                <include>**/arab/js/jbme.js</include>
                                <include>**/arab/js/jbme.ui.common.js</include>
                                <include>**/arab/js/jbme.tab.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/arab/js/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/arab/css/bme.min_zh.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/arab/css/bme.main.min_zh.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/arab/css/bme.page_zh.css</include>
                                <include>**/arab/css/frameset.css</include>
                                <include>**/arab/css/bme.css</include>
                                <include>**/arab/css/bme.tab.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/arab/css/bme.main.min_zh.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/arab/css/bme.min_zh.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/arab/css/bme.min_zh.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/arab/css/bme.page_zh.css</include>
                                <include>**/arab/css/bme.css</include>
                                <include>**/arab/css/bme.panel.css</include>
                                <include>**/arab/css/bme.datetimepicker.css</include>
                                <include>**/arab/css/bme.tab.css</include>
                                <include>**/arab/css/bme.popmenu.css</include>
                                <include>**/arab/css/bme.layout.css</include>
                                <include>**/arab/css/bme.tree.css</include>
                                <include>**/arab/css/bme.grid.css</include>
                                <include>**/arab/css/bme.portlet.css</include>
                                <include>**/arab/css/bme.tools.css</include>
                                <include>**/arab/css/jquery.jqplot.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/arab/css/bme.min_zh.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/arab/css/bme.min_zh.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/arab/css/bme.main.min_en.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/arab/css/bme.page_en.css</include>
                                <include>**/arab/css/frameset.css</include>
                                <include>**/arab/css/bme.css</include>
                                <include>**/arab/css/bme.tab.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/arab/css/bme.main.min_en.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/arab/css/bme.min_en.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/arab/css/bme.min_en.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/arab/css/bme.page_en.css</include>
                                <include>**/arab/css/bme.css</include>
                                <include>**/arab/css/bme.panel.css</include>
                                <include>**/arab/css/bme.datetimepicker.css</include>
                                <include>**/arab/css/bme.tab.css</include>
                                <include>**/arab/css/bme.popmenu.css</include>
                                <include>**/arab/css/bme.layout.css</include>
                                <include>**/arab/css/bme.tree.css</include>
                                <include>**/arab/css/bme.grid.css</include>
                                <include>**/arab/css/bme.portlet.css</include>
                                <include>**/arab/css/bme.tools.css</include>
                                <include>**/arab/css/jquery.jqplot.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/arab/css/bme.min_en.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/arab/css/bme.min_zh.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/arab/css/bme.main.min_ar.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/arab/css/bme.page_ar.css</include>
                                <include>**/arab/css/frameset.css</include>
                                <include>**/arab/css/bme.css</include>
                                <include>**/arab/css/bme.tab.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/arab/css/bme.main.min_ar.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/arab/css/bme.min_en.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/arab/css/bme.min_ar.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/arab/css/bme.page_ar.css</include>
                                <include>**/arab/css/bme.css</include>
                                <include>**/arab/css/bme.panel.css</include>
                                <include>**/arab/css/bme.datetimepicker.css</include>
                                <include>**/arab/css/bme.tab.css</include>
                                <include>**/arab/css/bme.popmenu.css</include>
                                <include>**/arab/css/bme.layout.css</include>
                                <include>**/arab/css/bme.tree.css</include>
                                <include>**/arab/css/bme.grid.css</include>
                                <include>**/arab/css/bme.portlet.css</include>
                                <include>**/arab/css/bme.tools.css</include>
                                <include>**/arab/css/jquery.jqplot.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/arab/css/bme.min_ar.css
                                </exclude>
                            </excludes>
                        </aggregation>


                        <!-- ////////////////////////////////////////////////////////////////////////////////// -->
                        <!--[Theme: metro] -->
                        <aggregation>
                            <!-- remove files after aggregation (metro: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (metro: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (metro: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/metro/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/metro/js/bme.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plugins from public, including bug fix by BME team.>>> -->
                                <include>**/metro/js/jquery-1.4.4.bmefix.js</include>
                                <include>**/metro/js/gcu_lib.js</include>
                                <include>**/metro/js/ui.datetimepicker.bmefix.js</include>
                                <include>**/metro/js/ui.datetimepicker.gcucal.js</include>
                                <include>**/metro/js/jquery.ui.i18n.all.bmefix.js</include>
                                <include>**/metro/js/jquery.jstree.bmefix.js</include>
                                <include>**/metro/js/jquery.cookie.js</include>
                                <!-- <<<jQuery plugins from BME team.>>> -->
                                <include>**/metro/js/jquery.bme.region.js</include>
                                <include>**/metro/js/jquery.bme.validate.js</include>
                                <include>**/metro/js/jquery.bme.uicommon.js</include>
                                <include>**/metro/js/jquery.bme.tools.js</include>
                                <!-- <<<Normal JS from BME team.>>> -->
                                <include>**/metro/js/jbme.js</include>
                                <include>**/metro/js/jbme.theme.js</include>
                                <include>**/metro/js/jbme.validate.rules.js</include>
                                <include>**/metro/js/jbme.tab.js</include>
                                <include>**/metro/js/jbme.layout.js</include>
                                <include>**/metro/js/jbme.grid.js</include>
                                <include>**/metro/js/jbme.spinner.js</include>
                                <include>**/metro/js/jbme.treetable.js</include>
                                <include>**/metro/js/jbme.portlet.js</include>
                                <include>**/metro/js/jbme.ui.common.js</include>
                                <include>**/metro/js/jbme.ui.popwin.js</include>
                                <include>**/metro/js/jbme.ui.accordion.js</include>
                                <include>**/metro/js/jbme.ui.autocomplete.js</include>
                                <include>**/metro/js/jbme.multimenu.js</include>
                                <include>**/metro/js/jbme.fire.js</include>
                                <include>**/metro/js/jbme.tips.js</include>
                                <include>**/metro/js/jbme.combobox.js</include>
                                <include>**/metro/js/jbme.checkradio.js</include>
                                <include>**/metro/js/jbme.field.js</include>
                                <include>**/metro/js/jbme.pagenation.js</include>
                                <include>**/metro/js/jbme.bigtable.js</include>
                                <include>**/metro/js/jbme.dblselectbox.js</include>
                                <include>**/metro/js/jbme.menu.js</include>
                                <include>**/metro/js/jbme.searchbox.js</include>
                                <include>**/metro/js/jbme.selecttree.js</include>
                                <include>**/metro/js/jbme.panel.js</include>
                                <include>**/metro/js/jbme.help.js</include>
                                <include>**/metro/js/jbme.ipaddress.js</include>
                                <include>**/metro/js/jbme.file.js</include>
                                <include>**/metro/js/jbme.timer.js</include>
                                <include>**/metro/js/jbme.msgbox.js</include>
                                <include>**/metro/js/jbme.popwin.js</include>
                                <include>**/metro/js/jbme.column.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/metro/js/bme.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>


                        <aggregation>
                            <!-- remove files after aggregation (metro: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (metro: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (metro: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/metro/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/metro/js/chart/bme.chart.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plot plugins from public,>>> -->
                                <include>**/metro/js/chart/jquery.jqplot.js</include>
                                <include>**/metro/js/chart/jbme.jqplot.js</include>
                                <include>**/metro/js/chart/jqplot.barrenderer.js</include>
                                <include>**/metro/js/chart/jqplot.pierenderer.js</include>
                                <include>**/metro/js/chart/jqplot.metergaugerenderer.js</include>
                                <include>**/metro/js/chart/jbme.lineargauge.js</include>
                                <include>**/metro/js/chart/jqplot.categoryaxisrenderer.js</include>
                                <include>**/metro/js/chart/jqplot.enhancedlegendrenderer.js</include>
                                <include>**/metro/js/chart/jqplot.canvasaxistickrenderer.js</include>
                                <include>**/metro/js/chart/jqplot.canvastextrenderer.js</include>
                                <include>**/metro/js/chart/jqplot.canvasaxislabelrenderer.js</include>
                                <include>**/metro/js/chart/jqplot.showtip.js</include>
                                <include>**/metro/js/chart/jqplot.pointlabels.js</include>
                                <include>**/metro/js/chart/jqplot.barlabel.js</include>
                                <include>**/metro/js/chart/jbme.chart.js</include>

                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/metro/js/chart/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (metro: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (metro: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (metro: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/metro/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/metro/js/chart/excanvas.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plot plugins from public,>>> -->
                                <include>**/metro/js/chart/excanvas.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/metro/js/chart/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (metro: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (metro: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (metro: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/metro/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/metro/js/bme.main.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plugins from public, including bug fix by BME team.>>> -->
                                <include>**/metro/js/jquery-1.4.4.bmefix.js</include>
                                <include>**/metro/js/jquery.cookie.js</include>
                                <include>**/metro/js/jbme.js</include>
                                <include>**/metro/js/jbme.ui.common.js</include>
                                <include>**/metro/js/jbme.tab.js</include>
                                <include>**/metro/js/jbme.menu.js</include>
                                <include>**/metro/js/jbme.timer.js</include>
                                <include>**/metro/js/jbme.tips.js</include>
                                <include>**/metro/js/frameset.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/metro/js/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (metro: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (metro: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (metro: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/metro/css/bme.min_zh.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/metro/css/bme.main.min_zh.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/metro/css/bme.page_zh.css</include>
                                <include>**/metro/css/frameset.css</include>
                                <include>**/metro/css/bme.basic.css</include>
                                <include>**/metro/css/bme.tab.css</include>
                                <include>**/metro/css/bme.button.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/metro/css/bme.main.min_zh.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (metro: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (metro: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (metro: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/metro/css/bme.min_zh.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/metro/css/bme.min_zh.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/metro/css/bme.page_zh.css</include>
                                <include>**/metro/css/bme.basic.css</include>
                                <include>**/metro/css/bme.panel.css</include>
                                <include>**/metro/css/bme.datetimepicker.css</include>
                                <include>**/metro/css/bme.tab.css</include>
                                <include>**/metro/css/bme.popmenu.css</include>
                                <include>**/metro/css/bme.layout.css</include>
                                <include>**/metro/css/bme.tree.css</include>
                                <include>**/metro/css/bme.grid.css</include>
                                <include>**/metro/css/bme.portlet.css</include>
                                <include>**/metro/css/bme.tools.css</include>
                                <include>**/metro/css/jquery.jqplot.css</include>
                                <include>**/metro/css/bme.accordion.css</include>
                                <include>**/metro/css/bme.autocomplete.css</include>
                                <include>**/metro/css/bme.bigtable.css</include>
                                <include>**/metro/css/bme.block.css</include>
                                <include>**/metro/css/bme.button.css</include>
                                <include>**/metro/css/bme.buttongroup.css</include>
                                <include>**/metro/css/bme.combobox.css</include>
                                <include>**/metro/css/bme.crumbs.css</include>
                                <include>**/metro/css/bme.checkradio.css</include>
                                <include>**/metro/css/bme.dblselectbox.css</include>
                                <include>**/metro/css/bme.field.css</include>
                                <include>**/metro/css/bme.file.css</include>
                                <include>**/metro/css/bme.help.css</include>
                                <include>**/metro/css/bme.img.css</include>
                                <include>**/metro/css/bme.ipaddress.css</include>
                                <include>**/metro/css/bme.label.css</include>
                                <include>**/metro/css/bme.link.css</include>
                                <include>**/metro/css/bme.menu.css</include>
                                <include>**/metro/css/bme.multimenu.css</include>
                                <include>**/metro/css/bme.pagenation.css</include>
                                <include>**/metro/css/bme.popupselect.css</include>
                                <include>**/metro/css/bme.progressbar.css</include>
                                <include>**/metro/css/bme.searchbox.css</include>
                                <include>**/metro/css/bme.select.css</include>
                                <include>**/metro/css/bme.selecttree.css</include>
                                <include>**/metro/css/bme.stepbox.css</include>
                                <include>**/metro/css/bme.timer.css</include>
                                <include>**/metro/css/bme.tips.css</include>
                                <include>**/metro/css/bme.toolbar.css</include>
                                <include>**/metro/css/bme.video.css</include>
                                <include>**/metro/css/bme.spinner.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/metro/css/bme.min_zh.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (metro: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (metro: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (metro: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/metro/css/bme.min_zh.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/metro/css/bme.main.min_en.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/metro/css/bme.page_en.css</include>
                                <include>**/metro/css/frameset.css</include>
                                <include>**/metro/css/bme.basic.css</include>
                                <include>**/metro/css/bme.tab.css</include>
                                <include>**/metro/css/bme.button.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/metro/css/bme.main.min_en.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (metro: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (metro: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (metro: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/metro/css/bme.min_en.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/metro/css/bme.min_en.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/metro/css/bme.page_en.css</include>
                                <include>**/metro/css/bme.basic.css</include>
                                <include>**/metro/css/bme.panel.css</include>
                                <include>**/metro/css/bme.datetimepicker.css</include>
                                <include>**/metro/css/bme.tab.css</include>
                                <include>**/metro/css/bme.popmenu.css</include>
                                <include>**/metro/css/bme.layout.css</include>
                                <include>**/metro/css/bme.tree.css</include>
                                <include>**/metro/css/bme.grid.css</include>
                                <include>**/metro/css/bme.portlet.css</include>
                                <include>**/metro/css/bme.tools.css</include>
                                <include>**/metro/css/jquery.jqplot.css</include>
                                <include>**/metro/css/bme.accordion.css</include>
                                <include>**/metro/css/bme.autocomplete.css</include>
                                <include>**/metro/css/bme.bigtable.css</include>
                                <include>**/metro/css/bme.block.css</include>
                                <include>**/metro/css/bme.button.css</include>
                                <include>**/metro/css/bme.buttongroup.css</include>
                                <include>**/metro/css/bme.combobox.css</include>
                                <include>**/metro/css/bme.crumbs.css</include>
                                <include>**/metro/css/bme.checkradio.css</include>
                                <include>**/metro/css/bme.dblselectbox.css</include>
                                <include>**/metro/css/bme.field.css</include>
                                <include>**/metro/css/bme.file.css</include>
                                <include>**/metro/css/bme.help.css</include>
                                <include>**/metro/css/bme.img.css</include>
                                <include>**/metro/css/bme.ipaddress.css</include>
                                <include>**/metro/css/bme.label.css</include>
                                <include>**/metro/css/bme.link.css</include>
                                <include>**/metro/css/bme.menu.css</include>
                                <include>**/metro/css/bme.multimenu.css</include>
                                <include>**/metro/css/bme.pagenation.css</include>
                                <include>**/metro/css/bme.popupselect.css</include>
                                <include>**/metro/css/bme.progressbar.css</include>
                                <include>**/metro/css/bme.searchbox.css</include>
                                <include>**/metro/css/bme.select.css</include>
                                <include>**/metro/css/bme.selecttree.css</include>
                                <include>**/metro/css/bme.stepbox.css</include>
                                <include>**/metro/css/bme.timer.css</include>
                                <include>**/metro/css/bme.tips.css</include>
                                <include>**/metro/css/bme.toolbar.css</include>
                                <include>**/metro/css/bme.video.css</include>
                                <include>**/metro/css/bme.spinner.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/metro/css/bme.min_en.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <!-- ////////////////////////////////////////////////////////////////////////////////// -->
                        <!--[Theme: agile] -->
                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/agile/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/agile/js/bme.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plugins from public, including bug fix by BME team.>>> -->
                                <include>**/agile/js/jquery-1.8.3.bmefix.js</include>
                                <include>**/agile/js/gcu_lib.js</include>
                                <include>**/agile/js/ui.datetimepicker.bmefix.js</include>
                                <include>**/agile/js/ui.datetimepicker.gcucal.js</include>
                                <include>**/agile/js/jquery.ui.i18n.all.bmefix.js</include>
                                <include>**/agile/js/jquery.jstree.bmefix.js</include>
                                <include>**/agile/js/jquery.cookie.js</include>
                                <!-- <<<jQuery plugins from BME team.>>> -->
                                <include>**/agile/js/jquery.bme.region.js</include>
                                <include>**/agile/js/jquery.bme.validate.js</include>
                                <include>**/agile/js/jquery.bme.uicommon.js</include>
                                <include>**/agile/js/jquery.bme.tools.js</include>
                                <!-- <<<Normal JS from BME team.>>> -->
                                <include>**/agile/js/jbme.js</include>
                                <include>**/agile/js/jbme.theme.js</include>
                                <include>**/agile/js/jbme.validate.rules.js</include>
                                <include>**/agile/js/jbme.tab.js</include>
                                <include>**/agile/js/jbme.layout.js</include>
                                <include>**/agile/js/jbme.grid.js</include>
                                <include>**/agile/js/jbme.spinner.js</include>
                                <include>**/agile/js/jbme.treetable.js</include>
                                <include>**/agile/js/jbme.portlet.js</include>
                                <include>**/agile/js/jbme.ui.common.js</include>
                                <include>**/agile/js/jbme.ui.popwin.js</include>
                                <include>**/agile/js/jbme.ui.accordion.js</include>
                                <include>**/agile/js/jbme.ui.autocomplete.js</include>
                                <include>**/agile/js/jbme.ui.dropdown.js</include>
                                <include>**/agile/js/jbme.multimenu.js</include>
                                <include>**/agile/js/jbme.imageslider.js</include>
                                <include>**/agile/js/jbme.ui.selectlist.js</include>
                                <include>**/agile/js/jbme.number.js</include>
                                <include>**/agile/js/jbme.checkradio.js</include>
                                <include>**/agile/js/jbme.selectbox.js</include>
                                <include>**/agile/js/jbme.selectpanel.js</include>
                                <include>**/agile/js/jbme.stepslider.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/agile/js/bme.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/agile/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/agile/js/bme-migrate.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plugins from public, including bug fix by BME team.>>> -->
                                <include>**/agile/js/jquery-1.4.4.bmefix.js</include>
                                <include>**/agile/js/gcu_lib.js</include>
                                <include>**/agile/js/ui.datetimepicker.bmefix.js</include>
                                <include>**/agile/js/ui.datetimepicker.gcucal.js</include>
                                <include>**/agile/js/jquery.ui.i18n.all.bmefix.js</include>
                                <include>**/agile/js/jquery.jstree.bmefix.js</include>
                                <include>**/agile/js/jquery.cookie.js</include>
                                <!-- <<<jQuery plugins from BME team.>>> -->
                                <include>**/agile/js/jquery.bme.region.js</include>
                                <include>**/agile/js/jquery.bme.validate.js</include>
                                <include>**/agile/js/jquery.bme.uicommon.js</include>
                                <include>**/agile/js/jquery.bme.tools.js</include>
                                <!-- <<<Normal JS from BME team.>>> -->
                                <include>**/agile/js/jbme.js</include>
                                <include>**/agile/js/jbme.theme.js</include>
                                <include>**/agile/js/jbme.validate.rules.js</include>
                                <include>**/agile/js/jbme.tab.js</include>
                                <include>**/agile/js/jbme.layout.js</include>
                                <include>**/agile/js/jbme.grid.js</include>
                                <include>**/agile/js/jbme.spinner.js</include>
                                <include>**/agile/js/jbme.treetable.js</include>
                                <include>**/agile/js/jbme.portlet.js</include>
                                <include>**/agile/js/jbme.ui.common.js</include>
                                <include>**/agile/js/jbme.ui.popwin.js</include>
                                <include>**/agile/js/jbme.ui.accordion.js</include>
                                <include>**/agile/js/jbme.ui.autocomplete.js</include>
                                <include>**/agile/js/jbme.ui.dropdown.js</include>
                                <include>**/agile/js/jbme.multimenu.js</include>
                                <include>**/agile/js/jbme.imageslider.js</include>
                                <include>**/agile/js/jbme.ui.selectlist.js</include>
                                <include>**/agile/js/jbme.number.js</include>
                                <include>**/agile/js/jbme.checkradio.js</include>
                                <include>**/agile/js/jbme.stepslider.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/agile/js/bme-migrate.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/agile/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/agile/js/bme.imagecropper.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery imagecropper plugins from public,>>> -->
                                <include>**/agile/js/jquery.Jcrop.js</include>
                                <include>**/agile/js/jquery.color.js</include>
                                <include>**/agile/js/jbme.imagecropper.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/agile/js/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/agile/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/agile/js/chart/bme.chart.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plot plugins from public,>>> -->
                                <include>**/agile/js/chart/jquery.jqplot.js</include>
                                <include>**/agile/js/chart/jbme.jqplot.js</include>
                                <include>**/agile/js/chart/jqplot.barrenderer.js</include>
                                <include>**/agile/js/chart/jqplot.pierenderer.js</include>
                                <include>**/agile/js/chart/jqplot.donutRenderer.js</include>
                                <include>**/agile/js/chart/jqplot.metergaugerenderer.js</include>
                                <include>**/agile/js/chart/jbme.lineargauge.js</include>
                                <include>**/agile/js/chart/jqplot.categoryaxisrenderer.js</include>
                                <include>**/agile/js/chart/jqplot.enhancedlegendrenderer.js</include>
                                <include>**/agile/js/chart/jqplot.canvasaxistickrenderer.js</include>
                                <include>**/agile/js/chart/jqplot.canvastextrenderer.js</include>
                                <include>**/agile/js/chart/jqplot.canvasaxislabelrenderer.js</include>
                                <include>**/agile/js/chart/jqplot.showtip.js</include>
                                <include>**/agile/js/chart/jqplot.pointlabels.js</include>
                                <include>**/agile/js/chart/jqplot.barlabel.js</include>
                                <include>**/agile/js/chart/jbme.chart.js</include>

                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/agile/js/chart/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/agile/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/agile/js/chart/excanvas.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plot plugins from public,>>> -->
                                <include>**/agile/js/chart/excanvas.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/agile/js/chart/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/agile/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/agile/js/bme.main.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plugins from public, including bug fix by BME team.>>> -->
                                <include>**/agile/js/jquery-1.8.3.bmefix.js</include>
                                <include>**/agile/js/jquery.cookie.js</include>
                                <include>**/agile/js/jbme.js</include>
                                <include>**/agile/js/jbme.ui.common.js</include>
                                <include>**/agile/js/jbme.tab.js</include>
                                <include>**/agile/js/frameset.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/agile/js/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/agile/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/agile/js/bme-migrate.main.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plugins from public, including bug fix by BME team.>>> -->
                                <include>**/agile/js/jquery-1.4.4.bmefix.js</include>
                                <include>**/agile/js/jquery.cookie.js</include>
                                <include>**/agile/js/jbme.js</include>
                                <include>**/agile/js/jbme.ui.common.js</include>
                                <include>**/agile/js/jbme.tab.js</include>
                                <include>**/agile/js/frameset.js</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/agile/js/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>true</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/agile/js/bme.min.js
                                </output> -->
                            <output>
                                src/main/webapp/theme/agile/js/jbme.timeline.min.js
                            </output>

                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <!-- <<<jQuery plot plugins from public,>>> -->
                                <include>**/agile/js/jbme.timeline.js</include>

                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/agile/js/*.min.js
                                </exclude>
                                <!-- <exclude> ${project.build.directory}/compressed/org/richfaces/renderkit/html/scripts/jquery.jcarousel-min.js
                                    </exclude> <exclude>**/scriptaculo*</exclude> -->
                            </excludes>
                        </aggregation>


                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/agile/css/bme.min_zh.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/agile/css/bme.main.min_zh.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/agile/css/bme.page_zh.css</include>
                                <include>**/agile/css/bme.css</include>
                                <include>**/agile/css/bme.tab.css</include>
                                <include>**/agile/css/frameset.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/agile/css/bme.main.min_zh.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/agile/css/bme.min_zh.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/agile/css/bme.min_zh.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/agile/css/bme.page_zh.css</include>
                                <include>**/agile/css/bme.css</include>
                                <include>**/agile/css/bme.panel.css</include>
                                <include>**/agile/css/bme.spinner.css</include>
                                <include>**/agile/css/bme.datetimepicker.css</include>
                                <include>**/agile/css/bme.tab.css</include>
                                <include>**/agile/css/bme.popmenu.css</include>
                                <include>**/agile/css/bme.layout.css</include>
                                <include>**/agile/css/bme.tree.css</include>
                                <include>**/agile/css/bme.grid.css</include>
                                <include>**/agile/css/bme.portlet.css</include>
                                <include>**/agile/css/bme.tools.css</include>
                                <include>**/agile/css/bme.imagecropper.css</include>
                                <include>**/agile/css/jquery.jqplot.css</include>
                                <include>**/agile/css/jquery.Jcrop.css</include>
                                <include>**/agile/css/bme.imageslider.css</include>
                                <include>**/agile/css/bme.selectbox.css</include>
                                <include>**/agile/css/bme.selectpanel.css</include>
                                <include>**/agile/css/bme.stepslider.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/agile/css/bme.min_zh.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/agile/css/bme.min_zh.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/agile/css/bme.main.min_en.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/agile/css/bme.page_en.css</include>
                                s
                                <include>**/agile/css/bme.css</include>
                                <include>**/agile/css/bme.tab.css</include>
                                <include>**/agile/css/frameset.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/agile/css/bme.main.min_en.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/agile/css/bme.min_en.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/agile/css/bme.min_en.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/agile/css/bme.page_en.css</include>
                                <include>**/agile/css/bme.css</include>
                                <include>**/agile/css/bme.panel.css</include>
                                <include>**/agile/css/bme.spinner.css</include>
                                <include>**/agile/css/bme.datetimepicker.css</include>
                                <include>**/agile/css/bme.tab.css</include>
                                <include>**/agile/css/bme.popmenu.css</include>
                                <include>**/agile/css/bme.layout.css</include>
                                <include>**/agile/css/bme.tree.css</include>
                                <include>**/agile/css/bme.grid.css</include>
                                <include>**/agile/css/bme.portlet.css</include>
                                <include>**/agile/css/bme.tools.css</include>
                                <include>**/agile/css/bme.imagecropper.css</include>
                                <include>**/agile/css/jquery.jqplot.css</include>
                                <include>**/agile/css/jquery.Jcrop.css</include>
                                <include>**/agile/css/bme.imageslider.css</include>
                                <include>**/agile/css/bme.selectbox.css</include>
                                <include>**/agile/css/bme.selectpanel.css</include>
                                <include>**/agile/css/bme.stepslider.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/agile/css/bme.min_en.css
                                </exclude>
                            </excludes>
                        </aggregation>

                        <aggregation>
                            <!-- remove files after aggregation (default: false) <removeIncluded>true</removeIncluded> -->
                            <!-- insert new line after each concatenation (default: false) -->
                            <insertNewLine>true</insertNewLine>
                            <!-- insert semicolon after each concatenation (default: false) -->
                            <insertSemicolon>false</insertSemicolon>
                            <!-- <output> ${project.build.directory}/${project.build.finalName}/theme/agile/css/bme.min_en.css
                                </output> -->
                            <output>
                                src/main/webapp/theme/agile/css/bme.timeline.min.css
                            </output>
                            <!-- files to include, path relative to output's directory or absolute
                                path -->
                            <includes>
                                <include>**/agile/css/bme.timeline.css</include>
                            </includes>
                            <!-- files to exclude, path relative to output's directory -->
                            <excludes>
                                <exclude>
                                    ${project.build.directory}/${project.build.finalName}/theme/agile/css/bme.min_en.css
                                </exclude>
                            </excludes>
                        </aggregation>

<!--                    </aggregations>-->
                </configuration>
            </plugin>
<!--            <plugin>-->
<!--                <artifactId>exec-maven-plugin</artifactId>-->
<!--                <groupId>org.codehaus.mojo</groupId>-->
<!--                <version>1.2</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>tld build</id>-->
<!--                        <phase>install</phase>-->
<!--                        <goals>-->
<!--                            <goal>exec</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <executable>codetool.bat</executable>-->
<!--                    <workingDirectory>${basedir}\..\..\tools\code-format\</workingDirectory>-->
<!--                </configuration>-->
<!--            </plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>

<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-compiler-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <source>1.8</source>-->
<!--                    <target>1.8</target>-->
<!--                    <encoding>UTF-8</encoding>-->
<!--                    <compilerArguments>-->
<!--                        <extdirs>src\main\webapp\WEB-INF\lib</extdirs>-->
<!--                    </compilerArguments>-->
<!--                </configuration>-->
<!--            </plugin>-->

        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-utils</artifactId>
            <version>3.0.24</version>  <!-- 选择与您的 Maven 兼容的版本 -->
        </dependency>
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.bme</groupId>
            <artifactId>com.huawei.bme.default.library</artifactId>
            <version>1.2.27B030-SNAPSHOT</version>
            <type>pom</type>
        </dependency>


<!--        <dependency>-->
<!--            <groupId>nggd-bme</groupId>-->
<!--            <artifactId>spring-mock</artifactId>-->
<!--            <version>2.5.6</version>-->
<!--            <scope>test</scope>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>javax.servlet.jsp</groupId>-->
<!--            <artifactId>com.springsource.javax.servlet.jsp</artifactId>-->
<!--            <scope>test</scope>-->
<!--            <version>${javax.servlet.jsp.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>com.springsource.javax.servlet</artifactId>
            <scope>test</scope>
            <version>${javax.servlet.version}</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
            <version>1.9.2</version>
        </dependency>
        <!--<dependency>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>2.3.2</version>
        </dependency>-->
        <dependency>
            <groupId>ch.ethz.ganymed</groupId>
            <artifactId>ganymed-ssh2</artifactId>
            <version>build210</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.oms</groupId>
            <artifactId>com.huawei.oms.gapi.fm</artifactId>
            <version>2.0.315022-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.oms</groupId>
            <artifactId>com.huawei.oms.log</artifactId>
            <version>2.0.315022-SNAPSHOT</version>
        </dependency>

        	<dependency>
                <groupId>com.huawei.i2000</groupId>
                <artifactId>com.huawei.oms.gapi.pm</artifactId>
                <version>8.233.000019-SNAPSHOT</version>
            </dependency>

        <dependency>
            <groupId>com.huawei.oms</groupId>
            <artifactId>com.huawei.oms.sbus</artifactId>
            <version>2.0.315022-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.osgi</groupId>
            <artifactId>org.osgi.core</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.i2000</groupId>
            <artifactId>com.huawei.oms.gapi.pm</artifactId>
            <version>8.233.000019-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.55</version>
        </dependency>
        <dependency>
            <groupId>org.snmp4j</groupId>
            <artifactId>snmp4j</artifactId>
            <version>2.8.8-htrunk2</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.16.18</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-server</artifactId>
            <version>9.4.43.v20210629</version>
        </dependency>

        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-core</artifactId>
            <version>3.3.6</version>
        </dependency>
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-frontend-jaxws</artifactId>
            <version>3.6.1</version>
        </dependency>

        <dependency>
            <groupId>com.huawei.i2000</groupId>
            <artifactId>com.huawei.i2000.gapi.med.soap</artifactId>
            <version>8.233.000031-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>

        <dependency>
            <groupId>jdom</groupId>
            <artifactId>jdom</artifactId>
            <version>1.0</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>net.sf.json-lib</groupId>-->
<!--            <artifactId>json-lib</artifactId>-->
<!--            <version>2.4</version>-->
<!--            <classifier>jdk15</classifier>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20090211</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.6.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>4.6.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>1.12.13</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <version>1.12.13</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>3.22.0</version>
            <scope>test</scope>
        </dependency>
        <!--<dependency>
            <groupId>com.huawei.bme</groupId>
            <artifactId>com.huawei.bme.dbpool</artifactId>
            <version>3.2.60.SPC211</version>
            <scope>compile</scope>
        </dependency>-->
    </dependencies>

</project>